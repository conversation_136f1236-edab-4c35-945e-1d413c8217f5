#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask
import os

app = Flask(__name__)

@app.route('/admin')
def admin():
    return '<h1>后台管理系统测试</h1><p>系统正常运行</p>'

@app.route('/admin/test')
def test():
    # 测试数据库连接
    import sqlite3
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    DB_PATH = os.path.join(BASE_DIR, 'DATABASE-HOSPITAL', 'hospital_indicator_system.db')
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM indicators")
        count = cursor.fetchone()[0]
        conn.close()
        return f'<h1>数据库测试</h1><p>指标数量: {count}</p>'
    except Exception as e:
        return f'<h1>数据库错误</h1><p>{str(e)}</p>'

if __name__ == '__main__':
    print("测试管理系统启动...")
    print("访问: http://localhost:5002/admin")
    app.run(debug=True, host='0.0.0.0', port=5002)
