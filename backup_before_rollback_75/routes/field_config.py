"""
指标字段配置管理路由
用于管理底层指标字段的显示配置
"""

from flask import Blueprint, request, jsonify, render_template
import sqlite3
import json
from datetime import datetime

field_config_bp = Blueprint('field_config', __name__)

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect('DATABASE-HOSPITAL/hospital_indicators.db')
    conn.row_factory = sqlite3.Row
    return conn

@field_config_bp.route('/admin/field-config')
def field_config_page():
    """字段配置管理页面"""
    return render_template('admin/field_config.html')

@field_config_bp.route('/api/field-config')
def get_field_configs():
    """获取所有字段配置"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT * FROM indicator_field_config 
            WHERE is_active = 1 
            ORDER BY display_section, sort_order
        """)
        
        configs = []
        for row in cursor.fetchall():
            config = dict(row)
            configs.append(config)
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': configs
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@field_config_bp.route('/api/field-config/<field_name>')
def get_field_config(field_name):
    """获取单个字段配置"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT * FROM indicator_field_config 
            WHERE field_name = ? AND is_active = 1
        """, (field_name,))
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return jsonify({
                'success': True,
                'data': dict(row)
            })
        else:
            return jsonify({
                'success': False,
                'error': '字段配置不存在'
            }), 404
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@field_config_bp.route('/api/field-config/<field_name>', methods=['PUT'])
def update_field_config(field_name):
    """更新字段配置"""
    try:
        data = request.get_json()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 更新字段配置
        cursor.execute("""
            UPDATE indicator_field_config 
            SET field_label = ?, 
                field_type = ?, 
                display_section = ?, 
                is_required = ?, 
                sort_order = ?, 
                help_text = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE field_name = ?
        """, (
            data.get('field_label'),
            data.get('field_type'),
            data.get('display_section'),
            data.get('is_required', 0),
            data.get('sort_order', 0),
            data.get('help_text'),
            field_name
        ))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '字段配置更新成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@field_config_bp.route('/api/indicators/<indicator_id>/field-values')
def get_indicator_field_values(indicator_id):
    """获取指标的字段值"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取指标基本信息
        cursor.execute("""
            SELECT id, name, indicator_type, is_bottom_level 
            FROM indicators 
            WHERE id = ?
        """, (indicator_id,))
        
        indicator = cursor.fetchone()
        if not indicator:
            return jsonify({
                'success': False,
                'error': '指标不存在'
            }), 404
        
        # 获取字段配置和值
        cursor.execute("""
            SELECT 
                ifc.field_name,
                ifc.field_label,
                ifc.field_type,
                ifc.display_section,
                ifc.is_required,
                ifc.sort_order,
                ifc.help_text,
                ifv.field_value
            FROM indicator_field_config ifc
            LEFT JOIN indicator_field_values ifv ON ifc.field_name = ifv.field_name 
                AND ifv.indicator_id = ?
            WHERE ifc.is_active = 1
            ORDER BY ifc.display_section, ifc.sort_order
        """, (indicator_id,))
        
        fields = []
        for row in cursor.fetchall():
            field = dict(row)
            fields.append(field)
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': {
                'indicator': dict(indicator),
                'fields': fields
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@field_config_bp.route('/api/indicators/<indicator_id>/field-values', methods=['PUT'])
def update_indicator_field_values(indicator_id):
    """更新指标的字段值"""
    try:
        data = request.get_json()
        field_values = data.get('field_values', {})
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 更新或插入字段值
        for field_name, field_value in field_values.items():
            if field_value is not None and field_value != '':
                cursor.execute("""
                    INSERT OR REPLACE INTO indicator_field_values 
                    (indicator_id, field_name, field_value, updated_at)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                """, (indicator_id, field_name, field_value))
            else:
                # 删除空值
                cursor.execute("""
                    DELETE FROM indicator_field_values 
                    WHERE indicator_id = ? AND field_name = ?
                """, (indicator_id, field_name))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '字段值更新成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@field_config_bp.route('/api/indicators/<indicator_id>/basic-fields')
def get_indicator_basic_fields(indicator_id):
    """获取指标的基础信息字段（用于前端显示）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取基础信息字段配置和值
        cursor.execute("""
            SELECT 
                ifc.field_name,
                ifc.field_label,
                ifc.field_type,
                ifc.is_required,
                ifc.sort_order,
                ifv.field_value
            FROM indicator_field_config ifc
            LEFT JOIN indicator_field_values ifv ON ifc.field_name = ifv.field_name 
                AND ifv.indicator_id = ?
            WHERE ifc.display_section = 'basic' AND ifc.is_active = 1
            ORDER BY ifc.sort_order
        """, (indicator_id,))
        
        basic_fields = []
        for row in cursor.fetchall():
            field = dict(row)
            basic_fields.append(field)
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': basic_fields
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
