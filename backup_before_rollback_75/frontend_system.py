#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院指标前端查看系统
"""

from flask import Flask
import sqlite3
import os

app = Flask(__name__)

# 数据库路径
DB_PATH = 'hospital-evaluation-database.db'

def get_db_connection():
    """获取数据库连接"""
    if not os.path.exists(DB_PATH):
        # 如果数据库不存在，创建示例数据
        return None
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/')
def frontend_home():
    """前端主页"""
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>医院指标查看系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .container { max-width: 1200px; margin: 0 auto; }
            .header { background: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; position: relative; }
            .admin-switch { position: absolute; top: 20px; right: 20px; background: #ff6b6b; color: white; padding: 10px 20px; border-radius: 25px; text-decoration: none; font-weight: bold; box-shadow: 0 2px 10px rgba(0,0,0,0.2); transition: all 0.3s ease; }
            .admin-switch:hover { background: #ff5252; transform: scale(1.05); }
            .nav-buttons { display: flex; gap: 15px; justify-content: center; margin-top: 20px; flex-wrap: wrap; }
            .nav-btn { padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 14px; font-weight: 500; transition: all 0.3s ease; }
            .nav-btn-primary { background: #007bff; color: white; }
            .nav-btn-success { background: #28a745; color: white; }
            .nav-btn-warning { background: #ffc107; color: #212529; }
            .nav-btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
            .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 30px; }
            .feature-card { background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; }
            .feature-icon { font-size: 48px; margin-bottom: 15px; }
            .system-info { background: rgba(255,255,255,0.95); padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #007bff; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="system-info">
                <h3 style="margin-top: 0; color: #007bff;">🌐 前端查看系统</h3>
                <p style="margin-bottom: 0;"><strong>当前端口：</strong>5003 | <strong>后端管理系统：</strong>5004 | <strong>跳转：</strong>右上角红色按钮</p>
            </div>
            
            <div class="header">
                <a href="http://localhost:5004" class="admin-switch" target="_blank">🔧 后台管理</a>
                <h1 style="color: #333; margin: 0 0 10px 0;">🏥 医院指标查看系统</h1>
                <p style="color: #666; margin: 0; font-size: 16px;">专注于指标查看和浏览的前端系统</p>
                
                <div class="nav-buttons">
                    <a href="/indicators" class="nav-btn nav-btn-primary">📊 浏览指标</a>
                    <a href="/chapters" class="nav-btn nav-btn-success">📚 按章节查看</a>
                    <a href="/search" class="nav-btn nav-btn-warning">🔍 搜索指标</a>
                </div>
            </div>
            
            <div class="features">
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>指标浏览</h3>
                    <p>按章节、小节浏览所有医院指标，查看详细的分子分母信息</p>
                    <a href="/indicators" style="background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-size: 12px;">立即浏览</a>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <h3>智能搜索</h3>
                    <p>快速搜索指标名称、编号或描述，精准定位所需指标</p>
                    <a href="/search" style="background: #ffc107; color: #212529; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-size: 12px;">开始搜索</a>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📋</div>
                    <h3>详细信息</h3>
                    <p>查看指标的完整信息，包括计算方法、数据来源、责任科室</p>
                    <a href="/indicators" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-size: 12px;">查看详情</a>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🌳</div>
                    <h3>层级关系</h3>
                    <p>清晰展示指标的父子关系，支持多级指标结构</p>
                    <a href="/chapters" style="background: #6c757d; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-size: 12px;">查看结构</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/indicators')
def frontend_indicators():
    """前端指标列表"""
    # 示例指标数据
    sample_indicators = [
        {'id': '1.1.1', 'name': '实际开放床位数', 'chapter': '第一章', 'section': '1.1', 'children': 2},
        {'id': '1.1.2', 'name': '床位使用率', 'chapter': '第一章', 'section': '1.1', 'children': 0},
        {'id': '1.2.1', 'name': '医师配置', 'chapter': '第一章', 'section': '1.2', 'children': 3},
        {'id': '2.1.1', 'name': '门诊服务量', 'chapter': '第二章', 'section': '2.1', 'children': 1},
        {'id': '2.2.1', 'name': '急诊服务质量', 'chapter': '第二章', 'section': '2.2', 'children': 0},
        {'id': '3.1.1', 'name': '手术安全指标', 'chapter': '第三章', 'section': '3.1', 'children': 4},
        {'id': '3.2.1', 'name': '医疗质量控制', 'chapter': '第三章', 'section': '3.2', 'children': 2},
    ]
    
    indicators_html = ""
    for indicator in sample_indicators:
        indicators_html += f'''
        <tr>
            <td style="padding: 12px; font-family: monospace; font-weight: bold; color: #007bff;">{indicator['id']}</td>
            <td style="padding: 12px;">{indicator['name']}</td>
            <td style="padding: 12px; text-align: center;">{indicator['chapter']}</td>
            <td style="padding: 12px; text-align: center;">{indicator['section']}</td>
            <td style="padding: 12px; text-align: center;">{indicator['children']}</td>
            <td style="padding: 12px; text-align: center;">
                <a href="/indicator/{indicator['id']}" style="background: #007bff; color: white; padding: 6px 12px; text-decoration: none; border-radius: 4px; font-size: 12px;">查看详情</a>
            </td>
        </tr>
        '''
    
    return f'''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>指标列表 - 前端查看系统</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .header {{ background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }}
            .admin-switch {{ background: #ff6b6b; color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }}
            .admin-switch:hover {{ background: #ff5252; }}
            .system-info {{ background: #e3f2fd; padding: 15px; border-radius: 4px; margin-bottom: 20px; color: #1976d2; }}
            table {{ width: 100%; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
            th {{ background: #f8f9fa; padding: 15px; text-align: left; font-weight: 600; border-bottom: 1px solid #dee2e6; }}
            td {{ padding: 12px; border-bottom: 1px solid #f1f3f4; }}
            tr:hover {{ background: #f8f9fa; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="system-info">
                <strong>🌐 前端查看系统</strong> | 端口: 5003 | 跳转到后台管理: 右上角按钮 → 端口 5004
            </div>
            
            <div class="header">
                <div>
                    <h1 style="margin: 0; color: #333;">📊 指标列表</h1>
                    <p style="margin: 5px 0 0 0; color: #666;">浏览医院等级评审指标</p>
                </div>
                <a href="http://localhost:5004" class="admin-switch" target="_blank">🔧 后台管理</a>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>指标ID</th>
                        <th>指标名称</th>
                        <th>章节</th>
                        <th>小节</th>
                        <th>子指标</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {indicators_html}
                </tbody>
            </table>
            
            <div style="margin-top: 20px; text-align: center;">
                <a href="/" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">🏠 返回首页</a>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/chapters')
def frontend_chapters():
    """前端章节浏览"""
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>章节浏览 - 前端查看系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1000px; margin: 0 auto; }
            .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }
            .admin-switch { background: #ff6b6b; color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }
            .admin-switch:hover { background: #ff5252; }
            .system-info { background: #e3f2fd; padding: 15px; border-radius: 4px; margin-bottom: 20px; color: #1976d2; }
            .demo-content { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="system-info">
                <strong>🌐 前端查看系统</strong> | 端口: 5003 | 跳转到后台管理: 右上角按钮 → 端口 5004
            </div>
            
            <div class="header">
                <div>
                    <h1 style="margin: 0; color: #333;">📚 章节浏览</h1>
                    <p style="margin: 5px 0 0 0; color: #666;">按章节结构浏览指标</p>
                </div>
                <a href="http://localhost:5004" class="admin-switch" target="_blank">🔧 后台管理</a>
            </div>
            
            <div class="demo-content">
                <h2>📚 章节结构浏览</h2>
                <p>这里是前端系统的章节浏览功能</p>
                <p>注意右上角的跳转按钮可以切换到后台管理系统</p>
                <div style="margin-top: 30px;">
                    <a href="/" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">🏠 返回首页</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/search')
def frontend_search():
    """前端搜索功能"""
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>搜索指标 - 前端查看系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1000px; margin: 0 auto; }
            .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }
            .admin-switch { background: #ff6b6b; color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }
            .admin-switch:hover { background: #ff5252; }
            .system-info { background: #e3f2fd; padding: 15px; border-radius: 4px; margin-bottom: 20px; color: #1976d2; }
            .demo-content { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="system-info">
                <strong>🌐 前端查看系统</strong> | 端口: 5003 | 跳转到后台管理: 右上角按钮 → 端口 5004
            </div>
            
            <div class="header">
                <div>
                    <h1 style="margin: 0; color: #333;">🔍 搜索指标</h1>
                    <p style="margin: 5px 0 0 0; color: #666;">智能搜索医院指标</p>
                </div>
                <a href="http://localhost:5004" class="admin-switch" target="_blank">🔧 后台管理</a>
            </div>
            
            <div class="demo-content">
                <h2>🔍 智能搜索功能</h2>
                <p>这里是前端系统的搜索功能</p>
                <p>注意右上角的跳转按钮可以切换到后台管理系统</p>
                <div style="margin-top: 30px;">
                    <a href="/" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">🏠 返回首页</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🌐 医院指标前端查看系统")
    print("📍 访问地址: http://localhost:5003")
    print("🔗 后台管理: http://localhost:5004")
    print("🎯 专注于指标查看和浏览")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5003)
