#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动判断和更新指标类型
- 简单指标：没有下级指标、也没有分子分母，仅只有指标标题的
- 复合指标：有分子、分母定义的
"""

import sqlite3
import sys
import os

def update_indicator_types():
    """
    根据指标的子指标和分子分母情况自动判断并更新指标类型
    """
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 开始分析指标类型...")
        
        # 获取所有指标及其相关信息
        cursor.execute("""
            SELECT 
                i.id,
                i.name,
                i.indicator_type as current_type,
                COUNT(DISTINCT children.id) as children_count,
                COUNT(DISTINCT ic.id) as component_count
            FROM indicators i
            LEFT JOIN indicators children ON i.id = children.parent_id AND children.is_active = 1
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id AND ic.is_active = 1
            WHERE i.is_active = 1
            GROUP BY i.id, i.name, i.indicator_type
            ORDER BY i.id
        """)
        
        indicators = cursor.fetchall()
        
        simple_count = 0
        composite_count = 0
        updated_count = 0
        
        print(f"\n📊 分析 {len(indicators)} 个指标...")
        
        for indicator_id, name, current_type, children_count, component_count in indicators:
            # 判断指标类型的逻辑
            # 简单指标：没有子指标 AND 没有分子分母组件
            # 复合指标：有分子分母组件 OR 有子指标
            
            if children_count == 0 and component_count == 0:
                # 简单指标：没有子指标，也没有分子分母
                new_type = 'simple'
                simple_count += 1
            else:
                # 复合指标：有子指标或有分子分母
                new_type = 'composite'
                composite_count += 1
            
            # 如果类型发生变化，更新数据库
            if current_type != new_type:
                cursor.execute("""
                    UPDATE indicators 
                    SET indicator_type = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (new_type, indicator_id))
                
                updated_count += 1
                type_change = f"{current_type} → {new_type}"
                print(f"  📝 {indicator_id} - {name[:50]}... ({type_change})")
        
        # 提交更改
        conn.commit()
        
        print(f"\n✅ 指标类型分析完成！")
        print(f"📈 统计结果:")
        print(f"  🔹 简单指标: {simple_count} 个")
        print(f"  🔸 复合指标: {composite_count} 个")
        print(f"  📝 更新数量: {updated_count} 个")
        
        # 显示一些示例
        print(f"\n📋 简单指标示例:")
        cursor.execute("""
            SELECT id, name 
            FROM indicators 
            WHERE indicator_type = 'simple' 
            ORDER BY id 
            LIMIT 5
        """)
        
        simple_examples = cursor.fetchall()
        for indicator_id, name in simple_examples:
            print(f"  🔹 {indicator_id} - {name}")
        
        print(f"\n📋 复合指标示例:")
        cursor.execute("""
            SELECT i.id, i.name,
                   COUNT(DISTINCT children.id) as children_count,
                   COUNT(DISTINCT ic.id) as component_count
            FROM indicators i
            LEFT JOIN indicators children ON i.id = children.parent_id AND children.is_active = 1
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id AND ic.is_active = 1
            WHERE i.indicator_type = 'composite'
            GROUP BY i.id, i.name
            ORDER BY i.id
            LIMIT 5
        """)
        
        composite_examples = cursor.fetchall()
        for indicator_id, name, children_count, component_count in composite_examples:
            details = []
            if children_count > 0:
                details.append(f"{children_count}个子指标")
            if component_count > 0:
                details.append(f"{component_count}个组件")
            detail_str = ", ".join(details)
            print(f"  🔸 {indicator_id} - {name} ({detail_str})")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新指标类型时出错: {e}")
        return False
    finally:
        if conn:
            conn.close()

def show_type_statistics():
    """
    显示指标类型统计信息
    """
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"\n" + "=" * 60)
        print("📊 指标类型统计")
        print("=" * 60)
        
        # 总体统计
        cursor.execute("""
            SELECT 
                indicator_type,
                COUNT(*) as count,
                ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM indicators WHERE is_active = 1), 2) as percentage
            FROM indicators 
            WHERE is_active = 1
            GROUP BY indicator_type
            ORDER BY count DESC
        """)
        
        type_stats = cursor.fetchall()
        
        for indicator_type, count, percentage in type_stats:
            type_name = "简单指标" if indicator_type == 'simple' else "复合指标"
            print(f"  {type_name}: {count} 个 ({percentage}%)")
        
        # 按章节统计
        print(f"\n📚 按章节统计:")
        cursor.execute("""
            SELECT 
                c.code,
                c.name,
                i.indicator_type,
                COUNT(*) as count
            FROM indicators i
            JOIN chapters c ON i.chapter_id = c.id
            WHERE i.is_active = 1
            GROUP BY c.code, c.name, i.indicator_type
            ORDER BY c.code, i.indicator_type
        """)
        
        chapter_stats = cursor.fetchall()
        current_chapter = None
        
        for chapter_code, chapter_name, indicator_type, count in chapter_stats:
            if current_chapter != chapter_code:
                current_chapter = chapter_code
                print(f"\n  第{chapter_code}章 - {chapter_name}:")
            
            type_name = "简单指标" if indicator_type == 'simple' else "复合指标"
            print(f"    {type_name}: {count} 个")
        
    except Exception as e:
        print(f"❌ 显示统计信息时出错: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🏥 医院指标类型自动判断工具")
    print("=" * 50)
    
    # 更新指标类型
    success = update_indicator_types()
    
    if success:
        # 显示统计信息
        show_type_statistics()
    else:
        print("❌ 更新失败")
        sys.exit(1)
