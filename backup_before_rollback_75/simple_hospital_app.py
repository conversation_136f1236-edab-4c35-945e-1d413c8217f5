#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版医院指标管理系统 - 专注于组件编辑功能
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import sqlite3
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'hospital_system_2024'

# 数据库路径
DB_PATH = 'DATABASE-HOSPITAL/hospital_indicator_system.db'

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def dict_from_row(row):
    """将sqlite3.Row转换为字典"""
    return dict(zip(row.keys(), row)) if row else None

@app.route('/')
def index():
    """主页"""
    return '''
    <h1>🏥 医院指标管理系统</h1>
    <p><a href="/admin">后台管理</a></p>
    <p><a href="/admin/indicators">指标管理</a></p>
    <p><a href="/test_component">测试组件功能</a></p>
    '''

@app.route('/admin')
def admin():
    """管理首页"""
    return '''
    <h1>📊 后台管理</h1>
    <p><a href="/admin/indicators">指标管理</a></p>
    <p><a href="/test_component">测试组件功能</a></p>
    '''

@app.route('/test_component')
def test_component():
    """测试组件功能"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 确保indicator_components表存在
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS indicator_components (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                indicator_id VARCHAR(20) NOT NULL,
                component_type VARCHAR(20) NOT NULL CHECK (component_type IN ('numerator', 'denominator', 'other')),
                name VARCHAR(200) NOT NULL,
                definition TEXT,
                unit VARCHAR(50),
                data_source VARCHAR(200),
                lead_department VARCHAR(100),
                logic_definition TEXT,
                collection_method VARCHAR(50),
                calculation_formula TEXT,
                notes TEXT,
                sort_order INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 检查是否已有测试数据
        cursor.execute("SELECT COUNT(*) FROM indicator_components")
        count = cursor.fetchone()[0]
        
        if count == 0:
            # 插入测试组件
            cursor.execute("""
                INSERT INTO indicator_components 
                (indicator_id, component_type, name, definition, unit, data_source, lead_department, logic_definition)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                '1.3.2.1',
                'numerator',
                '重症医学科开放床位数',
                '医院重症医学科实际开放并可收治患者的床位数量',
                '张',
                'HIS系统',
                '重症医学科',
                '统计期末重症医学科实际开放的床位数'
            ))
            
            cursor.execute("""
                INSERT INTO indicator_components 
                (indicator_id, component_type, name, definition, unit, data_source, lead_department, logic_definition)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                '1.3.2.1',
                'denominator',
                '医院开放床位总数',
                '医院实际开放并可收治患者的床位总数',
                '张',
                'HIS系统',
                '医务科',
                '统计期末医院实际开放的床位总数'
            ))
            
            conn.commit()
        
        # 获取所有组件
        cursor.execute("SELECT * FROM indicator_components ORDER BY id")
        components = [dict_from_row(row) for row in cursor.fetchall()]
        
        conn.close()
        
        html = '''
        <h1>🧮 组件管理测试</h1>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <tr style="background: #f5f5f5;">
                <th style="padding: 10px;">ID</th>
                <th style="padding: 10px;">指标ID</th>
                <th style="padding: 10px;">类型</th>
                <th style="padding: 10px;">名称</th>
                <th style="padding: 10px;">单位</th>
                <th style="padding: 10px;">牵头科室</th>
                <th style="padding: 10px;">操作</th>
            </tr>
        '''
        
        for comp in components:
            type_label = {'numerator': '分子', 'denominator': '分母', 'other': '其他'}.get(comp['component_type'], '未知')
            html += f'''
            <tr>
                <td style="padding: 8px;">{comp['id']}</td>
                <td style="padding: 8px;">{comp['indicator_id']}</td>
                <td style="padding: 8px;">{type_label}</td>
                <td style="padding: 8px;">{comp['name']}</td>
                <td style="padding: 8px;">{comp.get('unit', '-')}</td>
                <td style="padding: 8px;">{comp.get('lead_department', '-')}</td>
                <td style="padding: 8px;">
                    <a href="/admin/components/{comp['id']}/edit" style="background: #ffc107; color: white; padding: 4px 8px; text-decoration: none; border-radius: 3px;">编辑</a>
                </td>
            </tr>
            '''
        
        html += '''
        </table>
        <p><a href="/">返回首页</a></p>
        '''
        
        return html
        
    except Exception as e:
        return f'<h1>错误</h1><p>{str(e)}</p>'

@app.route('/admin/components/<component_id>/edit', methods=['GET', 'POST'])
def edit_component(component_id):
    """编辑组件"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if request.method == 'POST':
            # 处理表单提交
            data = request.form
            
            cursor.execute("""
                UPDATE indicator_components 
                SET name = ?, definition = ?, unit = ?, data_source = ?, 
                    lead_department = ?, logic_definition = ?, component_type = ?,
                    collection_method = ?, calculation_formula = ?, notes = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                data.get('name'),
                data.get('definition'),
                data.get('unit'),
                data.get('data_source'),
                data.get('lead_department'),
                data.get('logic_definition'),
                data.get('component_type'),
                data.get('collection_method'),
                data.get('calculation_formula'),
                data.get('notes'),
                component_id
            ))
            
            conn.commit()
            conn.close()
            
            return '''
            <script>
                alert('组件更新成功！');
                window.location.href = '/test_component';
            </script>
            '''
        
        # GET请求 - 显示编辑表单
        cursor.execute("""
            SELECT ic.*, i.name as indicator_name
            FROM indicator_components ic
            LEFT JOIN indicators i ON ic.indicator_id = i.id
            WHERE ic.id = ?
        """, (component_id,))
        
        component = dict_from_row(cursor.fetchone())
        if not component:
            return f'<h1>组件不存在</h1><p>组件 {component_id} 不存在</p>'
        
        conn.close()
        
        return f'''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>编辑组件 - {component.get('name', '未命名')}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
                .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .form-group {{ margin-bottom: 20px; }}
                .form-group label {{ display: block; margin-bottom: 5px; font-weight: 600; color: #333; }}
                .form-group input, .form-group select, .form-group textarea {{ width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }}
                .form-group textarea {{ height: 80px; resize: vertical; }}
                .form-row {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }}
                .btn {{ padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin-right: 10px; }}
                .btn-primary {{ background: #007bff; color: white; }}
                .btn-secondary {{ background: #6c757d; color: white; }}
                .btn:hover {{ opacity: 0.9; }}
                h1 {{ color: #333; margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🧮 编辑组件</h1>
                <p><strong>指标：</strong>{component['indicator_id']} - {component.get('indicator_name', '未知指标')}</p>
                
                <form method="POST">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="component_type">组件类型 *</label>
                            <select id="component_type" name="component_type" required>
                                <option value="numerator" {'selected' if component.get('component_type') == 'numerator' else ''}>分子</option>
                                <option value="denominator" {'selected' if component.get('component_type') == 'denominator' else ''}>分母</option>
                                <option value="other" {'selected' if component.get('component_type') == 'other' else ''}>其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="name">组件名称 *</label>
                            <input type="text" id="name" name="name" value="{component.get('name', '')}" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="definition">组件定义</label>
                        <textarea id="definition" name="definition" placeholder="详细描述组件的含义和计算逻辑">{component.get('definition', '')}</textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="unit">单位</label>
                            <input type="text" id="unit" name="unit" value="{component.get('unit', '')}" placeholder="例如：人、张、次、%">
                        </div>
                        <div class="form-group">
                            <label for="lead_department">牵头科室</label>
                            <input type="text" id="lead_department" name="lead_department" value="{component.get('lead_department', '')}" placeholder="负责此数据的科室">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="data_source">数据来源</label>
                        <input type="text" id="data_source" name="data_source" value="{component.get('data_source', '')}" placeholder="例如：HIS系统、手工统计、第三方系统">
                    </div>
                    
                    <div class="form-group">
                        <label for="logic_definition">逻辑定义</label>
                        <textarea id="logic_definition" name="logic_definition" placeholder="详细的计算逻辑和业务规则">{component.get('logic_definition', '')}</textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="collection_method">采集方式</label>
                            <input type="text" id="collection_method" name="collection_method" value="{component.get('collection_method', '')}" placeholder="例如：自动采集、手工录入、定期导入">
                        </div>
                        <div class="form-group">
                            <label for="calculation_formula">计算公式</label>
                            <input type="text" id="calculation_formula" name="calculation_formula" value="{component.get('calculation_formula', '')}" placeholder="具体的计算公式">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">备注</label>
                        <textarea id="notes" name="notes" placeholder="其他需要说明的信息">{component.get('notes', '')}</textarea>
                    </div>
                    
                    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                        <button type="submit" class="btn btn-primary">💾 保存更改</button>
                        <a href="/test_component" class="btn btn-secondary">❌ 取消</a>
                    </div>
                </form>
            </div>
        </body>
        </html>
        '''
        
    except Exception as e:
        return f'<h1>错误</h1><p>{str(e)}</p>'

if __name__ == '__main__':
    print("🏥 简化版医院指标管理系统启动中...")
    print("🌐 访问地址: http://localhost:5001")
    print("🧮 组件编辑测试: http://localhost:5001/test_component")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5001)
