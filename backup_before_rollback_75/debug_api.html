<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; color: #1a73e8; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .button { 
            background: #1a73e8; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 5px;
        }
        .button:hover { background: #1557b0; }
        .field { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        .field-name { font-weight: bold; color: #333; }
        .field-value { margin-top: 5px; color: #666; }
        .analysis-dimension { 
            background: #e8f5e8; 
            padding: 10px; 
            margin: 5px 0; 
            border-left: 3px solid #34a853; 
            border-radius: 3px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 API调试页面</h1>
        
        <div class="section">
            <h3>测试指标</h3>
            <button class="button" onclick="testIndicator('1.1.1')">测试 1.1.1 - 核定床位数</button>
            <button class="button" onclick="testIndicator('C-3.1')">测试 C-3.1 - 抗菌药物使用强度</button>
        </div>

        <div class="section" id="rawDataSection" style="display: none;">
            <h3>原始API数据</h3>
            <pre id="rawData"></pre>
        </div>

        <div class="section" id="parsedDataSection" style="display: none;">
            <h3>解析后的详细字段</h3>
            <div id="parsedData"></div>
        </div>
    </div>

    <script>
        async function testIndicator(indicatorId) {
            try {
                console.log(`测试指标: ${indicatorId}`);
                
                // 显示加载状态
                document.getElementById('rawDataSection').style.display = 'block';
                document.getElementById('rawData').textContent = '加载中...';
                document.getElementById('parsedDataSection').style.display = 'none';

                // 获取API数据
                const response = await fetch(`/api/indicators/${indicatorId}?t=${Date.now()}`);
                const result = await response.json();

                // 显示原始数据
                document.getElementById('rawData').textContent = JSON.stringify(result, null, 2);

                if (result.success) {
                    const indicator = result.data.indicator;
                    
                    // 解析详细字段
                    const detailFields = [
                        { name: '指标定义', key: 'indicator_definition' },
                        { name: '计算公式', key: 'calculation_formula' },
                        { name: '分子说明', key: 'numerator_description' },
                        { name: '分母说明', key: 'denominator_description' },
                        { name: '统计范围', key: 'statistical_scope' },
                        { name: '数据来源', key: 'data_sources' },
                        { name: '统计频率', key: 'collection_frequency_detail' },
                        { name: '参考值', key: 'reference_value' },
                        { name: '监测分析', key: 'monitoring_analysis' }
                    ];

                    let parsedHtml = `
                        <div class="field">
                            <div class="field-name">指标基本信息</div>
                            <div class="field-value">
                                ID: ${indicator.id}<br>
                                名称: ${indicator.name}<br>
                                类型: ${indicator.indicator_type}
                            </div>
                        </div>
                    `;

                    detailFields.forEach(field => {
                        const value = indicator[field.key];
                        const hasValue = value && value.trim() !== '';
                        
                        parsedHtml += `
                            <div class="field" style="background: ${hasValue ? '#e8f5e8' : '#fff3cd'};">
                                <div class="field-name">${field.name}</div>
                                <div class="field-value">${hasValue ? value : '❌ 无数据'}</div>
                            </div>
                        `;
                    });

                    // 分析维度
                    if (indicator.analysis_dimensions && indicator.analysis_dimensions.length > 0) {
                        parsedHtml += `
                            <div class="field">
                                <div class="field-name">分析维度 (${indicator.analysis_dimensions.length}个)</div>
                                <div class="field-value">
                        `;
                        
                        indicator.analysis_dimensions.forEach(dim => {
                            parsedHtml += `
                                <div class="analysis-dimension">
                                    <strong>${dim.dimension_name}</strong><br>
                                    ${dim.analysis_content}
                                </div>
                            `;
                        });
                        
                        parsedHtml += `
                                </div>
                            </div>
                        `;
                    } else {
                        parsedHtml += `
                            <div class="field" style="background: #fff3cd;">
                                <div class="field-name">分析维度</div>
                                <div class="field-value">❌ 无分析维度数据</div>
                            </div>
                        `;
                    }

                    document.getElementById('parsedData').innerHTML = parsedHtml;
                    document.getElementById('parsedDataSection').style.display = 'block';

                } else {
                    document.getElementById('parsedData').innerHTML = `
                        <div style="color: red;">
                            <strong>API错误:</strong> ${result.error}
                        </div>
                    `;
                    document.getElementById('parsedDataSection').style.display = 'block';
                }

            } catch (error) {
                console.error('测试失败:', error);
                document.getElementById('rawData').textContent = `错误: ${error.message}`;
                document.getElementById('parsedData').innerHTML = `
                    <div style="color: red;">
                        <strong>网络错误:</strong> ${error.message}
                    </div>
                `;
                document.getElementById('parsedDataSection').style.display = 'block';
            }
        }

        // 页面加载时自动测试1.1.1
        window.addEventListener('load', () => {
            console.log('页面加载完成，自动测试1.1.1指标');
            testIndicator('1.1.1');
        });
    </script>
</body>
</html>
