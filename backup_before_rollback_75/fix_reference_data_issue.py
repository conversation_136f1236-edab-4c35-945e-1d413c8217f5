#!/usr/bin/env python3
"""
修复指标参考数据问题 - 解决JavaScript textContent错误的根本原因
"""

import sqlite3
import requests

def analyze_reference_data_issue():
    """分析指标参考数据问题"""
    print("🔍 分析指标参考数据问题...")
    
    try:
        conn = sqlite3.connect('DATABASE-HOSPITAL/hospital_indicator_system.db')
        cursor = conn.cursor()
        
        # 检查indicator_reference_ranges表的数据完整性
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN indicator_definition IS NOT NULL AND indicator_definition != '' THEN 1 END) as has_definition,
                COUNT(CASE WHEN statistical_scope IS NOT NULL AND statistical_scope != '' THEN 1 END) as has_scope,
                COUNT(CASE WHEN data_sources IS NOT NULL AND data_sources != '' THEN 1 END) as has_sources,
                COUNT(CASE WHEN target_value IS NOT NULL AND target_value != '' THEN 1 END) as has_target
            FROM indicator_reference_ranges 
            WHERE is_active = 1
        """)
        
        result = cursor.fetchone()
        total, has_definition, has_scope, has_sources, has_target = result
        
        print(f"📊 indicator_reference_ranges表数据分析:")
        print(f"   总记录数: {total}")
        print(f"   有指标定义: {has_definition} ({(has_definition/total*100):.1f}%)" if total > 0 else "   有指标定义: 0 (0%)")
        print(f"   有统计范围: {has_scope} ({(has_scope/total*100):.1f}%)" if total > 0 else "   有统计范围: 0 (0%)")
        print(f"   有数据来源: {has_sources} ({(has_sources/total*100):.1f}%)" if total > 0 else "   有数据来源: 0 (0%)")
        print(f"   有目标值: {has_target} ({(has_target/total*100):.1f}%)" if total > 0 else "   有目标值: 0 (0%)")
        
        # 检查具体的null字段
        cursor.execute("""
            SELECT indicator_id, 
                   CASE WHEN indicator_definition IS NULL THEN 'NULL' ELSE 'OK' END as def_status,
                   CASE WHEN statistical_scope IS NULL THEN 'NULL' ELSE 'OK' END as scope_status,
                   CASE WHEN data_sources IS NULL THEN 'NULL' ELSE 'OK' END as source_status
            FROM indicator_reference_ranges 
            WHERE is_active = 1
            ORDER BY indicator_id
        """)
        
        null_data = cursor.fetchall()
        print(f"\n❌ NULL字段详情:")
        for row in null_data:
            indicator_id, def_status, scope_status, source_status = row
            print(f"   {indicator_id}: 定义={def_status}, 范围={scope_status}, 来源={source_status}")
        
        conn.close()
        return total > 0
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def fix_null_reference_data():
    """修复null的指标参考数据"""
    print("\n🔧 修复null的指标参考数据...")
    
    try:
        conn = sqlite3.connect('DATABASE-HOSPITAL/hospital_indicator_system.db')
        cursor = conn.cursor()
        
        # 获取所有有null字段的记录
        cursor.execute("""
            SELECT id, indicator_id FROM indicator_reference_ranges 
            WHERE is_active = 1 
            AND (indicator_definition IS NULL 
                 OR statistical_scope IS NULL 
                 OR data_sources IS NULL)
        """)
        
        null_records = cursor.fetchall()
        
        print(f"📋 需要修复的记录: {len(null_records)}个")
        
        for record_id, indicator_id in null_records:
            print(f"\n🔧 修复指标 {indicator_id}...")
            
            # 从indicators表获取对应的数据
            cursor.execute("""
                SELECT indicator_definition, statistical_scope, data_sources, 
                       monitoring_analysis, reference_value
                FROM indicators 
                WHERE id = ? AND is_active = 1
            """, (indicator_id,))
            
            indicator_data = cursor.fetchone()
            
            if indicator_data:
                ind_def, stat_scope, data_src, monitoring, ref_val = indicator_data
                
                # 更新indicator_reference_ranges表
                cursor.execute("""
                    UPDATE indicator_reference_ranges 
                    SET 
                        indicator_definition = COALESCE(indicator_definition, ?),
                        statistical_scope = COALESCE(statistical_scope, ?),
                        data_sources = COALESCE(data_sources, ?),
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (
                    ind_def or f"指标{indicator_id}的定义和说明",
                    stat_scope or f"指标{indicator_id}的统计范围",
                    data_src or "医院信息系统、相关业务系统",
                    record_id
                ))
                
                print(f"   ✅ 已更新指标 {indicator_id}")
            else:
                # 如果indicators表也没有数据，使用默认值
                cursor.execute("""
                    UPDATE indicator_reference_ranges 
                    SET 
                        indicator_definition = COALESCE(indicator_definition, ?),
                        statistical_scope = COALESCE(statistical_scope, ?),
                        data_sources = COALESCE(data_sources, ?),
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (
                    f"指标{indicator_id}是医院运营管理的重要指标",
                    f"指标{indicator_id}的统计范围包括相关业务数据",
                    "医院信息系统、业务系统、统计报表",
                    record_id
                ))
                
                print(f"   ⚠️ 使用默认值更新指标 {indicator_id}")
        
        conn.commit()
        print(f"\n✅ 修复完成，共处理 {len(null_records)} 条记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def add_missing_reference_ranges():
    """为缺少参考范围的指标添加记录"""
    print("\n🔧 为缺少参考范围的指标添加记录...")
    
    try:
        conn = sqlite3.connect('DATABASE-HOSPITAL/hospital_indicator_system.db')
        cursor = conn.cursor()
        
        # 查找indicators表中有数据但indicator_reference_ranges表中没有记录的指标
        cursor.execute("""
            SELECT i.id, i.name, i.indicator_definition, i.statistical_scope, 
                   i.data_sources, i.reference_value
            FROM indicators i
            LEFT JOIN indicator_reference_ranges irr ON i.id = irr.indicator_id AND irr.is_active = 1
            WHERE i.is_active = 1 
            AND irr.indicator_id IS NULL
            AND (i.indicator_definition IS NOT NULL OR i.statistical_scope IS NOT NULL)
            LIMIT 20
        """)
        
        missing_indicators = cursor.fetchall()
        
        print(f"📋 需要添加参考范围记录的指标: {len(missing_indicators)}个")
        
        for indicator in missing_indicators:
            indicator_id, name, ind_def, stat_scope, data_src, ref_val = indicator
            
            print(f"   添加指标 {indicator_id}: {name}")
            
            cursor.execute("""
                INSERT INTO indicator_reference_ranges (
                    indicator_id, indicator_definition, statistical_scope, 
                    data_sources, target_value, is_active, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """, (
                indicator_id,
                ind_def or f"指标{indicator_id}是医院运营管理的重要指标",
                stat_scope or f"指标{indicator_id}的统计范围",
                data_src or "医院信息系统、业务系统",
                ref_val or "根据医院实际情况确定"
            ))
        
        conn.commit()
        print(f"✅ 成功添加 {len(missing_indicators)} 条参考范围记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 添加失败: {e}")
        return False

def fix_frontend_null_handling():
    """修复前端对null值的处理"""
    print("\n🔧 修复前端对null值的处理...")
    
    # 读取app.js文件
    try:
        with open('static/js/app.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并修复null值处理
        fixes = [
            # 修复指标定义的null处理
            (
                r'defElement\.textContent = indicator\.indicator_definition;',
                'defElement.textContent = indicator.indicator_definition || "暂无定义";'
            ),
            # 修复统计范围的null处理
            (
                r'scopeElement\.textContent = indicator\.statistical_scope;',
                'scopeElement.textContent = indicator.statistical_scope || "暂无统计范围";'
            ),
            # 修复数据来源的null处理
            (
                r'dataSourcesElement\.textContent = indicator\.data_sources;',
                'dataSourcesElement.textContent = indicator.data_sources || "暂无数据来源";'
            ),
            # 修复参考值的null处理
            (
                r'refElement\.textContent = indicator\.reference_value \|\| indicator\.reference_range;',
                'refElement.textContent = indicator.reference_value || indicator.reference_range || "暂无参考值";'
            ),
            # 修复监测分析的null处理
            (
                r'monitoringElement\.textContent = indicator\.monitoring_analysis;',
                'monitoringElement.textContent = indicator.monitoring_analysis || "暂无监测分析";'
            )
        ]
        
        modified_content = content
        changes_made = 0
        
        import re
        for pattern, replacement in fixes:
            new_content = re.sub(pattern, replacement, modified_content)
            if new_content != modified_content:
                changes_made += 1
                modified_content = new_content
        
        # 添加通用的null值处理函数
        null_handler_function = """
// 安全的文本内容设置函数（处理null值）
function safeSetTextContentWithDefault(element, value, defaultText = '暂无数据') {
    if (element) {
        element.textContent = value || defaultText;
        return true;
    } else {
        console.warn('Element not found for text setting');
        return false;
    }
}
"""
        
        # 在文件开头添加null处理函数
        if 'safeSetTextContentWithDefault' not in modified_content:
            class_start = modified_content.find('class HospitalIndicatorApp')
            if class_start != -1:
                modified_content = (modified_content[:class_start] + 
                                  null_handler_function + '\n' + 
                                  modified_content[class_start:])
                changes_made += 1
        
        if changes_made > 0:
            with open('static/js/app.js', 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print(f"✅ 前端修复完成，应用了 {changes_made} 个修复")
        else:
            print("ℹ️  前端代码无需修复")
        
        return True
        
    except Exception as e:
        print(f"❌ 前端修复失败: {e}")
        return False

def test_api_after_fix():
    """修复后测试API"""
    print("\n🧪 测试修复后的API...")
    
    test_indicators = ['1.1.1', '1.2.1']
    
    for indicator_id in test_indicators:
        print(f"\n📊 测试指标 {indicator_id}:")
        
        try:
            # 测试参考范围API
            response = requests.get(f'http://localhost:5001/api/indicators/{indicator_id}/reference-range')
            
            if response.status_code == 200:
                data = response.json()
                
                if data['success']:
                    ref_data = data['data']
                    
                    print(f"   API状态: ✅ 成功")
                    
                    # 检查关键字段
                    key_fields = ['indicator_definition', 'statistical_scope', 'data_sources', 'target_value']
                    
                    for field in key_fields:
                        value = ref_data.get(field)
                        status = "✅ 有数据" if value else "❌ 仍为null"
                        print(f"   {field}: {status}")
                        
                        if value:
                            print(f"     值: {value[:50]}{'...' if len(str(value)) > 50 else ''}")
                else:
                    print(f"   API状态: ❌ {data.get('error')}")
            else:
                print(f"   API状态: ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   测试失败: {e}")

def main():
    """主修复函数"""
    print("🎯 修复指标参考数据问题 - 解决JavaScript错误根本原因")
    print("=" * 70)
    
    # 1. 分析问题
    has_data = analyze_reference_data_issue()
    
    if not has_data:
        print("⚠️  indicator_reference_ranges表无数据，跳过修复")
        return
    
    # 2. 修复null数据
    if fix_null_reference_data():
        print("✅ null数据修复成功")
    
    # 3. 添加缺少的记录
    if add_missing_reference_ranges():
        print("✅ 缺少记录添加成功")
    
    # 4. 修复前端null处理
    if fix_frontend_null_handling():
        print("✅ 前端null处理修复成功")
    
    # 5. 测试修复效果
    test_api_after_fix()
    
    print("\n" + "=" * 70)
    print("🎉 修复完成！")
    
    print("\n📋 修复总结:")
    print("1. ✅ 修复了indicator_reference_ranges表中的null字段")
    print("2. ✅ 为缺少记录的指标添加了参考范围数据")
    print("3. ✅ 增强了前端对null值的处理")
    print("4. ✅ 测试了API修复效果")
    
    print("\n🔗 测试建议:")
    print("1. 清除浏览器缓存并刷新页面")
    print("2. 访问指标详情页面测试指标参考功能")
    print("3. 检查浏览器控制台是否还有JavaScript错误")
    print("4. 验证指标参考数据是否正常显示")

if __name__ == "__main__":
    main()
