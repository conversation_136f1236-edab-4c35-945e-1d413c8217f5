#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理Excel文件，提取章节数据并生成SQL插入语句
"""

import pandas as pd
import re
import sys

def extract_chapter_number(text):
    """
    从文本中提取章节编号
    例如：'第一章' -> '1', '第二章' -> '2'
    """
    if pd.isna(text) or not isinstance(text, str):
        return None
    
    # 中文数字到阿拉伯数字的映射
    chinese_to_arabic = {
        '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
        '六': '6', '七': '7', '八': '8', '九': '9', '十': '10',
        '十一': '11', '十二': '12', '十三': '13', '十四': '14', '十五': '15',
        '十六': '16', '十七': '17', '十八': '18', '十九': '19', '二十': '20'
    }
    
    # 匹配"第X章"的模式
    pattern = r'第([一二三四五六七八九十]+)章'
    match = re.search(pattern, text)
    
    if match:
        chinese_num = match.group(1)
        if chinese_num in chinese_to_arabic:
            return chinese_to_arabic[chinese_num]
    
    return None

def clean_chapter_name(text):
    """
    清理章节名称，去除括号内容和多余信息
    例如：'重点专业质量控制指标（共18节1601个指标）' -> '重点专业质量控制指标'
    """
    if pd.isna(text) or not isinstance(text, str):
        return None
    
    # 去除括号及其内容
    cleaned = re.sub(r'[（(].*?[）)]', '', text)
    
    # 去除多余的空格
    cleaned = cleaned.strip()
    
    # 如果清理后为空，返回原文本
    if not cleaned:
        return text.strip()
    
    return cleaned

def process_excel_file(file_path):
    """
    处理Excel文件，提取章节数据
    """
    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {file_path}")
        df = pd.read_excel(file_path)
        
        print(f"Excel文件读取成功，共{len(df)}行，{len(df.columns)}列")
        print(f"列名: {df.columns.tolist()}")
        
        # 检查是否有足够的列
        if len(df.columns) < 2:
            print("错误：Excel文件至少需要2列数据")
            return None
        
        # 提取A列和B列数据
        a_column = df.iloc[:, 0]  # A列
        b_column = df.iloc[:, 1]  # B列
        
        print(f"\nA列前10行数据:")
        for i, val in enumerate(a_column.head(10)):
            print(f"  {i+1}: {val}")
        
        print(f"\nB列前10行数据:")
        for i, val in enumerate(b_column.head(10)):
            print(f"  {i+1}: {val}")
        
        # 处理数据
        chapters_data = []
        
        for i, (a_val, b_val) in enumerate(zip(a_column, b_column)):
            # 提取章节编号
            chapter_code = extract_chapter_number(str(a_val))
            
            if chapter_code:
                # 清理章节名称
                chapter_name = clean_chapter_name(str(b_val))
                
                if chapter_name:
                    chapters_data.append({
                        'code': chapter_code,
                        'name': chapter_name,
                        'original_a': str(a_val),
                        'original_b': str(b_val),
                        'row': i + 1
                    })
                    print(f"提取到章节: 编码={chapter_code}, 名称={chapter_name}")
        
        return chapters_data
        
    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        return None

def remove_duplicates(chapters_data):
    """
    去除重复的章节数据，保留第一次出现的
    """
    seen_codes = set()
    unique_chapters = []
    
    for chapter in chapters_data:
        if chapter['code'] not in seen_codes:
            seen_codes.add(chapter['code'])
            unique_chapters.append(chapter)
        else:
            print(f"发现重复章节编码 {chapter['code']}，已跳过")
    
    return unique_chapters

def generate_sql(chapters_data):
    """
    生成SQL插入语句
    """
    if not chapters_data:
        return ""
    
    # 预定义的图标和颜色
    icons = [
        'fas fa-chart-bar', 'fas fa-heartbeat', 'fas fa-check-circle', 
        'fas fa-clipboard-list', 'fas fa-microscope', 'fas fa-user-md',
        'fas fa-hospital', 'fas fa-pills', 'fas fa-stethoscope', 'fas fa-notes-medical'
    ]
    
    colors = [
        '#1a73e8', '#34a853', '#ea4335', '#fbbc04', '#9c27b0',
        '#ff6d01', '#00acc1', '#7b1fa2', '#388e3c', '#f57c00'
    ]
    
    sql_lines = []
    sql_lines.append("-- 从Excel文件提取的章节数据")
    sql_lines.append("INSERT INTO chapters (code, name, description, icon, color, sort_order) VALUES")
    
    values = []
    for i, chapter in enumerate(chapters_data):
        icon = icons[i % len(icons)]
        color = colors[i % len(colors)]
        description = f"第{chapter['code']}章：{chapter['name']}"
        
        value = f"('{chapter['code']}', '{chapter['name']}', '{description}', '{icon}', '{color}', {int(chapter['code'])})"
        values.append(value)
    
    sql_lines.append(',\n'.join(values) + ';')
    
    return '\n'.join(sql_lines)

def main():
    file_path = "第二部分-数据监测-省2022版-改_转换后.xlsx"
    
    # 处理Excel文件
    chapters_data = process_excel_file(file_path)
    
    if not chapters_data:
        print("未能提取到章节数据")
        return
    
    print(f"\n总共提取到 {len(chapters_data)} 个章节")
    
    # 去除重复数据
    unique_chapters = remove_duplicates(chapters_data)
    print(f"去重后剩余 {len(unique_chapters)} 个唯一章节")
    
    # 显示提取结果
    print("\n=== 提取结果 ===")
    for chapter in unique_chapters:
        print(f"编码: {chapter['code']}, 名称: {chapter['name']}")
    
    # 生成SQL语句
    sql = generate_sql(unique_chapters)
    
    # 保存SQL到文件
    with open('chapters_insert.sql', 'w', encoding='utf-8') as f:
        f.write(sql)
    
    print(f"\n=== 生成的SQL语句 ===")
    print(sql)
    print(f"\nSQL语句已保存到 chapters_insert.sql 文件")

if __name__ == "__main__":
    main()
