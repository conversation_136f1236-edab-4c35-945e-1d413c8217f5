#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面检查指标数据的完整性，包括分子、分母、牵头科室等信息
"""

import sqlite3
import pandas as pd

def check_database_completeness():
    """
    检查数据库中数据的完整性
    """
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=" * 100)
        print("🔍 医院等级评审指标管理系统 - 数据完整性检查")
        print("=" * 100)
        
        # 1. 检查指标总体情况
        cursor.execute("""
            SELECT 
                SUBSTR(id, 1, 1) as chapter,
                COUNT(*) as total_indicators
            FROM indicators 
            WHERE id LIKE '1.%' OR id LIKE '2.%'
            GROUP BY SUBSTR(id, 1, 1)
            ORDER BY chapter
        """)
        
        chapter_stats = cursor.fetchall()
        
        print("📊 指标总体统计:")
        total_indicators = 0
        for chapter, count in chapter_stats:
            print(f"  第{chapter}章: {count}个指标")
            total_indicators += count
        print(f"  总计: {total_indicators}个指标")
        
        # 2. 检查有分子分母的指标
        cursor.execute("""
            SELECT 
                SUBSTR(indicator_id, 1, 1) as chapter,
                COUNT(DISTINCT indicator_id) as indicators_with_components
            FROM indicator_components 
            WHERE indicator_id LIKE '1.%' OR indicator_id LIKE '2.%'
            GROUP BY SUBSTR(indicator_id, 1, 1)
            ORDER BY chapter
        """)
        
        component_stats = cursor.fetchall()
        
        print(f"\n🧮 有分子分母的指标统计:")
        total_with_components = 0
        for chapter, count in component_stats:
            print(f"  第{chapter}章: {count}个指标有分子分母")
            total_with_components += count
        print(f"  总计: {total_with_components}个指标有分子分母")
        
        # 3. 检查缺少分子分母的指标
        cursor.execute("""
            SELECT i.id, i.name, c.name as chapter_name
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id
            WHERE (i.id LIKE '1.%' OR i.id LIKE '2.%') 
            AND ic.indicator_id IS NULL
            ORDER BY i.id
        """)
        
        missing_components = cursor.fetchall()
        
        print(f"\n⚠️  缺少分子分母的指标 ({len(missing_components)}个):")
        if missing_components:
            for indicator_id, name, chapter_name in missing_components:
                print(f"  {indicator_id} - {name} ({chapter_name})")
        else:
            print("  ✅ 所有指标都有分子分母数据")
        
        # 4. 检查分子分母的详细信息完整性
        cursor.execute("""
            SELECT 
                indicator_id,
                component_type,
                CASE WHEN name IS NOT NULL AND name != '' THEN 1 ELSE 0 END as has_name,
                CASE WHEN unit IS NOT NULL AND unit != '' THEN 1 ELSE 0 END as has_unit,
                CASE WHEN lead_department IS NOT NULL AND lead_department != '' THEN 1 ELSE 0 END as has_department,
                CASE WHEN data_source IS NOT NULL AND data_source != '' THEN 1 ELSE 0 END as has_source,
                CASE WHEN logic_definition IS NOT NULL AND logic_definition != '' THEN 1 ELSE 0 END as has_logic
            FROM indicator_components
            WHERE indicator_id LIKE '1.%' OR indicator_id LIKE '2.%'
            ORDER BY indicator_id, component_type
        """)
        
        component_details = cursor.fetchall()
        
        print(f"\n📋 分子分母详细信息完整性:")
        
        # 统计各字段的完整性
        total_components = len(component_details)
        has_name_count = sum(1 for row in component_details if row[2])
        has_unit_count = sum(1 for row in component_details if row[3])
        has_dept_count = sum(1 for row in component_details if row[4])
        has_source_count = sum(1 for row in component_details if row[5])
        has_logic_count = sum(1 for row in component_details if row[6])
        
        print(f"  总组成部分数: {total_components}")
        print(f"  有名称: {has_name_count}/{total_components} ({has_name_count/total_components*100:.1f}%)")
        print(f"  有单位: {has_unit_count}/{total_components} ({has_unit_count/total_components*100:.1f}%)")
        print(f"  有牵头科室: {has_dept_count}/{total_components} ({has_dept_count/total_components*100:.1f}%)")
        print(f"  有数据来源: {has_source_count}/{total_components} ({has_source_count/total_components*100:.1f}%)")
        print(f"  有逻辑定义: {has_logic_count}/{total_components} ({has_logic_count/total_components*100:.1f}%)")
        
        # 5. 检查缺少关键信息的组成部分
        cursor.execute("""
            SELECT 
                indicator_id,
                component_type,
                name,
                unit,
                lead_department,
                data_source
            FROM indicator_components
            WHERE (indicator_id LIKE '1.%' OR indicator_id LIKE '2.%')
            AND (unit IS NULL OR unit = '' OR lead_department IS NULL OR lead_department = '')
            ORDER BY indicator_id, component_type
        """)
        
        incomplete_components = cursor.fetchall()
        
        print(f"\n⚠️  缺少关键信息的组成部分 ({len(incomplete_components)}个):")
        if incomplete_components:
            for indicator_id, comp_type, name, unit, dept, source in incomplete_components[:10]:  # 只显示前10个
                type_name = "分子" if comp_type == 'numerator' else "分母"
                missing_info = []
                if not unit: missing_info.append("单位")
                if not dept: missing_info.append("牵头科室")
                print(f"  {indicator_id} {type_name}: 缺少 {', '.join(missing_info)}")
            if len(incomplete_components) > 10:
                print(f"  ... 还有 {len(incomplete_components) - 10} 个组成部分缺少信息")
        else:
            print("  ✅ 所有组成部分都有完整的关键信息")
        
        # 6. 按章节详细统计
        print(f"\n📊 按章节详细统计:")
        
        for chapter_num in ['1', '2']:
            cursor.execute("""
                SELECT COUNT(*) FROM indicators WHERE id LIKE ?
            """, (f"{chapter_num}.%",))
            total_indicators = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT COUNT(DISTINCT indicator_id) FROM indicator_components WHERE indicator_id LIKE ?
            """, (f"{chapter_num}.%",))
            indicators_with_components = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT component_type, COUNT(*) FROM indicator_components 
                WHERE indicator_id LIKE ? 
                GROUP BY component_type
            """, (f"{chapter_num}.%",))
            component_counts = cursor.fetchall()
            
            print(f"\n  第{chapter_num}章:")
            print(f"    总指标数: {total_indicators}")
            print(f"    有分子分母的指标: {indicators_with_components}")
            print(f"    覆盖率: {indicators_with_components/total_indicators*100:.1f}%")
            
            for comp_type, count in component_counts:
                type_name = "分子" if comp_type == 'numerator' else "分母"
                print(f"    {type_name}: {count}个")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查数据完整性时出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if conn:
            conn.close()

def check_excel_vs_database():
    """
    对比Excel文件和数据库中的数据
    """
    print(f"\n" + "=" * 100)
    print("📋 Excel文件与数据库数据对比")
    print("=" * 100)
    
    try:
        # 检查第一章
        print(f"\n📖 第一章数据对比:")
        df1 = pd.read_excel("chapter1.xlsx", sheet_name=0)
        print(f"  Excel文件: {len(df1)}行数据")
        
        # 统计Excel中有分子分母的行数
        excel_with_components = 0
        for _, row in df1.iterrows():
            if (pd.notna(row.get('分子', '')) and str(row.get('分子', '')).strip()) or \
               (pd.notna(row.get('分母', '')) and str(row.get('分母', '')).strip()):
                excel_with_components += 1
        
        print(f"  Excel中有分子分母的行: {excel_with_components}行")
        
        # 检查第二章
        print(f"\n📖 第二章数据对比:")
        df2 = pd.read_excel("chapter2.xlsx", sheet_name=0)
        print(f"  Excel文件: {len(df2)}行数据")
        
        excel_with_components_2 = 0
        for _, row in df2.iterrows():
            if (pd.notna(row.get('分子', '')) and str(row.get('分子', '')).strip()) or \
               (pd.notna(row.get('分母', '')) and str(row.get('分母', '')).strip()):
                excel_with_components_2 += 1
        
        print(f"  Excel中有分子分母的行: {excel_with_components_2}行")
        
    except Exception as e:
        print(f"❌ 对比Excel文件时出错: {e}")

def show_sample_complete_indicators():
    """
    显示完整数据的指标样本
    """
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"\n" + "=" * 100)
        print("📋 完整数据指标样本")
        print("=" * 100)
        
        # 查找有完整分子分母信息的指标
        cursor.execute("""
            SELECT DISTINCT ic.indicator_id, i.name
            FROM indicator_components ic
            JOIN indicators i ON ic.indicator_id = i.id
            WHERE ic.unit IS NOT NULL AND ic.unit != ''
            AND ic.lead_department IS NOT NULL AND ic.lead_department != ''
            AND ic.data_source IS NOT NULL AND ic.data_source != ''
            ORDER BY ic.indicator_id
            LIMIT 3
        """)
        
        complete_indicators = cursor.fetchall()
        
        for indicator_id, indicator_name in complete_indicators:
            print(f"\n🎯 {indicator_id} - {indicator_name}")
            
            cursor.execute("""
                SELECT component_type, name, unit, lead_department, data_source, logic_definition
                FROM indicator_components
                WHERE indicator_id = ?
                ORDER BY component_type DESC
            """, (indicator_id,))
            
            components = cursor.fetchall()
            
            for comp_type, name, unit, dept, source, logic in components:
                type_name = "🔺 分子" if comp_type == 'numerator' else "🔻 分母"
                print(f"  {type_name}:")
                print(f"    名称: {name}")
                print(f"    单位: {unit}")
                print(f"    牵头科室: {dept}")
                print(f"    数据来源: {source}")
                if logic and logic.strip():
                    print(f"    逻辑定义: {logic[:100]}...")
        
    except Exception as e:
        print(f"❌ 显示样本数据时出错: {e}")
    finally:
        if conn:
            conn.close()

def main():
    # 检查数据库完整性
    success = check_database_completeness()
    
    if success:
        # 对比Excel文件
        check_excel_vs_database()
        
        # 显示完整数据样本
        show_sample_complete_indicators()
    
    print(f"\n" + "=" * 100)
    print("✅ 数据完整性检查完成")
    print("=" * 100)

if __name__ == "__main__":
    main()
