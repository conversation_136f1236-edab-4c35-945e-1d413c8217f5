<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量删除功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        /* 按钮样式 */
        .admin-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 5px;
        }
        
        .admin-btn-primary {
            background: #1976d2;
            color: white;
        }
        
        .admin-btn-warning {
            background: #f57c00;
            color: white;
        }
        
        .admin-btn-danger {
            background: #d32f2f;
            color: white;
        }
        
        .admin-btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .checkbox-column {
            width: 50px;
        }
        
        /* 模拟指标类型徽章 */
        .indicator-badge {
            background: #e3f2fd;
            color: #1565c0;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            border: 1px solid #2196f3;
            white-space: nowrap;
            min-width: 60px;
            display: inline-block;
            text-align: center;
        }
        
        .indicator-badge.simple {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        /* 操作按钮组 */
        .action-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        /* 结果显示 */
        .result-box {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        
        .result-box.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .result-box.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 批量删除功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 按钮布局测试</div>
            <p>测试批量删除按钮在操作栏中的显示效果：</p>
            
            <div class="action-buttons">
                <button class="admin-btn admin-btn-primary">
                    <i class="fas fa-plus"></i>
                    添加指标
                </button>
                <button class="admin-btn admin-btn-warning">
                    <i class="fas fa-edit"></i>
                    批量编辑
                </button>
                <button class="admin-btn admin-btn-danger" onclick="testBatchDelete()">
                    <i class="fas fa-trash-alt"></i>
                    批量删除
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 选择功能测试</div>
            <p>测试多选功能和批量删除确认对话框：</p>
            
            <table>
                <thead>
                    <tr>
                        <th class="checkbox-column">
                            <input type="checkbox" onchange="toggleSelectAll(this)">
                        </th>
                        <th>指标ID</th>
                        <th>指标名称</th>
                        <th>指标类型</th>
                        <th>章节</th>
                        <th>子指标</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><input type="checkbox" value="1.1.1"></td>
                        <td>1.1.1</td>
                        <td>核定床位数</td>
                        <td><span class="indicator-badge">复合指标</span></td>
                        <td>1</td>
                        <td>0</td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" value="1.1.2"></td>
                        <td>1.1.2</td>
                        <td>实际开放床位数</td>
                        <td><span class="indicator-badge simple">简单指标</span></td>
                        <td>1</td>
                        <td>0</td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" value="1.1.3"></td>
                        <td>1.1.3</td>
                        <td>床位使用率（名称修订）</td>
                        <td><span class="indicator-badge">复合指标</span></td>
                        <td>1</td>
                        <td>0</td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" value="1.2.1"></td>
                        <td>1.2.1</td>
                        <td>卫生技术人员与开放床位比</td>
                        <td><span class="indicator-badge">复合指标</span></td>
                        <td>1</td>
                        <td>2</td>
                    </tr>
                </tbody>
            </table>
            
            <div style="margin-top: 15px;">
                <button class="admin-btn admin-btn-danger" onclick="testBatchDelete()">
                    <i class="fas fa-trash-alt"></i>
                    批量删除选中项
                </button>
            </div>
            
            <div id="result" class="result-box" style="display: none;">
                <strong>测试结果：</strong>
                <div id="result-content"></div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 功能特性</div>
            <ul>
                <li>✅ 批量删除按钮位于"批量编辑"按钮右侧</li>
                <li>✅ 使用红色危险样式 (admin-btn-danger)</li>
                <li>✅ 图标使用 fas fa-trash-alt</li>
                <li>✅ 未选择指标时提示"请先选择要删除的指标"</li>
                <li>✅ 删除前显示详细确认对话框</li>
                <li>✅ 确认对话框显示将要删除的指标列表</li>
                <li>✅ 有子指标的指标会被跳过并显示错误信息</li>
                <li>✅ 支持软删除（设置 is_active = 0）</li>
                <li>✅ 删除后显示成功/失败统计</li>
            </ul>
        </div>
    </div>

    <script>
        // 全选/取消全选
        function toggleSelectAll(checkbox) {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = checkbox.checked);
        }
        
        // 测试批量删除功能
        function testBatchDelete() {
            const selected = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('result-content');
            
            if (selected.length === 0) {
                resultDiv.className = 'result-box error';
                resultContent.innerHTML = '请先选择要删除的指标';
                resultDiv.style.display = 'block';
                return;
            }
            
            // 获取选中的指标信息
            const selectedIndicators = [];
            selected.forEach(checkbox => {
                const row = checkbox.closest('tr');
                const cells = row.querySelectorAll('td');
                selectedIndicators.push({
                    id: checkbox.value,
                    name: cells[2].textContent.trim(),
                    hasChildren: cells[5].textContent.trim() !== '0'
                });
            });
            
            // 构建确认消息
            let confirmMessage = `确定要删除以下 ${selectedIndicators.length} 个指标吗？此操作不可恢复。\n\n`;
            confirmMessage += '将要删除的指标：\n';
            selectedIndicators.forEach((indicator, index) => {
                if (index < 5) {
                    confirmMessage += `• ${indicator.id} - ${indicator.name}`;
                    if (indicator.hasChildren) {
                        confirmMessage += ' ⚠️ (有子指标)';
                    }
                    confirmMessage += '\n';
                } else if (index === 5) {
                    confirmMessage += `• ... 还有 ${selectedIndicators.length - 5} 个指标\n`;
                }
            });
            confirmMessage += '\n⚠️ 注意：如果指标有子指标，需要先删除子指标。';
            
            if (confirm(confirmMessage)) {
                // 模拟删除结果
                let successCount = 0;
                let errorCount = 0;
                let errors = [];
                
                selectedIndicators.forEach(indicator => {
                    if (indicator.hasChildren) {
                        errorCount++;
                        errors.push(`指标 ${indicator.id} 有子指标，无法删除`);
                    } else {
                        successCount++;
                    }
                });
                
                // 显示结果
                let resultHtml = '';
                if (successCount > 0) {
                    resultHtml += `<div style="color: #28a745;">✅ 成功删除 ${successCount} 个指标</div>`;
                }
                if (errorCount > 0) {
                    resultHtml += `<div style="color: #dc3545;">❌ 删除失败 ${errorCount} 个指标</div>`;
                    errors.forEach(error => {
                        resultHtml += `<div style="color: #dc3545; font-size: 12px; margin-left: 20px;">• ${error}</div>`;
                    });
                }
                
                resultDiv.className = successCount > 0 ? 'result-box success' : 'result-box error';
                resultContent.innerHTML = resultHtml;
                resultDiv.style.display = 'block';
                
                // 清除选择
                document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
            }
        }
    </script>
</body>
</html>
