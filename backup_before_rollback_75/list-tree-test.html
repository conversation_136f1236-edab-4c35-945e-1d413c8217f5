<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>列表视图树形连接线测试</title>
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            padding: 20px;
            background-color: var(--gray-50);
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-title {
            font-size: 24px;
            margin-bottom: 20px;
            color: var(--gray-900);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">列表视图树形连接线效果测试</h1>
        
        <div class="list-view" style="display: block;">
            <div class="list-container">
                <div class="list-header">
                    <div class="list-col-id">编号</div>
                    <div class="list-col-title">指标名称</div>
                    <div class="list-col-desc">描述</div>
                    <div class="list-col-tags">标签</div>
                    <div class="list-col-actions">操作</div>
                </div>
                <div class="list-body" id="indicatorList">
                    <!-- 父指标 1.3.1 -->
                    <div class="list-item level-0">
                        <div class="list-item-container">
                            <div class="list-col-id">
                                <div class="list-id-section">
                                    <button class="expand-toggle-list expanded" onclick="toggleTestList('1.3.1')" title="展开/折叠子指标">
                                        <i class="fas fa-chevron-down" id="toggle-list-1.3.1"></i>
                                    </button>
                                    <span class="list-item-badge">1.3.1</span>
                                </div>
                            </div>
                            <div class="list-col-title">
                                <span class="list-item-title">急诊医学科</span>
                            </div>
                            <div class="list-col-desc">
                                <span class="list-item-desc">第一章1级指标：急诊医学科相关科室资源配置</span>
                            </div>
                            <div class="list-col-tags">
                                <span class="tag">第1章</span>
                                <span class="tag">第1.3节</span>
                                <span class="tag children-tag">2个子指标</span>
                            </div>
                            <div class="list-col-actions">
                                <button class="action-icon" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 子指标容器 -->
                    <div class="children-container-list" id="children-list-1.3.1" style="display: block;">
                        <!-- 子指标 1.3.1.1 -->
                        <div class="list-item level-1">
                            <div class="list-item-container" style="padding-left: 40px;">
                                <div class="list-col-id">
                                    <div class="list-id-section">
                                        <span class="expand-spacer-list"></span>
                                        <span class="list-item-badge">1.3.1.1</span>
                                    </div>
                                </div>
                                <div class="list-col-title">
                                    <span class="list-item-title">固定急诊医师人数占急诊在岗医师人数的比例</span>
                                </div>
                                <div class="list-col-desc">
                                    <span class="list-item-desc">第一章2级指标：固定急诊医师人数占急诊在岗医师人数的比例</span>
                                </div>
                                <div class="list-col-tags">
                                    <span class="tag">第1章</span>
                                    <span class="tag">第1.3节</span>
                                    <span class="tag">2个组件</span>
                                </div>
                                <div class="list-col-actions">
                                    <button class="action-icon" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 子指标 1.3.1.2 -->
                        <div class="list-item level-1">
                            <div class="list-item-container" style="padding-left: 40px;">
                                <div class="list-col-id">
                                    <div class="list-id-section">
                                        <span class="expand-spacer-list"></span>
                                        <span class="list-item-badge">1.3.1.2</span>
                                    </div>
                                </div>
                                <div class="list-col-title">
                                    <span class="list-item-title">固定急诊护士人数占急诊护士人数的比例</span>
                                </div>
                                <div class="list-col-desc">
                                    <span class="list-item-desc">第一章2级指标：固定急诊护士人数占急诊护士人数的比例</span>
                                </div>
                                <div class="list-col-tags">
                                    <span class="tag">第1章</span>
                                    <span class="tag">第1.3节</span>
                                    <span class="tag">2个组件</span>
                                </div>
                                <div class="list-col-actions">
                                    <button class="action-icon" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 另一个父指标 1.3.2 -->
                    <div class="list-item level-0">
                        <div class="list-item-container">
                            <div class="list-col-id">
                                <div class="list-id-section">
                                    <button class="expand-toggle-list" onclick="toggleTestList('1.3.2')" title="展开/折叠子指标">
                                        <i class="fas fa-chevron-down" id="toggle-list-1.3.2"></i>
                                    </button>
                                    <span class="list-item-badge">1.3.2</span>
                                </div>
                            </div>
                            <div class="list-col-title">
                                <span class="list-item-title">重症医学科</span>
                            </div>
                            <div class="list-col-desc">
                                <span class="list-item-desc">第一章1级指标：重症医学科相关科室资源配置</span>
                            </div>
                            <div class="list-col-tags">
                                <span class="tag">第1章</span>
                                <span class="tag">第1.3节</span>
                                <span class="tag children-tag">1个子指标</span>
                            </div>
                            <div class="list-col-actions">
                                <button class="action-icon" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 子指标容器（折叠状态） -->
                    <div class="children-container-list" id="children-list-1.3.2" style="display: none;">
                        <!-- 子指标内容将在展开时显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleTestList(indicatorId) {
            const childrenContainer = document.getElementById(`children-list-${indicatorId}`);
            const toggleIcon = document.getElementById(`toggle-list-${indicatorId}`);
            const toggleButton = toggleIcon?.parentElement;

            if (!childrenContainer) return;

            const isExpanded = childrenContainer.style.display !== 'none';

            if (isExpanded) {
                // 折叠子指标
                childrenContainer.style.display = 'none';
                if (toggleIcon) {
                    toggleIcon.className = 'fas fa-chevron-down';
                }
                if (toggleButton) {
                    toggleButton.classList.remove('expanded');
                }
            } else {
                // 展开子指标
                childrenContainer.style.display = 'block';
                if (toggleIcon) {
                    toggleIcon.className = 'fas fa-chevron-down';
                }
                if (toggleButton) {
                    toggleButton.classList.add('expanded');
                }
                
                // 如果是1.3.2，动态添加内容
                if (indicatorId === '1.3.2' && childrenContainer.children.length === 0) {
                    childrenContainer.innerHTML = `
                        <div class="list-item level-1">
                            <div class="list-item-container" style="padding-left: 40px;">
                                <div class="list-col-id">
                                    <div class="list-id-section">
                                        <span class="expand-spacer-list"></span>
                                        <span class="list-item-badge">1.3.2.1</span>
                                    </div>
                                </div>
                                <div class="list-col-title">
                                    <span class="list-item-title">重症医学科医师人数配置</span>
                                </div>
                                <div class="list-col-desc">
                                    <span class="list-item-desc">第一章2级指标：重症医学科医师人数配置要求</span>
                                </div>
                                <div class="list-col-tags">
                                    <span class="tag">第1章</span>
                                    <span class="tag">第1.3节</span>
                                    <span class="tag">1个组件</span>
                                </div>
                                <div class="list-col-actions">
                                    <button class="action-icon" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }
        }
    </script>
</body>
</html>
