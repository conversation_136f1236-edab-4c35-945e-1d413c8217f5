{% extends "admin/base.html" %}

{% block title %}编辑指标 - 后台管理系统{% endblock %}

{% block breadcrumb %}
<span>后台管理</span> > <a href="/admin/indicators">指标管理</a> > <span>编辑指标</span>
{% endblock %}

{% block content %}
<div style="max-width: 1200px; margin: 0 auto;">
    <!-- 页面标题 -->
    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 32px;">
        <div>
            <h1 style="margin: 0; font-size: 28px; font-weight: 400; color: var(--gray-900); font-family: 'Google Sans', sans-serif;">
                ✏️ 编辑指标
            </h1>
            <p style="margin: 8px 0 0 0; color: var(--gray-600); font-size: 16px;">
                修改指标的基本信息和配置参数
            </p>
        </div>
        <div style="display: flex; gap: 12px;">
            <a href="/admin/indicators/{{ indicator.id }}" class="admin-btn admin-btn-outline">
                <i class="fas fa-arrow-left"></i>
                返回详情
            </a>
            <a href="/admin/indicators" class="admin-btn admin-btn-outline">
                <i class="fas fa-list"></i>
                指标列表
            </a>
        </div>
    </div>

    <!-- 指标基本信息卡片 -->
    <div class="material-card" style="margin-bottom: 24px;">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-info-circle" style="color: var(--primary); margin-right: 8px;"></i>
                指标信息
            </h3>
        </div>
        <div class="card-content">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div class="info-item">
                    <div class="info-label">指标ID</div>
                    <div class="info-value" style="font-family: 'JetBrains Mono', monospace; font-weight: 600; color: var(--primary);">
                        {{ indicator.id }}
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">所属章节</div>
                    <div class="info-value">
                        <span class="badge badge-primary">{{ indicator.chapter_code }}</span>
                        {{ indicator.chapter_name }}
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">所属小节</div>
                    <div class="info-value">
                        <span class="badge badge-success">{{ indicator.section_code }}</span>
                        {{ indicator.section_name }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑表单 -->
    <div class="material-card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-edit" style="color: var(--warning); margin-right: 8px;"></i>
                编辑指标信息
            </h3>
        </div>
        <div class="card-content">
            <form method="POST" class="material-form">
                <!-- 基本信息区域 -->
                <div class="form-section">
                    <h4 class="section-title">基本信息</h4>
                    <div class="form-grid">
                        <!-- 指标名称 -->
                        <div class="form-field full-width">
                            <label class="field-label required">指标名称</label>
                            <div class="input-wrapper">
                                <input type="text" name="name" value="{{ indicator.name or '' }}" required
                                       class="material-input" placeholder="请输入指标名称">
                                <div class="input-underline"></div>
                            </div>
                        </div>

                        <!-- 指标分类 -->
                        <div class="form-field">
                            <label class="field-label">指标分类</label>
                            <div class="select-wrapper">
                                <select name="category" class="material-select">
                                    <option value="">请选择分类</option>
                                    <option value="结构指标" {% if indicator.category == '结构指标' %}selected{% endif %}>结构指标</option>
                                    <option value="过程指标" {% if indicator.category == '过程指标' %}selected{% endif %}>过程指标</option>
                                    <option value="结果指标" {% if indicator.category == '结果指标' %}selected{% endif %}>结果指标</option>
                                    <option value="质量指标" {% if indicator.category == '质量指标' %}selected{% endif %}>质量指标</option>
                                    <option value="效率指标" {% if indicator.category == '效率指标' %}selected{% endif %}>效率指标</option>
                                    <option value="安全指标" {% if indicator.category == '安全指标' %}selected{% endif %}>安全指标</option>
                                </select>
                                <div class="select-arrow">
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 指标描述 -->
                        <div class="form-field full-width">
                            <label class="field-label">指标描述</label>
                            <div class="textarea-wrapper">
                                <textarea name="description" rows="4" class="material-textarea"
                                          placeholder="请输入指标的详细描述">{{ indicator.description or '' }}</textarea>
                                <div class="input-underline"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数值信息区域 -->
                <div class="form-section">
                    <h4 class="section-title">数值信息</h4>
                    <div class="form-grid three-columns">
                        <div class="form-field">
                            <label class="field-label">目标值</label>
                            <div class="input-wrapper">
                                <input type="text" name="target_value" value="{{ indicator.target_value or '' }}"
                                       class="material-input" placeholder="设定目标值">
                                <div class="input-underline"></div>
                            </div>
                        </div>

                        <div class="form-field">
                            <label class="field-label">当前值</label>
                            <div class="input-wrapper">
                                <input type="text" name="current_value" value="{{ indicator.current_value or '' }}"
                                       class="material-input" placeholder="当前实际值">
                                <div class="input-underline"></div>
                            </div>
                        </div>

                        <div class="form-field">
                            <label class="field-label">完成率 (%)</label>
                            <div class="input-wrapper">
                                <input type="number" name="completion_rate" value="{{ indicator.completion_rate or '' }}"
                                       step="0.1" min="0" max="100" class="material-input" placeholder="0-100">
                                <div class="input-underline"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 扩展信息区域 -->
                <div class="form-section">
                    <h4 class="section-title">扩展信息</h4>
                    <div class="form-grid">
                        <div class="form-field">
                            <label class="field-label">单位</label>
                            <div class="input-wrapper">
                                <input type="text" name="unit" value="{{ indicator.unit or '' }}"
                                       class="material-input" placeholder="例如：张、人、%、次">
                                <div class="input-underline"></div>
                            </div>
                        </div>

                        <div class="form-field">
                            <label class="field-label">牵头科室</label>
                            <div class="input-wrapper">
                                <input type="text" name="lead_department" value="{{ indicator.lead_department or '' }}"
                                       class="material-input" placeholder="负责该指标的主要科室">
                                <div class="input-underline"></div>
                            </div>
                        </div>

                        <div class="form-field full-width">
                            <label class="field-label">数据来源</label>
                            <div class="input-wrapper">
                                <input type="text" name="data_source" value="{{ indicator.data_source or '' }}"
                                       class="material-input" placeholder="例如：HIS系统、手工统计、第三方系统">
                                <div class="input-underline"></div>
                            </div>
                        </div>

                        <div class="form-field full-width">
                            <label class="field-label">逻辑定义</label>
                            <div class="textarea-wrapper">
                                <textarea name="logic_definition" rows="3" class="material-textarea"
                                          placeholder="指标的计算逻辑和业务规则说明">{{ indicator.logic_definition or '' }}</textarea>
                                <div class="input-underline"></div>
                            </div>
                        </div>

                        <div class="form-field full-width">
                            <label class="field-label">参考范围</label>
                            <div class="input-wrapper">
                                <input type="text" name="reference_range" value="{{ indicator.reference_range or '' }}"
                                       class="material-input" placeholder="例如：≥90%、<5%、正常范围">
                                <div class="input-underline"></div>
                            </div>
                        </div>

                        <div class="form-field full-width">
                            <label class="field-label">计算方法</label>
                            <div class="textarea-wrapper">
                                <textarea name="calculation_method" rows="3" class="material-textarea"
                                          placeholder="详细描述指标的计算方法和公式">{{ indicator.calculation_method or '' }}</textarea>
                                <div class="input-underline"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 字段显示配置区域（仅底层指标显示） -->
                {% if indicator.is_bottom_level %}
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fas fa-eye" style="color: var(--primary); margin-right: 8px;"></i>
                        基础信息显示配置
                    </h4>
                    <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; margin-bottom: 20px;">
                        <p style="margin: 0; color: var(--gray-600); font-size: 14px;">
                            <i class="fas fa-info-circle" style="color: var(--info); margin-right: 4px;"></i>
                            选择在前端指标详情页面的"基础信息"模块中显示的字段
                        </p>
                    </div>
                    <div class="field-display-config">
                        <div class="checkbox-grid">
                            <div class="checkbox-item">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="show_unit_in_basic" value="1"
                                           {% if field_display.show_unit_in_basic %}checked{% endif %}>
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">单位</span>
                                </label>
                            </div>
                            <div class="checkbox-item">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="show_lead_department_in_basic" value="1"
                                           {% if field_display.show_lead_department_in_basic %}checked{% endif %}>
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">牵头科室</span>
                                </label>
                            </div>
                            <div class="checkbox-item">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="show_data_source_in_basic" value="1"
                                           {% if field_display.show_data_source_in_basic %}checked{% endif %}>
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">数据来源</span>
                                </label>
                            </div>
                            <div class="checkbox-item">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="show_logic_definition_in_basic" value="1"
                                           {% if field_display.show_logic_definition_in_basic %}checked{% endif %}>
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">逻辑定义</span>
                                </label>
                            </div>
                            <div class="checkbox-item">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="show_reference_range_in_basic" value="1"
                                           {% if field_display.show_reference_range_in_basic %}checked{% endif %}>
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">参考范围</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <button type="submit" class="material-btn primary">
                        <i class="fas fa-save"></i>
                        <span>保存更改</span>
                    </button>
                    <a href="/admin/indicators/{{ indicator.id }}" class="material-btn secondary">
                        <i class="fas fa-times"></i>
                        <span>取消</span>
                    </a>
                    <a href="/admin/indicators" class="material-btn outline">
                        <i class="fas fa-list"></i>
                        <span>返回列表</span>
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Material Design 样式 */
.material-card {
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

.material-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--gray-200);
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.card-title {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: var(--gray-900);
    display: flex;
    align-items: center;
}

.card-content {
    padding: 24px;
}

.info-item {
    margin-bottom: 16px;
}

.info-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.info-value {
    font-size: 14px;
    color: var(--gray-900);
    font-weight: 500;
}

.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 600;
    margin-right: 8px;
}

.badge-primary {
    background: var(--primary-light);
    color: var(--primary);
}

.badge-success {
    background: var(--success-light);
    color: var(--success-dark);
}

/* Material Form 样式 */
.material-form {
    max-width: none;
}

.form-section {
    margin-bottom: 32px;
}

.section-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--gray-900);
    margin: 0 0 20px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--primary-light);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.form-grid.three-columns {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.form-field {
    position: relative;
}

.form-field.full-width {
    grid-column: 1 / -1;
}

.field-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 8px;
    transition: color 0.3s ease;
}

.field-label.required::after {
    content: ' *';
    color: var(--danger);
}

.input-wrapper, .textarea-wrapper, .select-wrapper {
    position: relative;
}

.material-input, .material-textarea {
    width: 100%;
    padding: 12px 0;
    border: none;
    border-bottom: 2px solid var(--gray-300);
    background: transparent;
    font-size: 16px;
    color: var(--gray-900);
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.material-input:focus, .material-textarea:focus {
    outline: none;
    border-bottom-color: var(--primary);
}

.material-input:focus + .input-underline,
.material-textarea:focus + .input-underline {
    transform: scaleX(1);
}

.input-underline {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.material-select {
    width: 100%;
    padding: 12px 0;
    border: none;
    border-bottom: 2px solid var(--gray-300);
    background: transparent;
    font-size: 16px;
    color: var(--gray-900);
    appearance: none;
    cursor: pointer;
}

.select-arrow {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-500);
    pointer-events: none;
}

.material-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.material-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.material-btn.primary {
    background: var(--primary);
    color: white;
}

.material-btn.secondary {
    background: var(--gray-500);
    color: white;
}

.material-btn.outline {
    background: transparent;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-start;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid var(--gray-200);
}

/* 字段显示配置样式 */
.field-display-config {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--gray-200);
}

.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.checkbox-item {
    display: flex;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: var(--gray-700);
    transition: color 0.3s ease;
}

.checkbox-label:hover {
    color: var(--primary);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 4px;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
    background: white;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background: var(--primary);
    border-color: var(--primary);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-text {
    font-weight: 500;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .material-btn {
        justify-content: center;
    }

    .checkbox-grid {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Material Design 交互效果
document.addEventListener('DOMContentLoaded', function() {
    // 表单验证
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const name = document.querySelector('input[name="name"]').value.trim();
        if (!name) {
            showNotification('请输入指标名称', 'error');
            e.preventDefault();
            return false;
        }

        // 确认提交
        if (!confirm('确定要保存这些更改吗？')) {
            e.preventDefault();
            return false;
        }

        // 显示加载状态
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>保存中...</span>';
        submitBtn.disabled = true;
    });

    // Material Design 输入框效果
    document.querySelectorAll('.material-input, .material-textarea').forEach(element => {
        element.addEventListener('focus', function() {
            this.parentElement.querySelector('.field-label').style.color = 'var(--primary)';
        });

        element.addEventListener('blur', function() {
            this.parentElement.querySelector('.field-label').style.color = 'var(--gray-700)';
        });
    });

    // 自动计算完成率
    const targetInput = document.querySelector('input[name="target_value"]');
    const currentInput = document.querySelector('input[name="current_value"]');
    const completionInput = document.querySelector('input[name="completion_rate"]');

    function calculateCompletion() {
        const target = parseFloat(targetInput.value);
        const current = parseFloat(currentInput.value);

        if (target && current && target > 0) {
            const completion = Math.round((current / target) * 100 * 10) / 10;
            completionInput.value = Math.min(100, Math.max(0, completion));
        }
    }

    if (targetInput && currentInput && completionInput) {
        targetInput.addEventListener('input', calculateCompletion);
        currentInput.addEventListener('input', calculateCompletion);
    }
});

// 通知函数
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;

    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'error' ? 'var(--danger)' : 'var(--primary)'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        display: flex;
        align-items: center;
        gap: 8px;
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// 添加动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
