{% extends "admin/base.html" %}

{% block title %}数据总览 - 医院指标后台管理系统{% endblock %}

{% block breadcrumb %}
<span>后台管理</span> / <span>数据总览</span>
{% endblock %}

{% block content %}
<h1 class="admin-page-title">📊 数据总览</h1>

<!-- 统计卡片 -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-number">{{ stats.chapters or 0 }}</div>
        <div class="stat-label">章节总数</div>
    </div>

    <div class="stat-card">
        <div class="stat-number">{{ stats.sections or 0 }}</div>
        <div class="stat-label">小节总数</div>
    </div>

    <div class="stat-card success">
        <div class="stat-number">{{ stats.indicators or 0 }}</div>
        <div class="stat-label">指标总数</div>
    </div>

    <div class="stat-card warning">
        <div class="stat-number">{{ stats.components or 0 }}</div>
        <div class="stat-label">组件总数</div>
    </div>
</div>

<!-- 数据质量统计 -->
<div class="stats-grid" style="margin-top: 24px;">
    <div class="stat-card danger">
        <div class="stat-number">{{ stats.incomplete_indicators or 0 }}</div>
        <div class="stat-label">不完整指标</div>
    </div>

    <div class="stat-card danger">
        <div class="stat-number">{{ stats.incomplete_components or 0 }}</div>
        <div class="stat-label">不完整组件</div>
    </div>
</div>

<!-- 数据质量概览 -->
<div class="admin-table">
    <h3 style="padding: 20px 20px 0 20px; margin: 0; color: #2c3e50;">📋 数据质量概览</h3>
    <table>
        <thead>
            <tr>
                <th>检查项目</th>
                <th>状态</th>
                <th>问题数量</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>指标信息完整性</td>
                <td><span style="color: #27ae60;">✅ 正常</span></td>
                <td>0 个不完整指标</td>
                <td>
                    <a href="#" class="admin-btn admin-btn-primary">
                        <i class="fas fa-search"></i>
                        查看详情
                    </a>
                </td>
            </tr>
            <tr>
                <td>组件信息完整性</td>
                <td><span style="color: #27ae60;">✅ 正常</span></td>
                <td>0 个不完整组件</td>
                <td>
                    <a href="#" class="admin-btn admin-btn-warning">
                        <i class="fas fa-wrench"></i>
                        修复问题
                    </a>
                </td>
            </tr>
            <tr>
                <td>指标层级关系</td>
                <td><span style="color: #27ae60;">✅ 正常</span></td>
                <td>0 个关系错误</td>
                <td>
                    <a href="#" class="admin-btn admin-btn-primary">
                        <i class="fas fa-sitemap"></i>
                        检查关系
                    </a>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- 快速操作 -->
<div style="margin-top: 30px;">
    <h3 style="color: #2c3e50; margin-bottom: 20px;">🚀 快速操作</h3>
    <div style="display: flex; gap: 15px; flex-wrap: wrap;">
        <a href="/admin/chapters" class="admin-btn admin-btn-primary" style="padding: 12px 20px;">
            <i class="fas fa-book"></i>
            管理章节
        </a>
        <a href="/admin/sections" class="admin-btn admin-btn-primary" style="padding: 12px 20px;">
            <i class="fas fa-list"></i>
            管理小节
        </a>
        <a href="/admin/indicators" class="admin-btn admin-btn-warning" style="padding: 12px 20px;">
            <i class="fas fa-chart-bar"></i>
            管理指标
        </a>
        <a href="/admin/components" class="admin-btn admin-btn-warning" style="padding: 12px 20px;">
            <i class="fas fa-puzzle-piece"></i>
            管理组件
        </a>
        <a href="#" class="admin-btn admin-btn-danger" style="padding: 12px 20px;">
            <i class="fas fa-check-circle"></i>
            数据验证
        </a>
    </div>
</div>

<!-- 系统信息 -->
<div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #3498db;">
    <h4 style="margin: 0 0 10px 0; color: #2c3e50;">💡 系统提示</h4>
    <ul style="margin: 0; padding-left: 20px; color: #6c757d;">
        <li>定期检查数据完整性，确保指标信息准确</li>
        <li>使用批量操作功能可以提高数据修正效率</li>
        <li>所有操作都会记录日志，便于追踪和审计</li>
        <li>建议在修改重要数据前先备份数据库</li>
    </ul>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载时获取统计数据
document.addEventListener('DOMContentLoaded', function() {
    loadStatistics();
});

// 加载统计数据
function loadStatistics() {
    fetch('/api/admin/statistics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatistics(data.data);
            } else {
                showError('加载统计数据失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('网络错误: ' + error.message);
        });
}

// 更新统计数据
function updateStatistics(stats) {
    document.getElementById('chaptersCount').textContent = stats.chapters_count || 0;
    document.getElementById('sectionsCount').textContent = stats.sections_count || 0;
    document.getElementById('indicatorsCount').textContent = stats.indicators_count || 0;
    document.getElementById('componentsCount').textContent = stats.components_count || 0;
}

// 显示错误消息
function showError(message) {
    console.error(message);
    // 可以添加更友好的错误提示
}

// 自动刷新统计数据
setInterval(function() {
    loadStatistics();
}, 30000); // 30秒刷新一次
</script>
{% endblock %}
