#!/usr/bin/env python3
"""
诊断指标参考数据加载失败的问题
"""

import requests
import sqlite3

def check_backend_reference_loading():
    """检查后端指标参考加载情况"""
    print("🔍 检查后端指标参考加载情况...")
    
    # 测试几个有指标参考数据的指标
    test_indicators = ['1.1.1', '1.1.2', '1.2.1', '1.3.1']
    
    for indicator_id in test_indicators:
        print(f"\n📊 测试指标 {indicator_id}:")
        
        try:
            # 测试前端API
            response = requests.get(f'http://localhost:5001/api/indicators/{indicator_id}')
            data = response.json()
            
            if data['success']:
                indicator = data['data']['indicator']
                print(f"   前端API: ✅ 成功")
                print(f"   指标名称: {indicator['name']}")
                
                # 检查指标参考字段
                ref_fields = ['indicator_definition', 'statistical_scope', 'reference_value', 'monitoring_analysis']
                has_ref_data = any(indicator.get(field) for field in ref_fields)
                print(f"   指标参考数据: {'✅ 有' if has_ref_data else '❌ 无'}")
                
                if has_ref_data:
                    for field in ref_fields:
                        value = indicator.get(field)
                        if value:
                            print(f"     - {field}: {value[:50]}{'...' if len(str(value)) > 50 else ''}")
                
            else:
                print(f"   前端API: ❌ 失败 - {data.get('error')}")
            
            # 测试后端页面
            try:
                response = requests.get(f'http://localhost:5001/admin/indicators/{indicator_id}')
                if response.status_code == 200:
                    print(f"   后端页面: ✅ 可访问")
                else:
                    print(f"   后端页面: ❌ 状态码 {response.status_code}")
            except Exception as e:
                print(f"   后端页面: ❌ 异常 - {e}")
                
        except Exception as e:
            print(f"   测试异常: {e}")

def check_database_reference_data():
    """检查数据库中的指标参考数据"""
    print("\n🗄️ 检查数据库中的指标参考数据...")
    
    try:
        conn = sqlite3.connect('DATABASE-HOSPITAL/hospital_indicator_system.db')
        cursor = conn.cursor()
        
        # 检查indicators表中的指标参考字段
        cursor.execute("""
            SELECT id, name,
                   CASE WHEN indicator_definition IS NOT NULL AND indicator_definition != '' THEN '✅' ELSE '❌' END as 定义,
                   CASE WHEN statistical_scope IS NOT NULL AND statistical_scope != '' THEN '✅' ELSE '❌' END as 范围,
                   CASE WHEN reference_value IS NOT NULL AND reference_value != '' THEN '✅' ELSE '❌' END as 参考值,
                   CASE WHEN monitoring_analysis IS NOT NULL AND monitoring_analysis != '' THEN '✅' ELSE '❌' END as 分析
            FROM indicators 
            WHERE id IN ('1.1.1', '1.1.2', '1.2.1', '1.3.1')
            ORDER BY id
        """)
        
        results = cursor.fetchall()
        print(f"{'指标ID':<8} {'指标名称':<20} {'定义':<4} {'范围':<4} {'参考值':<6} {'分析':<4}")
        print("-" * 65)
        for row in results:
            print(f"{row[0]:<8} {row[1]:<20} {row[2]:<4} {row[3]:<4} {row[4]:<6} {row[5]:<4}")
        
        # 检查indicator_reference_ranges表是否存在
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='indicator_reference_ranges'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                print(f"\n📋 indicator_reference_ranges表: ✅ 存在")
                
                cursor.execute("SELECT COUNT(*) FROM indicator_reference_ranges WHERE is_active = 1")
                count = cursor.fetchone()[0]
                print(f"   活跃记录数: {count}")
                
                if count > 0:
                    cursor.execute("""
                        SELECT indicator_id, 
                               CASE WHEN indicator_definition IS NOT NULL THEN '✅' ELSE '❌' END as 定义,
                               CASE WHEN target_value IS NOT NULL THEN '✅' ELSE '❌' END as 目标值,
                               CASE WHEN notes IS NOT NULL THEN '✅' ELSE '❌' END as 注意事项
                        FROM indicator_reference_ranges 
                        WHERE is_active = 1
                        ORDER BY indicator_id
                    """)
                    
                    ref_results = cursor.fetchall()
                    print(f"\n   参考范围表数据:")
                    print(f"   {'指标ID':<25} {'定义':<4} {'目标值':<6} {'注意事项':<8}")
                    print("   " + "-" * 50)
                    for row in ref_results:
                        print(f"   {row[0]:<25} {row[1]:<4} {row[2]:<6} {row[3]:<8}")
            else:
                print(f"\n📋 indicator_reference_ranges表: ❌ 不存在")
                
        except Exception as e:
            print(f"\n📋 检查indicator_reference_ranges表失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")

def check_api_reference_range_endpoint():
    """检查参考范围API端点"""
    print("\n🔗 检查参考范围API端点...")
    
    test_indicators = ['1.1.1', '1.2.1']
    
    for indicator_id in test_indicators:
        try:
            response = requests.get(f'http://localhost:5001/api/indicators/{indicator_id}/reference-range')
            data = response.json()
            
            print(f"\n📊 {indicator_id} 参考范围API:")
            if data['success']:
                ref_data = data.get('data')
                if ref_data:
                    print(f"   状态: ✅ 有数据")
                    print(f"   定义: {ref_data.get('indicator_definition', '无')[:50]}...")
                    print(f"   目标值: {ref_data.get('target_value', '无')}")
                else:
                    print(f"   状态: ⚠️  无数据")
            else:
                print(f"   状态: ❌ 失败 - {data.get('error')}")
                
        except Exception as e:
            print(f"   状态: ❌ 异常 - {e}")

def analyze_issue_and_suggest_solution():
    """分析问题并建议解决方案"""
    print("\n💡 问题分析和解决方案")
    print("=" * 60)
    
    print("🔍 可能的问题原因:")
    print("1. 📊 数据层面:")
    print("   - indicators表中缺少指标参考字段数据")
    print("   - indicator_reference_ranges表不存在或无数据")
    print("   - 数据结构不匹配")
    
    print("\n2. 🔧 API层面:")
    print("   - 后端API查询逻辑有问题")
    print("   - 参考范围API端点异常")
    print("   - 数据库连接或查询失败")
    
    print("\n3. 🎨 前端层面:")
    print("   - 前端JavaScript未正确处理参考数据")
    print("   - 模态框显示逻辑有误")
    print("   - API调用失败")
    
    print("\n🎯 建议的解决方案:")
    print("1. 📋 数据完整性方案:")
    print("   - 为所有底层指标添加完整的指标参考数据")
    print("   - 使用批量导入脚本统一处理")
    print("   - 建立数据验证机制")
    
    print("\n2. 🏗️ 架构优化方案:")
    print("   - 统一使用indicators表存储指标参考数据")
    print("   - 简化数据结构，避免多表关联")
    print("   - 优化API查询性能")
    
    print("\n3. 🔄 批量处理方案:")
    print("   - 创建指标参考数据模板")
    print("   - 实现批量导入功能")
    print("   - 提供数据验证和修复工具")

def main():
    """主诊断函数"""
    print("🎯 指标参考数据加载失败问题诊断")
    print("=" * 60)
    
    # 检查后端指标参考加载情况
    check_backend_reference_loading()
    
    # 检查数据库中的指标参考数据
    check_database_reference_data()
    
    # 检查参考范围API端点
    check_api_reference_range_endpoint()
    
    # 分析问题并建议解决方案
    analyze_issue_and_suggest_solution()
    
    print("\n" + "=" * 60)
    print("📊 诊断完成")
    print("请根据上述分析结果选择合适的解决方案")

if __name__ == "__main__":
    main()
