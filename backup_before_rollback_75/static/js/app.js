// 医院等级评审指标说明手册 - 主应用脚本

class HospitalIndicatorApp {
    constructor() {
        this.currentChapter = null;
        this.currentSection = null;
        this.indicators = [];
        this.chapters = [];
        this.sections = [];
        this.init();
    }

    async init() {
        try {
            await this.loadChapters();
            await this.loadStatistics();
            this.setupEventListeners();
            this.setupSearch();
            console.log('应用初始化完成');
        } catch (error) {
            console.error('应用初始化失败:', error);
        }
    }

    async loadChapters() {
        try {
            const response = await fetch('/api/chapters');
            const data = await response.json();
            
            if (data.success) {
                this.chapters = data.data;
                this.renderChapters();
            } else {
                console.error('加载章节失败:', data.error);
            }
        } catch (error) {
            console.error('加载章节异常:', error);
        }
    }

    async loadStatistics() {
        try {
            const response = await fetch('/api/statistics');
            const data = await response.json();
            
            if (data.success) {
                this.renderStatistics(data.data);
            } else {
                console.error('加载统计失败:', data.error);
            }
        } catch (error) {
            console.error('加载统计异常:', error);
        }
    }

    renderChapters() {
        const chapterList = document.getElementById('chapterList');
        if (!chapterList) return;

        chapterList.innerHTML = this.chapters.map(chapter => `
            <div class="chapter-item" data-chapter="${chapter.id}">
                <h3>${chapter.name}</h3>
                <p>${chapter.description || ''}</p>
                <div class="chapter-stats">
                    <span><i class="fas fa-list"></i> ${chapter.section_count || 0} 个小节</span>
                    <span><i class="fas fa-chart-bar"></i> ${chapter.indicator_count || 0} 个指标</span>
                </div>
            </div>
        `).join('');

        // 添加点击事件
        chapterList.querySelectorAll('.chapter-item').forEach(item => {
            item.addEventListener('click', () => {
                const chapterId = item.dataset.chapter;
                this.selectChapter(chapterId);
            });
        });
    }

    renderStatistics(stats) {
        const statsContainer = document.getElementById('statisticsContainer');
        if (!statsContainer) return;

        statsContainer.innerHTML = `
            <div class="stat-card">
                <i class="fas fa-book"></i>
                <div class="stat-info">
                    <h3>${stats.total_chapters || 0}</h3>
                    <p>总章节数</p>
                </div>
            </div>
            <div class="stat-card">
                <i class="fas fa-list"></i>
                <div class="stat-info">
                    <h3>${stats.total_sections || 0}</h3>
                    <p>总小节数</p>
                </div>
            </div>
            <div class="stat-card">
                <i class="fas fa-chart-bar"></i>
                <div class="stat-info">
                    <h3>${stats.total_indicators || 0}</h3>
                    <p>总指标数</p>
                </div>
            </div>
        `;
    }

    async selectChapter(chapterId) {
        this.currentChapter = chapterId;
        
        try {
            // 加载章节的小节
            await this.loadSections(chapterId);
            // 加载章节的指标
            await this.loadIndicators(chapterId);
            
            // 更新UI
            this.updateChapterSelection();
            
        } catch (error) {
            console.error('选择章节失败:', error);
        }
    }

    async loadSections(chapterId) {
        try {
            const response = await fetch(`/api/chapters/${chapterId}/sections`);
            const data = await response.json();
            
            if (data.success) {
                this.sections = data.data;
                this.renderSections();
            } else {
                console.error('加载小节失败:', data.error);
            }
        } catch (error) {
            console.error('加载小节异常:', error);
        }
    }

    async loadIndicators(chapterId, sectionId = null) {
        try {
            let url = `/api/indicators?chapter=${chapterId}`;
            if (sectionId) {
                url += `&section=${sectionId}`;
            }
            
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.success) {
                this.indicators = data.data;
                this.renderIndicators();
            } else {
                console.error('加载指标失败:', data.error);
            }
        } catch (error) {
            console.error('加载指标异常:', error);
        }
    }

    renderSections() {
        const sectionList = document.getElementById('sectionList');
        if (!sectionList) return;

        sectionList.innerHTML = this.sections.map(section => `
            <div class="section-item" data-section="${section.id}">
                <h4>${section.name}</h4>
                <p>${section.description || ''}</p>
                <span class="indicator-count">${section.indicator_count || 0} 个指标</span>
            </div>
        `).join('');

        // 添加点击事件
        sectionList.querySelectorAll('.section-item').forEach(item => {
            item.addEventListener('click', () => {
                const sectionId = item.dataset.section;
                this.selectSection(sectionId);
            });
        });
    }

    renderIndicators() {
        const indicatorList = document.getElementById('indicatorList');
        if (!indicatorList) return;

        indicatorList.innerHTML = this.indicators.map(indicator => `
            <div class="indicator-item" data-indicator="${indicator.id}">
                <div class="indicator-header">
                    <h5>${indicator.id} ${indicator.name}</h5>
                    <span class="indicator-type">${indicator.indicator_type || 'simple'}</span>
                </div>
                <p class="indicator-description">${indicator.description || ''}</p>
                <div class="indicator-actions">
                    <button onclick="app.showIndicatorDetail('${indicator.id}')" class="btn-detail">
                        <i class="fas fa-eye"></i> 查看详情
                    </button>
                </div>
            </div>
        `).join('');
    }

    async showIndicatorDetail(indicatorId) {
        try {
            const response = await fetch(`/api/indicators/${indicatorId}`);
            const data = await response.json();
            
            if (data.success) {
                this.displayIndicatorModal(data.data.indicator);
            } else {
                console.error('加载指标详情失败:', data.error);
            }
        } catch (error) {
            console.error('加载指标详情异常:', error);
        }
    }

    displayIndicatorModal(indicator) {
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>${indicator.id} ${indicator.name}</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="indicator-detail">
                        <div class="detail-section">
                            <h3>基本信息</h3>
                            <p><strong>指标类型:</strong> ${indicator.indicator_type || 'simple'}</p>
                            <p><strong>描述:</strong> ${indicator.description || '暂无描述'}</p>
                            <p><strong>单位:</strong> ${indicator.unit || '暂无单位'}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 添加关闭事件
        const closeBtn = modal.querySelector('.close');
        closeBtn.onclick = () => {
            document.body.removeChild(modal);
        };

        modal.onclick = (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        };
    }

    selectSection(sectionId) {
        this.currentSection = sectionId;
        this.loadIndicators(this.currentChapter, sectionId);
        this.updateSectionSelection();
    }

    updateChapterSelection() {
        document.querySelectorAll('.chapter-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeChapter = document.querySelector(`[data-chapter="${this.currentChapter}"]`);
        if (activeChapter) {
            activeChapter.classList.add('active');
        }
    }

    updateSectionSelection() {
        document.querySelectorAll('.section-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeSection = document.querySelector(`[data-section="${this.currentSection}"]`);
        if (activeSection) {
            activeSection.classList.add('active');
        }
    }

    setupEventListeners() {
        // 菜单切换
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        
        if (menuToggle && sidebar) {
            menuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
            });
        }
    }

    setupSearch() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchIndicators(e.target.value);
            });
        }
    }

    searchIndicators(query) {
        if (!query.trim()) {
            this.renderIndicators();
            return;
        }

        const filteredIndicators = this.indicators.filter(indicator => 
            indicator.name.toLowerCase().includes(query.toLowerCase()) ||
            indicator.id.toLowerCase().includes(query.toLowerCase()) ||
            (indicator.description && indicator.description.toLowerCase().includes(query.toLowerCase()))
        );

        const indicatorList = document.getElementById('indicatorList');
        if (!indicatorList) return;

        indicatorList.innerHTML = filteredIndicators.map(indicator => `
            <div class="indicator-item" data-indicator="${indicator.id}">
                <div class="indicator-header">
                    <h5>${indicator.id} ${indicator.name}</h5>
                    <span class="indicator-type">${indicator.indicator_type || 'simple'}</span>
                </div>
                <p class="indicator-description">${indicator.description || ''}</p>
                <div class="indicator-actions">
                    <button onclick="app.showIndicatorDetail('${indicator.id}')" class="btn-detail">
                        <i class="fas fa-eye"></i> 查看详情
                    </button>
                </div>
            </div>
        `).join('');
    }
}

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new HospitalIndicatorApp();
});
