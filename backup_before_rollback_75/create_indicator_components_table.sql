-- 创建指标组成部分表（分子、分母等）
-- 用于存储每个指标的分子、分母详细信息

-- 指标组成部分表
CREATE TABLE IF NOT EXISTS indicator_components (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    indicator_id VARCHAR(20) NOT NULL,
    component_type VARCHAR(20) NOT NULL CHECK (component_type IN ('numerator', 'denominator', 'other')),
    name VARCHAR(200) NOT NULL,
    definition TEXT,
    unit VARCHAR(50),
    data_source VARCHAR(200),
    lead_department VARCHAR(100),
    logic_definition TEXT,
    collection_method VARCHAR(50),
    calculation_formula TEXT,
    notes TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_indicator_components_indicator_id ON indicator_components(indicator_id);
CREATE INDEX IF NOT EXISTS idx_indicator_components_type ON indicator_components(component_type);

-- 指标详细属性表（扩展指标表的属性）
CREATE TABLE IF NOT EXISTS indicator_attributes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    indicator_id VARCHAR(20) NOT NULL UNIQUE,
    unit VARCHAR(50),
    calculation_formula TEXT,
    target_value_description TEXT,
    data_collection_cycle VARCHAR(50),
    quality_requirements TEXT,
    exclusion_criteria TEXT,
    inclusion_criteria TEXT,
    benchmark_value DECIMAL(10,4),
    benchmark_source VARCHAR(200),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_indicator_attributes_indicator_id ON indicator_attributes(indicator_id);

-- 指标与科室的详细关联表（扩展现有的indicator_departments表）
-- 这个表已经存在，我们可以利用它来存储牵头科室信息

-- 显示创建完成信息
SELECT '指标组成部分相关表创建完成！' as message;
