-- 只创建字段显示配置表，不插入数据

-- 创建简单的指标字段显示配置表
CREATE TABLE IF NOT EXISTS indicator_field_display (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    indicator_id VARCHAR(20) NOT NULL,
    field_name VARCHAR(50) NOT NULL,
    show_in_basic INTEGER DEFAULT 0,  -- 是否在基础信息中显示 (0=否, 1=是)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(indicator_id, field_name),
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_field_display_indicator_id ON indicator_field_display(indicator_id);
CREATE INDEX IF NOT EXISTS idx_field_display_field_name ON indicator_field_display(field_name);
