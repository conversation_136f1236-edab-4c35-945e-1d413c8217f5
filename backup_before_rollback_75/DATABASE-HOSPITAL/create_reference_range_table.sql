-- 创建指标参考范围表
-- 用于存储底层指标的详细参考信息

CREATE TABLE IF NOT EXISTS indicator_reference_ranges (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    indicator_id VARCHAR(20) NOT NULL,
    
    -- 指标定义
    indicator_definition TEXT,
    
    -- 计算方法
    calculation_method TEXT,
    calculation_formula TEXT,
    
    -- 目标值与评价标准
    target_value VARCHAR(100),           -- 目标值（如：≥1.03:1）
    current_value VARCHAR(100),          -- 当前值（如：0.85:1）
    completion_rate DECIMAL(5,2),        -- 完成率（如：82.5%）
    evaluation_standard TEXT,            -- 评价标准说明
    
    -- 数据来源
    data_sources TEXT,                   -- 数据来源（如：医院人事管理系统、床位管理系统）
    
    -- 注意事项
    notes TEXT,                          -- 注意事项的详细说明
    
    -- 统计相关
    statistical_scope TEXT,              -- 统计范围
    collection_frequency VARCHAR(50),    -- 统计频率
    collection_method VARCHAR(50),       -- 收集方法
    
    -- 质量要求
    quality_requirements TEXT,           -- 质量要求
    
    -- 改进建议
    improvement_suggestions TEXT,        -- 改进建议
    
    -- 相关政策
    related_policies TEXT,               -- 相关政策文件
    
    -- 系统字段
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_reference_ranges_indicator_id ON indicator_reference_ranges(indicator_id);
CREATE INDEX IF NOT EXISTS idx_reference_ranges_active ON indicator_reference_ranges(is_active);

-- 插入示例数据（基于您提供的图片内容）
INSERT INTO indicator_reference_ranges (
    indicator_id,
    indicator_definition,
    calculation_method,
    calculation_formula,
    target_value,
    current_value,
    completion_rate,
    data_sources,
    notes
) VALUES (
    '卫生技术人员数与开放床位数比',
    '卫生技术人员数与开放床位数比是指医院卫生技术人员总数与同期开放床位数的比值，反映医院人力资源配置与床位资源的匹配程度。',
    '卫生技术人员数与开放床位数比 = 卫生技术人员总数 ÷ 开放床位数',
    '卫生技术人员总数 ÷ 开放床位数',
    '≥1.03:1',
    '0.85:1',
    82.5,
    '医院人事管理系统、床位管理系统',
    '1. 卫生技术人员包括执业医师、执业助理医师、注册护士、药师、检验技师、影像技师等持有相应资格证书的专业技术人员。
2. 开放床位数指实际开放使用的床位数量，不包括备用床位和未正式启用的床位。
3. 统计周期为上一自然年度。'
);

-- 创建触发器，自动更新 updated_at 字段
CREATE TRIGGER IF NOT EXISTS update_reference_ranges_timestamp 
    AFTER UPDATE ON indicator_reference_ranges
    FOR EACH ROW
BEGIN
    UPDATE indicator_reference_ranges 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;
