-- 为底层指标字段添加"基础"标识配置表
-- 创建指标字段配置表

-- 13. 指标字段配置表(indicator_field_config)
CREATE TABLE indicator_field_config (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    field_name VARCHAR(50) NOT NULL COMMENT '字段名称(如unit, lead_department, data_source等)',
    field_label VARCHAR(100) NOT NULL COMMENT '字段显示标签',
    field_type ENUM('text','textarea','select','number','date','boolean') DEFAULT 'text' COMMENT '字段类型',
    display_section ENUM('basic','detailed','hidden') DEFAULT 'detailed' COMMENT '显示区域(basic:基础信息, detailed:详细信息, hidden:隐藏)',
    is_required BOOLEAN DEFAULT FALSE COMMENT '是否必填',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    validation_rules JSON COMMENT '验证规则(JSON格式)',
    default_value TEXT COMMENT '默认值',
    help_text TEXT COMMENT '帮助说明',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_field_name (field_name),
    INDEX idx_display_section (display_section),
    INDEX idx_sort_order (sort_order)
) COMMENT='指标字段配置表';

-- 插入默认字段配置
INSERT INTO indicator_field_config (field_name, field_label, field_type, display_section, is_required, sort_order, help_text) VALUES
-- 基础信息字段
('unit', '单位', 'text', 'basic', TRUE, 1, '指标的计量单位，如：张、人、%、次等'),
('lead_department', '牵头科室', 'text', 'basic', TRUE, 2, '负责该指标数据收集和管理的主要科室'),
('data_source', '数据来源', 'text', 'basic', TRUE, 3, '指标数据的来源系统或部门'),
('logic_definition', '逻辑定义', 'textarea', 'basic', FALSE, 4, '指标的计算逻辑和业务规则说明'),

-- 详细信息字段
('indicator_definition', '指标定义', 'textarea', 'detailed', FALSE, 5, '指标的详细定义和说明'),
('significance', '意义', 'textarea', 'detailed', FALSE, 6, '指标的重要性和意义说明'),
('statistical_scope', '统计范围', 'textarea', 'detailed', FALSE, 7, '指标统计的范围和对象'),
('data_sources', '数据来源详细', 'textarea', 'detailed', FALSE, 8, '详细的数据来源说明'),
('collection_frequency_detail', '统计频率', 'text', 'detailed', FALSE, 9, '数据收集的频率说明'),
('reference_value', '标准值/参考值', 'text', 'detailed', FALSE, 10, '指标的标准值或参考范围'),
('monitoring_analysis', '监测分析', 'textarea', 'detailed', FALSE, 11, '指标的监测和分析方法'),
('calculation_formula', '计算公式', 'text', 'detailed', FALSE, 12, '指标的具体计算公式'),
('numerator_description', '分子描述', 'textarea', 'detailed', FALSE, 13, '分子的详细描述'),
('denominator_description', '分母描述', 'textarea', 'detailed', FALSE, 14, '分母的详细描述');

-- 14. 指标字段值表(indicator_field_values)
CREATE TABLE indicator_field_values (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    indicator_id VARCHAR(20) NOT NULL COMMENT '外键，关联指标表',
    field_name VARCHAR(50) NOT NULL COMMENT '字段名称',
    field_value TEXT COMMENT '字段值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE,
    FOREIGN KEY (field_name) REFERENCES indicator_field_config(field_name) ON DELETE CASCADE,
    UNIQUE KEY unique_indicator_field (indicator_id, field_name),
    INDEX idx_field_name (field_name)
) COMMENT='指标字段值表';

-- 创建视图：指标完整信息视图（包含字段配置）
CREATE VIEW v_indicator_complete_info AS
SELECT
    i.id,
    i.name,
    i.description,
    i.parent_id,
    i.chapter_id,
    i.section_id,
    i.indicator_type,
    i.is_bottom_level,
    -- 章节信息
    c.code AS chapter_code,
    c.name AS chapter_name,
    -- 小节信息
    s.code AS section_code,
    s.name AS section_name,
    -- 基础信息字段（从字段值表获取）
    (SELECT ifv.field_value FROM indicator_field_values ifv 
     WHERE ifv.indicator_id = i.id AND ifv.field_name = 'unit') AS unit,
    (SELECT ifv.field_value FROM indicator_field_values ifv 
     WHERE ifv.indicator_id = i.id AND ifv.field_name = 'lead_department') AS lead_department,
    (SELECT ifv.field_value FROM indicator_field_values ifv 
     WHERE ifv.indicator_id = i.id AND ifv.field_name = 'data_source') AS data_source,
    (SELECT ifv.field_value FROM indicator_field_values ifv 
     WHERE ifv.indicator_id = i.id AND ifv.field_name = 'logic_definition') AS logic_definition,
    -- 详细信息字段
    (SELECT ifv.field_value FROM indicator_field_values ifv 
     WHERE ifv.indicator_id = i.id AND ifv.field_name = 'indicator_definition') AS indicator_definition,
    (SELECT ifv.field_value FROM indicator_field_values ifv 
     WHERE ifv.indicator_id = i.id AND ifv.field_name = 'significance') AS significance,
    (SELECT ifv.field_value FROM indicator_field_values ifv 
     WHERE ifv.indicator_id = i.id AND ifv.field_name = 'statistical_scope') AS statistical_scope,
    (SELECT ifv.field_value FROM indicator_field_values ifv 
     WHERE ifv.indicator_id = i.id AND ifv.field_name = 'reference_value') AS reference_value,
    (SELECT ifv.field_value FROM indicator_field_values ifv 
     WHERE ifv.indicator_id = i.id AND ifv.field_name = 'monitoring_analysis') AS monitoring_analysis
FROM indicators i
LEFT JOIN chapters c ON i.chapter_id = c.id
LEFT JOIN sections s ON i.section_id = s.id
WHERE i.is_active = TRUE;

-- 示例：查询指标的基础信息字段配置
/*
SELECT 
    ifc.field_name,
    ifc.field_label,
    ifc.display_section,
    ifc.is_required,
    ifc.sort_order
FROM indicator_field_config ifc
WHERE ifc.display_section = 'basic' 
  AND ifc.is_active = TRUE
ORDER BY ifc.sort_order;
*/

-- 示例：查询指标的所有字段值
/*
SELECT 
    i.id,
    i.name,
    ifc.field_label,
    ifv.field_value,
    ifc.display_section
FROM indicators i
LEFT JOIN indicator_field_values ifv ON i.id = ifv.indicator_id
LEFT JOIN indicator_field_config ifc ON ifv.field_name = ifc.field_name
WHERE i.id = '1.1.1' 
  AND ifc.is_active = TRUE
ORDER BY ifc.display_section, ifc.sort_order;
*/
