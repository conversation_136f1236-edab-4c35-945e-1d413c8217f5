
// 全局JavaScript错误处理
window.addEventListener('error', function(event) {
    console.error('JavaScript Error:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
    });
    
    // 如果是textContent相关错误，显示友好提示
    if (event.message.includes('textContent') || event.message.includes('Cannot set properties of null')) {
        console.warn('DOM元素访问错误，可能是页面加载时序问题');
        
        // 延迟重试
        setTimeout(function() {
            console.log('尝试重新初始化页面功能...');
            // 这里可以添加重新初始化逻辑
        }, 1000);
    }
});

// 未捕获的Promise错误
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled Promise Rejection:', event.reason);
});
