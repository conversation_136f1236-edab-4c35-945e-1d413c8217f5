/* 医院等级评审指标说明手册 - Google风格样式 */

:root {
    --primary: #1a73e8;
    --primary-dark: #1765cc;
    --primary-light: #e8f0fe;
    --secondary: #34a853;
    --warning: #fbbc04;
    --error: #ea4335;
    --gray-50: #f8f9fa;
    --gray-100: #f1f3f4;
    --gray-200: #e8eaed;
    --gray-300: #dadce0;
    --gray-400: #bdc1c6;
    --gray-500: #9aa0a6;
    --gray-600: #80868b;
    --gray-700: #5f6368;
    --gray-800: #3c4043;
    --gray-900: #202124;
    --white: #ffffff;
    --shadow-1: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
    --shadow-2: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
    --shadow-3: 0 1px 3px 0 rgba(60, 64, 67, 0.3), 0 4px 8px 3px rgba(60, 64, 67, 0.15);
    --border-radius: 8px;
    --transition: all 0.2s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Google Sans', 'Roboto', 'Arial', sans-serif;
}

body {
    background-color: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* 顶部导航 */
.header {
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-300);
    padding: 8px 16px;
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
}

.menu-toggle {
    background: none;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--gray-700);
    margin-right: 16px;
    transition: var(--transition);
}

.menu-toggle:hover {
    background-color: var(--gray-100);
}

.logo {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: 500;
    color: var(--gray-900);
    text-decoration: none;
    margin-right: 24px;
}

.logo-icon {
    color: var(--primary);
    margin-right: 12px;
    font-size: 24px;
}

.search-wrapper {
    flex: 1;
    max-width: 720px;
    position: relative;
}

.search-input {
    width: 100%;
    height: 48px;
    border: none;
    border-radius: 24px;
    background-color: var(--gray-100);
    padding: 0 24px 0 48px;
    font-size: 16px;
    color: var(--gray-900);
    transition: var(--transition);
}

.search-input:focus {
    background-color: var(--white);
    box-shadow: var(--shadow-1);
    outline: none;
}

.search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-600);
    font-size: 18px;
}

.search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-2);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-dropdown.active {
    display: block;
}

.search-result-item {
    padding: 12px 16px;
    border-bottom: 1px solid var(--gray-200);
    cursor: pointer;
    transition: var(--transition);
}

.search-result-item:hover {
    background-color: var(--gray-50);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-title {
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: 4px;
}

.search-result-desc {
    font-size: 14px;
    color: var(--gray-700);
}

.search-result-meta {
    font-size: 12px;
    color: var(--gray-600);
    margin-top: 4px;
}

.header-right {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.header-btn {
    background: none;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--gray-700);
    margin-left: 8px;
    transition: var(--transition);
}

.header-btn:hover {
    background-color: var(--gray-100);
}

.admin-switch-btn {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background-color: var(--error);
    color: var(--white);
    text-decoration: none;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    margin-right: 16px;
    transition: var(--transition);
    box-shadow: var(--shadow-1);
}

.admin-switch-btn:hover {
    background-color: #d93025;
    box-shadow: var(--shadow-2);
    transform: translateY(-1px);
}

.admin-switch-btn i {
    margin-right: 6px;
    font-size: 16px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    margin-left: 16px;
    cursor: pointer;
}

/* 主内容区域 */
.main-container {
    display: flex;
    flex: 1;
}

/* 侧边导航 */
.sidebar {
    width: 256px;
    background-color: var(--white);
    border-right: 1px solid var(--gray-300);
    overflow-y: auto;
    padding: 12px 0;
    transition: all 0.3s ease;
    position: fixed;
    top: 65px;
    left: 0;
    bottom: 0;
    z-index: 100;
}

.sidebar.collapsed {
    width: 72px;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 24px;
    color: var(--gray-800);
    text-decoration: none;
    cursor: pointer;
    border-radius: 0 24px 24px 0;
    margin-right: 12px;
    transition: var(--transition);
}

.nav-item:hover {
    background-color: var(--gray-100);
}

.nav-item.active {
    background-color: var(--primary-light);
    color: var(--primary);
    font-weight: 500;
}

.nav-icon {
    margin-right: 24px;
    width: 24px;
    text-align: center;
    font-size: 18px;
}

.sidebar.collapsed .nav-icon {
    margin-right: 0;
}

.sidebar.collapsed .nav-text {
    display: none;
}

.nav-section {
    margin-top: 24px;
    padding: 0 24px 8px;
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-700);
}

.sidebar.collapsed .nav-section {
    display: none;
}

/* 主内容 */
.content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    margin-left: 256px;
}

.page-header {
    margin-bottom: 32px;
}

.page-title {
    font-size: 32px;
    font-weight: 400;
    color: var(--gray-900);
    margin-bottom: 8px;
}

.page-subtitle {
    font-size: 16px;
    color: var(--gray-700);
}

/* 统计概览 */
.stats-overview {
    margin-bottom: 32px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.stat-card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    padding: 24px;
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.stat-card:hover {
    box-shadow: var(--shadow-2);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-right: 16px;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 28px;
    font-weight: 500;
    color: var(--gray-900);
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: var(--gray-700);
    margin-top: 4px;
}

/* 工具栏 */
.toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.view-toggle {
    display: flex;
    background-color: var(--gray-100);
    border-radius: 24px;
    padding: 4px;
}

.view-btn {
    background: none;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    color: var(--gray-800);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.view-btn i {
    margin-right: 8px;
}

.view-btn.active {
    background-color: var(--white);
    color: var(--gray-900);
    box-shadow: var(--shadow-1);
}

.filter-controls {
    display: flex;
    gap: 12px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 20px;
    background-color: var(--white);
    color: var(--gray-900);
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 24px;
    border-radius: 24px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    border: none;
}

.btn-icon {
    margin-right: 8px;
}

.btn-outline {
    background-color: var(--white);
    color: var(--gray-900);
    border: 1px solid var(--gray-300);
}

.btn-outline:hover {
    background-color: var(--gray-100);
}

.btn-primary {
    background-color: var(--primary);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-sm {
    padding: 6px 16px;
    font-size: 13px;
}

/* 章节概览 */
.chapter-overview {
    margin-bottom: 32px;
}

.section-title {
    font-size: 20px;
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.section-title-icon {
    color: var(--primary);
    margin-right: 8px;
}

.chapter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
}

.chapter-card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    overflow: hidden;
    transition: var(--transition);
}

.chapter-card:hover {
    box-shadow: var(--shadow-2);
}

.chapter-card-header {
    padding: 16px;
    display: flex;
    align-items: center;
}

.chapter-card-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-right: 16px;
}

.chapter-card-title {
    flex: 1;
}

.chapter-code {
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-700);
}

.chapter-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--gray-900);
    margin-top: 2px;
}

.chapter-card-body {
    padding: 16px;
    border-top: 1px solid var(--gray-200);
}

.chapter-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
}

.chapter-stat {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 18px;
    font-weight: 500;
    color: var(--gray-900);
}

.stat-label {
    font-size: 12px;
    color: var(--gray-700);
}

.chapter-actions {
    display: flex;
    justify-content: flex-end;
}

/* 指标部分 */
.indicator-section {
    margin-bottom: 32px;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.section-actions {
    display: flex;
    gap: 12px;
}

/* 卡片视图 */
.card-view {
    display: block;
}

.card-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.indicator-card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid var(--gray-200);
}

.indicator-card:hover {
    box-shadow: var(--shadow-2);
    border-color: var(--primary);
}

.card-container {
    width: 100%;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.card-id-section {
    display: flex;
    align-items: center;
    gap: 8px;
}

.indicator-id-badge {
    background-color: var(--primary);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.card-id {
    display: flex;
    align-items: center;
}

.id-badge {
    color: var(--primary);
    font-weight: 500;
    font-size: 14px;
    background-color: var(--primary-light);
    padding: 4px 8px;
    border-radius: 12px;
}

.card-actions {
    display: flex;
    gap: 8px;
}

.action-icon {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-700);
    cursor: pointer;
    transition: var(--transition);
    background: none;
    border: none;
}

.action-icon:hover {
    background-color: var(--gray-100);
    color: var(--gray-900);
}

.card-body {
    padding: 16px;
}

.card-title {
    font-size: 18px;
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: 12px;
    line-height: 1.3;
}

.card-description {
    color: var(--gray-700);
    font-size: 14px;
    margin-bottom: 16px;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.card-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: center;
}

.meta-item {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: var(--gray-600);
    background-color: var(--gray-100);
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.meta-item i {
    margin-right: 6px;
    font-size: 12px;
    color: var(--primary);
}

.card-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.stat-badge {
    display: inline-flex;
    align-items: center;
    font-size: 12px;
    color: var(--gray-700);
    background-color: var(--gray-100);
    padding: 4px 8px;
    border-radius: 12px;
}

.stat-badge i {
    margin-right: 4px;
}

/* 列表视图 */
.list-view {
    display: none;
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    overflow: hidden;
}

.list-container {
    overflow-x: auto;
}

.list-header {
    display: flex;
    align-items: center;
    padding: 16px;
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 500;
    color: var(--gray-700);
    font-size: 14px;
}

.list-col-id {
    width: 180px; /* 增加宽度以容纳展开按钮和缩进 */
    flex-shrink: 0;
}

.list-col-title {
    flex: 2;
    min-width: 200px;
}

.list-col-type {
    width: 120px;
    flex-shrink: 0;
    text-align: center;
}

.list-col-desc {
    flex: 3;
    min-width: 300px;
}

.list-col-tags {
    flex: 1;
    min-width: 150px;
}

.list-col-actions {
    width: 80px;
    text-align: right;
    flex-shrink: 0;
}

.list-item {
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--gray-200);
    transition: var(--transition);
    position: relative;
}

.list-item:last-child {
    border-bottom: none;
}

.list-item:hover {
    background-color: var(--gray-50);
}

/* 列表项容器，支持缩进和树形连接线 */
.list-item-container {
    display: flex;
    align-items: center;
    padding: 16px;
    width: 100%;
    position: relative;
}

/* 列表视图的ID部分，包含展开按钮 */
.list-id-section {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 列表视图的展开按钮 */
.expand-toggle-list {
    background: none;
    border: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--gray-600);
    font-size: 12px;
}

.expand-toggle-list:hover {
    background-color: var(--gray-100);
    color: var(--primary);
}

.expand-toggle-list.expanded {
    background-color: var(--primary-light);
    color: var(--primary);
}

.expand-toggle-list i {
    transition: transform 0.2s ease;
}

.expand-toggle-list.expanded i {
    transform: rotate(180deg);
}

.expand-spacer-list {
    width: 24px;
    height: 24px;
    display: inline-block;
}

.list-item-badge {
    color: var(--primary);
    font-size: 14px;
    font-weight: 500;
}

.list-item-title {
    font-weight: 500;
    color: var(--gray-900);
    font-size: 14px;
}

.list-item-desc {
    color: var(--gray-700);
    font-size: 14px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.tag {
    display: inline-block;
    background-color: var(--gray-100);
    color: var(--gray-700);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin-right: 4px;
    margin-bottom: 4px;
}

.children-tag {
    background-color: var(--primary-light);
    color: var(--primary);
}

/* 列表视图的子指标容器 */
.children-container-list {
    margin-top: 0;
    margin-bottom: 0;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.children-container-list .list-item {
    margin-bottom: 0;
    position: relative;
}

/* 列表视图的树形连接线 */
.children-container-list .list-item-container {
    position: relative;
}

.children-container-list .list-item-container::before {
    content: '';
    position: absolute;
    left: 14px;
    top: 0;
    bottom: 50%;
    width: 2px;
    background-color: var(--gray-300);
    z-index: 1;
}

.children-container-list .list-item-container::after {
    content: '';
    position: absolute;
    left: 14px;
    top: 50%;
    width: 20px;
    height: 2px;
    background-color: var(--gray-300);
    z-index: 1;
}

/* 最后一个子指标的特殊处理 */
.children-container-list .list-item:last-child .list-item-container::before {
    bottom: 50%;
}

/* 垂直连接线 */
.children-container-list::before {
    content: '';
    position: absolute;
    left: 14px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--gray-300);
    z-index: 0;
}

.children-container-list .list-item:last-child::after {
    content: '';
    position: absolute;
    left: 14px;
    top: 50%;
    bottom: 0;
    width: 2px;
    background-color: var(--gray-50);
    z-index: 2;
}

/* 列表视图的子指标样式 */
.children-container-list .list-item {
    background-color: var(--gray-50);
    border-left: 2px solid var(--gray-200);
}

.children-container-list .list-item:hover {
    background-color: var(--white);
    border-left-color: var(--primary);
    transform: translateX(2px);
}

/* 列表视图的层级样式 */
.list-item.level-0 {
    border-left: 4px solid var(--primary);
}

.list-item.level-1 {
    border-left: 3px solid var(--warning);
}

.list-item.level-2 {
    border-left: 2px solid var(--success);
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 24px;
}

.pagination-controls {
    display: flex;
    gap: 8px;
}

.pagination-btn {
    width: 36px;
    height: 36px;
    border: 1px solid var(--gray-300);
    background-color: var(--white);
    color: var(--gray-700);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.pagination-btn:hover {
    background-color: var(--gray-100);
}

.pagination-btn.active {
    background-color: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

/* 快速操作 */
.quick-actions {
    position: fixed;
    bottom: 24px;
    right: 24px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    z-index: 50;
}

.quick-action-btn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: var(--primary);
    color: var(--white);
    border: none;
    box-shadow: var(--shadow-2);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.quick-action-btn:hover {
    background-color: var(--primary-dark);
    box-shadow: var(--shadow-3);
}

/* 弹窗 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    overflow-y: auto;
    padding: 20px;
}

.modal-overlay.active {
    display: flex;
}

.modal-container {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-3);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    animation: modal-appear 0.3s ease;
}

@keyframes modal-appear {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px;
    border-bottom: 1px solid var(--gray-200);
}

.modal-title {
    font-size: 20px;
    font-weight: 500;
    color: var(--gray-900);
}

.modal-close {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: none;
    border: none;
    color: var(--gray-700);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background-color: var(--gray-100);
}

.modal-body {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
}

/* 加载状态 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.loading-overlay.active {
    display: flex;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: var(--primary);
}

.loading-spinner i {
    font-size: 24px;
}

/* 指标详情 */
.indicator-detail {
    max-width: 100%;
}

.indicator-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--gray-200);
}

.indicator-title {
    display: flex;
    align-items: flex-start;
    flex: 1;
}

.indicator-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-right: 16px;
    flex-shrink: 0;
}

.indicator-info h2 {
    font-size: 20px;
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: 8px;
    line-height: 1.3;
}

.indicator-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.meta-badge {
    background-color: var(--primary-light);
    color: var(--primary);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.meta-text {
    color: var(--gray-600);
    font-size: 14px;
}

.indicator-actions {
    flex-shrink: 0;
    margin-left: 16px;
}

.detail-section {
    margin-bottom: 24px;
}

.detail-section .section-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.detail-section .section-title-icon {
    color: var(--primary);
    margin-right: 8px;
    font-size: 14px;
}

.section-content {
    color: var(--gray-700);
    line-height: 1.6;
}

.parent-indicator,
.child-indicator {
    background-color: var(--gray-50);
    border-radius: var(--border-radius);
    padding: 12px;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 8px;
}

.parent-indicator:hover,
.child-indicator:hover {
    background-color: var(--gray-100);
}

.parent-id,
.child-id {
    color: var(--primary);
    font-weight: 500;
    font-size: 14px;
}

.parent-name,
.child-name {
    color: var(--gray-900);
    margin-left: 8px;
}

.children-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 8px;
}

.components-grid {
    display: grid;
    gap: 16px;
}

.component-group {
    background-color: var(--gray-50);
    border-radius: var(--border-radius);
    padding: 16px;
}

.component-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--gray-200);
}

.component-item {
    background-color: var(--white);
    border-radius: var(--border-radius);
    padding: 12px;
    margin-bottom: 8px;
    border-left: 3px solid var(--primary);
}

.component-item:last-child {
    margin-bottom: 0;
}

.component-name {
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: 8px;
}

.component-details {
    font-size: 14px;
}

.component-detail {
    margin-bottom: 4px;
    color: var(--gray-700);
}

.component-detail:last-child {
    margin-bottom: 0;
}

.component-detail strong {
    color: var(--gray-900);
    font-weight: 500;
}

.search-no-results {
    padding: 16px;
    text-align: center;
    color: var(--gray-600);
    font-size: 14px;
}

/* 通知 */
.notification {
    position: fixed;
    top: 80px;
    right: 24px;
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-2);
    padding: 16px;
    z-index: 1500;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.notification.active {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-icon {
    font-size: 18px;
}

.notification.success .notification-icon {
    color: var(--secondary);
}

.notification.error .notification-icon {
    color: var(--error);
}

.notification.warning .notification-icon {
    color: var(--warning);
}

.notification-message {
    font-size: 14px;
    color: var(--gray-900);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        position: fixed;
        top: 65px;
        left: 0;
        bottom: 0;
        z-index: 200;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .content {
        padding: 16px;
    }

    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }

    .toolbar-left,
    .toolbar-right {
        justify-content: center;
    }

    .chapter-grid,
    .card-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .search-wrapper {
        max-width: none;
        flex: 1;
    }

    .logo {
        font-size: 16px;
        margin-right: 12px;
    }

    .page-title {
        font-size: 24px;
    }

    .quick-actions {
        bottom: 16px;
        right: 16px;
    }

    .modal-container {
        width: 95%;
        margin: 10px;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 8px 12px;
    }

    .content {
        padding: 12px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: 16px;
    }

    .page-title {
        font-size: 20px;
    }

    .section-title {
        font-size: 18px;
    }
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

.modal-overlay.show {
    display: flex;
}

.modal-container {
    background: white;
    border-radius: 12px;
    max-width: 1200px;
    width: 95%;
    max-height: 95vh;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transform: scale(0.9);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
}

.modal-overlay.show .modal-container {
    transform: scale(1);
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--white) 100%);
    position: sticky;
    top: 0;
    z-index: 10;
    flex-shrink: 0;
}

.modal-header-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.modal-indicator-badge {
    background: var(--primary);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
    min-width: 60px;
    text-align: center;
}

.modal-header-text {
    flex: 1;
}

.modal-title {
    margin: 0 0 4px 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--gray-900);
}

.modal-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--gray-600);
}

.modal-breadcrumb i {
    font-size: 12px;
}

.modal-header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-action-btn {
    background: none;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--gray-600);
    transition: var(--transition);
}

.modal-action-btn:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.modal-close {
    background: none;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--gray-600);
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.modal-body {
    padding: 24px;
    overflow-y: auto;
    background: var(--gray-50);
    flex: 1;
    min-height: 0;
}

/* 固定的指标头部 */
.indicator-header-sticky {
    position: sticky;
    top: 0;
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-200);
    z-index: 9;
    padding: 20px 24px;
    margin: -24px -24px 0 -24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 指标详情容器 */
.indicator-detail {
    position: relative;
}

/* 可滚动的内容区域 */
.indicator-content-scrollable {
    padding: 24px 0 0 0;
}

/* 指标头部样式 */
.indicator-title {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.indicator-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.indicator-info {
    flex: 1;
}

.indicator-info h2 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--gray-900);
    line-height: 1.3;
}

.indicator-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.meta-badge {
    background-color: var(--primary-light);
    color: var(--primary);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.meta-text {
    color: var(--gray-600);
    font-size: 14px;
}

.indicator-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 信息卡片样式 */
.info-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    margin-bottom: 20px;
    overflow: hidden;
    transition: var(--transition);
}

.info-card:hover {
    box-shadow: var(--shadow-2);
}

.info-card-header {
    padding: 16px 20px;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-card-title i {
    color: var(--primary);
    font-size: 14px;
}

.info-card-content {
    padding: 20px;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-item.full-width {
    grid-column: 1 / -1;
}

.info-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 6px;
}

.info-value {
    font-size: 15px;
    color: var(--gray-900);
    line-height: 1.4;
    min-height: 20px;
}

.info-value:empty::after {
    content: '-';
    color: var(--gray-400);
    font-style: italic;
}

/* 子指标导航卡片 */
.children-nav-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    margin-bottom: 20px;
    overflow: hidden;
}

.children-count {
    background: var(--primary);
    color: var(--white);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 8px;
}

.children-nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 12px;
}

.child-nav-item {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.child-nav-item:hover {
    background: var(--primary-light);
    border-color: var(--primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-1);
}

.child-nav-info {
    flex: 1;
}

.child-nav-id {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary);
    margin-bottom: 4px;
}

.child-nav-name {
    font-size: 15px;
    color: var(--gray-900);
    line-height: 1.3;
}

.child-nav-arrow {
    color: var(--gray-400);
    font-size: 16px;
    transition: var(--transition);
}

.child-nav-item:hover .child-nav-arrow {
    color: var(--primary);
    transform: translateX(4px);
}

/* 添加组件按钮 */
.add-component-btn {
    background: var(--primary);
    color: var(--white);
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: var(--transition);
}

.add-component-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-1);
}

/* 模态框内的详情样式 */
.detail-header-modal {
    padding: 24px;
    border-bottom: 1px solid #e0e0e0;
}

.detail-title-modal {
    font-size: 24px;
    font-weight: 600;
    color: #1a73e8;
    margin-bottom: 8px;
}

.detail-subtitle-modal {
    color: #5f6368;
    font-size: 16px;
    margin-bottom: 16px;
    line-height: 1.5;
}

.detail-meta-modal {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.detail-meta-modal .meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #5f6368;
    font-size: 14px;
}

.components-section-modal {
    padding: 24px;
}

.section-header-modal {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-title-modal {
    font-size: 18px;
    font-weight: 600;
    color: #202124;
    margin: 0;
}

.add-component-btn-modal {
    background: #1a73e8;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
}

.add-component-btn-modal:hover {
    background: #1557b0;
}

/* 模态框内的组件卡片样式 */
.modal-component-card {
    border: 1px solid #dadce0;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    position: relative;
}

.modal-component-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.modal-component-type {
    background: #e8f0fe;
    color: #1a73e8;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
}

.modal-component-type.denominator {
    background: #fce8e6;
    color: #d93025;
}

.modal-component-actions {
    display: flex;
    gap: 8px;
}

.modal-edit-btn, .modal-delete-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    color: #5f6368;
    transition: all 0.2s;
}

.modal-edit-btn:hover {
    background: #f1f3f4;
    color: #1a73e8;
}

.modal-delete-btn:hover {
    background: #fce8e6;
    color: #d93025;
}

.modal-component-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.modal-field-group {
    margin-bottom: 12px;
}

.modal-field-label {
    font-weight: 500;
    color: #202124;
    margin-bottom: 4px;
    font-size: 14px;
}

.modal-field-value {
    color: #5f6368;
    font-size: 14px;
    line-height: 1.4;
}

.modal-field-value.empty {
    color: #9aa0a6;
    font-style: italic;
}

/* 模态框内的编辑表单样式 */
.modal-edit-form {
    display: none;
    grid-template-columns: 1fr;
    gap: 16px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #dadce0;
}

.modal-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.modal-form-group {
    margin-bottom: 16px;
}

.modal-form-label {
    display: block;
    font-weight: 500;
    color: #202124;
    margin-bottom: 4px;
    font-size: 14px;
}

.modal-form-input, .modal-form-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dadce0;
    border-radius: 4px;
    font-size: 14px;
    font-family: inherit;
    transition: border-color 0.2s;
    box-sizing: border-box;
}

.modal-form-input:focus, .modal-form-textarea:focus {
    outline: none;
    border-color: #1a73e8;
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.modal-form-textarea {
    resize: vertical;
    min-height: 80px;
}

.modal-form-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.modal-save-btn, .modal-cancel-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.modal-save-btn {
    background: #1a73e8;
    color: white;
}

.modal-save-btn:hover {
    background: #1557b0;
}

.modal-cancel-btn {
    background: #f8f9fa;
    color: #5f6368;
    border: 1px solid #dadce0;
}

.modal-cancel-btn:hover {
    background: #f1f3f4;
}

/* 模态框内的提示信息 */
.modal-alert {
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 16px;
    font-size: 14px;
}

.modal-alert-success {
    background: #e6f4ea;
    color: #137333;
    border: 1px solid #34a853;
}

.modal-alert-error {
    background: #fce8e6;
    color: #d93025;
    border: 1px solid #ea4335;
}

/* 模态框内的信息卡片样式 */
.info-card {
    background: white;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.info-card-header {
    padding: 16px 20px;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-card-title i {
    color: var(--primary);
    font-size: 18px;
}

.info-card-content {
    padding: 20px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 4px;
}

.info-value {
    font-size: 14px;
    color: var(--gray-900);
    line-height: 1.4;
}

.info-value.empty {
    color: var(--gray-500);
    font-style: italic;
}

.indicator-id {
    font-family: 'Roboto Mono', monospace;
    font-weight: 600;
    color: var(--primary);
}

/* 子指标导航卡片 */
.children-nav-card {
    background: white;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.children-count {
    background: var(--primary);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    margin-left: 8px;
}

.children-nav-grid {
    display: grid;
    gap: 8px;
}

.child-nav-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: var(--gray-50);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.child-nav-item:hover {
    background: var(--primary-light);
    color: var(--primary);
}

.child-nav-info {
    flex: 1;
}

.child-nav-id {
    font-family: 'Roboto Mono', monospace;
    font-weight: 600;
    font-size: 12px;
    color: var(--primary);
    margin-bottom: 2px;
}

.child-nav-name {
    font-size: 14px;
    color: var(--gray-900);
    font-weight: 500;
}

.child-nav-arrow {
    color: var(--gray-500);
    font-size: 12px;
}

/* 指标类型徽章样式 */
.indicator-type-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    line-height: 1;
    white-space: nowrap;
    min-width: 60px;
}

.indicator-type-badge.simple-indicator {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #4caf50;
}

.indicator-type-badge.composite-indicator {
    background: #e3f2fd;
    color: #1565c0;
    border: 1px solid #2196f3;
}

/* 在卡片视图中显示指标类型 */
.indicator-card .indicator-type-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    z-index: 2;
}

/* 在列表视图中显示指标类型 */
.list-item .indicator-type-badge {
    margin-left: 8px;
}

/* 列表视图指标类型列的对齐 */
.list-col-type {
    display: flex;
    align-items: center;
    justify-content: center;
}

.list-col-type .indicator-type-badge {
    margin-left: 0;
    margin-right: 0;
}

/* 树状导航样式 */
.nav-tree {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-tree-item {
    margin: 0;
}

.nav-tree-toggle {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 8px 12px;
    background: none;
    border: none;
    text-align: left;
    cursor: pointer;
    color: #5f6368;
    font-size: 14px;
    transition: all 0.2s;
    border-radius: 4px;
    margin-bottom: 2px;
}

.nav-tree-toggle:hover {
    background: #f1f3f4;
    color: #202124;
}

.nav-tree-toggle.active {
    background: #e8f0fe;
    color: #1a73e8;
    font-weight: 500;
}

.nav-tree-icon {
    margin-right: 8px;
    font-size: 12px;
    transition: transform 0.2s;
    width: 12px;
    text-align: center;
}

.nav-tree-toggle.expanded .nav-tree-icon {
    transform: rotate(90deg);
}

.nav-tree-children {
    padding-left: 20px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.nav-tree-children.expanded {
    max-height: 1000px;
}

.nav-tree-child {
    display: block;
    padding: 6px 12px;
    color: #5f6368;
    text-decoration: none;
    font-size: 13px;
    border-radius: 4px;
    margin-bottom: 1px;
    transition: all 0.2s;
}

.nav-tree-child:hover {
    background: #f1f3f4;
    color: #202124;
    text-decoration: none;
}

.nav-tree-child.active {
    background: #e8f0fe;
    color: #1a73e8;
    font-weight: 500;
}

/* 层级指标卡片样式 */
.indicator-card.level-0 {
    border-left: 4px solid #1a73e8;
}

.indicator-card.level-1 {
    border-left: 4px solid #34a853;
    background: #f8f9fa;
}

.indicator-card.level-2 {
    border-left: 4px solid #fbbc04;
    background: #fefefe;
}

.expand-toggle {
    background: none;
    border: none;
    padding: 6px;
    cursor: pointer;
    color: var(--gray-600);
    transition: var(--transition);
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.expand-toggle:hover {
    background-color: var(--gray-100);
    color: var(--primary);
}

.expand-toggle i {
    font-size: 14px;
    transition: transform 0.2s;
}

.expand-toggle.expanded i {
    transform: rotate(180deg);
}

.expand-spacer {
    width: 28px;
    display: inline-block;
}

.card-actions {
    display: flex;
    gap: 4px;
}

.action-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
    background: none;
    border: none;
    font-size: 14px;
}

.action-icon:hover {
    background-color: var(--gray-100);
    color: var(--primary);
}

.children-container {
    margin-top: 8px;
    transition: all 0.3s ease;
}

.children-container .indicator-card {
    margin-bottom: 8px;
}

/* 子指标卡片样式优化 */
.child-indicator-card {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    margin: 8px 0;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.child-indicator-card:hover {
    background: #e8f0fe;
    border-color: #1a73e8;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.1);
}

.child-indicator-info {
    flex: 1;
}

.child-indicator-id {
    font-size: 14px;
    font-weight: 600;
    color: #1a73e8;
    margin-bottom: 4px;
}

.child-indicator-name {
    font-size: 16px;
    color: #202124;
    margin-bottom: 4px;
}

.child-indicator-desc {
    font-size: 14px;
    color: #5f6368;
    line-height: 1.4;
}

.child-indicator-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.child-stat-badge {
    background: #e8f0fe;
    color: #1a73e8;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* 加载和错误状态样式 */
.loading-indicator {
    text-align: center;
    padding: 16px;
    color: #5f6368;
    font-style: italic;
}

.loading-indicator::before {
    content: "⏳ ";
    margin-right: 8px;
}

.no-children {
    text-align: center;
    padding: 16px;
    color: #9aa0a6;
    font-style: italic;
    font-size: 14px;
}

.error-message {
    text-align: center;
    padding: 16px;
    color: #d93025;
    font-style: italic;
    font-size: 14px;
}

.error-message::before {
    content: "⚠️ ";
    margin-right: 8px;
}

/* 展开按钮动画优化 */
.expand-toggle i {
    transition: transform 0.3s ease;
}

/* 子指标容器动画 */
.children-container {
    overflow: hidden;
    transition: all 0.3s ease;
}

/* 层级指标的视觉增强 */
.indicator-card.level-0 {
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.1);
    border-left: 4px solid var(--primary);
}

.indicator-card.level-1 {
    box-shadow: 0 1px 4px rgba(26, 115, 232, 0.08);
    border-left: 3px solid var(--secondary);
}

.indicator-card.level-2 {
    box-shadow: 0 1px 3px rgba(26, 115, 232, 0.06);
    border-left: 2px solid var(--warning);
}

/* 子指标容器样式优化 */
.children-container {
    margin-top: 8px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.children-container .indicator-card {
    margin-bottom: 8px;
    position: relative;
}

.children-container .indicator-card:last-child {
    margin-bottom: 0;
}

/* 树形连接线样式 */
.tree-line {
    position: absolute;
    border-left: 2px solid var(--gray-300);
    border-bottom: 2px solid var(--gray-300);
    left: 14px;
    top: -8px;
    width: 20px;
    height: calc(50% + 8px);
    z-index: 1;
}

.tree-line-vertical {
    position: absolute;
    border-left: 2px solid var(--gray-300);
    left: 14px;
    top: -8px;
    bottom: 0;
    z-index: 1;
}

/* 父指标的连接线容器 */
.parent-connector {
    position: relative;
}

.parent-connector::after {
    content: '';
    position: absolute;
    left: 14px;
    top: 100%;
    width: 2px;
    height: 8px;
    background-color: var(--gray-300);
    z-index: 1;
}

/* 展开状态下显示连接线 */
.parent-connector.expanded::after {
    display: block;
}

/* 子指标的额外缩进和连接线 */
.children-container .card-container {
    position: relative;
    padding-left: 40px; /* 增加缩进到40px */
}

.children-container .card-container::before {
    content: '';
    position: absolute;
    left: 14px;
    top: 0;
    bottom: 50%;
    width: 2px;
    background-color: var(--gray-300);
    z-index: 1;
}

.children-container .card-container::after {
    content: '';
    position: absolute;
    left: 14px;
    top: 50%;
    width: 20px; /* 增加水平连接线长度 */
    height: 2px;
    background-color: var(--gray-300);
    z-index: 1;
}

/* 最后一个子指标的特殊处理 */
.children-container .indicator-card:last-child .card-container::before {
    bottom: 50%;
}

/* 垂直连接线 */
.children-container::before {
    content: '';
    position: absolute;
    left: 14px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--gray-300);
    z-index: 0;
}

.children-container .indicator-card:last-child::after {
    content: '';
    position: absolute;
    left: 14px;
    top: 50%;
    bottom: 0;
    width: 2px;
    background-color: var(--gray-50);
    z-index: 2;
}

/* 树形连接线的颜色和样式优化 */
.children-container::before,
.children-container .card-container::before,
.children-container .card-container::after,
.parent-connector::after {
    border-radius: 1px;
}

/* 悬停效果 */
.indicator-card:hover .children-container::before,
.indicator-card:hover .card-container::before,
.indicator-card:hover .card-container::after {
    background-color: var(--primary);
}

/* 展开状态的父指标连接线颜色 */
.parent-connector.expanded::after {
    background-color: var(--primary);
}

/* 子指标卡片的特殊样式 */
.children-container .indicator-card {
    background-color: var(--gray-50);
    border: 1px solid var(--gray-200);
}

.children-container .indicator-card:hover {
    background-color: var(--white);
    border-color: var(--primary);
    transform: translateX(2px);
}

/* 卡片标题样式优化 */
.card-title {
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

/* 元数据项样式优化 */
.meta-item {
    white-space: nowrap;
}

.meta-item:hover {
    background-color: var(--gray-200);
    transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-grid {
        gap: 8px;
    }

    .card-header {
        padding: 8px 12px;
    }

    .card-body {
        padding: 12px;
    }

    .card-meta {
        gap: 8px;
    }

    .meta-item {
        font-size: 12px;
        padding: 3px 6px;
    }
}





/* 组件编辑样式 */
.component-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.component-name {
    font-weight: 500;
    color: var(--gray-900);
    font-size: 16px;
}

.component-actions {
    display: flex;
    gap: 8px;
}

.edit-btn, .delete-btn {
    background: none;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.edit-btn {
    color: var(--primary);
}

.edit-btn:hover {
    background-color: var(--primary-light);
}

.delete-btn {
    color: var(--error);
}

.delete-btn:hover {
    background-color: #fce8e6;
}

.component-detail {
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.4;
}

.component-detail strong {
    color: var(--gray-800);
    margin-right: 8px;
}

.empty-value {
    color: var(--gray-500);
    font-style: italic;
}

/* 编辑表单样式 */
.edit-form {
    background-color: var(--gray-50);
    border-radius: var(--border-radius);
    padding: 16px;
    margin-top: 12px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-800);
    margin-bottom: 6px;
}

.form-group input,
.form-group textarea {
    padding: 10px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    font-size: 14px;
    color: var(--gray-900);
    background-color: var(--white);
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 16px;
}

.save-btn, .cancel-btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
}

.save-btn {
    background-color: var(--primary);
    color: var(--white);
}

.save-btn:hover {
    background-color: var(--primary-dark);
}

.cancel-btn {
    background-color: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.cancel-btn:hover {
    background-color: var(--gray-100);
}

.edit-form.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 新的指标层级样式（参考您提供的设计） */
.indicator-item {
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    margin-bottom: 2px;
    transition: var(--transition);
}

.indicator-item:hover {
    box-shadow: var(--shadow-1);
    border-color: var(--gray-300);
}

.indicator-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    min-height: 80px;
}

.indicator-left {
    display: flex;
    align-items: flex-start;
    flex: 1;
    gap: 16px;
}

.indicator-id-section {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 120px;
}

.expand-toggle-btn {
    background: none;
    border: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--gray-600);
    transition: var(--transition);
}

.expand-toggle-btn:hover {
    background-color: var(--gray-100);
    color: var(--primary);
}

.expand-toggle-btn i {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.expand-spacer {
    width: 24px;
    height: 24px;
}

.indicator-id {
    font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary);
    background-color: var(--primary-light);
    padding: 4px 8px;
    border-radius: 4px;
    min-width: 80px;
    text-align: center;
}

.indicator-content {
    flex: 1;
}

.indicator-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--gray-900);
    line-height: 1.4;
    margin-bottom: 6px;
}

.indicator-description {
    font-size: 14px;
    color: var(--gray-600);
    line-height: 1.4;
}

.indicator-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.indicator-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tag-badge {
    background-color: var(--gray-100);
    color: var(--gray-700);
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    white-space: nowrap;
}

.tag-badge.children-count {
    background-color: var(--primary-light);
    color: var(--primary);
}

.tag-badge.component-count {
    background-color: #e8f5e8;
    color: var(--secondary);
}

.indicator-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.action-btn.view-btn {
    color: var(--primary);
}

.action-btn.view-btn:hover {
    background-color: var(--primary-light);
}

.action-btn.edit-btn {
    color: var(--gray-600);
}

.action-btn.edit-btn:hover {
    background-color: var(--gray-100);
    color: var(--gray-900);
}

/* 子指标容器 */
.children-container {
    border-left: 2px solid var(--gray-200);
    margin-left: 20px;
    transition: all 0.3s ease;
}

/* 层级指标的视觉区分 */
.indicator-item.level-0 {
    border-left: 4px solid var(--primary);
}

.indicator-item.level-1 {
    border-left: 4px solid var(--secondary);
    background-color: #fafbfc;
}

.indicator-item.level-2 {
    border-left: 4px solid var(--warning);
    background-color: #fffef7;
}

/* 底层指标标识 */
.meta-badge.bottom-level {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* 底层指标数值显示 */
.indicator-values {
    margin-top: 16px;
}

.value-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.value-item {
    background-color: var(--gray-50);
    border-radius: var(--border-radius);
    padding: 16px;
    border: 1px solid var(--gray-200);
}

.value-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.value-content {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-900);
    line-height: 1.2;
}

.value-content.empty-value {
    color: var(--gray-500);
    font-style: italic;
    font-weight: 400;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .indicator-row {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        padding: 12px 16px;
    }

    .indicator-left {
        flex-direction: column;
        gap: 8px;
    }

    .indicator-id-section {
        min-width: auto;
    }

    .indicator-right {
        justify-content: space-between;
    }

    .indicator-tags {
        flex: 1;
    }
}


/* 暂时隐藏参考范围相关功能 */
.reference-range-section,
.reference-range-card,
.reference-range-button,
[id*="reference-range"],
[class*="reference-range"],
[href*="reference-range"],
.modal-reference-range,
#modalReferenceRangeCard {
    display: none !important;
}

/* 隐藏参考范围相关的表格列 */
th:has-text("参考范围"),
td:has-text("参考范围") {
    display: none !important;
}

/* 隐藏参考范围按钮 */
button[onclick*="reference"],
a[href*="reference-range"] {
    display: none !important;
}
