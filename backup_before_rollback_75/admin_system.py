#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院指标后端管理系统 - 完整版
与前端UI风格统一的管理系统
"""

from flask import Flask, render_template, jsonify, request, redirect, url_for, flash
import sqlite3
import os
from datetime import datetime

app = Flask(__name__)
app.config['SECRET_KEY'] = 'hospital_admin_system_2024'

# 数据库路径
DB_PATH = 'DATABASE-HOSPITAL/hospital_indicator_system.db'

def get_db_connection():
    """获取数据库连接"""
    if not os.path.exists(DB_PATH):
        raise Exception(f"数据库文件不存在: {DB_PATH}")
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def dict_from_row(row):
    """将sqlite3.Row转换为字典"""
    return dict(zip(row.keys(), row)) if row else None

@app.route('/')
def admin_home():
    """管理系统主页"""
    return render_template('admin/dashboard.html')

@app.route('/api/admin/statistics')
def get_admin_statistics():
    """获取管理系统统计数据"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取各种统计数据
        cursor.execute("SELECT COUNT(*) FROM chapters WHERE is_active = 1")
        chapters_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM sections WHERE is_active = 1")
        sections_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM indicators WHERE is_active = 1")
        indicators_count = cursor.fetchone()[0]

        # 检查组件表是否存在
        try:
            cursor.execute("SELECT COUNT(*) FROM indicator_components WHERE is_active = 1")
            components_count = cursor.fetchone()[0]
        except:
            components_count = 0

        # 获取最近更新的指标
        cursor.execute("""
            SELECT i.id, i.name, i.updated_at, c.name as chapter_name
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            WHERE i.is_active = 1
            ORDER BY i.updated_at DESC
            LIMIT 5
        """)
        recent_indicators = [dict_from_row(row) for row in cursor.fetchall()]

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'chapters_count': chapters_count,
                'sections_count': sections_count,
                'indicators_count': indicators_count,
                'components_count': components_count,
                'recent_indicators': recent_indicators
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/indicators')
def admin_indicators():
    """指标管理页面"""
    return render_template('admin/indicators.html')

@app.route('/api/admin/indicators')
def get_admin_indicators():
    """获取指标列表API"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        search = request.args.get('search', '')
        chapter_id = request.args.get('chapter_id', '')

        conn = get_db_connection()
        cursor = conn.cursor()

        # 构建查询条件
        where_conditions = ["i.is_active = 1"]
        params = []

        if search:
            where_conditions.append("(i.name LIKE ? OR i.id LIKE ?)")
            params.extend([f"%{search}%", f"%{search}%"])

        if chapter_id:
            where_conditions.append("i.chapter_id = ?")
            params.append(chapter_id)

        where_clause = " AND ".join(where_conditions)

        # 获取总数
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM indicators i
            WHERE {where_clause}
        """, params)
        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT i.*, c.name as chapter_name, s.name as section_name,
                   COUNT(child.id) as children_count
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN sections s ON i.section_id = s.id
            LEFT JOIN indicators child ON child.parent_id = i.id AND child.is_active = 1
            WHERE {where_clause}
            GROUP BY i.id
            ORDER BY i.id
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])

        indicators = [dict_from_row(row) for row in cursor.fetchall()]

        # 获取章节列表用于筛选
        cursor.execute("SELECT * FROM chapters WHERE is_active = 1 ORDER BY code")
        chapters = [dict_from_row(row) for row in cursor.fetchall()]

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'indicators': indicators,
                'chapters': chapters,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/components')
def admin_components():
    """组件管理页面"""
    return render_template('admin/components.html')

@app.route('/api/admin/components')
def get_admin_components():
    """获取组件列表API"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        search = request.args.get('search', '')
        indicator_id = request.args.get('indicator_id', '')
        component_type = request.args.get('component_type', '')

        conn = get_db_connection()
        cursor = conn.cursor()

        # 确保组件表存在
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS indicator_components (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                indicator_id VARCHAR(20) NOT NULL,
                component_type VARCHAR(20) NOT NULL CHECK (component_type IN ('numerator', 'denominator', 'other')),
                name VARCHAR(200) NOT NULL,
                definition TEXT,
                unit VARCHAR(50),
                data_source VARCHAR(200),
                lead_department VARCHAR(100),
                logic_definition TEXT,
                collection_method VARCHAR(50),
                calculation_formula TEXT,
                notes TEXT,
                sort_order INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 构建查询条件
        where_conditions = ["ic.is_active = 1"]
        params = []

        if search:
            where_conditions.append("(ic.name LIKE ? OR ic.indicator_id LIKE ?)")
            params.extend([f"%{search}%", f"%{search}%"])

        if indicator_id:
            where_conditions.append("ic.indicator_id = ?")
            params.append(indicator_id)

        if component_type:
            where_conditions.append("ic.component_type = ?")
            params.append(component_type)

        where_clause = " AND ".join(where_conditions)

        # 获取总数
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM indicator_components ic
            WHERE {where_clause}
        """, params)
        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT ic.*, i.name as indicator_name
            FROM indicator_components ic
            LEFT JOIN indicators i ON ic.indicator_id = i.id
            WHERE {where_clause}
            ORDER BY ic.indicator_id, ic.component_type, ic.sort_order
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])

        components = [dict_from_row(row) for row in cursor.fetchall()]

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'components': components,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/components/<int:component_id>')
def get_component(component_id):
    """获取单个组件信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT ic.*, i.name as indicator_name
            FROM indicator_components ic
            LEFT JOIN indicators i ON ic.indicator_id = i.id
            WHERE ic.id = ? AND ic.is_active = 1
        """, (component_id,))

        component = dict_from_row(cursor.fetchone())
        conn.close()

        if component:
            return jsonify({'success': True, 'data': component})
        else:
            return jsonify({'success': False, 'error': '组件不存在'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/components/<int:component_id>', methods=['PUT'])
def update_component(component_id):
    """更新组件信息"""
    try:
        data = request.get_json()

        conn = get_db_connection()
        cursor = conn.cursor()

        # 更新组件信息
        cursor.execute("""
            UPDATE indicator_components
            SET name = ?, definition = ?, unit = ?, data_source = ?,
                lead_department = ?, logic_definition = ?, notes = ?,
                collection_method = ?, calculation_formula = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """, (
            data.get('name'),
            data.get('definition'),
            data.get('unit'),
            data.get('data_source'),
            data.get('lead_department'),
            data.get('logic_definition'),
            data.get('notes'),
            data.get('collection_method'),
            data.get('calculation_formula'),
            component_id
        ))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '组件更新成功'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/components', methods=['POST'])
def create_component():
    """创建新组件"""
    try:
        data = request.get_json()

        conn = get_db_connection()
        cursor = conn.cursor()

        # 插入新组件
        cursor.execute("""
            INSERT INTO indicator_components
            (indicator_id, component_type, name, definition, unit, data_source,
             lead_department, logic_definition, notes, collection_method,
             calculation_formula, sort_order, is_active, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """, (
            data.get('indicator_id'),
            data.get('component_type'),
            data.get('name'),
            data.get('definition'),
            data.get('unit'),
            data.get('data_source'),
            data.get('lead_department'),
            data.get('logic_definition'),
            data.get('notes'),
            data.get('collection_method'),
            data.get('calculation_formula')
        ))

        component_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '组件创建成功', 'component_id': component_id})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/components/<int:component_id>', methods=['DELETE'])
def delete_component(component_id):
    """删除组件"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 软删除组件
        cursor.execute("""
            UPDATE indicator_components
            SET is_active = 0, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """, (component_id,))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '组件删除成功'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("🔧 医院指标后端管理系统 - 完整版")
    print("📍 访问地址: http://localhost:5004")
    print("🔗 前端系统: http://localhost:5001")
    print("🎯 完整的管理功能和统一的UI风格")
    print("=" * 50)

    app.run(debug=True, host='0.0.0.0', port=5004)
