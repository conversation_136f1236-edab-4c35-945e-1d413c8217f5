#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示完整的数据库状态报告
"""

import sqlite3

def show_complete_database_status():
    """
    显示完整的数据库状态
    """
    db_path = "hospital_indicator_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=" * 100)
        print("🏥 医院等级评审指标管理系统 - 完整数据库状态报告")
        print("=" * 100)
        
        # 1. 章节总览
        cursor.execute("""
            SELECT c.code, c.name, c.icon, c.color,
                   COUNT(DISTINCT s.id) as section_count,
                   COUNT(DISTINCT i.id) as indicator_count,
                   COUNT(DISTINCT ic.id) as component_count
            FROM chapters c
            LEFT JOIN sections s ON c.id = s.chapter_id
            LEFT JOIN indicators i ON c.id = i.chapter_id
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id
            GROUP BY c.id, c.code, c.name, c.icon, c.color
            ORDER BY c.code
        """)
        
        chapters = cursor.fetchall()
        
        print("📚 章节总览:")
        print("-" * 80)
        total_sections = 0
        total_indicators = 0
        total_components = 0
        
        for code, name, icon, color, sections_count, indicators_count, components_count in chapters:
            print(f"第{code}章: {name}")
            print(f"  📖 小节: {sections_count}个")
            print(f"  📊 指标: {indicators_count}个")
            print(f"  🧮 分子分母: {components_count}个")
            print(f"  🎨 图标: {icon} | 颜色: {color}")
            print()
            
            total_sections += sections_count
            total_indicators += indicators_count
            total_components += components_count
        
        print(f"📈 总计:")
        print(f"  📖 总小节数: {total_sections}")
        print(f"  📊 总指标数: {total_indicators}")
        print(f"  🧮 总分子分母: {total_components}")
        
        # 2. 各章节详细统计
        print(f"\n" + "=" * 100)
        print("📋 各章节详细统计")
        print("=" * 100)
        
        for chapter_code in ['1', '2', '3', '5']:
            cursor.execute("""
                SELECT c.name
                FROM chapters c
                WHERE c.code = ?
            """, (chapter_code,))
            
            chapter_name = cursor.fetchone()
            if not chapter_name:
                continue
                
            print(f"\n📖 第{chapter_code}章: {chapter_name[0]}")
            print("-" * 60)
            
            # 小节统计
            cursor.execute("""
                SELECT s.code, s.name, COUNT(i.id) as indicator_count
                FROM sections s
                LEFT JOIN indicators i ON s.id = i.section_id
                WHERE s.chapter_id = (SELECT id FROM chapters WHERE code = ?)
                GROUP BY s.id, s.code, s.name
                ORDER BY s.sort_order
            """, (chapter_code,))
            
            sections_data = cursor.fetchall()
            
            print(f"小节详情:")
            for section_code, section_name, indicator_count in sections_data:
                print(f"  {section_code} - {section_name}: {indicator_count}个指标")
            
            # 指标层级统计
            cursor.execute("""
                SELECT 
                    CASE 
                        WHEN parent_id IS NULL THEN '一级指标'
                        WHEN id IN (SELECT DISTINCT parent_id FROM indicators WHERE parent_id IS NOT NULL AND id LIKE ?) THEN '二级指标'
                        ELSE '三级指标'
                    END as level_type,
                    COUNT(*) as count
                FROM indicators 
                WHERE id LIKE ?
                GROUP BY level_type
            """, (f"{chapter_code}.%", f"{chapter_code}.%"))
            
            level_stats = cursor.fetchall()
            
            print(f"指标层级:")
            for level_type, count in level_stats:
                print(f"  {level_type}: {count}个")
            
            # 分子分母统计
            cursor.execute("""
                SELECT 
                    component_type,
                    COUNT(*) as total,
                    SUM(CASE WHEN unit != '' THEN 1 ELSE 0 END) as has_unit,
                    SUM(CASE WHEN lead_department != '' THEN 1 ELSE 0 END) as has_dept,
                    SUM(CASE WHEN data_source != '' THEN 1 ELSE 0 END) as has_source
                FROM indicator_components 
                WHERE indicator_id LIKE ?
                GROUP BY component_type
            """, (f"{chapter_code}.%",))
            
            component_stats = cursor.fetchall()
            
            print(f"分子分母完整性:")
            for comp_type, total, has_unit, has_dept, has_source in component_stats:
                type_name = "分子" if comp_type == 'numerator' else "分母"
                unit_pct = (has_unit / total * 100) if total > 0 else 0
                dept_pct = (has_dept / total * 100) if total > 0 else 0
                source_pct = (has_source / total * 100) if total > 0 else 0
                
                print(f"  {type_name}: {total}个")
                print(f"    单位完整性: {has_unit}/{total} ({unit_pct:.1f}%)")
                print(f"    科室完整性: {has_dept}/{total} ({dept_pct:.1f}%)")
                print(f"    来源完整性: {has_source}/{total} ({source_pct:.1f}%)")
        
        # 3. 数据库整体健康状况
        print(f"\n" + "=" * 100)
        print("🔍 数据库整体健康状况")
        print("=" * 100)
        
        # 检查孤立指标
        cursor.execute("""
            SELECT COUNT(*) FROM indicators 
            WHERE section_id IS NULL AND id LIKE '1.%' OR id LIKE '2.%' OR id LIKE '3.%' OR id LIKE '5.%'
        """)
        orphan_indicators = cursor.fetchone()[0]
        
        # 检查缺少分子分母的指标
        cursor.execute("""
            SELECT COUNT(DISTINCT i.id) FROM indicators i
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id
            WHERE (i.id LIKE '1.%' OR i.id LIKE '2.%' OR i.id LIKE '3.%' OR i.id LIKE '5.%')
            AND ic.indicator_id IS NULL
        """)
        indicators_without_components = cursor.fetchone()[0]
        
        # 检查数据完整性
        cursor.execute("""
            SELECT COUNT(*) FROM indicator_components
            WHERE (unit = '' OR lead_department = '' OR data_source = '')
            AND (indicator_id LIKE '1.%' OR indicator_id LIKE '2.%' OR indicator_id LIKE '3.%' OR indicator_id LIKE '5.%')
        """)
        incomplete_components = cursor.fetchone()[0]
        
        print(f"数据质量检查:")
        print(f"  🔗 孤立指标（未关联小节）: {orphan_indicators}个")
        print(f"  ⚠️  缺少分子分母的指标: {indicators_without_components}个")
        print(f"  📝 信息不完整的组成部分: {incomplete_components}个")
        
        # 计算整体完整性
        cursor.execute("SELECT COUNT(*) FROM indicators WHERE id LIKE '1.%' OR id LIKE '2.%' OR id LIKE '3.%' OR id LIKE '5.%'")
        total_indicators_all = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM indicator_components WHERE indicator_id LIKE '1.%' OR indicator_id LIKE '2.%' OR indicator_id LIKE '3.%' OR indicator_id LIKE '5.%'")
        total_components_all = cursor.fetchone()[0]
        
        completeness_pct = ((total_components_all - incomplete_components) / total_components_all * 100) if total_components_all > 0 else 0
        
        print(f"\n整体数据完整性: {completeness_pct:.1f}%")
        
        if completeness_pct >= 95:
            print("✅ 数据质量: 优秀")
        elif completeness_pct >= 90:
            print("🟡 数据质量: 良好")
        elif completeness_pct >= 80:
            print("🟠 数据质量: 一般")
        else:
            print("🔴 数据质量: 需要改进")
        
        # 4. 数据库文件信息
        import os
        db_size = os.path.getsize(db_path)
        db_size_mb = db_size / (1024 * 1024)
        
        print(f"\n📁 数据库文件信息:")
        print(f"  文件大小: {db_size_mb:.2f} MB")
        print(f"  文件路径: {db_path}")
        
        print(f"\n" + "=" * 100)
        print("✅ 数据库状态报告完成")
        print("=" * 100)
        
    except Exception as e:
        print(f"❌ 生成报告时出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if conn:
            conn.close()

def main():
    show_complete_database_status()

if __name__ == "__main__":
    main()
