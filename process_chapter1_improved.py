#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版：处理chapter1.xlsx文件，正确提取第一章的小节和指标数据并导入数据库
"""

import pandas as pd
import sqlite3
import re
import sys
import os

def analyze_excel_structure(file_path):
    """
    分析Excel文件的结构
    """
    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {file_path}")
        df = pd.read_excel(file_path, sheet_name=0)
        
        print(f"数据维度: {df.shape[0]}行 × {df.shape[1]}列")
        print(f"列名: {df.columns.tolist()}")
        
        return df
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def process_chapter1_data_improved(df):
    """
    改进版：根据Excel的列结构正确处理第一章数据
    """
    sections = []
    indicators = []
    
    print("\n开始处理第一章数据...")
    
    # 根据列名分析数据结构
    # 列名: ['章节编号', '章节名称', '小节编号', '小节名称', '一级指标编号', '一级指标名称', ...]
    
    for index, row in df.iterrows():
        try:
            # 提取小节信息
            section_number = row.get('小节编号', '')
            section_name = row.get('小节名称', '')
            
            # 提取指标信息
            indicator_id = row.get('一级指标编号', '')
            indicator_name = row.get('一级指标名称', '')
            
            # 处理小节数据
            if pd.notna(section_number) and pd.notna(section_name):
                section_number_str = str(section_number).strip()
                section_name_str = str(section_name).strip()
                
                # 转换中文数字编号为阿拉伯数字
                section_code = convert_chinese_section_to_code(section_number_str)
                
                if section_code and section_name_str:
                    # 清理小节名称，去除括号内容
                    clean_section_name = clean_name(section_name_str)
                    
                    sections.append({
                        'code': section_code,
                        'name': clean_section_name,
                        'original_number': section_number_str,
                        'original_name': section_name_str,
                        'row': index + 1
                    })
                    print(f"发现小节: {section_code} - {clean_section_name}")
            
            # 处理指标数据
            if pd.notna(indicator_id) and pd.notna(indicator_name):
                indicator_id_str = str(indicator_id).strip()
                indicator_name_str = str(indicator_name).strip()
                
                if indicator_id_str and indicator_name_str and indicator_id_str.startswith('1.'):
                    # 确定所属小节
                    section_code = '.'.join(indicator_id_str.split('.')[:2])
                    
                    indicators.append({
                        'id': indicator_id_str,
                        'name': indicator_name_str,
                        'section_code': section_code,
                        'row': index + 1
                    })
                    print(f"发现指标: {indicator_id_str} - {indicator_name_str}")
                    
        except Exception as e:
            print(f"处理第{index+1}行时出错: {e}")
            continue
    
    return sections, indicators

def convert_chinese_section_to_code(chinese_number):
    """
    将中文小节编号转换为数字编码
    例如：'一、' -> '1.1', '二、' -> '1.2'
    """
    chinese_to_arabic = {
        '一': '1',
        '二': '2', 
        '三': '3',
        '四': '4',
        '五': '5',
        '六': '6',
        '七': '7',
        '八': '8',
        '九': '9',
        '十': '10'
    }
    
    # 去除标点符号
    clean_number = chinese_number.replace('、', '').replace('，', '').replace('。', '').strip()
    
    if clean_number in chinese_to_arabic:
        return f"1.{chinese_to_arabic[clean_number]}"
    
    return None

def clean_name(name):
    """
    清理名称，去除括号内容
    """
    # 去除括号及其内容
    cleaned = re.sub(r'[（(].*?[）)]', '', name)
    return cleaned.strip()

def remove_duplicates(data_list, key_field):
    """
    去除重复数据
    """
    seen = set()
    unique_data = []
    
    for item in data_list:
        key = item[key_field]
        if key not in seen:
            seen.add(key)
            unique_data.append(item)
        else:
            print(f"发现重复{key_field}: {key}，已跳过")
    
    return unique_data

def insert_sections_to_db(sections, db_path):
    """
    将小节数据插入数据库
    """
    if not sections:
        print("没有小节数据需要插入")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取第一章的ID
        cursor.execute("SELECT id FROM chapters WHERE code = '1'")
        chapter_result = cursor.fetchone()
        if not chapter_result:
            print("错误：数据库中未找到第一章数据")
            return
        
        chapter_id = chapter_result[0]
        
        # 插入小节数据
        for i, section in enumerate(sections):
            cursor.execute("""
                INSERT OR REPLACE INTO sections 
                (chapter_id, code, name, description, sort_order, is_active, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            """, (
                chapter_id,
                section['code'],
                section['name'],
                f"第一章小节：{section['name']}",
                i + 1
            ))
            print(f"插入小节: {section['code']} - {section['name']}")
        
        conn.commit()
        print(f"成功插入 {len(sections)} 个小节")
        
    except Exception as e:
        print(f"插入小节数据时出错: {e}")
    finally:
        if conn:
            conn.close()

def insert_indicators_to_db(indicators, db_path):
    """
    将指标数据插入数据库
    """
    if not indicators:
        print("没有指标数据需要插入")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取第一章的ID
        cursor.execute("SELECT id FROM chapters WHERE code = '1'")
        chapter_result = cursor.fetchone()
        if not chapter_result:
            print("错误：数据库中未找到第一章数据")
            return
        
        chapter_id = chapter_result[0]
        
        # 为每个指标找到对应的小节ID
        for i, indicator in enumerate(indicators):
            # 查找对应的小节ID
            cursor.execute("SELECT id FROM sections WHERE code = ? AND chapter_id = ?", 
                         (indicator['section_code'], chapter_id))
            section_result = cursor.fetchone()
            section_id = section_result[0] if section_result else None
            
            if not section_id:
                print(f"警告：未找到小节 {indicator['section_code']}，指标 {indicator['id']} 将不关联小节")
            
            # 插入指标数据
            cursor.execute("""
                INSERT OR REPLACE INTO indicators 
                (id, name, description, chapter_id, section_id, category, sort_order, is_active, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            """, (
                indicator['id'],
                indicator['name'],
                f"第一章指标：{indicator['name']}",
                chapter_id,
                section_id,
                '资源配置与运行数据',
                i + 1
            ))
            print(f"插入指标: {indicator['id']} - {indicator['name']}")
        
        conn.commit()
        print(f"成功插入 {len(indicators)} 个指标")
        
    except Exception as e:
        print(f"插入指标数据时出错: {e}")
    finally:
        if conn:
            conn.close()

def main():
    file_path = "chapter1.xlsx"
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件 {file_path} 不存在")
        return
    
    if not os.path.exists(db_path):
        print(f"错误：数据库文件 {db_path} 不存在")
        return
    
    # 分析Excel文件结构
    df = analyze_excel_structure(file_path)
    if df is None:
        return
    
    # 显示前几行数据以便调试
    print("\n=== 前5行关键列数据 ===")
    key_columns = ['小节编号', '小节名称', '一级指标编号', '一级指标名称']
    for i, row in df.head(5).iterrows():
        print(f"第{i+1}行:")
        for col in key_columns:
            if col in df.columns:
                print(f"  {col}: {row[col]}")
        print()
    
    # 处理第一章数据
    sections, indicators = process_chapter1_data_improved(df)
    
    # 去重
    unique_sections = remove_duplicates(sections, 'code')
    unique_indicators = remove_duplicates(indicators, 'id')
    
    print(f"\n=== 处理结果 ===")
    print(f"提取到 {len(unique_sections)} 个唯一小节")
    print(f"提取到 {len(unique_indicators)} 个唯一指标")
    
    # 显示提取结果
    if unique_sections:
        print("\n=== 小节列表 ===")
        for section in unique_sections:
            print(f"{section['code']} - {section['name']}")
    
    if unique_indicators:
        print("\n=== 指标列表 ===")
        for indicator in unique_indicators:
            print(f"{indicator['id']} - {indicator['name']}")
    
    # 插入数据库
    if unique_sections or unique_indicators:
        print("\n=== 开始导入数据库 ===")
        insert_sections_to_db(unique_sections, db_path)
        insert_indicators_to_db(unique_indicators, db_path)
        print("数据导入完成！")
    else:
        print("未找到有效的第一章数据")

if __name__ == "__main__":
    main()
