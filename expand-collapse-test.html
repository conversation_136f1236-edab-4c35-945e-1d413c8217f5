<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>展开/收起功能测试</title>
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            padding: 20px;
            background-color: var(--gray-50);
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-title {
            font-size: 24px;
            margin-bottom: 20px;
            color: var(--gray-900);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-buttons {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }
        .test-btn {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .test-btn.outline {
            background: transparent;
            color: #007bff;
            border: 1px solid #007bff;
        }
        .test-btn.outline:hover {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">展开/收起功能测试</h1>
        
        <!-- 控制按钮 -->
        <div class="test-section">
            <h3>控制按钮</h3>
            <div class="test-buttons">
                <button class="test-btn" id="expandAllBtn">
                    <i class="fas fa-expand-arrows-alt"></i>
                    展开全部
                </button>
                <button class="test-btn outline" id="collapseAllBtn">
                    <i class="fas fa-compress-arrows-alt"></i>
                    收起全部
                </button>
                <button class="test-btn" onclick="testExpandCollapse()">
                    测试功能
                </button>
            </div>
        </div>

        <!-- 卡片视图测试 -->
        <div class="test-section">
            <h3>卡片视图测试</h3>
            <div class="card-view" style="display: block;">
                <div class="card-grid">
                    <!-- 父指标 1.3.1 -->
                    <div class="indicator-card level-0">
                        <div class="card-container parent-connector">
                            <div class="card-header">
                                <div class="card-id-section">
                                    <button class="expand-toggle" onclick="toggleTest('1.3.1')" title="展开/折叠子指标">
                                        <i class="fas fa-chevron-down" id="toggle-1.3.1"></i>
                                    </button>
                                    <span class="indicator-id-badge">1.3.1</span>
                                </div>
                                <div class="card-actions">
                                    <button class="action-icon" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <h3 class="card-title">急诊医学科</h3>
                                <div class="card-description">第一章1级指标：急诊医学科</div>
                                <div class="card-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-sitemap"></i>
                                        <span>2个子指标</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 子指标容器 -->
                    <div class="children-container" id="children-1.3.1" style="display: none;">
                        <!-- 子指标 ******* -->
                        <div class="indicator-card level-1">
                            <div class="card-container">
                                <div class="card-header">
                                    <div class="card-id-section">
                                        <span class="expand-spacer"></span>
                                        <span class="indicator-id-badge">*******</span>
                                    </div>
                                    <div class="card-actions">
                                        <button class="action-icon" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <h3 class="card-title">固定急诊医师人数占急诊在岗医师人数的比例</h3>
                                    <div class="card-description">第一章2级指标</div>
                                </div>
                            </div>
                        </div>

                        <!-- 子指标 ******* -->
                        <div class="indicator-card level-1">
                            <div class="card-container">
                                <div class="card-header">
                                    <div class="card-id-section">
                                        <span class="expand-spacer"></span>
                                        <span class="indicator-id-badge">*******</span>
                                    </div>
                                    <div class="card-actions">
                                        <button class="action-icon" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <h3 class="card-title">固定急诊护士人数占急诊护士人数的比例</h3>
                                    <div class="card-description">第一章2级指标</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 列表视图测试 -->
        <div class="test-section">
            <h3>列表视图测试</h3>
            <div class="list-view" style="display: block;">
                <div class="list-container">
                    <div class="list-header">
                        <div class="list-col-id">编号</div>
                        <div class="list-col-title">指标名称</div>
                        <div class="list-col-desc">描述</div>
                        <div class="list-col-tags">标签</div>
                        <div class="list-col-actions">操作</div>
                    </div>
                    <div class="list-body">
                        <!-- 父指标 1.3.1 -->
                        <div class="list-item level-0">
                            <div class="list-item-container">
                                <div class="list-col-id">
                                    <div class="list-id-section">
                                        <button class="expand-toggle-list" onclick="toggleTestList('1.3.1')" title="展开/折叠子指标">
                                            <i class="fas fa-chevron-down" id="toggle-list-1.3.1"></i>
                                        </button>
                                        <span class="list-item-badge">1.3.1</span>
                                    </div>
                                </div>
                                <div class="list-col-title">
                                    <span class="list-item-title">急诊医学科</span>
                                </div>
                                <div class="list-col-desc">
                                    <span class="list-item-desc">第一章1级指标：急诊医学科</span>
                                </div>
                                <div class="list-col-tags">
                                    <span class="tag children-tag">2个子指标</span>
                                </div>
                                <div class="list-col-actions">
                                    <button class="action-icon" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 子指标容器 -->
                        <div class="children-container-list" id="children-list-1.3.1" style="display: none;">
                            <!-- 子指标 ******* -->
                            <div class="list-item level-1">
                                <div class="list-item-container" style="padding-left: 40px;">
                                    <div class="list-col-id">
                                        <div class="list-id-section">
                                            <span class="expand-spacer-list"></span>
                                            <span class="list-item-badge">*******</span>
                                        </div>
                                    </div>
                                    <div class="list-col-title">
                                        <span class="list-item-title">固定急诊医师人数占急诊在岗医师人数的比例</span>
                                    </div>
                                    <div class="list-col-desc">
                                        <span class="list-item-desc">第一章2级指标</span>
                                    </div>
                                    <div class="list-col-tags">
                                        <span class="tag">2个组件</span>
                                    </div>
                                    <div class="list-col-actions">
                                        <button class="action-icon" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 子指标 ******* -->
                            <div class="list-item level-1">
                                <div class="list-item-container" style="padding-left: 40px;">
                                    <div class="list-col-id">
                                        <div class="list-id-section">
                                            <span class="expand-spacer-list"></span>
                                            <span class="list-item-badge">*******</span>
                                        </div>
                                    </div>
                                    <div class="list-col-title">
                                        <span class="list-item-title">固定急诊护士人数占急诊护士人数的比例</span>
                                    </div>
                                    <div class="list-col-desc">
                                        <span class="list-item-desc">第一章2级指标</span>
                                    </div>
                                    <div class="list-col-tags">
                                        <span class="tag">2个组件</span>
                                    </div>
                                    <div class="list-col-actions">
                                        <button class="action-icon" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="static/js/app.js"></script>
    <script>
        // 测试展开/收起功能
        function testExpandCollapse() {
            console.log('=== 测试展开/收起功能 ===');
            
            // 检查按钮是否存在
            const expandBtn = document.getElementById('expandAllBtn');
            const collapseBtn = document.getElementById('collapseAllBtn');
            
            console.log('展开按钮:', expandBtn ? '存在' : '不存在');
            console.log('收起按钮:', collapseBtn ? '存在' : '不存在');
            
            // 检查容器是否存在
            const cardContainers = document.querySelectorAll('.children-container');
            const listContainers = document.querySelectorAll('.children-container-list');
            
            console.log('卡片子指标容器数量:', cardContainers.length);
            console.log('列表子指标容器数量:', listContainers.length);
            
            // 检查app对象
            console.log('app对象:', typeof window.app);
            if (window.app) {
                console.log('app.expandAllIndicators:', typeof window.app.expandAllIndicators);
                console.log('app.collapseAllIndicators:', typeof window.app.collapseAllIndicators);
            }
        }

        // 单个指标的展开/收起测试
        function toggleTest(indicatorId) {
            const childrenContainer = document.getElementById(`children-${indicatorId}`);
            const toggleIcon = document.getElementById(`toggle-${indicatorId}`);
            const toggleButton = toggleIcon?.parentElement;
            const parentContainer = toggleIcon?.closest('.indicator-card')?.querySelector('.card-container');

            if (!childrenContainer) return;

            const isExpanded = childrenContainer.style.display !== 'none';

            if (isExpanded) {
                childrenContainer.style.display = 'none';
                if (toggleIcon) {
                    toggleIcon.className = 'fas fa-chevron-down';
                    toggleIcon.style.transform = 'rotate(0deg)';
                }
                if (toggleButton) {
                    toggleButton.classList.remove('expanded');
                }
                if (parentContainer) {
                    parentContainer.classList.remove('expanded');
                }
            } else {
                childrenContainer.style.display = 'block';
                if (toggleIcon) {
                    toggleIcon.className = 'fas fa-chevron-down';
                    toggleIcon.style.transform = 'rotate(180deg)';
                }
                if (toggleButton) {
                    toggleButton.classList.add('expanded');
                }
                if (parentContainer) {
                    parentContainer.classList.add('expanded');
                }
            }
        }

        function toggleTestList(indicatorId) {
            const childrenContainer = document.getElementById(`children-list-${indicatorId}`);
            const toggleIcon = document.getElementById(`toggle-list-${indicatorId}`);
            const toggleButton = toggleIcon?.parentElement;

            if (!childrenContainer) return;

            const isExpanded = childrenContainer.style.display !== 'none';

            if (isExpanded) {
                childrenContainer.style.display = 'none';
                if (toggleIcon) {
                    toggleIcon.className = 'fas fa-chevron-down';
                    toggleIcon.style.transform = 'rotate(0deg)';
                }
                if (toggleButton) {
                    toggleButton.classList.remove('expanded');
                }
            } else {
                childrenContainer.style.display = 'block';
                if (toggleIcon) {
                    toggleIcon.className = 'fas fa-chevron-down';
                    toggleIcon.style.transform = 'rotate(180deg)';
                }
                if (toggleButton) {
                    toggleButton.classList.add('expanded');
                }
            }
        }
    </script>
</body>
</html>
