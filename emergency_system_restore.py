#!/usr/bin/env python3
"""
紧急系统恢复 - 回退到稳定版本并修复核心问题
"""

import os
import shutil
import sqlite3
import subprocess

def backup_current_state():
    """备份当前状态"""
    print("📦 备份当前状态...")
    
    backup_dir = "backup_broken_state"
    if os.path.exists(backup_dir):
        shutil.rmtree(backup_dir)
    
    os.makedirs(backup_dir)
    
    # 备份关键文件
    files_to_backup = [
        'hospital_indicator_app.py',
        'static/js/app.js',
        'static/js/error-handler.js',
        'static/js/modal-error-handler.js',
        'static/css/style.css',
        'templates/admin/base.html'
    ]
    
    for file_path in files_to_backup:
        if os.path.exists(file_path):
            dest_dir = os.path.join(backup_dir, os.path.dirname(file_path))
            os.makedirs(dest_dir, exist_ok=True)
            shutil.copy2(file_path, os.path.join(backup_dir, file_path))
            print(f"   ✅ 已备份: {file_path}")
    
    print("✅ 当前状态备份完成")

def restore_clean_app_js():
    """恢复干净的app.js文件"""
    print("\n🔧 恢复干净的app.js文件...")
    
    clean_app_js = '''// 医院等级评审指标说明手册 - 主应用脚本

class HospitalIndicatorApp {
    constructor() {
        this.currentChapter = null;
        this.currentSection = null;
        this.indicators = [];
        this.chapters = [];
        this.sections = [];
        this.init();
    }

    async init() {
        try {
            await this.loadChapters();
            await this.loadStatistics();
            this.setupEventListeners();
            this.setupSearch();
            console.log('应用初始化完成');
        } catch (error) {
            console.error('应用初始化失败:', error);
        }
    }

    async loadChapters() {
        try {
            const response = await fetch('/api/chapters');
            const data = await response.json();
            
            if (data.success) {
                this.chapters = data.data;
                this.renderChapters();
            } else {
                console.error('加载章节失败:', data.error);
            }
        } catch (error) {
            console.error('加载章节异常:', error);
        }
    }

    async loadStatistics() {
        try {
            const response = await fetch('/api/statistics');
            const data = await response.json();
            
            if (data.success) {
                this.renderStatistics(data.data);
            } else {
                console.error('加载统计失败:', data.error);
            }
        } catch (error) {
            console.error('加载统计异常:', error);
        }
    }

    renderChapters() {
        const chapterList = document.getElementById('chapterList');
        if (!chapterList) return;

        chapterList.innerHTML = this.chapters.map(chapter => `
            <div class="chapter-item" data-chapter="${chapter.id}">
                <h3>${chapter.name}</h3>
                <p>${chapter.description || ''}</p>
                <div class="chapter-stats">
                    <span><i class="fas fa-list"></i> ${chapter.section_count || 0} 个小节</span>
                    <span><i class="fas fa-chart-bar"></i> ${chapter.indicator_count || 0} 个指标</span>
                </div>
            </div>
        `).join('');

        // 添加点击事件
        chapterList.querySelectorAll('.chapter-item').forEach(item => {
            item.addEventListener('click', () => {
                const chapterId = item.dataset.chapter;
                this.selectChapter(chapterId);
            });
        });
    }

    renderStatistics(stats) {
        const statsContainer = document.getElementById('statisticsContainer');
        if (!statsContainer) return;

        statsContainer.innerHTML = `
            <div class="stat-card">
                <i class="fas fa-book"></i>
                <div class="stat-info">
                    <h3>${stats.total_chapters || 0}</h3>
                    <p>总章节数</p>
                </div>
            </div>
            <div class="stat-card">
                <i class="fas fa-list"></i>
                <div class="stat-info">
                    <h3>${stats.total_sections || 0}</h3>
                    <p>总小节数</p>
                </div>
            </div>
            <div class="stat-card">
                <i class="fas fa-chart-bar"></i>
                <div class="stat-info">
                    <h3>${stats.total_indicators || 0}</h3>
                    <p>总指标数</p>
                </div>
            </div>
        `;
    }

    async selectChapter(chapterId) {
        this.currentChapter = chapterId;
        
        try {
            // 加载章节的小节
            await this.loadSections(chapterId);
            // 加载章节的指标
            await this.loadIndicators(chapterId);
            
            // 更新UI
            this.updateChapterSelection();
            
        } catch (error) {
            console.error('选择章节失败:', error);
        }
    }

    async loadSections(chapterId) {
        try {
            const response = await fetch(`/api/chapters/${chapterId}/sections`);
            const data = await response.json();
            
            if (data.success) {
                this.sections = data.data;
                this.renderSections();
            } else {
                console.error('加载小节失败:', data.error);
            }
        } catch (error) {
            console.error('加载小节异常:', error);
        }
    }

    async loadIndicators(chapterId, sectionId = null) {
        try {
            let url = `/api/indicators?chapter=${chapterId}`;
            if (sectionId) {
                url += `&section=${sectionId}`;
            }
            
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.success) {
                this.indicators = data.data;
                this.renderIndicators();
            } else {
                console.error('加载指标失败:', data.error);
            }
        } catch (error) {
            console.error('加载指标异常:', error);
        }
    }

    renderSections() {
        const sectionList = document.getElementById('sectionList');
        if (!sectionList) return;

        sectionList.innerHTML = this.sections.map(section => `
            <div class="section-item" data-section="${section.id}">
                <h4>${section.name}</h4>
                <p>${section.description || ''}</p>
                <span class="indicator-count">${section.indicator_count || 0} 个指标</span>
            </div>
        `).join('');

        // 添加点击事件
        sectionList.querySelectorAll('.section-item').forEach(item => {
            item.addEventListener('click', () => {
                const sectionId = item.dataset.section;
                this.selectSection(sectionId);
            });
        });
    }

    renderIndicators() {
        const indicatorList = document.getElementById('indicatorList');
        if (!indicatorList) return;

        indicatorList.innerHTML = this.indicators.map(indicator => `
            <div class="indicator-item" data-indicator="${indicator.id}">
                <div class="indicator-header">
                    <h5>${indicator.id} ${indicator.name}</h5>
                    <span class="indicator-type">${indicator.indicator_type || 'simple'}</span>
                </div>
                <p class="indicator-description">${indicator.description || ''}</p>
                <div class="indicator-actions">
                    <button onclick="app.showIndicatorDetail('${indicator.id}')" class="btn-detail">
                        <i class="fas fa-eye"></i> 查看详情
                    </button>
                </div>
            </div>
        `).join('');
    }

    async showIndicatorDetail(indicatorId) {
        try {
            const response = await fetch(`/api/indicators/${indicatorId}`);
            const data = await response.json();
            
            if (data.success) {
                this.displayIndicatorModal(data.data.indicator);
            } else {
                console.error('加载指标详情失败:', data.error);
            }
        } catch (error) {
            console.error('加载指标详情异常:', error);
        }
    }

    displayIndicatorModal(indicator) {
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>${indicator.id} ${indicator.name}</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="indicator-detail">
                        <div class="detail-section">
                            <h3>基本信息</h3>
                            <p><strong>指标类型:</strong> ${indicator.indicator_type || 'simple'}</p>
                            <p><strong>描述:</strong> ${indicator.description || '暂无描述'}</p>
                            <p><strong>单位:</strong> ${indicator.unit || '暂无单位'}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 添加关闭事件
        const closeBtn = modal.querySelector('.close');
        closeBtn.onclick = () => {
            document.body.removeChild(modal);
        };

        modal.onclick = (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        };
    }

    selectSection(sectionId) {
        this.currentSection = sectionId;
        this.loadIndicators(this.currentChapter, sectionId);
        this.updateSectionSelection();
    }

    updateChapterSelection() {
        document.querySelectorAll('.chapter-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeChapter = document.querySelector(`[data-chapter="${this.currentChapter}"]`);
        if (activeChapter) {
            activeChapter.classList.add('active');
        }
    }

    updateSectionSelection() {
        document.querySelectorAll('.section-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeSection = document.querySelector(`[data-section="${this.currentSection}"]`);
        if (activeSection) {
            activeSection.classList.add('active');
        }
    }

    setupEventListeners() {
        // 菜单切换
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        
        if (menuToggle && sidebar) {
            menuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
            });
        }
    }

    setupSearch() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchIndicators(e.target.value);
            });
        }
    }

    searchIndicators(query) {
        if (!query.trim()) {
            this.renderIndicators();
            return;
        }

        const filteredIndicators = this.indicators.filter(indicator => 
            indicator.name.toLowerCase().includes(query.toLowerCase()) ||
            indicator.id.toLowerCase().includes(query.toLowerCase()) ||
            (indicator.description && indicator.description.toLowerCase().includes(query.toLowerCase()))
        );

        const indicatorList = document.getElementById('indicatorList');
        if (!indicatorList) return;

        indicatorList.innerHTML = filteredIndicators.map(indicator => `
            <div class="indicator-item" data-indicator="${indicator.id}">
                <div class="indicator-header">
                    <h5>${indicator.id} ${indicator.name}</h5>
                    <span class="indicator-type">${indicator.indicator_type || 'simple'}</span>
                </div>
                <p class="indicator-description">${indicator.description || ''}</p>
                <div class="indicator-actions">
                    <button onclick="app.showIndicatorDetail('${indicator.id}')" class="btn-detail">
                        <i class="fas fa-eye"></i> 查看详情
                    </button>
                </div>
            </div>
        `).join('');
    }
}

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new HospitalIndicatorApp();
});
'''
    
    with open('static/js/app.js', 'w', encoding='utf-8') as f:
        f.write(clean_app_js)
    
    print("✅ app.js文件已恢复为干净版本")

def restore_clean_error_handler():
    """恢复干净的错误处理文件"""
    print("\n🔧 恢复干净的错误处理文件...")
    
    clean_error_handler = '''// 全局错误处理脚本

// 安全的DOM操作函数
function safeSetTextContent(element, text) {
    if (element) {
        element.textContent = text || '';
        return true;
    } else {
        console.warn('Element not found for text setting');
        return false;
    }
}

function safeSetInnerHTML(element, html) {
    if (element) {
        element.innerHTML = html || '';
        return true;
    } else {
        console.warn('Element not found for HTML setting');
        return false;
    }
}

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
});

window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled Promise Rejection:', e.reason);
});

// 通知系统
function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    if (notification) {
        const icon = notification.querySelector('.notification-icon');
        const messageEl = notification.querySelector('.notification-message');
        
        if (icon && messageEl) {
            // 设置图标
            icon.className = `notification-icon fas ${type === 'success' ? 'fa-check-circle' : 
                                                    type === 'error' ? 'fa-exclamation-circle' : 
                                                    type === 'warning' ? 'fa-exclamation-triangle' : 
                                                    'fa-info-circle'}`;
            
            // 设置消息
            messageEl.textContent = message;
            
            // 设置样式
            notification.className = `notification ${type}`;
            notification.style.display = 'block';
            
            // 自动隐藏
            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }
    } else {
        console.log(`Notification: ${message}`);
    }
}

console.log('Error handler loaded');
'''
    
    with open('static/js/error-handler.js', 'w', encoding='utf-8') as f:
        f.write(clean_error_handler)
    
    print("✅ error-handler.js文件已恢复")

def restore_clean_modal_handler():
    """恢复干净的模态框处理文件"""
    print("\n🔧 恢复干净的模态框处理文件...")
    
    clean_modal_handler = '''// 模态框错误处理脚本

// 模态框安全操作函数
function safeShowModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        return true;
    } else {
        console.warn(`Modal not found: ${modalId}`);
        return false;
    }
}

function safeHideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        return true;
    } else {
        console.warn(`Modal not found: ${modalId}`);
        return false;
    }
}

// 模态框通用关闭处理
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
    }
    
    if (e.target.classList.contains('close')) {
        const modal = e.target.closest('.modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }
});

// ESC键关闭模态框
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (modal.style.display === 'block') {
                modal.style.display = 'none';
            }
        });
    }
});

console.log('Modal error handler loaded');
'''
    
    with open('static/js/modal-error-handler.js', 'w', encoding='utf-8') as f:
        f.write(clean_modal_handler)
    
    print("✅ modal-error-handler.js文件已恢复")

def restore_clean_admin_base():
    """恢复干净的admin base模板"""
    print("\n🔧 恢复干净的admin base模板...")
    
    # 检查文件是否存在
    if not os.path.exists('templates/admin/base.html'):
        print("   ⚠️  admin/base.html不存在，跳过")
        return
    
    # 读取当前文件
    with open('templates/admin/base.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 确保包含notification组件
    if 'id="notification"' not in content:
        # 在body开始后添加notification
        notification_html = '''
    <!-- 通知组件 -->
    <div id="notification" class="notification">
        <i class="notification-icon fas fa-info-circle"></i>
        <span class="notification-message"></span>
    </div>
    
    <!-- 隐藏的页面元素 -->
    <div id="page-title" style="display: none;"></div>
    <div id="page-subtitle" style="display: none;"></div>
'''
        
        # 在<body>标签后插入
        content = content.replace('<body>', '<body>' + notification_html)
        
        with open('templates/admin/base.html', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("   ✅ 已添加notification组件到admin/base.html")
    else:
        print("   ✅ admin/base.html已包含notification组件")

def restore_clean_app_py():
    """恢复干净的主应用文件"""
    print("\n🔧 恢复主应用文件中的参考范围API...")
    
    # 恢复参考范围API为简单版本
    try:
        with open('hospital_indicator_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 恢复简单的参考范围API
        simple_reference_api = '''@app.route('/api/indicators/<indicator_id>/reference-range', methods=['GET'])
def get_indicator_reference_range(indicator_id):
    """获取指标参考范围"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT * FROM indicator_reference_ranges
            WHERE indicator_id = ? AND is_active = 1
            ORDER BY created_at DESC
            LIMIT 1
        """, (indicator_id,))

        reference_range = dict_from_row(cursor.fetchone())
        conn.close()

        if reference_range:
            return jsonify({'success': True, 'data': reference_range})
        else:
            # 返回空数据而不是错误
            return jsonify({'success': True, 'data': None})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})'''
        
        # 替换被禁用的API
        content = re.sub(
            r'# 暂时禁用参考范围功能\s*@app\.route\(\'/api/indicators/<indicator_id>/reference-range\'.*?return jsonify\(\{\'success\': False, \'error\': \'参考范围功能暂时禁用\'\}\)',
            simple_reference_api,
            content,
            flags=re.DOTALL
        )
        
        with open('hospital_indicator_app.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("   ✅ 主应用文件已恢复")
        
    except Exception as e:
        print(f"   ❌ 恢复主应用文件失败: {e}")

def test_system_after_restore():
    """恢复后测试系统"""
    print("\n🧪 测试系统恢复效果...")
    
    import requests
    import time
    
    time.sleep(2)  # 等待服务器重启
    
    try:
        # 测试前端
        response = requests.get('http://localhost:5001/')
        if response.status_code == 200:
            print("   ✅ 前端首页正常")
        else:
            print(f"   ❌ 前端首页失败: {response.status_code}")
        
        # 测试后端
        response = requests.get('http://localhost:5001/admin')
        if response.status_code == 200:
            print("   ✅ 后端首页正常")
        else:
            print(f"   ❌ 后端首页失败: {response.status_code}")
        
        # 测试API
        response = requests.get('http://localhost:5001/api/indicators/1.1.1')
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print("   ✅ 指标API正常")
            else:
                print(f"   ❌ 指标API失败: {data.get('error')}")
        else:
            print(f"   ❌ 指标API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")

def main():
    """主恢复函数"""
    print("🚨 紧急系统恢复 - 回退到稳定版本")
    print("=" * 70)
    
    # 1. 备份当前状态
    backup_current_state()
    
    # 2. 恢复干净的JavaScript文件
    restore_clean_app_js()
    restore_clean_error_handler()
    restore_clean_modal_handler()
    
    # 3. 恢复admin模板
    restore_clean_admin_base()
    
    # 4. 恢复主应用文件
    restore_clean_app_py()
    
    # 5. 测试恢复效果
    test_system_after_restore()
    
    print("\n" + "=" * 70)
    print("🎉 系统恢复完成！")
    
    print("\n📋 恢复总结:")
    print("✅ 已备份损坏状态到 backup_broken_state/")
    print("✅ 已恢复干净的前端JavaScript文件")
    print("✅ 已恢复稳定的错误处理机制")
    print("✅ 已恢复基本的参考范围API")
    print("✅ 已确保admin模板包含必要组件")
    
    print("\n🔗 下一步:")
    print("1. 重启应用服务器")
    print("2. 清除浏览器缓存")
    print("3. 测试基本功能")
    print("4. 确认系统稳定后再考虑新功能")

if __name__ == "__main__":
    main()
