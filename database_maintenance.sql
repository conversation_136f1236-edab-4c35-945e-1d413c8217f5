-- 医院等级评审指标管理系统 - 数据库维护脚本

USE hospital_indicator_system;

-- ========================================
-- 数据库维护和优化脚本
-- ========================================

-- 1. 创建存储过程：自动更新指标状态
DELIMITER //
CREATE PROCEDURE UpdateIndicatorStatus()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE indicator_id_var VARCHAR(20);
    DECLARE latest_value DECIMAL(15,4);
    DECLARE target_val DECIMAL(10,4);
    DECLARE completion_rate_val DECIMAL(5,2);
    DECLARE new_status ENUM('normal','warning','danger');
    
    -- 声明游标
    DECLARE indicator_cursor CURSOR FOR 
        SELECT i.id, i.target_value,
               (SELECT idh.value FROM indicator_data_history idh 
                WHERE idh.indicator_id = i.id AND idh.status = 'approved' 
                ORDER BY idh.data_period DESC LIMIT 1) as latest_val
        FROM indicators i 
        WHERE i.is_active = TRUE AND i.target_value > 0;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN indicator_cursor;
    
    read_loop: LOOP
        FETCH indicator_cursor INTO indicator_id_var, target_val, latest_value;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        IF latest_value IS NOT NULL AND target_val > 0 THEN
            SET completion_rate_val = ROUND(latest_value / target_val * 100, 2);
            
            -- 根据完成率确定状态
            IF completion_rate_val >= 95 THEN
                SET new_status = 'normal';
            ELSEIF completion_rate_val >= 80 THEN
                SET new_status = 'warning';
            ELSE
                SET new_status = 'danger';
            END IF;
            
            -- 更新指标
            UPDATE indicators 
            SET current_value = latest_value,
                completion_rate = completion_rate_val,
                status = new_status,
                updated_at = NOW()
            WHERE id = indicator_id_var;
        END IF;
    END LOOP;
    
    CLOSE indicator_cursor;
END //
DELIMITER ;

-- 2. 创建存储过程：数据清理
DELIMITER //
CREATE PROCEDURE CleanupOldData(IN months_to_keep INT)
BEGIN
    DECLARE cutoff_date DATE;
    SET cutoff_date = DATE_SUB(CURDATE(), INTERVAL months_to_keep MONTH);
    
    -- 删除旧的草稿数据
    DELETE FROM indicator_data_history 
    WHERE status = 'draft' AND created_at < cutoff_date;
    
    -- 删除旧的审核流程记录
    DELETE daf FROM data_audit_flow daf
    LEFT JOIN indicator_data_history idh ON daf.data_history_id = idh.id
    WHERE idh.id IS NULL;
    
    SELECT ROW_COUNT() AS deleted_records;
END //
DELIMITER ;

-- 3. 创建函数：计算指标完成率
DELIMITER //
CREATE FUNCTION GetIndicatorCompletionRate(indicator_id_param VARCHAR(20))
RETURNS DECIMAL(5,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE completion_rate DECIMAL(5,2) DEFAULT NULL;
    DECLARE latest_value DECIMAL(15,4);
    DECLARE target_value DECIMAL(10,4);
    
    SELECT i.target_value INTO target_value
    FROM indicators i 
    WHERE i.id = indicator_id_param;
    
    SELECT idh.value INTO latest_value
    FROM indicator_data_history idh 
    WHERE idh.indicator_id = indicator_id_param 
    AND idh.status = 'approved' 
    ORDER BY idh.data_period DESC 
    LIMIT 1;
    
    IF latest_value IS NOT NULL AND target_value > 0 THEN
        SET completion_rate = ROUND(latest_value / target_value * 100, 2);
    END IF;
    
    RETURN completion_rate;
END //
DELIMITER ;

-- 4. 创建触发器：自动记录数据变更
DELIMITER //
CREATE TRIGGER tr_indicator_data_history_update
    AFTER UPDATE ON indicator_data_history
    FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO system_log (table_name, record_id, action_type, old_value, new_value, user_id, created_at)
        VALUES ('indicator_data_history', NEW.id, 'status_change', OLD.status, NEW.status, NEW.collector_id, NOW());
    END IF;
END //
DELIMITER ;

-- 5. 创建系统日志表（如果不存在）
CREATE TABLE IF NOT EXISTS system_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(50) NOT NULL,
    record_id INT NOT NULL,
    action_type VARCHAR(20) NOT NULL,
    old_value TEXT,
    new_value TEXT,
    user_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_table_record (table_name, record_id),
    INDEX idx_created_at (created_at)
) COMMENT='系统操作日志表';

-- 6. 创建定期任务事件
-- 注意：需要开启事件调度器 SET GLOBAL event_scheduler = ON;

-- 每日更新指标状态
CREATE EVENT IF NOT EXISTS ev_daily_update_indicator_status
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
  CALL UpdateIndicatorStatus();

-- 每周清理旧数据
CREATE EVENT IF NOT EXISTS ev_weekly_cleanup
ON SCHEDULE EVERY 1 WEEK
STARTS CURRENT_TIMESTAMP
DO
  CALL CleanupOldData(36); -- 保留36个月的数据

-- ========================================
-- 性能优化脚本
-- ========================================

-- 7. 分析表统计信息
ANALYZE TABLE indicators;
ANALYZE TABLE indicator_data_history;
ANALYZE TABLE indicator_departments;
ANALYZE TABLE indicator_tags;

-- 8. 优化表结构
OPTIMIZE TABLE indicators;
OPTIMIZE TABLE indicator_data_history;

-- 9. 检查索引使用情况
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    SUB_PART,
    PACKED,
    NULLABLE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'hospital_indicator_system'
ORDER BY TABLE_NAME, INDEX_NAME;

-- ========================================
-- 数据完整性检查脚本
-- ========================================

-- 10. 检查孤立记录
SELECT '检查孤立的指标标签关联' AS check_type;
SELECT it.* FROM indicator_tags it
LEFT JOIN indicators i ON it.indicator_id = i.id
LEFT JOIN tags t ON it.tag_id = t.id
WHERE i.id IS NULL OR t.id IS NULL;

SELECT '检查孤立的指标科室关联' AS check_type;
SELECT id.* FROM indicator_departments id
LEFT JOIN indicators i ON id.indicator_id = i.id
LEFT JOIN departments d ON id.department_id = d.id
WHERE i.id IS NULL OR d.id IS NULL;

SELECT '检查孤立的数据历史记录' AS check_type;
SELECT idh.* FROM indicator_data_history idh
LEFT JOIN indicators i ON idh.indicator_id = i.id
WHERE i.id IS NULL;

-- 11. 检查数据一致性
SELECT '检查指标当前值与历史数据一致性' AS check_type;
SELECT 
    i.id,
    i.name,
    i.current_value,
    (SELECT idh.value FROM indicator_data_history idh 
     WHERE idh.indicator_id = i.id AND idh.status = 'approved' 
     ORDER BY idh.data_period DESC LIMIT 1) AS latest_history_value
FROM indicators i
WHERE i.current_value != (
    SELECT idh.value FROM indicator_data_history idh 
    WHERE idh.indicator_id = i.id AND idh.status = 'approved' 
    ORDER BY idh.data_period DESC LIMIT 1
) AND EXISTS (
    SELECT 1 FROM indicator_data_history idh2 
    WHERE idh2.indicator_id = i.id AND idh2.status = 'approved'
);

-- ========================================
-- 备份和恢复脚本
-- ========================================

-- 12. 创建备份表
CREATE TABLE IF NOT EXISTS indicators_backup AS SELECT * FROM indicators WHERE 1=0;
CREATE TABLE IF NOT EXISTS indicator_data_history_backup AS SELECT * FROM indicator_data_history WHERE 1=0;

-- 13. 备份关键数据（示例）
-- INSERT INTO indicators_backup SELECT * FROM indicators WHERE updated_at >= CURDATE() - INTERVAL 1 DAY;

-- ========================================
-- 监控查询
-- ========================================

-- 14. 系统状态监控
SELECT 
    '指标总数' AS metric,
    COUNT(*) AS value
FROM indicators
UNION ALL
SELECT 
    '活跃指标数',
    COUNT(*)
FROM indicators WHERE is_active = TRUE
UNION ALL
SELECT 
    '待审核数据数',
    COUNT(*)
FROM indicator_data_history WHERE status = 'submitted'
UNION ALL
SELECT 
    '用户总数',
    COUNT(*)
FROM users WHERE is_active = TRUE;

-- 15. 性能监控
SELECT 
    table_name,
    table_rows,
    ROUND(data_length/1024/1024, 2) AS data_size_mb,
    ROUND(index_length/1024/1024, 2) AS index_size_mb
FROM information_schema.tables 
WHERE table_schema = 'hospital_indicator_system'
ORDER BY data_length DESC;

-- ========================================
-- 使用说明
-- ========================================

/*
维护脚本使用说明：

1. 定期执行（建议每日）：
   CALL UpdateIndicatorStatus();

2. 数据清理（建议每周）：
   CALL CleanupOldData(36);

3. 性能优化（建议每月）：
   ANALYZE TABLE table_name;
   OPTIMIZE TABLE table_name;

4. 数据完整性检查（建议每月）：
   执行上述检查脚本

5. 启用事件调度器：
   SET GLOBAL event_scheduler = ON;

6. 查看事件状态：
   SHOW EVENTS;

7. 查看存储过程：
   SHOW PROCEDURE STATUS WHERE Db = 'hospital_indicator_system';
*/
