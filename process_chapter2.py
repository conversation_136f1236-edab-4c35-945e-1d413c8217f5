#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理chapter2.xlsx文件，提取第二章的小节和指标数据并导入数据库
参考第一章的处理逻辑
"""

import pandas as pd
import sqlite3
import re
import sys
import os

def analyze_excel_structure(file_path):
    """
    分析Excel文件的结构
    """
    try:
        print(f"正在读取Excel文件: {file_path}")
        df = pd.read_excel(file_path, sheet_name=0)
        
        print(f"数据维度: {df.shape[0]}行 × {df.shape[1]}列")
        print(f"列名: {df.columns.tolist()}")
        
        # 显示前几行数据以便分析结构
        print("\n=== 前5行关键列数据 ===")
        key_columns = ['章节编号', '章节名称', '小节编号', '小节名称', '一级指标编号', '一级指标名称', '二级指标编号', '二级指标名称']
        for i, row in df.head(5).iterrows():
            print(f"第{i+1}行:")
            for col in key_columns:
                if col in df.columns:
                    value = row[col]
                    if pd.notna(value):
                        print(f"  {col}: {value}")
            print()
        
        return df
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def process_chapter2_data(df):
    """
    处理第二章数据，提取小节和指标信息
    """
    sections = []
    indicators = []
    
    print("\n开始处理第二章数据...")
    
    for index, row in df.iterrows():
        try:
            # 提取小节信息
            section_number = row.get('小节编号', '')
            section_name = row.get('小节名称', '')
            
            # 提取各级指标信息
            level1_id = row.get('一级指标编号', '')
            level1_name = row.get('一级指标名称', '')
            level2_id = row.get('二级指标编号', '')
            level2_name = row.get('二级指标名称', '')
            level3_id = row.get('三级指标编号', '')
            level3_name = row.get('三级指标名称', '')
            
            # 处理小节数据
            if pd.notna(section_number) and pd.notna(section_name):
                section_code = convert_chinese_section_to_code(str(section_number).strip(), chapter_num=2)
                clean_section_name = clean_name(str(section_name).strip())
                
                if section_code and clean_section_name:
                    sections.append({
                        'code': section_code,
                        'name': clean_section_name,
                        'row': index + 1
                    })
                    print(f"发现小节: {section_code} - {clean_section_name}")
            
            # 处理一级指标
            if pd.notna(level1_id) and pd.notna(level1_name):
                level1_id_str = str(level1_id).strip()
                level1_name_str = str(level1_name).strip()
                
                if level1_id_str.startswith('2.'):
                    section_code = '.'.join(level1_id_str.split('.')[:2])
                    
                    indicators.append({
                        'id': level1_id_str,
                        'name': level1_name_str,
                        'level': 1,
                        'parent_id': None,
                        'section_code': section_code,
                        'row': index + 1
                    })
                    print(f"发现一级指标: {level1_id_str} - {level1_name_str}")
            
            # 处理二级指标
            if pd.notna(level2_id) and pd.notna(level2_name):
                level2_id_str = str(level2_id).strip()
                level2_name_str = str(level2_name).strip()
                
                if level2_id_str and level1_id_str:
                    section_code = '.'.join(level1_id_str.split('.')[:2])
                    
                    indicators.append({
                        'id': level2_id_str,
                        'name': level2_name_str,
                        'level': 2,
                        'parent_id': level1_id_str,
                        'section_code': section_code,
                        'row': index + 1
                    })
                    print(f"发现二级指标: {level2_id_str} - {level2_name_str} (父级: {level1_id_str})")
            
            # 处理三级指标
            if pd.notna(level3_id) and pd.notna(level3_name):
                level3_id_str = str(level3_id).strip()
                level3_name_str = str(level3_name).strip()
                
                if level3_id_str and level2_id_str:
                    section_code = '.'.join(level1_id_str.split('.')[:2])
                    
                    indicators.append({
                        'id': level3_id_str,
                        'name': level3_name_str,
                        'level': 3,
                        'parent_id': level2_id_str,
                        'section_code': section_code,
                        'row': index + 1
                    })
                    print(f"发现三级指标: {level3_id_str} - {level3_name_str} (父级: {level2_id_str})")
                    
        except Exception as e:
            print(f"处理第{index+1}行时出错: {e}")
            continue
    
    return sections, indicators

def convert_chinese_section_to_code(chinese_number, chapter_num=2):
    """
    将中文小节编号转换为数字编码
    例如：'一、' -> '2.1', '二、' -> '2.2' (第二章)
    """
    chinese_to_arabic = {
        '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
        '六': '6', '七': '7', '八': '8', '九': '9', '十': '10'
    }
    
    clean_number = chinese_number.replace('、', '').replace('，', '').replace('。', '').strip()
    
    if clean_number in chinese_to_arabic:
        return f"{chapter_num}.{chinese_to_arabic[clean_number]}"
    
    return None

def clean_name(name):
    """
    清理名称，去除括号内容
    """
    cleaned = re.sub(r'[（(].*?[）)]', '', name)
    return cleaned.strip()

def remove_duplicates(data_list, key_field):
    """
    去除重复数据
    """
    seen = set()
    unique_data = []
    
    for item in data_list:
        key = item[key_field]
        if key not in seen:
            seen.add(key)
            unique_data.append(item)
        else:
            print(f"发现重复{key_field}: {key}，已跳过")
    
    return unique_data

def insert_sections_to_db(sections, db_path):
    """
    将小节数据插入数据库
    """
    if not sections:
        print("没有小节数据需要插入")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取第二章的ID
        cursor.execute("SELECT id FROM chapters WHERE code = '2'")
        chapter_result = cursor.fetchone()
        if not chapter_result:
            print("错误：数据库中未找到第二章数据")
            return
        
        chapter_id = chapter_result[0]
        
        # 插入小节数据
        for i, section in enumerate(sections):
            cursor.execute("""
                INSERT OR REPLACE INTO sections 
                (chapter_id, code, name, description, sort_order, is_active, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            """, (
                chapter_id,
                section['code'],
                section['name'],
                f"第二章小节：{section['name']}",
                i + 1
            ))
            print(f"插入小节: {section['code']} - {section['name']}")
        
        conn.commit()
        print(f"成功插入 {len(sections)} 个小节")
        
    except Exception as e:
        print(f"插入小节数据时出错: {e}")
    finally:
        if conn:
            conn.close()

def insert_indicators_to_db(indicators, db_path):
    """
    将指标数据插入数据库
    """
    if not indicators:
        print("没有指标数据需要插入")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取第二章的ID
        cursor.execute("SELECT id FROM chapters WHERE code = '2'")
        chapter_result = cursor.fetchone()
        if not chapter_result:
            print("错误：数据库中未找到第二章数据")
            return
        
        chapter_id = chapter_result[0]
        
        # 清除现有的第二章指标数据
        cursor.execute("DELETE FROM indicators WHERE chapter_id = ?", (chapter_id,))
        print("已清除现有的第二章指标数据")
        
        # 按级别排序插入，确保父级指标先插入
        sorted_indicators = sorted(indicators, key=lambda x: (x['level'], x['id']))
        
        for i, indicator in enumerate(sorted_indicators):
            # 查找对应的小节ID
            cursor.execute("SELECT id FROM sections WHERE code = ? AND chapter_id = ?", 
                         (indicator['section_code'], chapter_id))
            section_result = cursor.fetchone()
            section_id = section_result[0] if section_result else None
            
            if not section_id:
                print(f"警告：未找到小节 {indicator['section_code']}，指标 {indicator['id']} 将不关联小节")
            
            # 插入指标数据
            cursor.execute("""
                INSERT OR REPLACE INTO indicators 
                (id, name, description, parent_id, chapter_id, section_id, category, sort_order, is_active, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            """, (
                indicator['id'],
                indicator['name'],
                f"第二章{indicator['level']}级指标：{indicator['name']}",
                indicator['parent_id'],
                chapter_id,
                section_id,
                '医疗服务能力与医院质量安全',
                i + 1
            ))
            
            level_text = f"{indicator['level']}级"
            parent_text = f" (父级: {indicator['parent_id']})" if indicator['parent_id'] else ""
            print(f"插入{level_text}指标: {indicator['id']} - {indicator['name']}{parent_text}")
        
        conn.commit()
        print(f"成功插入 {len(indicators)} 个多级指标")
        
    except Exception as e:
        print(f"插入指标数据时出错: {e}")
    finally:
        if conn:
            conn.close()

def main():
    file_path = "chapter2.xlsx"
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    print("=" * 80)
    print("🏥 医院等级评审指标管理系统 - 第二章数据处理")
    print("=" * 80)
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件 {file_path} 不存在")
        return
    
    if not os.path.exists(db_path):
        print(f"错误：数据库文件 {db_path} 不存在")
        return
    
    # 分析Excel文件结构
    df = analyze_excel_structure(file_path)
    if df is None:
        return
    
    # 处理第二章数据
    sections, indicators = process_chapter2_data(df)
    
    # 去重
    unique_sections = remove_duplicates(sections, 'code')
    unique_indicators = remove_duplicates(indicators, 'id')
    
    print(f"\n=== 处理结果 ===")
    print(f"提取到 {len(unique_sections)} 个唯一小节")
    print(f"提取到 {len(unique_indicators)} 个唯一指标")
    
    # 按级别统计指标
    level_stats = {}
    for indicator in unique_indicators:
        level = indicator['level']
        level_stats[level] = level_stats.get(level, 0) + 1
    
    print(f"\n=== 指标级别统计 ===")
    for level in sorted(level_stats.keys()):
        print(f"{level}级指标: {level_stats[level]}个")
    
    # 显示提取结果
    if unique_sections:
        print(f"\n=== 小节列表 ===")
        for section in unique_sections:
            print(f"{section['code']} - {section['name']}")
    
    if unique_indicators:
        print(f"\n=== 指标列表（按级别分组） ===")
        for level in sorted(set(ind['level'] for ind in unique_indicators)):
            level_indicators = [ind for ind in unique_indicators if ind['level'] == level]
            print(f"\n{level}级指标 ({len(level_indicators)}个):")
            for indicator in level_indicators:
                parent_text = f" (父级: {indicator['parent_id']})" if indicator['parent_id'] else ""
                print(f"  {indicator['id']} - {indicator['name']}{parent_text}")
    
    # 插入数据库
    if unique_sections or unique_indicators:
        print("\n=== 开始导入数据库 ===")
        insert_sections_to_db(unique_sections, db_path)
        insert_indicators_to_db(unique_indicators, db_path)
        print("第二章数据导入完成！")
    else:
        print("未找到有效的第二章数据")

if __name__ == "__main__":
    main()
