// 调试列表视图功能的脚本
// 在浏览器控制台中运行此脚本

console.log('=== 列表视图功能调试 ===');

// 检查DOM元素是否存在
const cardView = document.getElementById('cardView');
const listView = document.getElementById('listView');
const viewButtons = document.querySelectorAll('.view-btn');

console.log('DOM元素检查:');
console.log('cardView:', cardView ? '✅ 存在' : '❌ 不存在');
console.log('listView:', listView ? '✅ 存在' : '❌ 不存在');
console.log('viewButtons:', viewButtons.length > 0 ? `✅ 找到${viewButtons.length}个按钮` : '❌ 未找到按钮');

// 检查当前视图状态
if (cardView && listView) {
    console.log('\n当前视图状态:');
    console.log('cardView display:', window.getComputedStyle(cardView).display);
    console.log('listView display:', window.getComputedStyle(listView).display);
}

// 检查按钮状态
viewButtons.forEach((btn, index) => {
    console.log(`按钮${index + 1}:`, {
        view: btn.dataset.view,
        active: btn.classList.contains('active'),
        text: btn.textContent.trim()
    });
});

// 测试视图切换功能
function testViewSwitch() {
    console.log('\n=== 测试视图切换 ===');
    
    const listBtn = document.querySelector('[data-view="list"]');
    const cardBtn = document.querySelector('[data-view="card"]');
    
    if (listBtn && cardBtn) {
        console.log('点击列表视图按钮...');
        listBtn.click();
        
        setTimeout(() => {
            console.log('切换后状态:');
            console.log('cardView display:', window.getComputedStyle(cardView).display);
            console.log('listView display:', window.getComputedStyle(listView).display);
            console.log('listBtn active:', listBtn.classList.contains('active'));
            console.log('cardBtn active:', cardBtn.classList.contains('active'));
            
            // 切换回卡片视图
            setTimeout(() => {
                console.log('\n点击卡片视图按钮...');
                cardBtn.click();
                
                setTimeout(() => {
                    console.log('切换回卡片视图后状态:');
                    console.log('cardView display:', window.getComputedStyle(cardView).display);
                    console.log('listView display:', window.getComputedStyle(listView).display);
                    console.log('listBtn active:', listBtn.classList.contains('active'));
                    console.log('cardBtn active:', cardBtn.classList.contains('active'));
                }, 100);
            }, 1000);
        }, 100);
    } else {
        console.log('❌ 未找到视图切换按钮');
    }
}

// 检查app对象是否存在
if (typeof app !== 'undefined') {
    console.log('\n✅ app对象存在');
    console.log('app.switchView方法:', typeof app.switchView === 'function' ? '✅ 存在' : '❌ 不存在');
} else {
    console.log('\n❌ app对象不存在');
}

// 手动测试switchView方法
function manualTestSwitchView() {
    console.log('\n=== 手动测试switchView方法 ===');
    
    if (typeof app !== 'undefined' && typeof app.switchView === 'function') {
        console.log('测试切换到列表视图...');
        app.switchView('list');
        
        setTimeout(() => {
            console.log('列表视图状态:');
            console.log('cardView display:', window.getComputedStyle(cardView).display);
            console.log('listView display:', window.getComputedStyle(listView).display);
            
            setTimeout(() => {
                console.log('测试切换到卡片视图...');
                app.switchView('card');
                
                setTimeout(() => {
                    console.log('卡片视图状态:');
                    console.log('cardView display:', window.getComputedStyle(cardView).display);
                    console.log('listView display:', window.getComputedStyle(listView).display);
                }, 100);
            }, 1000);
        }, 100);
    } else {
        console.log('❌ 无法测试switchView方法');
    }
}

// 检查列表视图内容
function checkListViewContent() {
    console.log('\n=== 检查列表视图内容 ===');
    
    if (listView) {
        const listContainer = listView.querySelector('.list-container');
        const listHeader = listView.querySelector('.list-header');
        const listBody = listView.querySelector('.list-body');
        
        console.log('listContainer:', listContainer ? '✅ 存在' : '❌ 不存在');
        console.log('listHeader:', listHeader ? '✅ 存在' : '❌ 不存在');
        console.log('listBody:', listBody ? '✅ 存在' : '❌ 不存在');
        
        if (listBody) {
            const listItems = listBody.querySelectorAll('.list-item');
            console.log(`列表项数量: ${listItems.length}`);
        }
    }
}

// 运行所有检查
console.log('\n开始运行调试检查...');
checkListViewContent();

// 延迟运行测试，等待页面完全加载
setTimeout(() => {
    testViewSwitch();
    setTimeout(() => {
        manualTestSwitchView();
    }, 3000);
}, 1000);

console.log('\n=== 调试脚本加载完成 ===');
console.log('请在浏览器中查看控制台输出');
console.log('也可以手动点击"列表视图"按钮测试功能');

// 导出测试函数供手动调用
window.debugListView = {
    testViewSwitch,
    manualTestSwitchView,
    checkListViewContent
};
