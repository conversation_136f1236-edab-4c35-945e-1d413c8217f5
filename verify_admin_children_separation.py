#!/usr/bin/env python3
"""
后端管理界面子指标模块分离验证脚本
验证后端管理界面的子指标模块已与前端保持一致
"""

import requests
from bs4 import BeautifulSoup

def test_admin_html_structure():
    """测试后端管理界面HTML结构"""
    print("🧪 测试后端管理界面HTML结构...")
    
    try:
        with open('templates/admin/indicator_detail.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 检查子指标卡片结构
        if 'id="childrenNavCard"' in html_content:
            print("✅ 后端子指标导航卡片存在")
            
            # 检查是否使用info-card类
            if 'class="info-card" id="childrenNavCard"' in html_content:
                print("✅ 后端子指标模块使用info-card样式")
                
                # 检查是否有折叠功能
                if 'toggleChildrenCollapse' in html_content:
                    print("✅ 后端子指标模块支持折叠功能")
                    
                    # 检查是否有数量徽章
                    if 'children-count' in html_content:
                        print("✅ 后端子指标模块包含数量徽章")
                        
                        # 检查是否有导航网格
                        if 'children-nav-grid' in html_content:
                            print("✅ 后端子指标模块包含导航网格")
                            return True
                        else:
                            print("❌ 后端子指标模块缺少导航网格")
                            return False
                    else:
                        print("❌ 后端子指标模块缺少数量徽章")
                        return False
                else:
                    print("❌ 后端子指标模块缺少折叠功能")
                    return False
            else:
                print("❌ 后端子指标模块未使用info-card样式")
                return False
        else:
            print("❌ 后端子指标导航卡片不存在")
            return False
            
    except FileNotFoundError:
        print("❌ 后端模板文件不存在")
        return False
    except Exception as e:
        print(f"❌ 读取后端模板文件失败: {e}")
        return False

def test_admin_css_styles():
    """测试后端管理界面CSS样式"""
    print("\n🧪 测试后端管理界面CSS样式...")
    
    try:
        with open('templates/admin/indicator_detail.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 检查子指标相关样式
        required_styles = [
            '.children-count',
            '.children-nav-grid',
            '.child-nav-item',
            '.child-nav-info',
            '.child-nav-id',
            '.child-nav-name',
            '.child-nav-arrow'
        ]
        
        missing_styles = []
        for style in required_styles:
            if style not in html_content:
                missing_styles.append(style)
        
        if not missing_styles:
            print("✅ 后端所有必需的CSS样式都存在")
            return True
        else:
            print(f"❌ 后端缺少CSS样式: {', '.join(missing_styles)}")
            return False
            
    except FileNotFoundError:
        print("❌ 后端模板文件不存在")
        return False
    except Exception as e:
        print(f"❌ 读取后端模板文件失败: {e}")
        return False

def test_admin_javascript_logic():
    """测试后端管理界面JavaScript逻辑"""
    print("\n🧪 测试后端管理界面JavaScript逻辑...")
    
    try:
        with open('templates/admin/indicator_detail.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 检查关键JavaScript函数
        required_functions = [
            'renderChildrenTable',
            'toggleChildrenCollapse',
            'updateComponentsVisibility'
        ]
        
        missing_functions = []
        for func in required_functions:
            if f'function {func}' not in html_content:
                missing_functions.append(func)
        
        if not missing_functions:
            print("✅ 后端所有必需的JavaScript函数都存在")
            
            # 检查简单指标隐藏逻辑
            if "indicatorType === 'simple'" in html_content and 'childrenCard.style.display' in html_content:
                print("✅ 后端包含简单指标隐藏逻辑")
                
                # 检查卡片式渲染逻辑
                if 'child-nav-item' in html_content and 'child-nav-info' in html_content:
                    print("✅ 后端包含卡片式渲染逻辑")
                    return True
                else:
                    print("❌ 后端缺少卡片式渲染逻辑")
                    return False
            else:
                print("❌ 后端缺少简单指标隐藏逻辑")
                return False
        else:
            print(f"❌ 后端缺少JavaScript函数: {', '.join(missing_functions)}")
            return False
            
    except FileNotFoundError:
        print("❌ 后端模板文件不存在")
        return False
    except Exception as e:
        print(f"❌ 读取后端模板文件失败: {e}")
        return False

def test_frontend_backend_consistency():
    """测试前后端一致性"""
    print("\n🧪 测试前后端一致性...")
    
    try:
        # 读取前端模板
        with open('templates/base.html', 'r', encoding='utf-8') as f:
            frontend_content = f.read()
        
        # 读取后端模板
        with open('templates/admin/indicator_detail.html', 'r', encoding='utf-8') as f:
            backend_content = f.read()
        
        # 检查关键元素一致性
        consistency_checks = [
            ('childrenNavCard', '子指标导航卡片ID'),
            ('children-count', '子指标数量徽章'),
            ('children-nav-grid', '子指标导航网格'),
            ('child-nav-item', '子指标导航项'),
            ('child-nav-info', '子指标信息区域'),
            ('child-nav-id', '子指标ID'),
            ('child-nav-name', '子指标名称'),
            ('child-nav-arrow', '子指标箭头')
        ]
        
        inconsistent_elements = []
        for element, description in consistency_checks:
            frontend_has = element in frontend_content
            backend_has = element in backend_content
            
            if frontend_has != backend_has:
                inconsistent_elements.append(f"{description}({element})")
        
        if not inconsistent_elements:
            print("✅ 前后端子指标模块结构完全一致")
            return True
        else:
            print(f"❌ 前后端不一致的元素: {', '.join(inconsistent_elements)}")
            return False
            
    except FileNotFoundError:
        print("❌ 模板文件不存在")
        return False
    except Exception as e:
        print(f"❌ 读取模板文件失败: {e}")
        return False

def test_api_integration():
    """测试API集成"""
    print("\n🧪 测试API集成...")
    
    try:
        # 测试有子指标的指标
        response = requests.get('http://localhost:5001/api/indicators/1.3.1')
        data = response.json()
        
        if data['success']:
            indicator = data['data']['indicator']
            children = data['data']['children']
            
            print(f"✅ API响应正常")
            print(f"   指标: {indicator['id']} - {indicator['name']}")
            print(f"   指标类型: {indicator.get('indicator_type', 'composite')}")
            print(f"   子指标数量: {len(children)}")
            
            # 检查指标类型对子指标显示的影响
            if indicator.get('indicator_type') == 'simple' and len(children) > 0:
                print("⚠️  简单指标不应该有子指标")
                return False
            
            return True
        else:
            print(f"❌ API请求失败: {data.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 后端管理界面子指标模块分离验证")
    print("=" * 60)
    
    tests = [
        ("后端HTML结构测试", test_admin_html_structure),
        ("后端CSS样式测试", test_admin_css_styles),
        ("后端JavaScript逻辑测试", test_admin_javascript_logic),
        ("前后端一致性测试", test_frontend_backend_consistency),
        ("API集成测试", test_api_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！后端管理界面子指标模块已成功分离并与前端保持一致")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    main()
