#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为所有缺失分子分母的指标添加默认信息
"""

import sqlite3

def get_missing_indicators():
    """
    获取所有缺失分子分母的指标
    """
    conn = sqlite3.connect("DATABASE-HOSPITAL/hospital_indicator_system.db")
    cursor = conn.cursor()
    
    # 查找没有分子分母的指标
    cursor.execute("""
        SELECT i.id, i.name, i.category
        FROM indicators i
        LEFT JOIN indicator_components ic ON i.id = ic.indicator_id
        WHERE ic.id IS NULL
        ORDER BY i.id
    """)
    
    missing_indicators = cursor.fetchall()
    conn.close()
    
    return missing_indicators

def add_default_components_for_indicator(indicator_id, indicator_name, category):
    """
    为指定指标添加默认的分子分母信息
    """
    
    # 根据指标类型和名称推断分子分母
    if '率' in indicator_name or '比例' in indicator_name or '比' in indicator_name:
        # 比率类指标
        if '床位' in indicator_name:
            numerator = {
                'name': f'{indicator_name}相关事件数',
                'unit': '次/人/张',
                'lead_department': '医务科',
                'data_source': '医院信息系统',
                'logic_definition': f'统计期内与{indicator_name}相关的事件或人员数量'
            }
            denominator = {
                'name': '总床位数或总人数',
                'unit': '张/人',
                'lead_department': '医务科',
                'data_source': '医院信息系统',
                'logic_definition': '统计期内相应的总数量'
            }
        elif '人员' in indicator_name or '医师' in indicator_name or '护士' in indicator_name:
            numerator = {
                'name': f'符合条件的{indicator_name.replace("比例", "").replace("比", "")}人数',
                'unit': '人',
                'lead_department': '人事科',
                'data_source': '人事管理系统',
                'logic_definition': f'统计时点符合{indicator_name}条件的人员数量'
            }
            denominator = {
                'name': '相应总人数',
                'unit': '人',
                'lead_department': '人事科',
                'data_source': '人事管理系统',
                'logic_definition': '统计时点相应类别的总人数'
            }
        else:
            numerator = {
                'name': f'{indicator_name}分子',
                'unit': '个',
                'lead_department': '相关科室',
                'data_source': '医院信息系统',
                'logic_definition': f'{indicator_name}的分子部分'
            }
            denominator = {
                'name': f'{indicator_name}分母',
                'unit': '个',
                'lead_department': '相关科室',
                'data_source': '医院信息系统',
                'logic_definition': f'{indicator_name}的分母部分'
            }
    elif '数' in indicator_name or '量' in indicator_name:
        # 数量类指标
        numerator = {
            'name': f'实际{indicator_name}',
            'unit': '个/人/张',
            'lead_department': '相关科室',
            'data_source': '医院信息系统',
            'logic_definition': f'统计期内实际的{indicator_name}'
        }
        denominator = {
            'name': '标准或目标数量',
            'unit': '个/人/张',
            'lead_department': '相关科室',
            'data_source': '医院标准',
            'logic_definition': f'相应的标准或目标{indicator_name}'
        }
    else:
        # 其他类型指标
        numerator = {
            'name': f'{indicator_name}实际值',
            'unit': '个',
            'lead_department': '相关科室',
            'data_source': '医院信息系统',
            'logic_definition': f'{indicator_name}的实际测量值'
        }
        denominator = {
            'name': f'{indicator_name}基准值',
            'unit': '个',
            'lead_department': '相关科室',
            'data_source': '医院标准',
            'logic_definition': f'{indicator_name}的基准或标准值'
        }
    
    return numerator, denominator

def add_components_to_database(indicator_id, numerator, denominator):
    """
    将分子分母信息添加到数据库
    """
    conn = sqlite3.connect("DATABASE-HOSPITAL/hospital_indicator_system.db")
    cursor = conn.cursor()
    
    try:
        # 添加分子
        cursor.execute("""
            INSERT INTO indicator_components 
            (indicator_id, component_type, name, definition, unit, data_source, lead_department, logic_definition, sort_order, is_active, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
        """, (
            indicator_id,
            'numerator',
            numerator['name'],
            numerator['name'],
            numerator['unit'],
            numerator['data_source'],
            numerator['lead_department'],
            numerator['logic_definition'],
            1
        ))
        
        # 添加分母
        cursor.execute("""
            INSERT INTO indicator_components 
            (indicator_id, component_type, name, definition, unit, data_source, lead_department, logic_definition, sort_order, is_active, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
        """, (
            indicator_id,
            'denominator',
            denominator['name'],
            denominator['name'],
            denominator['unit'],
            denominator['data_source'],
            denominator['lead_department'],
            denominator['logic_definition'],
            2
        ))
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ 为指标 {indicator_id} 添加分子分母时出错: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def main():
    print("=" * 80)
    print("🔧 为所有缺失分子分母的指标添加默认信息")
    print("=" * 80)
    
    # 获取缺失分子分母的指标
    missing_indicators = get_missing_indicators()
    
    if not missing_indicators:
        print("✅ 所有指标都已有分子分母信息")
        return
    
    print(f"📊 找到 {len(missing_indicators)} 个缺失分子分母的指标")
    
    success_count = 0
    
    for indicator_id, indicator_name, category in missing_indicators:
        print(f"\n🔧 处理指标: {indicator_id} - {indicator_name}")
        
        # 生成默认分子分母
        numerator, denominator = add_default_components_for_indicator(
            indicator_id, indicator_name, category
        )
        
        # 添加到数据库
        if add_components_to_database(indicator_id, numerator, denominator):
            print(f"   ✅ 成功添加分子分母")
            print(f"   📊 分子: {numerator['name']}")
            print(f"   📊 分母: {denominator['name']}")
            success_count += 1
        else:
            print(f"   ❌ 添加失败")
    
    print(f"\n" + "=" * 80)
    print(f"✅ 成功为 {success_count}/{len(missing_indicators)} 个指标添加了分子分母信息")
    
    # 最终验证
    remaining_missing = get_missing_indicators()
    if remaining_missing:
        print(f"⚠️  仍有 {len(remaining_missing)} 个指标缺失分子分母:")
        for indicator_id, indicator_name, category in remaining_missing:
            print(f"   - {indicator_id}: {indicator_name}")
    else:
        print("🎉 所有指标现在都有分子分母信息了！")

if __name__ == "__main__":
    main()
