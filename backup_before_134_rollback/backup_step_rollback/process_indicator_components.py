#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理chapter1.xlsx文件中的分子、分母等详细信息
"""

import pandas as pd
import sqlite3
import re
import sys
import os

def analyze_excel_columns(file_path):
    """
    分析Excel文件的所有列，找出分子、分母相关的列
    """
    try:
        print(f"正在分析Excel文件列结构: {file_path}")
        df = pd.read_excel(file_path, sheet_name=0)
        
        print(f"数据维度: {df.shape[0]}行 × {df.shape[1]}列")
        print(f"\n所有列名:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1:2d}. {col}")
        
        # 查找包含分子、分母、单位等关键词的列
        component_columns = {}
        for col in df.columns:
            col_lower = str(col).lower()
            if '分子' in col or 'numerator' in col_lower:
                component_columns['numerator'] = component_columns.get('numerator', []) + [col]
            elif '分母' in col or 'denominator' in col_lower:
                component_columns['denominator'] = component_columns.get('denominator', []) + [col]
            elif '单位' in col or 'unit' in col_lower:
                component_columns['unit'] = component_columns.get('unit', []) + [col]
            elif '科室' in col or 'department' in col_lower:
                component_columns['department'] = component_columns.get('department', []) + [col]
            elif '数据来源' in col or 'source' in col_lower:
                component_columns['source'] = component_columns.get('source', []) + [col]
            elif '定义' in col or 'definition' in col_lower:
                component_columns['definition'] = component_columns.get('definition', []) + [col]
        
        print(f"\n发现的组成部分相关列:")
        for key, cols in component_columns.items():
            print(f"  {key}: {cols}")
        
        return df, component_columns
        
    except Exception as e:
        print(f"分析Excel文件时出错: {e}")
        return None, None

def process_indicator_components(df, component_columns):
    """
    处理指标的分子、分母等组成部分
    """
    components = []
    indicator_attributes = []
    
    print("\n开始处理指标组成部分...")
    
    for index, row in df.iterrows():
        try:
            # 获取指标ID
            indicator_ids = []
            
            # 检查各级指标ID
            for level in ['一级指标编号', '二级指标编号', '三级指标编号']:
                if level in df.columns:
                    indicator_id = row.get(level, '')
                    if pd.notna(indicator_id) and str(indicator_id).strip():
                        indicator_ids.append(str(indicator_id).strip())
            
            # 处理每个指标ID
            for indicator_id in indicator_ids:
                if not indicator_id.startswith('1.'):
                    continue
                
                # 处理分子信息
                if 'numerator' in component_columns:
                    for col in component_columns['numerator']:
                        numerator_value = row.get(col, '')
                        if pd.notna(numerator_value) and str(numerator_value).strip():
                            components.append({
                                'indicator_id': indicator_id,
                                'component_type': 'numerator',
                                'name': f"{indicator_id}分子",
                                'definition': str(numerator_value).strip(),
                                'row': index + 1,
                                'column': col
                            })
                            print(f"发现分子: {indicator_id} - {str(numerator_value)[:50]}...")
                
                # 处理分母信息
                if 'denominator' in component_columns:
                    for col in component_columns['denominator']:
                        denominator_value = row.get(col, '')
                        if pd.notna(denominator_value) and str(denominator_value).strip():
                            components.append({
                                'indicator_id': indicator_id,
                                'component_type': 'denominator',
                                'name': f"{indicator_id}分母",
                                'definition': str(denominator_value).strip(),
                                'row': index + 1,
                                'column': col
                            })
                            print(f"发现分母: {indicator_id} - {str(denominator_value)[:50]}...")
                
                # 处理指标属性
                attributes = {
                    'indicator_id': indicator_id,
                    'unit': '',
                    'data_source': '',
                    'lead_department': '',
                    'calculation_formula': ''
                }
                
                # 提取单位
                if 'unit' in component_columns:
                    for col in component_columns['unit']:
                        unit_value = row.get(col, '')
                        if pd.notna(unit_value) and str(unit_value).strip():
                            attributes['unit'] = str(unit_value).strip()
                            break
                
                # 提取数据来源
                if 'source' in component_columns:
                    for col in component_columns['source']:
                        source_value = row.get(col, '')
                        if pd.notna(source_value) and str(source_value).strip():
                            attributes['data_source'] = str(source_value).strip()
                            break
                
                # 提取牵头科室
                if 'department' in component_columns:
                    for col in component_columns['department']:
                        dept_value = row.get(col, '')
                        if pd.notna(dept_value) and str(dept_value).strip():
                            attributes['lead_department'] = str(dept_value).strip()
                            break
                
                # 如果有任何属性信息，就添加到列表中
                if any(attributes[key] for key in ['unit', 'data_source', 'lead_department']):
                    indicator_attributes.append(attributes)
                    print(f"提取属性: {indicator_id} - 单位:{attributes['unit']}, 来源:{attributes['data_source'][:30]}...")
                    
        except Exception as e:
            print(f"处理第{index+1}行时出错: {e}")
            continue
    
    return components, indicator_attributes

def insert_components_to_db(components, db_path):
    """
    将组成部分数据插入数据库
    """
    if not components:
        print("没有组成部分数据需要插入")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 清除现有的组成部分数据
        cursor.execute("DELETE FROM indicator_components WHERE indicator_id LIKE '1.%'")
        print("已清除现有的第一章组成部分数据")
        
        # 插入新的组成部分数据
        for i, component in enumerate(components):
            cursor.execute("""
                INSERT INTO indicator_components 
                (indicator_id, component_type, name, definition, sort_order, is_active, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            """, (
                component['indicator_id'],
                component['component_type'],
                component['name'],
                component['definition'],
                i + 1
            ))
            
            comp_type = "分子" if component['component_type'] == 'numerator' else "分母"
            print(f"插入{comp_type}: {component['indicator_id']} - {component['definition'][:50]}...")
        
        conn.commit()
        print(f"成功插入 {len(components)} 个组成部分")
        
    except Exception as e:
        print(f"插入组成部分数据时出错: {e}")
    finally:
        if conn:
            conn.close()

def insert_attributes_to_db(attributes, db_path):
    """
    将指标属性数据插入数据库
    """
    if not attributes:
        print("没有指标属性数据需要插入")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 清除现有的属性数据
        cursor.execute("DELETE FROM indicator_attributes WHERE indicator_id LIKE '1.%'")
        print("已清除现有的第一章属性数据")
        
        # 插入新的属性数据
        for attribute in attributes:
            cursor.execute("""
                INSERT OR REPLACE INTO indicator_attributes 
                (indicator_id, unit, data_collection_cycle, created_at, updated_at) 
                VALUES (?, ?, ?, datetime('now'), datetime('now'))
            """, (
                attribute['indicator_id'],
                attribute['unit'],
                attribute['data_source']
            ))
            
            print(f"插入属性: {attribute['indicator_id']} - 单位:{attribute['unit']}")
        
        conn.commit()
        print(f"成功插入 {len(attributes)} 个指标属性")
        
    except Exception as e:
        print(f"插入指标属性数据时出错: {e}")
    finally:
        if conn:
            conn.close()

def show_sample_data(df, component_columns):
    """
    显示样本数据以便调试
    """
    print(f"\n=== 样本数据展示 ===")
    
    # 显示前3行的关键列数据
    key_columns = ['一级指标编号', '一级指标名称', '二级指标编号', '二级指标名称']
    
    # 添加组成部分相关列
    for comp_type, cols in component_columns.items():
        key_columns.extend(cols[:2])  # 只取前2个相关列
    
    for i, row in df.head(3).iterrows():
        print(f"\n第{i+1}行样本数据:")
        for col in key_columns:
            if col in df.columns:
                value = row[col]
                if pd.notna(value):
                    print(f"  {col}: {str(value)[:100]}...")

def main():
    file_path = "chapter1.xlsx"
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件 {file_path} 不存在")
        return
    
    if not os.path.exists(db_path):
        print(f"错误：数据库文件 {db_path} 不存在")
        return
    
    # 分析Excel文件结构
    df, component_columns = analyze_excel_columns(file_path)
    if df is None:
        return
    
    # 显示样本数据
    show_sample_data(df, component_columns)
    
    # 处理组成部分数据
    components, attributes = process_indicator_components(df, component_columns)
    
    print(f"\n=== 处理结果 ===")
    print(f"提取到 {len(components)} 个组成部分")
    print(f"提取到 {len(attributes)} 个指标属性")
    
    # 按类型统计组成部分
    comp_stats = {}
    for comp in components:
        comp_type = comp['component_type']
        comp_stats[comp_type] = comp_stats.get(comp_type, 0) + 1
    
    print(f"\n=== 组成部分统计 ===")
    for comp_type, count in comp_stats.items():
        type_name = "分子" if comp_type == 'numerator' else "分母" if comp_type == 'denominator' else comp_type
        print(f"{type_name}: {count}个")
    
    # 插入数据库
    if components or attributes:
        print("\n=== 开始导入数据库 ===")
        insert_components_to_db(components, db_path)
        insert_attributes_to_db(attributes, db_path)
        print("组成部分数据导入完成！")
    else:
        print("未找到有效的组成部分数据")

if __name__ == "__main__":
    main()
