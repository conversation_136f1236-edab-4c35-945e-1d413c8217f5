
// 模态框功能测试
function testModalFunctionality() {
    console.log('🧪 开始测试模态框功能...');
    
    // 测试安全函数
    const testResults = {
        safeSetTextContent: typeof safeSetTextContent === 'function',
        safeGetElement: typeof safeGetElement === 'function',
        handleModalError: typeof handleModalError === 'function',
        safeShowModal: typeof safeShowModal === 'function',
        safeHideModal: typeof safeHideModal === 'function'
    };
    
    console.log('📊 测试结果:', testResults);
    
    // 测试DOM元素访问
    const testElements = [
        'notification',
        'indicatorModal',
        'modalIndicatorTitle',
        'modalIndicatorBadge'
    ];
    
    console.log('🔍 测试DOM元素:');
    testElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        console.log(`  ${elementId}: ${element ? '✅ 存在' : '❌ 不存在'}`);
    });
    
    return testResults;
}

// 页面加载完成后自动测试
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(testModalFunctionality, 1000);
});
