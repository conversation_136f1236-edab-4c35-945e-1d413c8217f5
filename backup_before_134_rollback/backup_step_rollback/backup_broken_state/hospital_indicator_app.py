#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院等级评审指标说明程序 - Flask Web应用
基于Google风格设计的完整指标管理系统
"""

from flask import Flask, render_template, jsonify, request, send_from_directory, flash, redirect
import sqlite3
import json
import os
from datetime import datetime

app = Flask(__name__)
app.config['SECRET_KEY'] = 'hospital_indicator_system_2024'
app.config['JSON_AS_ASCII'] = False  # 确保JSON输出中文正常
app.config['JSONIFY_MIMETYPE'] = 'application/json; charset=utf-8'

# 数据库路径
DB_PATH = 'DATABASE-HOSPITAL/hospital_indicator_system.db'

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def dict_from_row(row):
    """将sqlite3.Row转换为字典"""
    return dict(zip(row.keys(), row)) if row else None

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/test-modal')
def test_modal():
    """测试模态框页面"""
    return send_from_directory('.', 'test_modal.html')

@app.route('/test/children-separation')
def test_children_separation():
    """子指标模块分离测试页面"""
    return render_template('test_children_separation.html')

@app.route('/api/chapters')
def get_chapters():
    """获取所有章节"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT c.*,
                   COUNT(DISTINCT s.id) as section_count,
                   COUNT(DISTINCT i.id) as indicator_count,
                   COUNT(DISTINCT ic.id) as component_count
            FROM chapters c
            LEFT JOIN sections s ON c.id = s.chapter_id
            LEFT JOIN indicators i ON c.id = i.chapter_id
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id
            GROUP BY c.id
            ORDER BY c.code
        """)

        chapters = []
        for row in cursor.fetchall():
            chapter = dict_from_row(row)
            chapters.append(chapter)

        conn.close()
        return jsonify({'success': True, 'data': chapters})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/chapters/<chapter_code>/sections')
def get_chapter_sections(chapter_code):
    """获取章节的所有小节"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT s.*, COUNT(i.id) as indicator_count
            FROM sections s
            LEFT JOIN indicators i ON s.id = i.section_id
            WHERE s.chapter_id = (SELECT id FROM chapters WHERE code = ?)
            GROUP BY s.id
            ORDER BY s.sort_order
        """, (chapter_code,))

        sections = []
        for row in cursor.fetchall():
            section = dict_from_row(row)
            sections.append(section)

        conn.close()
        return jsonify({'success': True, 'data': sections})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/sections')
def get_sections():
    """获取节信息（支持按章节筛选）"""
    try:
        chapter_code = request.args.get('chapter')

        conn = get_db_connection()
        cursor = conn.cursor()

        if chapter_code:
            cursor.execute("""
                SELECT s.*, COUNT(i.id) as indicator_count
                FROM sections s
                LEFT JOIN indicators i ON s.id = i.section_id
                WHERE s.chapter_id = (SELECT id FROM chapters WHERE code = ?)
                GROUP BY s.id
                ORDER BY s.sort_order
            """, (chapter_code,))
        else:
            cursor.execute("""
                SELECT s.*, COUNT(i.id) as indicator_count
                FROM sections s
                LEFT JOIN indicators i ON s.id = i.section_id
                GROUP BY s.id
                ORDER BY s.chapter_id, s.sort_order
            """)

        sections = []
        for row in cursor.fetchall():
            section = dict_from_row(row)
            sections.append(section)

        conn.close()
        return jsonify({'success': True, 'data': sections})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/indicators')
def get_indicators():
    """获取指标列表"""
    try:
        chapter_code = request.args.get('chapter')
        section_code = request.args.get('section')
        search = request.args.get('search', '')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))

        conn = get_db_connection()
        cursor = conn.cursor()

        # 构建查询条件
        where_conditions = []
        params = []

        if chapter_code:
            where_conditions.append("c.code = ?")
            params.append(chapter_code)

        if section_code:
            where_conditions.append("s.code = ?")
            params.append(section_code)

        if search:
            where_conditions.append("(i.name LIKE ? OR i.description LIKE ?)")
            params.extend([f"%{search}%", f"%{search}%"])

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # 获取总数
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM indicators i
            LEFT JOIN sections s ON i.section_id = s.id
            LEFT JOIN chapters c ON i.chapter_id = c.id
            WHERE {where_clause}
        """, params)

        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT i.*, s.name as section_name, s.code as section_code,
                   c.name as chapter_name, c.code as chapter_code,
                   COUNT(DISTINCT ic.id) as component_count,
                   (SELECT COUNT(*) FROM indicators children WHERE children.parent_id = i.id AND children.is_active = 1) as children_count
            FROM indicators i
            LEFT JOIN sections s ON i.section_id = s.id
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id
            WHERE {where_clause}
            GROUP BY i.id
            ORDER BY i.id
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])

        indicators = []
        for row in cursor.fetchall():
            indicator = dict_from_row(row)
            indicators.append(indicator)

        conn.close()

        return jsonify({
            'success': True,
            'data': indicators,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/indicators/<indicator_id>')
def get_indicator_detail(indicator_id):
    """获取指标详情"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 确保indicators表有必要的字段
        cursor.execute("""
            PRAGMA table_info(indicators)
        """)
        columns = [row[1] for row in cursor.fetchall()]

        # 添加缺少的字段
        if 'unit' not in columns:
            cursor.execute("ALTER TABLE indicators ADD COLUMN unit VARCHAR(50)")
        if 'lead_department' not in columns:
            cursor.execute("ALTER TABLE indicators ADD COLUMN lead_department VARCHAR(100)")
        if 'logic_definition' not in columns:
            cursor.execute("ALTER TABLE indicators ADD COLUMN logic_definition TEXT")
        if 'indicator_type' not in columns:
            cursor.execute("ALTER TABLE indicators ADD COLUMN indicator_type VARCHAR(20) DEFAULT 'composite'")
        if 'reference_range' not in columns:
            cursor.execute("ALTER TABLE indicators ADD COLUMN reference_range TEXT")

        # 添加分子分母详细字段
        if 'numerator_unit' not in columns:
            cursor.execute("ALTER TABLE indicators ADD COLUMN numerator_unit VARCHAR(50)")
        if 'numerator_department' not in columns:
            cursor.execute("ALTER TABLE indicators ADD COLUMN numerator_department VARCHAR(100)")
        if 'numerator_source' not in columns:
            cursor.execute("ALTER TABLE indicators ADD COLUMN numerator_source VARCHAR(200)")
        if 'numerator_logic' not in columns:
            cursor.execute("ALTER TABLE indicators ADD COLUMN numerator_logic TEXT")
        if 'denominator_unit' not in columns:
            cursor.execute("ALTER TABLE indicators ADD COLUMN denominator_unit VARCHAR(50)")
        if 'denominator_department' not in columns:
            cursor.execute("ALTER TABLE indicators ADD COLUMN denominator_department VARCHAR(100)")
        if 'denominator_source' not in columns:
            cursor.execute("ALTER TABLE indicators ADD COLUMN denominator_source VARCHAR(200)")
        if 'denominator_logic' not in columns:
            cursor.execute("ALTER TABLE indicators ADD COLUMN denominator_logic TEXT")

        # 获取指标基本信息（包含新增的详细字段）
        cursor.execute("""
            SELECT i.*, s.name as section_name, s.code as section_code,
                   c.name as chapter_name, c.code as chapter_code, c.icon, c.color
            FROM indicators i
            LEFT JOIN sections s ON i.section_id = s.id
            LEFT JOIN chapters c ON i.chapter_id = c.id
            WHERE i.id = ?
        """, (indicator_id,))

        indicator = dict_from_row(cursor.fetchone())
        if not indicator:
            return jsonify({'success': False, 'error': '指标不存在'})

        # 获取分子分母信息
        cursor.execute("""
            SELECT * FROM indicator_components
            WHERE indicator_id = ?
            ORDER BY component_type, sort_order
        """, (indicator_id,))

        components = []
        for row in cursor.fetchall():
            component = dict_from_row(row)
            components.append(component)

        # 获取子指标（包含组件数量）
        cursor.execute("""
            SELECT i.*, COUNT(ic.id) as component_count
            FROM indicators i
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id
            WHERE i.parent_id = ?
            GROUP BY i.id
            ORDER BY i.sort_order
        """, (indicator_id,))

        children = []
        for row in cursor.fetchall():
            child = dict_from_row(row)
            children.append(child)

        # 获取父指标
        parent = None
        if indicator['parent_id']:
            cursor.execute("""
                SELECT * FROM indicators
                WHERE id = ?
            """, (indicator['parent_id'],))

            parent_row = cursor.fetchone()
            if parent_row:
                parent = dict_from_row(parent_row)

        # 获取分析维度数据
        analysis_dimensions = []
        try:
            cursor.execute("""
                SELECT dimension_name, analysis_content, sort_order
                FROM analysis_dimensions
                WHERE indicator_id = ?
                ORDER BY sort_order, id
            """, (indicator_id,))

            for row in cursor.fetchall():
                dimension = dict_from_row(row)
                analysis_dimensions.append(dimension)
        except Exception as e:
            # 如果表不存在或查询失败，忽略错误
            print(f"Warning: Could not fetch analysis dimensions: {e}")

        # 将分析维度添加到指标数据中
        if analysis_dimensions:
            indicator['analysis_dimensions'] = analysis_dimensions

        # 获取参考范围数据
        reference_range = None
        try:
            cursor.execute("""
                SELECT * FROM indicator_reference_ranges
                WHERE indicator_id = ? AND is_active = 1
                ORDER BY created_at DESC
                LIMIT 1
            """, (indicator_id,))

            reference_row = cursor.fetchone()
            if reference_row:
                reference_range = dict_from_row(reference_row)
        except Exception as e:
            # 如果表不存在或查询失败，忽略错误
            print(f"Warning: Could not fetch reference range: {e}")

        # 将参考范围添加到指标数据中
        if reference_range:
            indicator['reference_range_data'] = reference_range

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'indicator': indicator,
                'components': components,
                'children': children,
                'parent': parent
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/search')
def search_indicators():
    """搜索指标"""
    try:
        query = request.args.get('q', '').strip()
        if not query:
            return jsonify({'success': True, 'data': []})

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT i.id, i.name, i.description, s.name as section_name, c.name as chapter_name
            FROM indicators i
            LEFT JOIN sections s ON i.section_id = s.id
            LEFT JOIN chapters c ON i.chapter_id = c.id
            WHERE i.name LIKE ? OR i.description LIKE ? OR i.id LIKE ?
            ORDER BY
                CASE
                    WHEN i.id LIKE ? THEN 1
                    WHEN i.name LIKE ? THEN 2
                    ELSE 3
                END,
                i.id
            LIMIT 20
        """, (f"%{query}%", f"%{query}%", f"%{query}%", f"{query}%", f"{query}%"))

        results = []
        for row in cursor.fetchall():
            result = dict_from_row(row)
            results.append(result)

        conn.close()
        return jsonify({'success': True, 'data': results})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/indicators/batch-update', methods=['POST'])
def batch_update_indicators():
    """批量更新指标"""
    try:
        data = request.get_json()
        indicator_ids = data.get('indicator_ids', [])
        update_data = data.get('update_data', {})

        if not indicator_ids:
            return jsonify({'success': False, 'error': '未选择要更新的指标'})

        if not update_data:
            return jsonify({'success': False, 'error': '未提供更新数据'})

        conn = get_db_connection()
        cursor = conn.cursor()

        # 构建更新SQL
        update_fields = []
        update_values = []

        for field, value in update_data.items():
            if field in ['indicator_type', 'category', 'lead_department', 'data_source', 'unit', 'logic_definition', 'reference_range']:
                update_fields.append(f'{field} = ?')
                update_values.append(value)

        if not update_fields:
            return jsonify({'success': False, 'error': '没有有效的更新字段'})

        # 添加更新时间
        update_fields.append('updated_at = CURRENT_TIMESTAMP')

        # 构建WHERE子句
        placeholders = ','.join(['?' for _ in indicator_ids])

        # 执行批量更新
        sql = f"""
            UPDATE indicators
            SET {', '.join(update_fields)}
            WHERE id IN ({placeholders})
        """

        cursor.execute(sql, update_values + indicator_ids)
        updated_count = cursor.rowcount

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'批量更新成功，共更新了 {updated_count} 个指标',
            'updated_count': updated_count
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/indicators/<indicator_id>/components/<int:component_id>', methods=['PUT'])
def update_component(indicator_id, component_id):
    """更新指标分子分母信息"""
    try:
        data = request.get_json()

        conn = get_db_connection()
        cursor = conn.cursor()

        # 验证组件是否存在且属于该指标
        cursor.execute("""
            SELECT id FROM indicator_components
            WHERE id = ? AND indicator_id = ?
        """, (component_id, indicator_id))

        if not cursor.fetchone():
            return jsonify({'success': False, 'error': '组件不存在或不属于该指标'})

        # 更新组件信息
        cursor.execute("""
            UPDATE indicator_components
            SET name = ?, definition = ?, unit = ?, data_source = ?,
                lead_department = ?, logic_definition = ?, notes = ?,
                updated_at = datetime('now')
            WHERE id = ? AND indicator_id = ?
        """, (
            data.get('name', ''),
            data.get('definition', ''),
            data.get('unit', ''),
            data.get('data_source', ''),
            data.get('lead_department', ''),
            data.get('logic_definition', ''),
            data.get('notes', ''),
            component_id,
            indicator_id
        ))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '组件信息更新成功'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/indicators/<indicator_id>/components', methods=['POST'])
def add_component(indicator_id):
    """为指标添加新的分子分母组件"""
    try:
        data = request.get_json()

        conn = get_db_connection()
        cursor = conn.cursor()

        # 验证指标是否存在
        cursor.execute("SELECT id FROM indicators WHERE id = ?", (indicator_id,))
        if not cursor.fetchone():
            return jsonify({'success': False, 'error': '指标不存在'})

        # 获取下一个排序号
        cursor.execute("""
            SELECT COALESCE(MAX(sort_order), 0) + 1
            FROM indicator_components
            WHERE indicator_id = ? AND component_type = ?
        """, (indicator_id, data.get('component_type', 'numerator')))

        next_sort_order = cursor.fetchone()[0]

        # 添加新组件
        cursor.execute("""
            INSERT INTO indicator_components
            (indicator_id, component_type, name, definition, unit, data_source,
             lead_department, logic_definition, notes, sort_order, is_active,
             created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
        """, (
            indicator_id,
            data.get('component_type', 'numerator'),
            data.get('name', ''),
            data.get('definition', ''),
            data.get('unit', ''),
            data.get('data_source', ''),
            data.get('lead_department', ''),
            data.get('logic_definition', ''),
            data.get('notes', ''),
            next_sort_order
        ))

        component_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '组件添加成功', 'component_id': component_id})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/indicators/<indicator_id>/components/<int:component_id>', methods=['DELETE'])
def delete_component(indicator_id, component_id):
    """删除指标分子分母组件"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 验证组件是否存在且属于该指标
        cursor.execute("""
            SELECT id FROM indicator_components
            WHERE id = ? AND indicator_id = ?
        """, (component_id, indicator_id))

        if not cursor.fetchone():
            return jsonify({'success': False, 'error': '组件不存在或不属于该指标'})

        # 删除组件
        cursor.execute("""
            DELETE FROM indicator_components
            WHERE id = ? AND indicator_id = ?
        """, (component_id, indicator_id))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '组件删除成功'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/indicators/<indicator_id>/values', methods=['PUT'])
def update_indicator_values(indicator_id):
    """更新指标数值信息（目标值、当前值、完成率等）"""
    try:
        data = request.get_json()
        print(f"Received data for indicator {indicator_id}: {data}")

        conn = get_db_connection()
        cursor = conn.cursor()

        # 验证指标是否存在
        cursor.execute("SELECT id FROM indicators WHERE id = ?", (indicator_id,))
        if not cursor.fetchone():
            print(f"Indicator {indicator_id} not found")
            return jsonify({'success': False, 'error': '指标不存在'})

        # 更新指标数值信息
        cursor.execute("""
            UPDATE indicators
            SET target_value = ?, current_value = ?, completion_rate = ?,
                data_source = ?, calculation_method = ?,
                updated_at = datetime('now')
            WHERE id = ?
        """, (
            data.get('target_value', ''),
            data.get('current_value', ''),
            data.get('completion_rate', None),
            data.get('data_source', ''),
            data.get('calculation_method', ''),
            indicator_id
        ))

        conn.commit()
        conn.close()

        print(f"Successfully updated indicator {indicator_id}")
        return jsonify({'success': True, 'message': '指标数值更新成功'})

    except Exception as e:
        print(f"Error updating indicator {indicator_id}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/indicators/<indicator_id>/attributes', methods=['PUT'])
def update_indicator_attributes(indicator_id):
    """更新简单指标的属性"""
    try:
        data = request.get_json()

        conn = get_db_connection()
        cursor = conn.cursor()

        # 验证指标是否存在
        cursor.execute("SELECT indicator_type FROM indicators WHERE id = ?", (indicator_id,))
        result = cursor.fetchone()
        if not result:
            return jsonify({'success': False, 'error': '指标不存在'})

        # 更新指标属性
        cursor.execute("""
            UPDATE indicators
            SET unit = ?, lead_department = ?, data_source = ?, logic_definition = ?
            WHERE id = ?
        """, (
            data.get('unit'),
            data.get('lead_department'),
            data.get('data_source'),
            data.get('logic_definition'),
            indicator_id
        ))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '属性更新成功'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/indicator/<indicator_id>')
def indicator_detail(indicator_id):
    """指标详情页面"""
    return render_template('indicator_detail.html', indicator_id=indicator_id)

@app.route('/tree-line-test')
def tree_line_test():
    """树形连接线测试页面"""
    with open('tree-line-test.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/list-tree-test')
def list_tree_test():
    """列表视图树形连接线测试页面"""
    with open('list-tree-test.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/debug-frontend')
def debug_frontend():
    """前端调试页面"""
    with open('debug-frontend.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/expand-collapse-test')
def expand_collapse_test():
    """展开/收起功能测试页面"""
    with open('expand-collapse-test.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/test-indicator-detail')
def test_indicator_detail():
    """指标详情测试页面"""
    with open('test_indicator_detail.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/simple-debug')
def simple_debug():
    """简单调试页面"""
    with open('simple_debug.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/debug-api')
def debug_api():
    """API调试页面"""
    with open('debug_api.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/test-badge-style')
def test_badge_style():
    """徽章样式测试页面"""
    with open('test_badge_style.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/test-batch-delete')
def test_batch_delete():
    """批量删除功能测试页面"""
    with open('test_batch_delete.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/api/statistics')
def get_statistics():
    """获取统计信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 总体统计
        cursor.execute("SELECT COUNT(*) FROM chapters")
        chapter_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM sections")
        section_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM indicators")
        indicator_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM indicator_components")
        component_count = cursor.fetchone()[0]

        # 各章节统计
        cursor.execute("""
            SELECT c.code, c.name, c.icon, c.color,
                   COUNT(DISTINCT s.id) as sections,
                   COUNT(DISTINCT i.id) as indicators,
                   COUNT(DISTINCT ic.id) as components
            FROM chapters c
            LEFT JOIN sections s ON c.id = s.chapter_id
            LEFT JOIN indicators i ON c.id = i.chapter_id
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id
            GROUP BY c.id
            ORDER BY c.code
        """)

        chapter_stats = []
        for row in cursor.fetchall():
            stat = dict_from_row(row)
            chapter_stats.append(stat)

        # 数据完整性统计
        cursor.execute("""
            SELECT
                COUNT(*) as total_components,
                SUM(CASE WHEN unit != '' THEN 1 ELSE 0 END) as has_unit,
                SUM(CASE WHEN lead_department != '' THEN 1 ELSE 0 END) as has_department,
                SUM(CASE WHEN data_source != '' THEN 1 ELSE 0 END) as has_source
            FROM indicator_components
        """)

        completeness = dict_from_row(cursor.fetchone())

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'overview': {
                    'chapters': chapter_count,
                    'sections': section_count,
                    'indicators': indicator_count,
                    'components': component_count
                },
                'chapters': chapter_stats,
                'completeness': completeness
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/statistics')
def get_admin_statistics():
    """获取后台管理统计信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取各种统计数据
        stats = {}

        # 章节统计
        cursor.execute("SELECT COUNT(*) FROM chapters WHERE is_active = 1")
        stats['total_chapters'] = cursor.fetchone()[0]

        # 小节统计
        cursor.execute("SELECT COUNT(*) FROM sections WHERE is_active = 1")
        stats['total_sections'] = cursor.fetchone()[0]

        # 指标统计
        cursor.execute("SELECT COUNT(*) FROM indicators WHERE is_active = 1")
        stats['total_indicators'] = cursor.fetchone()[0]

        # 组件统计
        cursor.execute("SELECT COUNT(*) FROM indicator_components WHERE is_active = 1")
        stats['total_components'] = cursor.fetchone()[0]

        # 不完整数据统计
        cursor.execute("""
            SELECT COUNT(*) FROM indicators
            WHERE is_active = 1 AND (
                name IS NULL OR name = '' OR
                description IS NULL OR description = ''
            )
        """)
        stats['incomplete_indicators'] = cursor.fetchone()[0]

        # 缺少组件的指标
        cursor.execute("""
            SELECT COUNT(*) FROM indicators i
            WHERE i.is_active = 1
            AND NOT EXISTS (
                SELECT 1 FROM indicator_components ic
                WHERE ic.indicator_id = i.id AND ic.is_active = 1
            )
        """)
        stats['indicators_without_components'] = cursor.fetchone()[0]

        # 最近更新的指标数量（7天内）
        cursor.execute("""
            SELECT COUNT(*) FROM indicators
            WHERE is_active = 1
            AND updated_at >= datetime('now', '-7 days')
        """)
        stats['recently_updated'] = cursor.fetchone()[0]

        conn.close()
        return jsonify({'success': True, 'data': stats})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# 暂时禁用参考范围功能
@app.route('/api/indicators/<indicator_id>/reference-range', methods=['GET'])
def get_indicator_reference_range(indicator_id):
    """获取指标参考范围 - 暂时禁用"""
    return jsonify({'success': False, 'error': '参考范围功能暂时禁用'})

# 暂时禁用参考范围功能
@app.route('/api/indicators/<indicator_id>/reference-range', methods=['POST', 'PUT'])
def save_indicator_reference_range(indicator_id):
    """保存或更新指标参考范围 - 暂时禁用"""
    return jsonify({'success': False, 'error': '参考范围功能暂时禁用'})

@app.route('/api/indicators/<indicator_id>', methods=['PUT'])
def update_indicator(indicator_id):
    """更新指标信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        data = request.get_json()

        # 构建更新SQL
        update_fields = []
        update_values = []

        # 基本字段
        if 'name' in data:
            update_fields.append('name = ?')
            update_values.append(data['name'])
        if 'description' in data:
            update_fields.append('description = ?')
            update_values.append(data['description'])
        if 'calculation_method' in data:
            update_fields.append('calculation_method = ?')
            update_values.append(data['calculation_method'])
        if 'indicator_type' in data:
            update_fields.append('indicator_type = ?')
            update_values.append(data['indicator_type'])

        # 简单指标字段
        if 'unit' in data:
            update_fields.append('unit = ?')
            update_values.append(data['unit'])
        if 'lead_department' in data:
            update_fields.append('lead_department = ?')
            update_values.append(data['lead_department'])
        if 'logic_definition' in data:
            update_fields.append('logic_definition = ?')
            update_values.append(data['logic_definition'])
        if 'reference_range' in data:
            update_fields.append('reference_range = ?')
            update_values.append(data['reference_range'])
        if 'data_source' in data:
            update_fields.append('data_source = ?')
            update_values.append(data['data_source'])

        # 复合指标详细字段
        if 'calculation_formula' in data:
            update_fields.append('calculation_formula = ?')
            update_values.append(data['calculation_formula'])
        if 'numerator_description' in data:
            update_fields.append('numerator_description = ?')
            update_values.append(data['numerator_description'])
        if 'denominator_description' in data:
            update_fields.append('denominator_description = ?')
            update_values.append(data['denominator_description'])

        # 分子详细字段
        if 'numerator_unit' in data:
            update_fields.append('numerator_unit = ?')
            update_values.append(data['numerator_unit'])
        if 'numerator_department' in data:
            update_fields.append('numerator_department = ?')
            update_values.append(data['numerator_department'])
        if 'numerator_source' in data:
            update_fields.append('numerator_source = ?')
            update_values.append(data['numerator_source'])
        if 'numerator_logic' in data:
            update_fields.append('numerator_logic = ?')
            update_values.append(data['numerator_logic'])

        # 分母详细字段
        if 'denominator_unit' in data:
            update_fields.append('denominator_unit = ?')
            update_values.append(data['denominator_unit'])
        if 'denominator_department' in data:
            update_fields.append('denominator_department = ?')
            update_values.append(data['denominator_department'])
        if 'denominator_source' in data:
            update_fields.append('denominator_source = ?')
            update_values.append(data['denominator_source'])
        if 'denominator_logic' in data:
            update_fields.append('denominator_logic = ?')
            update_values.append(data['denominator_logic'])

        # 通用详细字段
        if 'indicator_definition' in data:
            update_fields.append('indicator_definition = ?')
            update_values.append(data['indicator_definition'])
        if 'statistical_scope' in data:
            update_fields.append('statistical_scope = ?')
            update_values.append(data['statistical_scope'])
        if 'data_sources' in data:
            update_fields.append('data_sources = ?')
            update_values.append(data['data_sources'])
        if 'collection_frequency_detail' in data:
            update_fields.append('collection_frequency_detail = ?')
            update_values.append(data['collection_frequency_detail'])
        if 'reference_value' in data:
            update_fields.append('reference_value = ?')
            update_values.append(data['reference_value'])
        if 'monitoring_analysis' in data:
            update_fields.append('monitoring_analysis = ?')
            update_values.append(data['monitoring_analysis'])

        if not update_fields:
            return jsonify({'success': False, 'error': '没有要更新的字段'})

        # 添加更新时间
        update_fields.append('updated_at = datetime("now")')
        update_values.append(indicator_id)

        # 执行更新
        sql = f"UPDATE indicators SET {', '.join(update_fields)} WHERE id = ?"
        cursor.execute(sql, update_values)

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '指标更新成功'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ==================== 后台管理功能 ====================

@app.route('/admin')
@app.route('/admin/login')
def admin_login():
    """后台管理登录页面"""
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>后台管理系统</title>
        <style>
            body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 50px; }
            .login-container { max-width: 400px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { text-align: center; color: #333; margin-bottom: 30px; }
            .form-group { margin-bottom: 20px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
            button { width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
            button:hover { background: #0056b3; }
            .demo-info { background: #e9ecef; padding: 15px; border-radius: 4px; margin-bottom: 20px; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="login-container">
            <h1>🏥 后台管理系统</h1>
            <div class="demo-info">
                <strong>演示账号：</strong><br>
                用户名：admin<br>
                密码：hospital2024
            </div>
            <form action="/admin/dashboard" method="get">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" value="admin" required>
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" value="hospital2024" required>
                </div>
                <button type="submit">登录</button>
            </form>
        </div>
    </body>
    </html>
    '''



@app.route('/admin/dashboard')
def admin_dashboard():
    """后台管理首页"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取统计数据
        stats = {}

        # 章节统计
        cursor.execute("SELECT COUNT(*) as count FROM chapters WHERE is_active = 1")
        stats['chapters'] = cursor.fetchone()[0]

        # 小节统计
        cursor.execute("SELECT COUNT(*) as count FROM sections WHERE is_active = 1")
        stats['sections'] = cursor.fetchone()[0]

        # 指标统计
        cursor.execute("SELECT COUNT(*) as count FROM indicators WHERE is_active = 1")
        stats['indicators'] = cursor.fetchone()[0]

        # 组件统计
        cursor.execute("SELECT COUNT(*) as count FROM indicator_components WHERE is_active = 1")
        stats['components'] = cursor.fetchone()[0]

        # 数据完整性检查
        cursor.execute("""
            SELECT COUNT(*) as count FROM indicators
            WHERE is_active = 1 AND (name IS NULL OR name = '' OR description IS NULL OR description = '')
        """)
        stats['incomplete_indicators'] = cursor.fetchone()[0]

        cursor.execute("""
            SELECT COUNT(*) as count FROM indicator_components
            WHERE is_active = 1 AND (name IS NULL OR name = '' OR unit IS NULL OR unit = '')
        """)
        stats['incomplete_components'] = cursor.fetchone()[0]

        conn.close()

        return render_template('admin/dashboard.html', stats=stats)

    except Exception as e:
        flash(f'加载数据失败: {str(e)}', 'error')
        return render_template('admin/dashboard.html', stats={})

@app.route('/admin/indicators')
def admin_indicators():
    """后台指标管理页面"""
    try:
        chapter_id = request.args.get('chapter_id')
        section_id = request.args.get('section_id')
        search = request.args.get('search', '')
        page = int(request.args.get('page', 1))
        per_page = 20

        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取章节和小节列表用于筛选
        cursor.execute("SELECT * FROM chapters WHERE is_active = 1 ORDER BY code")
        chapters = [dict_from_row(row) for row in cursor.fetchall()]

        cursor.execute("SELECT * FROM sections WHERE is_active = 1 ORDER BY chapter_id, sort_order")
        sections = [dict_from_row(row) for row in cursor.fetchall()]

        # 构建查询条件
        where_conditions = ["i.is_active = 1"]
        params = []

        if chapter_id:
            where_conditions.append("i.chapter_id = ?")
            params.append(chapter_id)

        if section_id:
            where_conditions.append("i.section_id = ?")
            params.append(section_id)

        if search:
            where_conditions.append("(i.name LIKE ? OR i.id LIKE ? OR i.description LIKE ?)")
            params.extend([f"%{search}%", f"%{search}%", f"%{search}%"])

        where_clause = " AND ".join(where_conditions)

        # 获取总数
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM indicators i
            WHERE {where_clause}
        """, params)

        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT i.*,
                   c.name as chapter_name, c.code as chapter_code,
                   s.name as section_name, s.code as section_code,
                   p.name as parent_name,
                   COUNT(DISTINCT child.id) as children_count
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN sections s ON i.section_id = s.id
            LEFT JOIN indicators p ON i.parent_id = p.id
            LEFT JOIN indicators child ON child.parent_id = i.id AND child.is_active = 1
            WHERE {where_clause}
            GROUP BY i.id
            ORDER BY i.id
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])

        indicators = [dict_from_row(row) for row in cursor.fetchall()]

        conn.close()

        # 计算分页信息
        total_pages = (total + per_page - 1) // per_page
        pagination = {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': total_pages
        }

        return render_template('admin/indicators.html',
                             indicators=indicators,
                             chapters=chapters,
                             sections=sections,
                             selected_chapter=chapter_id,
                             selected_section=section_id,
                             search=search,
                             pagination=pagination)

    except Exception as e:
        flash(f'加载指标数据失败: {str(e)}', 'error')
        return render_template('admin/indicators.html', indicators=[], chapters=[], sections=[])

@app.route('/admin/indicators/<indicator_id>/edit', methods=['GET', 'POST'])
def edit_indicator(indicator_id):
    """编辑指标"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        if request.method == 'POST':
            # 处理表单提交
            data = request.form

            cursor.execute("""
                UPDATE indicators
                SET name = ?, description = ?, category = ?, target_value = ?,
                    current_value = ?, completion_rate = ?, data_source = ?,
                    calculation_method = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                data.get('name'),
                data.get('description'),
                data.get('category'),
                data.get('target_value'),
                data.get('current_value'),
                data.get('completion_rate'),
                data.get('data_source'),
                data.get('calculation_method'),
                indicator_id
            ))

            conn.commit()
            conn.close()

            flash('指标更新成功！', 'success')
            return redirect(f'/admin/indicators/{indicator_id}')

        # GET请求 - 显示编辑表单
        cursor.execute("""
            SELECT i.*,
                   c.name as chapter_name, c.code as chapter_code,
                   s.name as section_name, s.code as section_code
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN sections s ON i.section_id = s.id
            WHERE i.id = ?
        """, (indicator_id,))

        indicator = dict_from_row(cursor.fetchone())
        if not indicator:
            flash('指标不存在', 'error')
            return redirect('/admin/indicators')

        conn.close()

        return render_template('admin/edit_indicator.html', indicator=indicator)

    except Exception as e:
        flash(f'编辑指标失败: {str(e)}', 'error')
        return redirect('/admin/indicators')

@app.route('/admin/indicators/<indicator_id>/delete', methods=['POST'])
def delete_indicator(indicator_id):
    """删除指标（软删除）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查是否有子指标
        cursor.execute("SELECT COUNT(*) FROM indicators WHERE parent_id = ? AND is_active = 1", (indicator_id,))
        children_count = cursor.fetchone()[0]

        if children_count > 0:
            flash(f'无法删除指标：该指标有 {children_count} 个子指标，请先删除子指标', 'error')
            return redirect('/admin/indicators')

        # 软删除指标
        cursor.execute("UPDATE indicators SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?", (indicator_id,))

        # 软删除相关组件
        cursor.execute("UPDATE indicator_components SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE indicator_id = ?", (indicator_id,))

        conn.commit()
        conn.close()

        flash('指标删除成功！', 'success')
        return redirect('/admin/indicators')

    except Exception as e:
        flash(f'删除指标失败: {str(e)}', 'error')
        return redirect('/admin/indicators')

@app.route('/admin/indicators/batch-delete', methods=['POST'])
def batch_delete_indicators():
    """批量删除指标（软删除）"""
    try:
        indicator_ids = request.form.getlist('indicator_ids')

        if not indicator_ids:
            flash('未选择要删除的指标', 'error')
            return redirect('/admin/indicators')

        conn = get_db_connection()
        cursor = conn.cursor()

        success_count = 0
        error_count = 0
        error_messages = []

        for indicator_id in indicator_ids:
            try:
                # 检查是否有子指标
                cursor.execute("SELECT COUNT(*) FROM indicators WHERE parent_id = ? AND is_active = 1", (indicator_id,))
                children_count = cursor.fetchone()[0]

                if children_count > 0:
                    error_count += 1
                    error_messages.append(f'指标 {indicator_id} 有 {children_count} 个子指标，无法删除')
                    continue

                # 软删除指标
                cursor.execute("UPDATE indicators SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?", (indicator_id,))

                if cursor.rowcount > 0:
                    # 软删除相关组件
                    cursor.execute("UPDATE indicator_components SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE indicator_id = ?", (indicator_id,))
                    success_count += 1
                else:
                    error_count += 1
                    error_messages.append(f'指标 {indicator_id} 不存在或已删除')

            except Exception as e:
                error_count += 1
                error_messages.append(f'删除指标 {indicator_id} 失败: {str(e)}')

        conn.commit()
        conn.close()

        # 构建结果消息
        if success_count > 0:
            flash(f'成功删除 {success_count} 个指标', 'success')

        if error_count > 0:
            flash(f'删除失败 {error_count} 个指标', 'error')
            for error_msg in error_messages[:3]:  # 只显示前3个错误消息
                flash(error_msg, 'error')
            if len(error_messages) > 3:
                flash(f'还有 {len(error_messages) - 3} 个其他错误...', 'error')

    except Exception as e:
        flash(f'批量删除操作失败: {str(e)}', 'error')

    return redirect('/admin/indicators')

@app.route('/admin/components')
def admin_components():
    """后台组件管理页面"""
    try:
        indicator_id = request.args.get('indicator_id')
        component_type = request.args.get('component_type')
        search = request.args.get('search', '')
        page = int(request.args.get('page', 1))
        per_page = 20

        conn = get_db_connection()
        cursor = conn.cursor()

        # 构建查询条件
        where_conditions = ["ic.is_active = 1"]
        params = []

        if indicator_id:
            where_conditions.append("ic.indicator_id = ?")
            params.append(indicator_id)

        if component_type:
            where_conditions.append("ic.component_type = ?")
            params.append(component_type)

        if search:
            where_conditions.append("(ic.name LIKE ? OR ic.indicator_id LIKE ? OR ic.unit LIKE ?)")
            params.extend([f"%{search}%", f"%{search}%", f"%{search}%"])

        where_clause = " AND ".join(where_conditions)

        # 获取总数
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM indicator_components ic
            WHERE {where_clause}
        """, params)

        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT ic.*,
                   i.name as indicator_name
            FROM indicator_components ic
            LEFT JOIN indicators i ON ic.indicator_id = i.id
            WHERE {where_clause}
            ORDER BY ic.indicator_id, ic.component_type, ic.sort_order
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])

        components = [dict_from_row(row) for row in cursor.fetchall()]

        conn.close()

        # 计算分页信息
        total_pages = (total + per_page - 1) // per_page
        pagination = {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': total_pages
        }

        return render_template('admin/components.html',
                             components=components,
                             selected_indicator=indicator_id,
                             selected_type=component_type,
                             search=search,
                             pagination=pagination)

    except Exception as e:
        flash(f'加载组件数据失败: {str(e)}', 'error')
        return render_template('admin/components.html', components=[])

@app.route('/admin/components/<component_id>/delete', methods=['POST'])
def admin_delete_component(component_id):
    """删除组件（软删除）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取组件信息用于返回
        cursor.execute("SELECT indicator_id FROM indicator_components WHERE id = ?", (component_id,))
        result = cursor.fetchone()
        if not result:
            flash('组件不存在', 'error')
            return redirect('/admin/components')

        indicator_id = result[0]

        # 软删除组件
        cursor.execute("UPDATE indicator_components SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?", (component_id,))

        conn.commit()
        conn.close()

        flash('组件删除成功！', 'success')
        return redirect(f'/admin/indicators/{indicator_id}')

    except Exception as e:
        flash(f'删除组件失败: {str(e)}', 'error')
        return redirect('/admin/components')

@app.route('/admin/chapters')
def admin_chapters():
    """后台章节管理页面"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT c.*,
                   COUNT(DISTINCT s.id) as section_count,
                   COUNT(DISTINCT i.id) as indicator_count
            FROM chapters c
            LEFT JOIN sections s ON c.id = s.chapter_id AND s.is_active = 1
            LEFT JOIN indicators i ON c.id = i.chapter_id AND i.is_active = 1
            WHERE c.is_active = 1
            GROUP BY c.id
            ORDER BY c.code
        """)

        chapters = [dict_from_row(row) for row in cursor.fetchall()]
        conn.close()

        return render_template('admin/chapters.html', chapters=chapters)

    except Exception as e:
        flash(f'加载章节数据失败: {str(e)}', 'error')
        return render_template('admin/chapters.html', chapters=[])

@app.route('/admin/sections')
def admin_sections():
    """后台小节管理页面"""
    try:
        chapter_id = request.args.get('chapter_id')
        search = request.args.get('search', '')
        page = int(request.args.get('page', 1))
        per_page = 20

        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取章节列表用于筛选
        cursor.execute("SELECT * FROM chapters WHERE is_active = 1 ORDER BY code")
        chapters = [dict_from_row(row) for row in cursor.fetchall()]

        # 构建查询条件
        where_conditions = ["s.is_active = 1"]
        params = []

        if chapter_id:
            where_conditions.append("s.chapter_id = ?")
            params.append(chapter_id)

        if search:
            where_conditions.append("(s.name LIKE ? OR s.code LIKE ? OR s.description LIKE ?)")
            params.extend([f"%{search}%", f"%{search}%", f"%{search}%"])

        where_clause = " AND ".join(where_conditions)

        # 获取总数
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM sections s
            WHERE {where_clause}
        """, params)

        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT s.*, c.name as chapter_name, c.code as chapter_code,
                   COUNT(i.id) as indicator_count
            FROM sections s
            LEFT JOIN chapters c ON s.chapter_id = c.id
            LEFT JOIN indicators i ON s.id = i.section_id AND i.is_active = 1
            WHERE {where_clause}
            GROUP BY s.id
            ORDER BY c.code, s.sort_order
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])

        sections = [dict_from_row(row) for row in cursor.fetchall()]
        conn.close()

        # 计算分页信息
        total_pages = (total + per_page - 1) // per_page
        pagination = {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': total_pages
        }

        return render_template('admin/sections.html',
                             sections=sections,
                             chapters=chapters,
                             selected_chapter=chapter_id,
                             search=search,
                             pagination=pagination)

    except Exception as e:
        flash(f'加载小节数据失败: {str(e)}', 'error')
        return render_template('admin/sections.html', sections=[], chapters=[])

@app.route('/admin/sections/<section_id>/edit', methods=['GET', 'POST'])
def edit_section(section_id):
    """编辑小节"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        if request.method == 'POST':
            # 处理表单提交
            data = request.form

            cursor.execute("""
                UPDATE sections
                SET name = ?, description = ?, sort_order = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                data.get('name'),
                data.get('description'),
                data.get('sort_order'),
                section_id
            ))

            conn.commit()
            conn.close()

            flash('小节更新成功！', 'success')
            return redirect('/admin/sections')

        # GET请求 - 显示编辑表单
        cursor.execute("""
            SELECT s.*,
                   c.name as chapter_name, c.code as chapter_code
            FROM sections s
            LEFT JOIN chapters c ON s.chapter_id = c.id
            WHERE s.id = ?
        """, (section_id,))

        section = dict_from_row(cursor.fetchone())
        if not section:
            flash('小节不存在', 'error')
            return redirect('/admin/sections')

        conn.close()

        return render_template('admin/edit_section.html', section=section)

    except Exception as e:
        flash(f'编辑小节失败: {str(e)}', 'error')
        return redirect('/admin/sections')

@app.route('/admin/sections/<section_id>/delete', methods=['POST'])
def delete_section(section_id):
    """删除小节（软删除）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查是否有指标
        cursor.execute("SELECT COUNT(*) FROM indicators WHERE section_id = ? AND is_active = 1", (section_id,))
        indicators_count = cursor.fetchone()[0]

        if indicators_count > 0:
            flash(f'无法删除小节：该小节有 {indicators_count} 个指标，请先删除指标', 'error')
            return redirect('/admin/sections')

        # 软删除小节
        cursor.execute("UPDATE sections SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?", (section_id,))

        conn.commit()
        conn.close()

        flash('小节删除成功！', 'success')
        return redirect('/admin/sections')

    except Exception as e:
        flash(f'删除小节失败: {str(e)}', 'error')
        return redirect('/admin/sections')

@app.route('/admin/indicators/<indicator_id>')
def admin_indicator_detail(indicator_id):
    """后台指标详情页面 - 使用统一API确保数据一致性"""
    return render_template('admin/indicator_detail.html', indicator_id=indicator_id)

@app.route('/admin/indicators/<indicator_id>/edit')
def admin_indicator_edit(indicator_id):
    """后台指标编辑页面"""
    return render_template('admin/indicator_edit.html', indicator_id=indicator_id)

@app.route('/admin/indicators/batch-edit')
def admin_batch_edit():
    """批量编辑指标页面"""
    return render_template('admin/batch_edit.html')

@app.route('/admin/components/<component_id>/edit', methods=['GET', 'POST'])
def edit_component(component_id):
    """编辑组件页面"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        if request.method == 'POST':
            # 处理表单提交
            data = request.form

            # 确保indicator_components表存在
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS indicator_components (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    indicator_id VARCHAR(20) NOT NULL,
                    component_type VARCHAR(20) NOT NULL CHECK (component_type IN ('numerator', 'denominator', 'other')),
                    name VARCHAR(200) NOT NULL,
                    definition TEXT,
                    unit VARCHAR(50),
                    data_source VARCHAR(200),
                    lead_department VARCHAR(100),
                    logic_definition TEXT,
                    collection_method VARCHAR(50),
                    calculation_formula TEXT,
                    notes TEXT,
                    sort_order INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 更新组件信息
            cursor.execute("""
                UPDATE indicator_components
                SET name = ?, definition = ?, unit = ?, data_source = ?,
                    lead_department = ?, logic_definition = ?, collection_method = ?,
                    calculation_formula = ?, notes = ?, component_type = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                data.get('name'),
                data.get('definition'),
                data.get('unit'),
                data.get('data_source'),
                data.get('lead_department'),
                data.get('logic_definition'),
                data.get('collection_method'),
                data.get('calculation_formula'),
                data.get('notes'),
                data.get('component_type'),
                component_id
            ))

            conn.commit()

            # 获取指标ID用于重定向
            cursor.execute("SELECT indicator_id FROM indicator_components WHERE id = ?", (component_id,))
            result = cursor.fetchone()
            indicator_id = result[0] if result else None

            conn.close()

            if indicator_id:
                return f'''
                <script>
                    alert('组件更新成功！');
                    window.location.href = '/admin/indicators/{indicator_id}';
                </script>
                '''
            else:
                return f'''
                <script>
                    alert('组件更新成功！');
                    window.location.href = '/admin/indicators';
                </script>
                '''

        # GET请求 - 显示编辑表单
        # 确保indicator_components表存在
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS indicator_components (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                indicator_id VARCHAR(20) NOT NULL,
                component_type VARCHAR(20) NOT NULL CHECK (component_type IN ('numerator', 'denominator', 'other')),
                name VARCHAR(200) NOT NULL,
                definition TEXT,
                unit VARCHAR(50),
                data_source VARCHAR(200),
                lead_department VARCHAR(100),
                logic_definition TEXT,
                collection_method VARCHAR(50),
                calculation_formula TEXT,
                notes TEXT,
                sort_order INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 获取组件信息
        cursor.execute("""
            SELECT ic.*, i.name as indicator_name
            FROM indicator_components ic
            LEFT JOIN indicators i ON ic.indicator_id = i.id
            WHERE ic.id = ?
        """, (component_id,))

        component = dict_from_row(cursor.fetchone())
        if not component:
            # 如果组件不存在，创建一个默认组件
            return f'''
            <h1>组件不存在</h1>
            <p>组件 {component_id} 不存在。</p>
            <p><a href="/admin/indicators">返回指标列表</a></p>
            '''

        conn.close()

        return f'''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>编辑组件 - {component.get('name', '未命名')}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
                .container {{ max-width: 800px; margin: 0 auto; }}
                .header {{ background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .form-container {{ background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .form-group {{ margin-bottom: 20px; }}
                .form-group label {{ display: block; margin-bottom: 5px; font-weight: 600; color: #333; }}
                .form-group input, .form-group select, .form-group textarea {{ width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }}
                .form-group textarea {{ height: 80px; resize: vertical; }}
                .form-row {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }}
                .btn {{ padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin-right: 10px; }}
                .btn-primary {{ background: #007bff; color: white; }}
                .btn-secondary {{ background: #6c757d; color: white; }}
                .btn:hover {{ opacity: 0.9; }}
                .breadcrumb {{ color: #666; margin-bottom: 10px; }}
                .breadcrumb a {{ color: #007bff; text-decoration: none; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="breadcrumb">
                        <a href="/admin/dashboard">后台管理</a> /
                        <a href="/admin/indicators">指标管理</a> /
                        <a href="/admin/indicators/{component['indicator_id']}">{component['indicator_id']}</a> /
                        <span>编辑组件</span>
                    </div>
                    <h1>🧮 编辑组件</h1>
                    <p>指标：{component['indicator_id']} - {component.get('indicator_name', '未知指标')}</p>
                </div>

                <div class="form-container">
                    <form method="POST">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="component_type">组件类型 *</label>
                                <select id="component_type" name="component_type" required>
                                    <option value="numerator" {'selected' if component.get('component_type') == 'numerator' else ''}>分子</option>
                                    <option value="denominator" {'selected' if component.get('component_type') == 'denominator' else ''}>分母</option>
                                    <option value="other" {'selected' if component.get('component_type') == 'other' else ''}>其他</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="name">组件名称 *</label>
                                <input type="text" id="name" name="name" value="{component.get('name', '')}" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="definition">组件定义</label>
                            <textarea id="definition" name="definition" placeholder="详细描述组件的含义和计算逻辑">{component.get('definition', '')}</textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="unit">单位</label>
                                <input type="text" id="unit" name="unit" value="{component.get('unit', '')}" placeholder="例如：人、张、次、%">
                            </div>
                            <div class="form-group">
                                <label for="lead_department">牵头科室</label>
                                <input type="text" id="lead_department" name="lead_department" value="{component.get('lead_department', '')}" placeholder="负责此数据的科室">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="data_source">数据来源</label>
                            <input type="text" id="data_source" name="data_source" value="{component.get('data_source', '')}" placeholder="例如：HIS系统、手工统计、第三方系统">
                        </div>

                        <div class="form-group">
                            <label for="logic_definition">逻辑定义</label>
                            <textarea id="logic_definition" name="logic_definition" placeholder="详细的计算逻辑和业务规则">{component.get('logic_definition', '')}</textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="collection_method">采集方式</label>
                                <input type="text" id="collection_method" name="collection_method" value="{component.get('collection_method', '')}" placeholder="例如：自动采集、手工录入、定期导入">
                            </div>
                            <div class="form-group">
                                <label for="calculation_formula">计算公式</label>
                                <input type="text" id="calculation_formula" name="calculation_formula" value="{component.get('calculation_formula', '')}" placeholder="具体的计算公式">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">备注</label>
                            <textarea id="notes" name="notes" placeholder="其他需要说明的信息">{component.get('notes', '')}</textarea>
                        </div>

                        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                            <button type="submit" class="btn btn-primary">💾 保存更改</button>
                            <a href="/admin/indicators/{component['indicator_id']}" class="btn btn-secondary">❌ 取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </body>
        </html>
        '''

    except Exception as e:
        return f'<h1>错误</h1><p>{str(e)}</p>'

@app.route('/admin/indicators/<indicator_id>/add_component', methods=['GET', 'POST'])
def add_component_form(indicator_id):
    """为指标添加组件"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 确保indicator_components表存在
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS indicator_components (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                indicator_id VARCHAR(20) NOT NULL,
                component_type VARCHAR(20) NOT NULL CHECK (component_type IN ('numerator', 'denominator', 'other')),
                name VARCHAR(200) NOT NULL,
                definition TEXT,
                unit VARCHAR(50),
                data_source VARCHAR(200),
                lead_department VARCHAR(100),
                logic_definition TEXT,
                collection_method VARCHAR(50),
                calculation_formula TEXT,
                notes TEXT,
                sort_order INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        if request.method == 'POST':
            # 处理表单提交
            data = request.form

            # 插入新组件
            cursor.execute("""
                INSERT INTO indicator_components
                (indicator_id, component_type, name, definition, unit, data_source,
                 lead_department, logic_definition, collection_method, calculation_formula, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                indicator_id,
                data.get('component_type'),
                data.get('name'),
                data.get('definition'),
                data.get('unit'),
                data.get('data_source'),
                data.get('lead_department'),
                data.get('logic_definition'),
                data.get('collection_method'),
                data.get('calculation_formula'),
                data.get('notes')
            ))

            conn.commit()
            conn.close()

            return f'''
            <script>
                alert('组件添加成功！');
                window.location.href = '/admin/indicators/{indicator_id}';
            </script>
            '''

        # GET请求 - 显示添加表单
        # 获取指标信息
        cursor.execute("SELECT * FROM indicators WHERE id = ?", (indicator_id,))
        indicator = dict_from_row(cursor.fetchone())

        if not indicator:
            return f'<h1>指标不存在</h1><p>指标 {indicator_id} 不存在</p>'

        conn.close()

        return f'''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>添加组件 - {indicator['name']}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
                .container {{ max-width: 800px; margin: 0 auto; }}
                .header {{ background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .form-container {{ background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .form-group {{ margin-bottom: 20px; }}
                .form-group label {{ display: block; margin-bottom: 5px; font-weight: 600; color: #333; }}
                .form-group input, .form-group select, .form-group textarea {{ width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }}
                .form-group textarea {{ height: 80px; resize: vertical; }}
                .form-row {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }}
                .btn {{ padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin-right: 10px; }}
                .btn-primary {{ background: #007bff; color: white; }}
                .btn-secondary {{ background: #6c757d; color: white; }}
                .btn:hover {{ opacity: 0.9; }}
                .breadcrumb {{ color: #666; margin-bottom: 10px; }}
                .breadcrumb a {{ color: #007bff; text-decoration: none; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="breadcrumb">
                        <a href="/admin/dashboard">后台管理</a> /
                        <a href="/admin/indicators">指标管理</a> /
                        <a href="/admin/indicators/{indicator_id}">{indicator_id}</a> /
                        <span>添加组件</span>
                    </div>
                    <h1>➕ 添加组件</h1>
                    <p>指标：{indicator_id} - {indicator['name']}</p>
                </div>

                <div class="form-container">
                    <form method="POST">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="component_type">组件类型 *</label>
                                <select id="component_type" name="component_type" required>
                                    <option value="">请选择组件类型</option>
                                    <option value="numerator">分子</option>
                                    <option value="denominator">分母</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="name">组件名称 *</label>
                                <input type="text" id="name" name="name" required placeholder="例如：实际开放床位数">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="definition">组件定义</label>
                            <textarea id="definition" name="definition" placeholder="详细描述组件的含义和计算逻辑"></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="unit">单位</label>
                                <input type="text" id="unit" name="unit" placeholder="例如：人、张、次、%">
                            </div>
                            <div class="form-group">
                                <label for="lead_department">牵头科室</label>
                                <input type="text" id="lead_department" name="lead_department" placeholder="负责此数据的科室">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="data_source">数据来源</label>
                            <input type="text" id="data_source" name="data_source" placeholder="例如：HIS系统、手工统计、第三方系统">
                        </div>

                        <div class="form-group">
                            <label for="logic_definition">逻辑定义</label>
                            <textarea id="logic_definition" name="logic_definition" placeholder="详细的计算逻辑和业务规则"></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="collection_method">采集方式</label>
                                <input type="text" id="collection_method" name="collection_method" placeholder="例如：自动采集、手工录入、定期导入">
                            </div>
                            <div class="form-group">
                                <label for="calculation_formula">计算公式</label>
                                <input type="text" id="calculation_formula" name="calculation_formula" placeholder="具体的计算公式">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">备注</label>
                            <textarea id="notes" name="notes" placeholder="其他需要说明的信息"></textarea>
                        </div>

                        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                            <button type="submit" class="btn btn-primary">💾 添加组件</button>
                            <a href="/admin/indicators/{indicator_id}" class="btn btn-secondary">❌ 取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </body>
        </html>
        '''

    except Exception as e:
        return f'<h1>错误</h1><p>{str(e)}</p>'

@app.errorhandler(404)
def not_found(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

# ========================================
# 字段配置管理API
# ========================================

@app.route('/api/field-config')
def get_field_configs():
    """获取所有字段配置"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT * FROM indicator_field_config
            WHERE is_active = 1
            ORDER BY display_section, sort_order
        """)

        configs = []
        for row in cursor.fetchall():
            config = dict_from_row(row)
            configs.append(config)

        conn.close()

        return jsonify({
            'success': True,
            'data': configs
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/indicators/<indicator_id>/basic-fields')
def get_indicator_basic_fields(indicator_id):
    """获取指标的基础信息字段（用于前端显示）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取指标基本信息和字段显示配置
        cursor.execute("""
            SELECT
                i.id,
                i.name,
                i.unit,
                i.lead_department,
                i.data_source,
                i.logic_definition,
                i.reference_range,
                -- 字段显示配置
                COALESCE(ifd_unit.show_in_basic, 0) as show_unit_in_basic,
                COALESCE(ifd_dept.show_in_basic, 0) as show_lead_department_in_basic,
                COALESCE(ifd_source.show_in_basic, 0) as show_data_source_in_basic,
                COALESCE(ifd_logic.show_in_basic, 0) as show_logic_definition_in_basic,
                COALESCE(ifd_ref.show_in_basic, 0) as show_reference_range_in_basic
            FROM indicators i
            LEFT JOIN indicator_field_display ifd_unit ON i.id = ifd_unit.indicator_id AND ifd_unit.field_name = 'unit'
            LEFT JOIN indicator_field_display ifd_dept ON i.id = ifd_dept.indicator_id AND ifd_dept.field_name = 'lead_department'
            LEFT JOIN indicator_field_display ifd_source ON i.id = ifd_source.indicator_id AND ifd_source.field_name = 'data_source'
            LEFT JOIN indicator_field_display ifd_logic ON i.id = ifd_logic.indicator_id AND ifd_logic.field_name = 'logic_definition'
            LEFT JOIN indicator_field_display ifd_ref ON i.id = ifd_ref.indicator_id AND ifd_ref.field_name = 'reference_range'
            WHERE i.id = ?
        """, (indicator_id,))

        indicator_data = cursor.fetchone()
        if not indicator_data:
            return jsonify({
                'success': False,
                'error': '指标不存在'
            }), 404

        indicator = dict_from_row(indicator_data)

        # 构建基础字段列表（只包含配置为显示的字段）
        basic_fields = []

        field_configs = [
            ('unit', '单位', indicator['unit']),
            ('lead_department', '牵头科室', indicator['lead_department']),
            ('data_source', '数据来源', indicator['data_source']),
            ('logic_definition', '逻辑定义', indicator['logic_definition']),
            ('reference_range', '参考范围', indicator['reference_range'])
        ]

        for field_name, field_label, field_value in field_configs:
            show_key = f'show_{field_name}_in_basic'
            if indicator.get(show_key, 0) and field_value:
                basic_fields.append({
                    'field_name': field_name,
                    'field_label': field_label,
                    'field_value': field_value
                })

        conn.close()

        return jsonify({
            'success': True,
            'data': basic_fields
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/indicators/<indicator_id>/field-display', methods=['PUT'])
def update_indicator_field_display(indicator_id):
    """更新指标字段显示配置"""
    try:
        data = request.get_json()

        conn = get_db_connection()
        cursor = conn.cursor()

        # 验证指标是否存在
        cursor.execute("SELECT id FROM indicators WHERE id = ?", (indicator_id,))
        if not cursor.fetchone():
            return jsonify({
                'success': False,
                'error': '指标不存在'
            }), 404

        # 更新字段显示配置
        field_configs = [
            ('unit', data.get('show_unit_in_basic', 0)),
            ('lead_department', data.get('show_lead_department_in_basic', 0)),
            ('data_source', data.get('show_data_source_in_basic', 0)),
            ('logic_definition', data.get('show_logic_definition_in_basic', 0)),
            ('reference_range', data.get('show_reference_range_in_basic', 0))
        ]

        for field_name, show_in_basic in field_configs:
            cursor.execute("""
                INSERT OR REPLACE INTO indicator_field_display
                (indicator_id, field_name, show_in_basic, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            """, (indicator_id, field_name, show_in_basic))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '字段显示配置更新成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/admin/field-config')
def field_config_page():
    """字段配置管理页面"""
    return render_template('admin/field_config.html')

@app.route('/api/field-config/<field_name>', methods=['PUT'])
def update_field_config(field_name):
    """更新字段配置"""
    try:
        data = request.get_json()

        conn = get_db_connection()
        cursor = conn.cursor()

        # 更新字段配置
        cursor.execute("""
            UPDATE indicator_field_config
            SET field_label = ?,
                field_type = ?,
                display_section = ?,
                is_required = ?,
                sort_order = ?,
                help_text = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE field_name = ?
        """, (
            data.get('field_label'),
            data.get('field_type'),
            data.get('display_section'),
            data.get('is_required', 0),
            data.get('sort_order', 0),
            data.get('help_text'),
            field_name
        ))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '字段配置更新成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 暂时禁用参考范围功能
@app.route('/admin/indicators/<indicator_id>/reference-range')
def admin_reference_range_edit(indicator_id):
    """后端参考范围编辑页面 - 暂时禁用"""
    flash('参考范围功能暂时禁用', 'warning')
    return redirect('/admin/indicators')

if __name__ == '__main__':
    try:
        # 检查数据库文件是否存在
        if not os.path.exists(DB_PATH):
            print(f"错误：数据库文件 {DB_PATH} 不存在")
            print("请先运行数据导入脚本创建数据库")
            exit(1)

        print("🏥 医院等级评审指标说明系统启动中...")
        print("📊 数据库连接正常")
        print("🌐 访问地址: http://localhost:5001")
        print("=" * 50)

        app.run(debug=True, host='0.0.0.0', port=5001)
    except Exception as e:
        print(f"应用启动失败: {e}")
        import traceback
        traceback.print_exc()
