#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院指标管理系统 - 前后端跳转功能演示
"""

from flask import Flask, request
import sqlite3
import os

app = Flask(__name__)

# 数据库路径 - 使用当前目录下的数据库
DB_PATH = 'hospital-evaluation-database.db'

def get_db_connection():
    """获取数据库连接"""
    if not os.path.exists(DB_PATH):
        raise Exception(f"数据库文件不存在: {DB_PATH}")
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def dict_from_row(row):
    """将sqlite3.Row转换为字典"""
    return dict(zip(row.keys(), row)) if row else None

# ==================== 前端系统 ====================

@app.route('/')
def frontend_home():
    """前端主页 - 指标查看系统"""
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>医院指标查看系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .container { max-width: 1200px; margin: 0 auto; }
            .header { background: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; position: relative; }
            .admin-switch { position: absolute; top: 20px; right: 20px; background: #ff6b6b; color: white; padding: 10px 20px; border-radius: 25px; text-decoration: none; font-weight: bold; box-shadow: 0 2px 10px rgba(0,0,0,0.2); transition: all 0.3s ease; }
            .admin-switch:hover { background: #ff5252; transform: scale(1.05); }
            .nav-buttons { display: flex; gap: 15px; justify-content: center; margin-top: 20px; flex-wrap: wrap; }
            .nav-btn { padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 14px; font-weight: 500; transition: all 0.3s ease; }
            .nav-btn-primary { background: #007bff; color: white; }
            .nav-btn-success { background: #28a745; color: white; }
            .nav-btn-warning { background: #ffc107; color: #212529; }
            .nav-btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
            .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 30px; }
            .feature-card { background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; }
            .feature-icon { font-size: 48px; margin-bottom: 15px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <a href="/admin" class="admin-switch">🔧 后台管理</a>
                <h1 style="color: #333; margin: 0 0 10px 0;">🏥 医院指标查看系统</h1>
                <p style="color: #666; margin: 0; font-size: 16px;">查看和浏览医院等级评审指标</p>
                
                <div class="nav-buttons">
                    <a href="/indicators" class="nav-btn nav-btn-primary">📊 浏览指标</a>
                    <a href="/chapters" class="nav-btn nav-btn-success">📚 按章节查看</a>
                    <a href="/search" class="nav-btn nav-btn-warning">🔍 搜索指标</a>
                </div>
            </div>
            
            <div class="features">
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>指标浏览</h3>
                    <p>按章节、小节浏览所有医院指标，查看详细的分子分母信息</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <h3>智能搜索</h3>
                    <p>快速搜索指标名称、编号或描述，精准定位所需指标</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📋</div>
                    <h3>详细信息</h3>
                    <p>查看指标的完整信息，包括计算方法、数据来源、责任科室</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🌳</div>
                    <h3>层级关系</h3>
                    <p>清晰展示指标的父子关系，支持多级指标结构</p>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/indicators')
def frontend_indicators():
    """前端指标列表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取指标数据 - 使用简化的查询
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        # 创建示例指标数据
        sample_indicators = [
            {'id': '1.1.1', 'name': '实际开放床位数', 'chapter': '第一章', 'section': '1.1', 'children': 2},
            {'id': '1.1.2', 'name': '床位使用率', 'chapter': '第一章', 'section': '1.1', 'children': 0},
            {'id': '1.2.1', 'name': '医师配置', 'chapter': '第一章', 'section': '1.2', 'children': 3},
            {'id': '2.1.1', 'name': '门诊服务量', 'chapter': '第二章', 'section': '2.1', 'children': 1},
            {'id': '2.2.1', 'name': '急诊服务质量', 'chapter': '第二章', 'section': '2.2', 'children': 0},
        ]
        
        conn.close()
        
        indicators_html = ""
        for indicator in sample_indicators:
            indicators_html += f'''
            <tr>
                <td style="padding: 12px; font-family: monospace; font-weight: bold; color: #007bff;">{indicator['id']}</td>
                <td style="padding: 12px;">{indicator['name']}</td>
                <td style="padding: 12px; text-align: center;">{indicator['chapter']}</td>
                <td style="padding: 12px; text-align: center;">{indicator['section']}</td>
                <td style="padding: 12px; text-align: center;">{indicator['children']}</td>
                <td style="padding: 12px; text-align: center;">
                    <a href="/indicator/{indicator['id']}" style="background: #007bff; color: white; padding: 6px 12px; text-decoration: none; border-radius: 4px; font-size: 12px;">查看详情</a>
                </td>
            </tr>
            '''
        
        return f'''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>指标列表 - 医院指标查看系统</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                .header {{ background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }}
                .admin-switch {{ background: #ff6b6b; color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }}
                .admin-switch:hover {{ background: #ff5252; }}
                table {{ width: 100%; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                th {{ background: #f8f9fa; padding: 15px; text-align: left; font-weight: 600; border-bottom: 1px solid #dee2e6; }}
                td {{ padding: 12px; border-bottom: 1px solid #f1f3f4; }}
                tr:hover {{ background: #f8f9fa; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div>
                        <h1 style="margin: 0; color: #333;">📊 指标列表</h1>
                        <p style="margin: 5px 0 0 0; color: #666;">浏览医院等级评审指标</p>
                    </div>
                    <a href="/admin" class="admin-switch">🔧 后台管理</a>
                </div>
                
                <table>
                    <thead>
                        <tr>
                            <th>指标ID</th>
                            <th>指标名称</th>
                            <th>章节</th>
                            <th>小节</th>
                            <th>子指标</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {indicators_html}
                    </tbody>
                </table>
                
                <div style="margin-top: 20px; text-align: center;">
                    <a href="/" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">🏠 返回首页</a>
                </div>
            </div>
        </body>
        </html>
        '''
        
    except Exception as e:
        return f'<h1>错误</h1><p>{str(e)}</p><p><a href="/">返回首页</a></p>'

# ==================== 后端管理系统 ====================

@app.route('/admin')
def admin_home():
    """后端管理主页"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取数据库表信息
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        
        return f'''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>后台管理系统</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                .header {{ background: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; position: relative; }}
                .frontend-switch {{ position: absolute; top: 20px; right: 20px; background: #28a745; color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }}
                .frontend-switch:hover {{ background: #218838; }}
                .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }}
                .stat-card {{ background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; }}
                .stat-number {{ font-size: 36px; font-weight: bold; margin-bottom: 10px; }}
                .stat-label {{ color: #666; font-size: 14px; }}
                .management-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
                .management-card {{ background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }}
                .management-card h3 {{ margin-top: 0; color: #333; }}
                .btn {{ padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; font-size: 14px; }}
                .btn-primary {{ background: #007bff; color: white; }}
                .btn-success {{ background: #28a745; color: white; }}
                .btn-warning {{ background: #ffc107; color: #212529; }}
                .btn-danger {{ background: #dc3545; color: white; }}
                .btn:hover {{ opacity: 0.9; transform: translateY(-1px); }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <a href="/" class="frontend-switch">🌐 前端系统</a>
                    <h1 style="color: #333; margin: 0 0 10px 0;">🔧 后台管理系统</h1>
                    <p style="color: #666; margin: 0; font-size: 16px;">医院指标数据管理和维护</p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" style="color: #007bff;">{len(tables)}</div>
                        <div class="stat-label">数据表总数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" style="color: #28a745;">5</div>
                        <div class="stat-label">示例指标</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" style="color: #ffc107;">2</div>
                        <div class="stat-label">章节数量</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" style="color: #dc3545;">4</div>
                        <div class="stat-label">组件数量</div>
                    </div>
                </div>
                
                <div class="management-grid">
                    <div class="management-card">
                        <h3>📊 指标管理</h3>
                        <p>管理医院指标的基本信息、层级关系和分子分母组件</p>
                        <a href="/admin/indicators" class="btn btn-primary">指标列表</a>
                        <a href="/admin/components" class="btn btn-warning">组件管理</a>
                    </div>
                    <div class="management-card">
                        <h3>📚 结构管理</h3>
                        <p>管理指标体系的章节和小节结构</p>
                        <a href="/admin/chapters" class="btn btn-success">章节管理</a>
                        <a href="/admin/sections" class="btn btn-success">小节管理</a>
                    </div>
                    <div class="management-card">
                        <h3>🔧 系统工具</h3>
                        <p>数据验证、批量操作和系统维护工具</p>
                        <a href="/admin/validate" class="btn btn-danger">数据验证</a>
                        <a href="/admin/tools" class="btn btn-warning">系统工具</a>
                    </div>
                    <div class="management-card">
                        <h3>📈 数据分析</h3>
                        <p>查看系统使用情况和数据质量报告</p>
                        <a href="/admin/reports" class="btn btn-primary">数据报告</a>
                        <a href="/admin/logs" class="btn btn-warning">操作日志</a>
                    </div>
                </div>
            </div>
        </body>
        </html>
        '''
        
    except Exception as e:
        return f'<h1>错误</h1><p>{str(e)}</p><p><a href="/">返回首页</a></p>'

@app.route('/admin/components')
def admin_components():
    """后端组件管理"""
    # 创建示例组件数据
    sample_components = [
        {'id': 1, 'indicator_id': '1.1.1', 'type': 'numerator', 'name': '实际开放床位数', 'unit': '张', 'department': '医务科'},
        {'id': 2, 'indicator_id': '1.1.1', 'type': 'denominator', 'name': '标准床位配置数', 'unit': '张', 'department': '规划科'},
        {'id': 3, 'indicator_id': '2.1.1', 'type': 'numerator', 'name': '门诊就诊人次', 'unit': '人次', 'department': '门诊部'},
        {'id': 4, 'indicator_id': '2.1.1', 'type': 'denominator', 'name': '门诊工作日数', 'unit': '天', 'department': '医务科'},
    ]
    
    components_html = ""
    for comp in sample_components:
        type_label = {'numerator': '🔢 分子', 'denominator': '🔣 分母'}.get(comp['type'], '📊 其他')
        components_html += f'''
        <tr>
            <td style="padding: 12px; text-align: center;">{comp['id']}</td>
            <td style="padding: 12px; text-align: center; font-family: monospace; font-weight: bold;">{comp['indicator_id']}</td>
            <td style="padding: 12px; text-align: center;">{type_label}</td>
            <td style="padding: 12px;">{comp['name']}</td>
            <td style="padding: 12px; text-align: center;">{comp['unit']}</td>
            <td style="padding: 12px;">{comp['department']}</td>
            <td style="padding: 12px; text-align: center;">
                <a href="/admin/components/{comp['id']}/edit" style="background: #ffc107; color: white; padding: 6px 12px; text-decoration: none; border-radius: 4px; font-size: 12px;">✏️ 编辑</a>
            </td>
        </tr>
        '''
    
    return f'''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>组件管理 - 后台管理系统</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .header {{ background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }}
            .nav-switches {{ display: flex; gap: 10px; }}
            .nav-switch {{ padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }}
            .frontend-switch {{ background: #28a745; color: white; }}
            .admin-switch {{ background: #6c757d; color: white; }}
            .nav-switch:hover {{ opacity: 0.9; }}
            table {{ width: 100%; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
            th {{ background: #f8f9fa; padding: 15px; text-align: left; font-weight: 600; border-bottom: 1px solid #dee2e6; }}
            td {{ padding: 12px; border-bottom: 1px solid #f1f3f4; }}
            tr:hover {{ background: #f8f9fa; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div>
                    <h1 style="margin: 0; color: #333;">🧮 组件管理</h1>
                    <p style="margin: 5px 0 0 0; color: #666;">管理指标的分子分母组件</p>
                </div>
                <div class="nav-switches">
                    <a href="/" class="nav-switch frontend-switch">🌐 前端系统</a>
                    <a href="/admin" class="nav-switch admin-switch">🔧 管理首页</a>
                </div>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>指标ID</th>
                        <th>类型</th>
                        <th>名称</th>
                        <th>单位</th>
                        <th>牵头科室</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {components_html}
                </tbody>
            </table>
        </div>
    </body>
    </html>
    '''

@app.route('/admin/components/<component_id>/edit')
def edit_component_demo(component_id):
    """组件编辑演示页面"""
    return f'''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>编辑组件 {component_id}</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
            .container {{ max-width: 800px; margin: 0 auto; }}
            .header {{ background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }}
            .nav-switches {{ display: flex; gap: 10px; }}
            .nav-switch {{ padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }}
            .frontend-switch {{ background: #28a745; color: white; }}
            .admin-switch {{ background: #6c757d; color: white; }}
            .nav-switch:hover {{ opacity: 0.9; }}
            .demo-content {{ background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }}
            .success-message {{ background: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin-bottom: 20px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div>
                    <h1 style="margin: 0; color: #333;">🧮 编辑组件 #{component_id}</h1>
                    <p style="margin: 5px 0 0 0; color: #666;">组件编辑功能演示</p>
                </div>
                <div class="nav-switches">
                    <a href="/" class="nav-switch frontend-switch">🌐 前端系统</a>
                    <a href="/admin" class="nav-switch admin-switch">🔧 管理首页</a>
                </div>
            </div>
            
            <div class="demo-content">
                <div class="success-message">
                    ✅ 前后端跳转功能演示成功！
                </div>
                
                <h2>🎉 功能演示完成</h2>
                <p>您已成功体验了前后端系统间的跳转功能：</p>
                
                <ul style="text-align: left; max-width: 400px; margin: 20px auto;">
                    <li>✅ 前端系统 → 后台管理</li>
                    <li>✅ 后台管理 → 前端系统</li>
                    <li>✅ 组件管理 → 编辑页面</li>
                    <li>✅ 跨页面导航保持</li>
                </ul>
                
                <div style="margin-top: 30px;">
                    <a href="/admin/components" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px;">返回组件列表</a>
                    <a href="/admin" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px;">管理首页</a>
                    <a href="/" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px;">前端首页</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🏥 医院指标管理系统启动中...")
    print("🌐 前端系统: http://localhost:5001")
    print("🔧 后端管理: http://localhost:5001/admin")
    print("🧮 组件管理: http://localhost:5001/admin/components")
    print("✨ 前后端跳转功能演示")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5001)
