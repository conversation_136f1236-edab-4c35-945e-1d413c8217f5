{% extends "admin/base.html" %}

{% block title %}组件管理 - 后台管理系统{% endblock %}

{% block breadcrumb %}
<span>后台管理</span> > <span>组件管理</span>
{% endblock %}

{% block content %}
<h1 class="admin-page-title">🧩 组件管理</h1>

<!-- 筛选器 -->
<div class="admin-filters">
    <form method="GET">
        <div class="filter-row">
            <div class="filter-group">
                <label>指标ID</label>
                <input type="text" name="indicator_id" value="{{ selected_indicator }}" placeholder="输入指标ID">
            </div>
            <div class="filter-group">
                <label>组件类型</label>
                <select name="component_type">
                    <option value="">全部类型</option>
                    <option value="numerator" {% if selected_type == 'numerator' %}selected{% endif %}>分子</option>
                    <option value="denominator" {% if selected_type == 'denominator' %}selected{% endif %}>分母</option>
                    <option value="other" {% if selected_type == 'other' %}selected{% endif %}>其他</option>
                </select>
            </div>
            <div class="filter-group">
                <label>搜索</label>
                <input type="text" name="search" value="{{ search }}" placeholder="组件名称、单位">
            </div>
            <div class="filter-group">
                <button type="submit" class="admin-btn admin-btn-primary">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
            </div>
        </div>
    </form>
</div>

<!-- 操作栏 -->
<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
    <div>
        <button class="admin-btn admin-btn-primary" onclick="addComponent()">
            <i class="fas fa-plus"></i>
            添加组件
        </button>
    </div>
    <div style="color: var(--gray-600); font-size: 14px;">
        共 {{ pagination.total }} 个组件，第 {{ pagination.page }} / {{ pagination.pages }} 页
    </div>
</div>

<!-- 数据表格 -->
<div class="admin-table">
    <table>
        <thead>
            <tr>
                <th style="width: 50px;">
                    <input type="checkbox" onchange="toggleSelectAll(this)">
                </th>
                <th style="width: 120px;">指标ID</th>
                <th style="width: 80px;">类型</th>
                <th>组件名称</th>
                <th style="width: 80px;">单位</th>
                <th style="width: 120px;">牵头科室</th>
                <th style="width: 120px;">数据来源</th>
                <th style="width: 180px;">操作</th>
            </tr>
        </thead>
        <tbody>
            {% if components %}
                {% for component in components %}
                <tr>
                    <td>
                        <input type="checkbox" value="{{ component.id }}">
                    </td>
                    <td>
                        <a href="/admin/indicators/{{ component.indicator_id }}" 
                           style="background: var(--primary-light); color: var(--primary); padding: 4px 8px; border-radius: 4px; font-family: monospace; font-size: 12px; font-weight: 500; text-decoration: none;">
                            {{ component.indicator_id }}
                        </a>
                    </td>
                    <td>
                        {% if component.component_type == 'numerator' %}
                        <span style="background: var(--success-light); color: var(--success-dark); padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 500;">
                            分子
                        </span>
                        {% elif component.component_type == 'denominator' %}
                        <span style="background: var(--warning-light); color: var(--warning-dark); padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 500;">
                            分母
                        </span>
                        {% else %}
                        <span style="background: var(--gray-100); color: var(--gray-700); padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 500;">
                            其他
                        </span>
                        {% endif %}
                    </td>
                    <td>
                        <div style="font-weight: 500; color: var(--gray-900);">{{ component.name }}</div>
                        {% if component.definition %}
                        <div style="font-size: 12px; color: var(--gray-600); margin-top: 4px;">
                            {{ component.definition[:60] }}{% if component.definition|length > 60 %}...{% endif %}
                        </div>
                        {% endif %}
                    </td>
                    <td>
                        {% if component.unit %}
                        <span style="background: var(--gray-100); color: var(--gray-700); padding: 2px 6px; border-radius: 4px; font-size: 11px;">
                            {{ component.unit }}
                        </span>
                        {% else %}
                        <span style="color: var(--gray-400);">-</span>
                        {% endif %}
                    </td>
                    <td>
                        <span style="font-size: 12px; color: var(--gray-600);">
                            {{ component.lead_department or '-' }}
                        </span>
                    </td>
                    <td>
                        <span style="font-size: 12px; color: var(--gray-600);">
                            {{ component.data_source[:15] if component.data_source else '-' }}{% if component.data_source and component.data_source|length > 15 %}...{% endif %}
                        </span>
                    </td>
                    <td>
                        <div class="admin-actions">
                            <a href="/admin/components/{{ component.id }}/edit" class="admin-btn admin-btn-warning" title="编辑">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button onclick="deleteComponent('{{ component.id }}')" class="admin-btn admin-btn-danger" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="8" style="text-align: center; padding: 60px; color: var(--gray-500);">
                    <div style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;">🧩</div>
                    <div style="font-size: 16px; margin-bottom: 8px;">暂无组件数据</div>
                    <div style="font-size: 14px;">请调整筛选条件或添加新组件</div>
                </td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- 分页 -->
{% if pagination.pages > 1 %}
<div style="display: flex; justify-content: center; align-items: center; margin-top: 24px; gap: 8px;">
    {% if pagination.page > 1 %}
    <a href="?page={{ pagination.page - 1 }}&indicator_id={{ selected_indicator or '' }}&component_type={{ selected_type or '' }}&search={{ search }}" 
       class="admin-btn admin-btn-outline">
        <i class="fas fa-chevron-left"></i>
        上一页
    </a>
    {% endif %}
    
    <span style="padding: 8px 16px; color: var(--gray-600); font-size: 14px;">
        第 {{ pagination.page }} / {{ pagination.pages }} 页
    </span>
    
    {% if pagination.page < pagination.pages %}
    <a href="?page={{ pagination.page + 1 }}&indicator_id={{ selected_indicator or '' }}&component_type={{ selected_type or '' }}&search={{ search }}" 
       class="admin-btn admin-btn-outline">
        下一页
        <i class="fas fa-chevron-right"></i>
    </a>
    {% endif %}
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// 全选/取消全选
function toggleSelectAll(checkbox) {
    const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
    checkboxes.forEach(cb => cb.checked = checkbox.checked);
}

// 添加组件
function addComponent() {
    const indicatorId = prompt('请输入要添加组件的指标ID:');
    if (indicatorId && indicatorId.trim()) {
        window.location.href = `/admin/indicators/${indicatorId.trim()}/add_component`;
    }
}

// 删除组件
function deleteComponent(id) {
    if (confirm('确定要删除这个组件吗？此操作不可恢复。')) {
        // 创建表单并提交删除请求
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/components/${id}/delete`;
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
