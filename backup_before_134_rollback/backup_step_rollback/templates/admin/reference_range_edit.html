<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑参考范围 - 医院指标管理系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
    <style>
        .reference-range-form {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .form-header {
            background: linear-gradient(135deg, #1976d2, #1565c0);
            color: white;
            padding: 24px;
            text-align: center;
        }

        .form-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 500;
        }

        .form-header .indicator-info {
            margin-top: 8px;
            opacity: 0.9;
            font-size: 14px;
        }

        .form-content {
            padding: 32px;
        }

        .form-section {
            margin-bottom: 32px;
        }

        .form-section-title {
            font-size: 18px;
            font-weight: 500;
            color: #333;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-section-title .material-icons {
            color: #1976d2;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .form-input, .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            font-family: inherit;
        }

        .form-input:focus, .form-textarea:focus {
            outline: none;
            border-color: #1976d2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-textarea.large {
            min-height: 150px;
        }

        .value-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }

        .value-item {
            text-align: center;
        }

        .value-item .form-input {
            text-align: center;
            font-weight: 600;
        }

        .target-value { color: #1976d2; }
        .current-value { color: #f57c00; }
        .completion-rate { color: #388e3c; }

        .form-actions {
            display: flex;
            justify-content: center;
            gap: 16px;
            padding-top: 24px;
            border-top: 1px solid #e0e0e0;
            margin-top: 32px;
        }

        .btn {
            padding: 12px 32px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: #1976d2;
            color: white;
        }

        .btn-primary:hover {
            background: #1565c0;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
        }

        .btn-secondary {
            background: #f5f5f5;
            color: #666;
        }

        .btn-secondary:hover {
            background: #e0e0e0;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #c8e6c9;
        }

        .alert.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #ffcdd2;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        @media (max-width: 768px) {
            .form-content {
                padding: 20px;
            }

            .value-grid {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 导航栏 -->
        <nav class="admin-nav">
            <div class="nav-brand">
                <span class="material-icons">local_hospital</span>
                <span>医院指标管理系统</span>
            </div>
            <div class="nav-links">
                <a href="/admin" class="nav-link">
                    <span class="material-icons">dashboard</span>
                    <span>仪表板</span>
                </a>
                <a href="/admin/indicators" class="nav-link active">
                    <span class="material-icons">analytics</span>
                    <span>指标管理</span>
                </a>
                <a href="/" class="nav-link">
                    <span class="material-icons">visibility</span>
                    <span>前端查看</span>
                </a>
            </div>
        </nav>

        <!-- 主要内容 -->
        <main class="admin-main">
            <div class="reference-range-form">
                <div class="form-header">
                    <h1>编辑参考范围</h1>
                    <div class="indicator-info" id="indicatorInfo">
                        正在加载指标信息...
                    </div>
                </div>

                <div class="form-content">
                    <div class="alert" id="alertMessage"></div>

                    <form id="referenceRangeForm">
                        <!-- 目标值与评价标准 -->
                        <div class="form-section">
                            <div class="form-section-title">
                                <span class="material-icons">track_changes</span>
                                目标值与评价标准
                            </div>
                            <div class="value-grid">
                                <div class="value-item">
                                    <label class="form-label">目标值</label>
                                    <input type="text" name="target_value" class="form-input target-value" placeholder="如：≥1.03:1">
                                </div>
                                <div class="value-item">
                                    <label class="form-label">当前值</label>
                                    <input type="text" name="current_value" class="form-input current-value" placeholder="如：0.85:1">
                                </div>
                                <div class="value-item">
                                    <label class="form-label">完成率(%)</label>
                                    <input type="number" name="completion_rate" class="form-input completion-rate" placeholder="如：82.5" step="0.1">
                                </div>
                            </div>
                        </div>

                        <!-- 指标定义与计算方法 -->
                        <div class="form-section">
                            <div class="form-section-title">
                                <span class="material-icons">description</span>
                                指标定义与计算方法
                            </div>
                            <div class="form-group full-width">
                                <label class="form-label">指标定义</label>
                                <textarea name="indicator_definition" class="form-textarea" placeholder="请输入指标的详细定义说明"></textarea>
                            </div>
                            <div class="form-group full-width">
                                <label class="form-label">计算方法</label>
                                <textarea name="calculation_method" class="form-textarea" placeholder="请输入计算方法的详细说明"></textarea>
                            </div>
                            <div class="form-group full-width">
                                <label class="form-label">计算公式</label>
                                <input type="text" name="calculation_formula" class="form-input" placeholder="如：卫生技术人员总数 ÷ 开放床位数">
                            </div>
                        </div>

                        <!-- 数据来源与统计信息 -->
                        <div class="form-section">
                            <div class="form-section-title">
                                <span class="material-icons">source</span>
                                数据来源与统计信息
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">数据来源</label>
                                    <textarea name="data_sources" class="form-textarea" placeholder="如：医院人事管理系统、床位管理系统"></textarea>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">统计范围</label>
                                    <textarea name="statistical_scope" class="form-textarea" placeholder="请输入统计范围"></textarea>
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">统计频率</label>
                                    <input type="text" name="collection_frequency" class="form-input" placeholder="如：年度统计">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">收集方法</label>
                                    <input type="text" name="collection_method" class="form-input" placeholder="如：系统自动统计">
                                </div>
                            </div>
                        </div>

                        <!-- 注意事项与要求 -->
                        <div class="form-section">
                            <div class="form-section-title">
                                <span class="material-icons">warning</span>
                                注意事项与要求
                            </div>
                            <div class="form-group full-width">
                                <label class="form-label">注意事项</label>
                                <textarea name="notes" class="form-textarea large" placeholder="请输入注意事项，每条注意事项请另起一行"></textarea>
                            </div>
                            <div class="form-group full-width">
                                <label class="form-label">质量要求</label>
                                <textarea name="quality_requirements" class="form-textarea" placeholder="请输入质量要求"></textarea>
                            </div>
                        </div>

                        <!-- 改进建议与政策 -->
                        <div class="form-section">
                            <div class="form-section-title">
                                <span class="material-icons">lightbulb</span>
                                改进建议与政策
                            </div>
                            <div class="form-group full-width">
                                <label class="form-label">改进建议</label>
                                <textarea name="improvement_suggestions" class="form-textarea" placeholder="请输入改进建议"></textarea>
                            </div>
                            <div class="form-group full-width">
                                <label class="form-label">相关政策</label>
                                <textarea name="related_policies" class="form-textarea" placeholder="请输入相关政策文件"></textarea>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <span class="material-icons">save</span>
                                保存参考范围
                            </button>
                            <a href="#" onclick="history.back()" class="btn btn-secondary">
                                <span class="material-icons">arrow_back</span>
                                返回
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script>
        class ReferenceRangeEditor {
            constructor() {
                this.indicatorId = this.getIndicatorIdFromUrl();
                this.init();
            }

            getIndicatorIdFromUrl() {
                const pathParts = window.location.pathname.split('/');
                return pathParts[pathParts.length - 2]; // 获取指标ID
            }

            async init() {
                await this.loadIndicatorInfo();
                await this.loadReferenceRange();
                this.bindEvents();
            }

            async loadIndicatorInfo() {
                try {
                    const response = await fetch(`/api/indicators/${this.indicatorId}`);
                    const result = await response.json();

                    if (result.success) {
                        const indicator = result.data.indicator;
                        document.getElementById('indicatorInfo').textContent = 
                            `${indicator.id} - ${indicator.name}`;
                    }
                } catch (error) {
                    console.error('加载指标信息失败:', error);
                }
            }

            async loadReferenceRange() {
                try {
                    const response = await fetch(`/api/indicators/${this.indicatorId}/reference-range`);
                    const result = await response.json();

                    if (result.success && result.data) {
                        this.populateForm(result.data);
                    }
                } catch (error) {
                    console.error('加载参考范围失败:', error);
                }
            }

            populateForm(data) {
                const form = document.getElementById('referenceRangeForm');
                const inputs = form.querySelectorAll('input, textarea');

                inputs.forEach(input => {
                    if (data[input.name] !== undefined && data[input.name] !== null) {
                        input.value = data[input.name];
                    }
                });
            }

            bindEvents() {
                const form = document.getElementById('referenceRangeForm');
                form.addEventListener('submit', (e) => this.handleSubmit(e));
            }

            async handleSubmit(e) {
                e.preventDefault();
                
                const form = e.target;
                const formData = new FormData(form);
                const data = {};

                for (let [key, value] of formData.entries()) {
                    data[key] = value;
                }

                try {
                    form.classList.add('loading');
                    
                    const response = await fetch(`/api/indicators/${this.indicatorId}/reference-range`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showAlert('参考范围保存成功！', 'success');
                    } else {
                        this.showAlert('保存失败：' + result.error, 'error');
                    }
                } catch (error) {
                    this.showAlert('网络错误：' + error.message, 'error');
                } finally {
                    form.classList.remove('loading');
                }
            }

            showAlert(message, type) {
                const alert = document.getElementById('alertMessage');
                alert.textContent = message;
                alert.className = `alert ${type}`;
                alert.style.display = 'block';

                setTimeout(() => {
                    alert.style.display = 'none';
                }, 5000);
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            new ReferenceRangeEditor();
        });
    </script>
</body>
</html>
