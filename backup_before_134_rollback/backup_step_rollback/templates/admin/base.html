<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}医院指标后台管理系统{% endblock %}</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Design Icons -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />

    <!-- Font Awesome (备用) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Material Design 3 风格样式 -->
    <style>
        /* Material Design 3 Color System */
        :root {
            /* Primary Colors */
            --md-primary: #1976d2;
            --md-primary-container: #e3f2fd;
            --md-on-primary: #ffffff;
            --md-on-primary-container: #0d47a1;

            /* Secondary Colors */
            --md-secondary: #03dac6;
            --md-secondary-container: #e0f7fa;
            --md-on-secondary: #000000;
            --md-on-secondary-container: #004d40;

            /* Tertiary Colors */
            --md-tertiary: #ff9800;
            --md-tertiary-container: #fff3e0;
            --md-on-tertiary: #000000;
            --md-on-tertiary-container: #e65100;

            /* Error Colors */
            --md-error: #d32f2f;
            --md-error-container: #ffebee;
            --md-on-error: #ffffff;
            --md-on-error-container: #b71c1c;

            /* Warning Colors */
            --md-warning: #ff9800;
            --md-warning-container: #fff3e0;
            --md-on-warning: #000000;
            --md-on-warning-container: #e65100;

            /* Success Colors */
            --md-success: #4caf50;
            --md-success-container: #e8f5e8;
            --md-on-success: #ffffff;
            --md-on-success-container: #1b5e20;

            /* Surface Colors */
            --md-surface: #ffffff;
            --md-surface-variant: #f5f5f5;
            --md-surface-container: #fafafa;
            --md-surface-container-high: #f0f0f0;
            --md-surface-container-highest: #e8e8e8;
            --md-on-surface: #1c1b1f;
            --md-on-surface-variant: #49454f;

            /* Outline Colors */
            --md-outline: #79747e;
            --md-outline-variant: #cac4d0;

            /* Background */
            --md-background: #fefbff;
            --md-on-background: #1c1b1f;

            /* Elevation Shadows */
            --md-elevation-1: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
            --md-elevation-2: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
            --md-elevation-3: 0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);
            --md-elevation-4: 0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
            --md-elevation-5: 0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15);

            /* Border Radius */
            --md-corner-none: 0px;
            --md-corner-xs: 4px;
            --md-corner-sm: 8px;
            --md-corner-md: 12px;
            --md-corner-lg: 16px;
            --md-corner-xl: 28px;
            --md-corner-full: 50%;

            /* Typography */
            --md-font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
            --md-font-family-mono: 'JetBrains Mono', 'Courier New', monospace;

            /* Motion */
            --md-motion-duration-short1: 50ms;
            --md-motion-duration-short2: 100ms;
            --md-motion-duration-short3: 150ms;
            --md-motion-duration-short4: 200ms;
            --md-motion-duration-medium1: 250ms;
            --md-motion-duration-medium2: 300ms;
            --md-motion-duration-medium3: 350ms;
            --md-motion-duration-medium4: 400ms;
            --md-motion-duration-long1: 450ms;
            --md-motion-duration-long2: 500ms;
            --md-motion-duration-long3: 550ms;
            --md-motion-duration-long4: 600ms;

            --md-motion-easing-standard: cubic-bezier(0.2, 0.0, 0, 1.0);
            --md-motion-easing-emphasized: cubic-bezier(0.2, 0.0, 0, 1.0);
        }

        /* Material Design Icons Styles */
        .material-symbols-outlined,
        .material-symbols-rounded {
            font-variation-settings:
                'FILL' 0,
                'wght' 400,
                'GRAD' 0,
                'opsz' 24;
            user-select: none;
            vertical-align: middle;
        }

        .material-symbols-filled {
            font-variation-settings:
                'FILL' 1,
                'wght' 400,
                'GRAD' 0,
                'opsz' 24;
        }

        .material-symbols-small {
            font-variation-settings:
                'FILL' 0,
                'wght' 400,
                'GRAD' 0,
                'opsz' 20;
            font-size: 20px;
        }

        .material-symbols-large {
            font-variation-settings:
                'FILL' 0,
                'wght' 400,
                'GRAD' 0,
                'opsz' 48;
            font-size: 48px;
        }

        /* Icon Button Styles */
        .icon-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: var(--md-corner-full);
            border: none;
            background: transparent;
            color: var(--md-on-surface-variant);
            cursor: pointer;
            transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
            position: relative;
            overflow: hidden;
        }

        .icon-button:hover {
            background-color: var(--md-primary-container);
            color: var(--md-on-primary-container);
        }

        .icon-button.filled {
            background-color: var(--md-primary);
            color: var(--md-on-primary);
        }

        .icon-button.filled:hover {
            background-color: var(--md-primary);
            box-shadow: var(--md-elevation-1);
        }

        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--md-font-family);
            background-color: var(--md-background);
            color: var(--md-on-background);
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        /* Material Design 3 Layout System */
        .admin-container {
            display: flex;
            min-height: 100vh;
            background-color: var(--md-background);
            font-family: var(--md-font-family);
        }

        /* Navigation Rail (Sidebar) */
        .admin-sidebar {
            width: 280px;
            background: var(--md-surface);
            border-right: 1px solid var(--md-outline-variant);
            display: flex;
            flex-direction: column;
            box-shadow: var(--md-elevation-1);
        }

        .admin-header {
            padding: 24px 20px;
            border-bottom: 1px solid var(--md-outline-variant);
            background: var(--md-primary);
            color: var(--md-on-primary);
        }

        .admin-header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            letter-spacing: 0.1px;
        }

        .admin-nav {
            padding: 16px 0;
            flex: 1;
        }

        .admin-nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--md-on-surface);
            text-decoration: none;
            transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
            border-radius: 0 var(--md-corner-xl) var(--md-corner-xl) 0;
            margin: 2px 12px 2px 0;
            font-size: 14px;
            font-weight: 500;
            position: relative;
        }

        .admin-nav-item:hover {
            background-color: var(--md-primary-container);
            color: var(--md-on-primary-container);
        }

        .admin-nav-item.active {
            background-color: var(--md-secondary-container);
            color: var(--md-on-secondary-container);
        }

        .admin-nav-item i {
            margin-right: 12px;
            width: 20px;
            font-size: 16px;
        }

        /* Main Content Area */
        .admin-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: var(--md-surface-variant);
        }

        .admin-topbar {
            background: var(--md-surface);
            padding: 16px 24px;
            border-bottom: 1px solid var(--md-outline-variant);
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--md-elevation-1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .admin-content {
            padding: 24px;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
            flex: 1;
        }

        .admin-page-title {
            margin: 0 0 24px 0;
            font-size: 28px;
            font-weight: 400;
            color: var(--md-on-background);
            letter-spacing: -0.5px;
        }

        /* Material Design 3 Button System */
        .md-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 10px 24px;
            border: none;
            border-radius: var(--md-corner-xl);
            font-family: var(--md-font-family);
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            letter-spacing: 0.1px;
            cursor: pointer;
            text-decoration: none;
            transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
            position: relative;
            overflow: hidden;
            min-height: 40px;
        }

        .md-button:disabled {
            opacity: 0.38;
            cursor: not-allowed;
        }

        /* Filled Button (Primary) */
        .md-button.filled {
            background-color: var(--md-primary);
            color: var(--md-on-primary);
            box-shadow: var(--md-elevation-1);
        }

        .md-button.filled:hover {
            background-color: var(--md-primary);
            box-shadow: var(--md-elevation-2);
            transform: translateY(-1px);
        }

        .md-button.filled:active {
            box-shadow: var(--md-elevation-1);
            transform: translateY(0);
        }

        /* Filled Tonal Button (Secondary) */
        .md-button.filled-tonal {
            background-color: var(--md-secondary-container);
            color: var(--md-on-secondary-container);
        }

        .md-button.filled-tonal:hover {
            background-color: var(--md-secondary-container);
            box-shadow: var(--md-elevation-1);
            transform: translateY(-1px);
        }

        /* Outlined Button */
        .md-button.outlined {
            background-color: transparent;
            color: var(--md-primary);
            border: 1px solid var(--md-outline);
        }

        .md-button.outlined:hover {
            background-color: var(--md-primary-container);
            border-color: var(--md-primary);
        }

        /* Text Button */
        .md-button.text {
            background-color: transparent;
            color: var(--md-primary);
            padding: 10px 12px;
        }

        .md-button.text:hover {
            background-color: var(--md-primary-container);
        }

        /* Error/Danger Buttons */
        .md-button.error {
            background-color: var(--md-error);
            color: var(--md-on-error);
            box-shadow: var(--md-elevation-1);
        }

        .md-button.error:hover {
            background-color: var(--md-error);
            box-shadow: var(--md-elevation-2);
            transform: translateY(-1px);
        }

        /* Warning Buttons */
        .md-button.warning {
            background-color: var(--md-warning);
            color: var(--md-on-warning);
            box-shadow: var(--md-elevation-1);
        }

        .md-button.warning:hover {
            background-color: var(--md-warning);
            box-shadow: var(--md-elevation-2);
            transform: translateY(-1px);
        }

        /* Success Buttons */
        .md-button.success {
            background-color: var(--md-success);
            color: var(--md-on-success);
            box-shadow: var(--md-elevation-1);
        }

        .md-button.success:hover {
            background-color: var(--md-success);
            box-shadow: var(--md-elevation-2);
            transform: translateY(-1px);
        }

        /* Legacy Button Classes for Compatibility */
        .admin-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 10px 20px;
            border: none;
            border-radius: var(--md-corner-xl);
            font-family: var(--md-font-family);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
            min-height: 40px;
        }

        .admin-btn-primary {
            background-color: var(--md-primary);
            color: var(--md-on-primary);
            box-shadow: var(--md-elevation-1);
        }

        .admin-btn-primary:hover {
            background-color: var(--md-primary);
            box-shadow: var(--md-elevation-2);
            transform: translateY(-1px);
        }

        .admin-btn-warning {
            background-color: var(--md-warning);
            color: var(--md-on-warning);
            box-shadow: var(--md-elevation-1);
        }

        .admin-btn-warning:hover {
            background-color: var(--md-warning);
            box-shadow: var(--md-elevation-2);
            transform: translateY(-1px);
        }

        .admin-btn-danger {
            background-color: var(--md-error);
            color: var(--md-on-error);
            box-shadow: var(--md-elevation-1);
        }

        .admin-btn-danger:hover {
            background-color: var(--md-error);
            box-shadow: var(--md-elevation-2);
            transform: translateY(-1px);
        }

        .admin-btn-outline {
            background-color: transparent;
            color: var(--md-primary);
            border: 1px solid var(--md-outline);
        }

        .admin-btn-outline:hover {
            background-color: var(--md-primary-container);
            border-color: var(--md-primary);
        }

        /* Statistics Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: var(--md-surface);
            padding: 24px;
            border-radius: var(--md-corner-lg);
            box-shadow: var(--md-elevation-1);
            border: 1px solid var(--md-outline-variant);
            transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--md-primary);
        }

        .stat-card.warning::before {
            background: var(--md-warning);
        }

        .stat-card.danger::before {
            background: var(--md-error);
        }

        .stat-card.success::before {
            background: var(--md-success);
        }

        .stat-card:hover {
            box-shadow: var(--md-elevation-2);
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 36px;
            font-weight: 400;
            color: var(--md-on-surface);
            margin-bottom: 8px;
            line-height: 1;
        }

        .stat-label {
            color: var(--md-on-surface-variant);
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Data Tables - Material Design 3 */
        .admin-table {
            background: var(--md-surface);
            border-radius: var(--md-corner-lg);
            overflow: hidden;
            box-shadow: var(--md-elevation-1);
            border: 1px solid var(--md-outline-variant);
        }

        .admin-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .admin-table th {
            background: var(--md-surface-container);
            padding: 16px 20px;
            text-align: left;
            font-weight: 500;
            color: var(--md-on-surface-variant);
            border-bottom: 1px solid var(--md-outline-variant);
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .admin-table td {
            padding: 16px 20px;
            border-bottom: 1px solid var(--md-outline-variant);
            color: var(--md-on-surface);
            font-size: 14px;
        }

        .admin-table tr:hover {
            background-color: var(--md-surface-container);
        }

        .admin-table tr:last-child td {
            border-bottom: none;
        }

        /* Action Buttons */
        .admin-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        /* Form Controls - Material Design 3 */
        .admin-filters {
            background: var(--md-surface);
            padding: 24px;
            border-radius: var(--md-corner-lg);
            margin-bottom: 24px;
            box-shadow: var(--md-elevation-1);
            border: 1px solid var(--md-outline-variant);
        }

        .filter-row {
            display: flex;
            gap: 20px;
            align-items: end;
            flex-wrap: wrap;
        }

        .filter-group {
            flex: 1;
            min-width: 200px;
        }

        .filter-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--md-on-surface);
            font-size: 14px;
        }

        .filter-group select,
        .filter-group input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--md-outline);
            border-radius: var(--md-corner-xs);
            font-size: 14px;
            transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
            background: var(--md-surface);
            color: var(--md-on-surface);
        }

        .filter-group select:focus,
        .filter-group input:focus {
            outline: none;
            border-color: var(--md-primary);
            box-shadow: 0 0 0 3px var(--md-primary-container);
        }

        /* Flash Messages - Material Design 3 */
        .flash-messages {
            margin-bottom: 24px;
        }

        .flash-message {
            padding: 16px 20px;
            border-radius: var(--md-corner-sm);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
            box-shadow: var(--md-elevation-1);
        }

        .flash-message::before {
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            font-size: 16px;
        }

        .flash-message.success {
            background: var(--md-success-container);
            color: var(--md-on-success-container);
            border-left: 4px solid var(--md-success);
        }

        .flash-message.success::before {
            content: "\f00c";
        }

        .flash-message.error {
            background: var(--md-error-container);
            color: var(--md-on-error-container);
            border-left: 4px solid var(--md-error);
        }

        .flash-message.error::before {
            content: "\f071";
        }

        .flash-message.info {
            background: var(--md-primary-container);
            color: var(--md-on-primary-container);
            border-left: 4px solid var(--md-primary);
        }

        .flash-message.info::before {
            content: "\f05a";
        }

        /* Breadcrumb Navigation - Material Design 3 */
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--md-on-surface-variant);
            font-size: 14px;
        }

        .breadcrumb span {
            color: var(--md-on-surface);
            font-weight: 500;
        }

        .breadcrumb a {
            color: var(--md-primary);
            text-decoration: none;
            transition: color var(--md-motion-duration-short4) var(--md-motion-easing-standard);
        }

        .breadcrumb a:hover {
            color: var(--md-primary);
            text-decoration: underline;
        }

        /* Responsive Design - Material Design 3 */
        @media (max-width: 1024px) {
            .admin-sidebar {
                width: 240px;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .admin-container {
                flex-direction: column;
            }

            .admin-sidebar {
                width: 100%;
                position: relative;
                box-shadow: none;
                border-right: none;
                border-bottom: 1px solid var(--md-outline-variant);
            }

            .admin-content {
                padding: 16px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .filter-row {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
            }

            .admin-topbar {
                padding: 12px 16px;
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }

            .admin-page-title {
                font-size: 24px;
            }

            .admin-actions {
                flex-direction: column;
                gap: 8px;
            }

            .md-button,
            .admin-btn {
                width: 100%;
                justify-content: center;
            }

            .notification {
                top: 10px;
                right: 10px;
                left: 10px;
                min-width: auto;
                max-width: none;
            }
        }

        /* Notification System */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--md-surface);
            color: var(--md-on-surface);
            padding: 16px 20px;
            border-radius: var(--md-corner-md);
            box-shadow: var(--md-elevation-3);
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 300px;
            max-width: 500px;
            transform: translateX(100%);
            opacity: 0;
            transition: all var(--md-motion-duration-medium2) var(--md-motion-easing-standard);
            z-index: 1000;
            border-left: 4px solid var(--md-primary);
        }

        .notification.active {
            transform: translateX(0);
            opacity: 1;
        }

        .notification.success {
            border-left-color: var(--md-success);
            background: var(--md-success-container);
            color: var(--md-on-success-container);
        }

        .notification.error {
            border-left-color: var(--md-error);
            background: var(--md-error-container);
            color: var(--md-on-error-container);
        }

        .notification.warning {
            border-left-color: var(--md-warning);
            background: var(--md-warning-container);
            color: var(--md-on-warning-container);
        }

        .notification-icon {
            font-size: 20px;
            flex-shrink: 0;
        }

        .notification-message {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
        }

        /* Page Title Elements (Hidden) */
        .page-title,
        .page-subtitle {
            display: none;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <div class="admin-sidebar">
            <div class="admin-header">
                <h1>
                    🏥 后台管理
                </h1>
            </div>
            <nav class="admin-nav">
                <a href="/admin/dashboard" class="admin-nav-item">
                    📊 数据总览
                </a>
                <a href="/admin/chapters" class="admin-nav-item">
                    📚 章节管理
                </a>
                <a href="/admin/sections" class="admin-nav-item">
                    📋 小节管理
                </a>
                <a href="/admin/indicators" class="admin-nav-item">
                    📈 指标管理
                </a>
                <a href="/admin/components" class="admin-nav-item">
                    🧩 组件管理
                </a>
                <a href="#" class="admin-nav-item">
                    ✅ 数据验证
                </a>
                <a href="#" class="admin-nav-item">
                    📝 操作日志
                </a>
            </nav>
        </div>

        <!-- 主内容区 -->
        <div class="admin-main">
            <!-- 顶部栏 -->
            <div class="admin-topbar">
                <div class="breadcrumb">
                    {% block breadcrumb %}
                    <span>后台管理</span>
                    {% endblock %}
                </div>
                <div class="admin-user" style="display: flex; align-items: center; gap: 12px;">
                    <a href="http://localhost:5001" class="md-button filled" target="_blank">
                        🌐 前端系统
                    </a>
                    <span style="color: var(--md-on-surface); font-weight: 500;">欢迎，{{ session.admin_user or 'Admin' }}</span>
                    <button class="md-button outlined" onclick="alert('退出功能开发中...')">
                        🚪 退出
                    </button>
                </div>
            </div>

            <!-- 内容区 -->
            <div class="admin-content">
                <!-- 消息提示 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="flash-messages">
                            {% for category, message in messages %}
                                <div class="flash-message {{ category }}">{{ message }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- 通知组件 -->
    <div id="notification" class="notification">
        <i class="notification-icon fas fa-info-circle"></i>
        <span class="notification-message"></span>
    </div>

    <!-- 页面标题元素 (隐藏，供JavaScript使用) -->
    <div style="display: none;">
        <div class="page-title"></div>
        <div class="page-subtitle"></div>
    </div>

    <!-- 全局错误处理 -->
    <script src="/static/js/error-handler.js"></script>

    <!-- Material Design 3 JavaScript Enhancements -->
    <script>
        // Material Design 3 Ripple Effect
        document.addEventListener('DOMContentLoaded', function() {
            // Add ripple effect to buttons
            const buttons = document.querySelectorAll('.md-button, .admin-btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.3);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;

                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Add active navigation highlighting
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.admin-nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    item.classList.add('active');
                }
            });
        });

        // Add ripple animation keyframes
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
