{% extends "admin/base.html" %}

{% block title %}组件管理 - 后台管理系统{% endblock %}

{% block breadcrumb %}
<span>后台管理</span> > <span>组件管理</span>
{% endblock %}

{% block content %}
<h1 class="admin-page-title">🧩 组件管理</h1>

<!-- 筛选器 -->
<div class="admin-filters">
    <form method="GET">
        <div class="filter-row">
            <div class="filter-group">
                <label>指标ID</label>
                <input type="text" name="indicator_id" value="{{ selected_indicator }}" placeholder="输入指标ID">
            </div>
            <div class="filter-group">
                <label>组件类型</label>
                <select name="component_type">
                    <option value="">全部类型</option>
                    <option value="numerator" {% if selected_type == 'numerator' %}selected{% endif %}>分子</option>
                    <option value="denominator" {% if selected_type == 'denominator' %}selected{% endif %}>分母</option>
                    <option value="other" {% if selected_type == 'other' %}selected{% endif %}>其他</option>
                </select>
            </div>
            <div class="filter-group">
                <label>搜索</label>
                <input type="text" name="search" value="{{ search }}" placeholder="组件名称、单位">
            </div>
            <div class="filter-group">
                <button type="submit" class="admin-btn admin-btn-primary">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
            </div>
        </div>
    </form>
</div>

<!-- 操作栏 -->
<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
    <div>
        <button class="admin-btn admin-btn-primary" onclick="addComponent()">
            <i class="fas fa-plus"></i>
            添加组件
        </button>
    </div>
    <div style="color: var(--gray-600); font-size: 14px;">
        共 {{ pagination.total }} 个组件，第 {{ pagination.page }} / {{ pagination.pages }} 页
    </div>
</div>

<!-- 数据表格 -->
<div class="admin-table">
    <table>
        <thead>
            <tr>
                <th style="width: 50px;">
                    <input type="checkbox" onchange="toggleSelectAll(this)">
                </th>
                <th style="width: 120px;">指标ID</th>
                <th style="width: 80px;">类型</th>
                <th>组件名称</th>
                <th style="width: 80px;">单位</th>
                <th style="width: 120px;">牵头科室</th>
                <th style="width: 120px;">数据来源</th>
                <th style="width: 180px;">操作</th>
            </tr>
        </thead>
        <tbody>
            {% if components %}
                {% for component in components %}
                <tr>
                    <td>
                        <input type="checkbox" value="{{ component.id }}">
                    </td>
                    <td>
                        <a href="/admin/indicators/{{ component.indicator_id }}"
                           style="background: var(--primary-light); color: var(--primary); padding: 4px 8px; border-radius: 4px; font-family: monospace; font-size: 12px; font-weight: 500; text-decoration: none;">
                            {{ component.indicator_id }}
                        </a>
                    </td>
                    <td>
                        {% if component.component_type == 'numerator' %}
                        <span style="background: var(--success-light); color: var(--success-dark); padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 500;">
                            分子
                        </span>
                        {% elif component.component_type == 'denominator' %}
                        <span style="background: var(--warning-light); color: var(--warning-dark); padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 500;">
                            分母
                        </span>
                        {% else %}
                        <span style="background: var(--gray-100); color: var(--gray-700); padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 500;">
                            其他
                        </span>
                        {% endif %}
                    </td>
                    <td>
                        <div style="font-weight: 500; color: var(--gray-900);">{{ component.name }}</div>
                        {% if component.definition %}
                        <div style="font-size: 12px; color: var(--gray-600); margin-top: 4px;">
                            {{ component.definition[:60] }}{% if component.definition|length > 60 %}...{% endif %}
                        </div>
                        {% endif %}
                    </td>
                    <td>
                        {% if component.unit %}
                        <span style="background: var(--gray-100); color: var(--gray-700); padding: 2px 6px; border-radius: 4px; font-size: 11px;">
                            {{ component.unit }}
                        </span>
                        {% else %}
                        <span style="color: var(--gray-400);">-</span>
                        {% endif %}
                    </td>
                    <td>
                        <span style="font-size: 12px; color: var(--gray-600);">
                            {{ component.lead_department or '-' }}
                        </span>
                    </td>
                    <td>
                        <span style="font-size: 12px; color: var(--gray-600);">
                            {{ component.data_source[:15] if component.data_source else '-' }}{% if component.data_source and component.data_source|length > 15 %}...{% endif %}
                        </span>
                    </td>
                    <td>
                        <div class="admin-actions">
                            <a href="/admin/components/{{ component.id }}/edit" class="admin-btn admin-btn-warning" title="编辑">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button onclick="deleteComponent('{{ component.id }}')" class="admin-btn admin-btn-danger" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="8" style="text-align: center; padding: 60px; color: var(--gray-500);">
                    <div style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;">🧩</div>
                    <div style="font-size: 16px; margin-bottom: 8px;">暂无组件数据</div>
                    <div style="font-size: 14px;">请调整筛选条件或添加新组件</div>
                </td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- 分页 -->
{% if pagination.pages > 1 %}
<div style="display: flex; justify-content: center; align-items: center; margin-top: 24px; gap: 8px;">
    {% if pagination.page > 1 %}
    <a href="?page={{ pagination.page - 1 }}&indicator_id={{ selected_indicator or '' }}&component_type={{ selected_type or '' }}&search={{ search }}"
       class="admin-btn admin-btn-outline">
        <i class="fas fa-chevron-left"></i>
        上一页
    </a>
    {% endif %}

    <span style="padding: 8px 16px; color: var(--gray-600); font-size: 14px;">
        第 {{ pagination.page }} / {{ pagination.pages }} 页
    </span>

    {% if pagination.page < pagination.pages %}
    <a href="?page={{ pagination.page + 1 }}&indicator_id={{ selected_indicator or '' }}&component_type={{ selected_type or '' }}&search={{ search }}"
       class="admin-btn admin-btn-outline">
        下一页
        <i class="fas fa-chevron-right"></i>
    </a>
    {% endif %}
</div>
{% endif %}
{% block extra_js %}
<script>
// 全选/取消全选
function toggleSelectAll(checkbox) {
    const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
    checkboxes.forEach(cb => cb.checked = checkbox.checked);
}

// 添加组件
function addComponent() {
    const indicatorId = prompt('请输入要添加组件的指标ID:');
    if (indicatorId && indicatorId.trim()) {
        window.location.href = `/admin/indicators/${indicatorId.trim()}/add_component`;
    }
}

// 删除组件
function deleteComponent(id) {
    if (confirm('确定要删除这个组件吗？此操作不可恢复。')) {
        // 创建表单并提交删除请求
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/components/${id}/delete`;

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}

<style>
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e1e8ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.modal-close:hover {
    background-color: #f1f3f4;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #e1e8ed;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let totalPages = 1;
let isEditMode = false;

// 页面加载时获取组件数据
document.addEventListener('DOMContentLoaded', function() {
    loadComponents();
});

// 加载组件数据
function loadComponents(page = 1) {
    currentPage = page;

    const params = new URLSearchParams({
        page: page,
        per_page: 20,
        search: document.getElementById('searchFilter').value,
        indicator_id: document.getElementById('indicatorFilter').value,
        component_type: document.getElementById('typeFilter').value
    });

    fetch(`/api/admin/components?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderComponents(data.data.components);
                renderPagination(data.data.pagination);
            } else {
                showError('加载组件数据失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('网络错误: ' + error.message);
        });
}

// 渲染组件表格
function renderComponents(components) {
    const tbody = document.getElementById('componentsTableBody');

    if (components.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" style="text-align: center; padding: 40px; color: #666;">
                    <i class="fas fa-inbox"></i><br>
                    暂无组件数据
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = components.map(component => {
        const typeLabels = {
            'numerator': '<span style="color: #3498db;">🔢 分子</span>',
            'denominator': '<span style="color: #e74c3c;">🔣 分母</span>',
            'other': '<span style="color: #95a5a6;">📊 其他</span>'
        };

        return `
            <tr>
                <td>${component.id}</td>
                <td><strong>${component.indicator_id}</strong></td>
                <td>${typeLabels[component.component_type] || component.component_type}</td>
                <td>${component.name}</td>
                <td>${component.unit || '-'}</td>
                <td>${component.lead_department || '-'}</td>
                <td>${component.data_source || '-'}</td>
                <td>${component.updated_at ? new Date(component.updated_at).toLocaleDateString() : '-'}</td>
                <td class="admin-actions">
                    <button class="admin-btn admin-btn-primary" onclick="editComponent(${component.id})">
                        <i class="fas fa-edit"></i>
                        编辑
                    </button>
                    <button class="admin-btn admin-btn-danger" onclick="deleteComponent(${component.id})">
                        <i class="fas fa-trash"></i>
                        删除
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

// 渲染分页
function renderPagination(pagination) {
    const container = document.getElementById('pagination');
    totalPages = pagination.pages;

    if (totalPages <= 1) {
        container.innerHTML = '';
        return;
    }

    let html = '';

    // 上一页
    html += `<button ${pagination.page <= 1 ? 'disabled' : ''} onclick="loadComponents(${pagination.page - 1})">
        <i class="fas fa-chevron-left"></i>
    </button>`;

    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === pagination.page) {
            html += `<button class="active">${i}</button>`;
        } else if (i === 1 || i === totalPages || Math.abs(i - pagination.page) <= 2) {
            html += `<button onclick="loadComponents(${i})">${i}</button>`;
        } else if (i === pagination.page - 3 || i === pagination.page + 3) {
            html += `<span>...</span>`;
        }
    }

    // 下一页
    html += `<button ${pagination.page >= totalPages ? 'disabled' : ''} onclick="loadComponents(${pagination.page + 1})">
        <i class="fas fa-chevron-right"></i>
    </button>`;

    container.innerHTML = html;
}

// 显示新增模态框
function showAddModal() {
    isEditMode = false;
    document.getElementById('modalTitle').textContent = '新增组件';
    document.getElementById('componentForm').reset();
    document.getElementById('componentId').value = '';
    document.getElementById('componentModal').style.display = 'flex';
}

// 编辑组件
function editComponent(componentId) {
    isEditMode = true;
    document.getElementById('modalTitle').textContent = '编辑组件';

    // 获取组件详情
    fetch(`/api/admin/components/${componentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const component = data.data;
                document.getElementById('componentId').value = component.id;
                document.getElementById('indicatorId').value = component.indicator_id || '';
                document.getElementById('componentType').value = component.component_type || '';
                document.getElementById('componentName').value = component.name || '';
                document.getElementById('definition').value = component.definition || '';
                document.getElementById('unit').value = component.unit || '';
                document.getElementById('leadDepartment').value = component.lead_department || '';
                document.getElementById('dataSource').value = component.data_source || '';
                document.getElementById('logicDefinition').value = component.logic_definition || '';
                document.getElementById('collectionMethod').value = component.collection_method || '';
                document.getElementById('calculationFormula').value = component.calculation_formula || '';
                document.getElementById('notes').value = component.notes || '';

                document.getElementById('componentModal').style.display = 'flex';
            } else {
                showError('获取组件信息失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('网络错误: ' + error.message);
        });
}

// 保存组件
function saveComponent() {
    const formData = {
        indicator_id: document.getElementById('indicatorId').value,
        component_type: document.getElementById('componentType').value,
        name: document.getElementById('componentName').value,
        definition: document.getElementById('definition').value,
        unit: document.getElementById('unit').value,
        lead_department: document.getElementById('leadDepartment').value,
        data_source: document.getElementById('dataSource').value,
        logic_definition: document.getElementById('logicDefinition').value,
        collection_method: document.getElementById('collectionMethod').value,
        calculation_formula: document.getElementById('calculationFormula').value,
        notes: document.getElementById('notes').value
    };

    // 验证必填字段
    if (!formData.indicator_id || !formData.component_type || !formData.name) {
        showError('请填写所有必填字段');
        return;
    }

    const url = isEditMode ?
        `/api/admin/components/${document.getElementById('componentId').value}` :
        '/api/admin/components';
    const method = isEditMode ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(isEditMode ? '组件更新成功' : '组件创建成功');
            closeModal();
            loadComponents(currentPage);
        } else {
            showError('保存失败: ' + data.error);
        }
    })
    .catch(error => {
        showError('网络错误: ' + error.message);
    });
}

// 关闭模态框
function closeModal() {
    document.getElementById('componentModal').style.display = 'none';
}

// 删除组件
function deleteComponent(componentId) {
    if (confirm('确定要删除这个组件吗？')) {
        fetch(`/api/admin/components/${componentId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess('组件删除成功');
                loadComponents(currentPage);
            } else {
                showError('删除失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('网络错误: ' + error.message);
        });
    }
}

// 显示成功消息
function showSuccess(message) {
    alert('✅ ' + message);
}

// 显示错误消息
function showError(message) {
    alert('❌ ' + message);
}

// 回车搜索
document.getElementById('searchFilter').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        loadComponents(1);
    }
});

document.getElementById('indicatorFilter').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        loadComponents(1);
    }
});

// 点击模态框外部关闭
document.getElementById('componentModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>
{% endblock %}
