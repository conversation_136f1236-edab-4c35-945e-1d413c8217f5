#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院指标管理系统 - 简化版后台管理程序
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session
import sqlite3
import os
from functools import wraps

app = Flask(__name__)
app.config['SECRET_KEY'] = 'hospital_admin_system_2024'

# 数据库路径
import os
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DB_PATH = os.path.join(BASE_DIR, 'DATABASE-HOSPITAL', 'hospital_indicator_system.db')

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def dict_from_row(row):
    """将sqlite3.Row转换为字典"""
    return dict(zip(row.keys(), row)) if row else None

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_logged_in' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# 登录页面
@app.route('/admin/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if username == 'admin' and password == 'hospital2024':
            session['admin_logged_in'] = True
            session['admin_user'] = username
            flash('登录成功', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误', 'error')

    return render_template('admin/login.html')

@app.route('/admin/logout')
def logout():
    session.clear()
    flash('已退出登录', 'info')
    return redirect(url_for('login'))

# 管理首页
@app.route('/admin')
@app.route('/admin/dashboard')
@login_required
def dashboard():
    """管理首页 - 数据总览"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取统计数据
        stats = {}

        # 章节统计
        cursor.execute("SELECT COUNT(*) as count FROM chapters WHERE is_active = 1")
        stats['chapters'] = cursor.fetchone()['count']

        # 小节统计
        cursor.execute("SELECT COUNT(*) as count FROM sections WHERE is_active = 1")
        stats['sections'] = cursor.fetchone()['count']

        # 指标统计
        cursor.execute("SELECT COUNT(*) as count FROM indicators WHERE is_active = 1")
        stats['indicators'] = cursor.fetchone()['count']

        conn.close()

        return render_template('admin/dashboard.html', stats=stats, recent_logs=[])

    except Exception as e:
        flash(f'加载数据失败: {str(e)}', 'error')
        return render_template('admin/dashboard.html', stats={}, recent_logs=[])

# 章节管理
@app.route('/admin/chapters')
@login_required
def chapters():
    """章节管理页面"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT c.*,
                   COUNT(DISTINCT s.id) as section_count,
                   COUNT(DISTINCT i.id) as indicator_count
            FROM chapters c
            LEFT JOIN sections s ON c.id = s.chapter_id AND s.is_active = 1
            LEFT JOIN indicators i ON c.id = i.chapter_id AND i.is_active = 1
            WHERE c.is_active = 1
            GROUP BY c.id
            ORDER BY c.code
        """)

        chapters = [dict_from_row(row) for row in cursor.fetchall()]
        conn.close()

        return render_template('admin/chapters.html', chapters=chapters)

    except Exception as e:
        flash(f'加载章节数据失败: {str(e)}', 'error')
        return render_template('admin/chapters.html', chapters=[])

# 小节管理
@app.route('/admin/sections')
@login_required
def sections():
    """小节管理页面"""
    try:
        chapter_id = request.args.get('chapter_id')

        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取章节列表用于筛选
        cursor.execute("SELECT * FROM chapters WHERE is_active = 1 ORDER BY code")
        chapters = [dict_from_row(row) for row in cursor.fetchall()]

        # 获取小节数据
        if chapter_id:
            cursor.execute("""
                SELECT s.*, c.name as chapter_name, c.code as chapter_code,
                       COUNT(i.id) as indicator_count
                FROM sections s
                LEFT JOIN chapters c ON s.chapter_id = c.id
                LEFT JOIN indicators i ON s.id = i.section_id AND i.is_active = 1
                WHERE s.is_active = 1 AND s.chapter_id = ?
                GROUP BY s.id
                ORDER BY s.sort_order
            """, (chapter_id,))
        else:
            cursor.execute("""
                SELECT s.*, c.name as chapter_name, c.code as chapter_code,
                       COUNT(i.id) as indicator_count
                FROM sections s
                LEFT JOIN chapters c ON s.chapter_id = c.id
                LEFT JOIN indicators i ON s.id = i.section_id AND i.is_active = 1
                WHERE s.is_active = 1
                GROUP BY s.id
                ORDER BY c.code, s.sort_order
            """)

        sections = [dict_from_row(row) for row in cursor.fetchall()]
        conn.close()

        return render_template('admin/sections.html',
                             sections=sections,
                             chapters=chapters,
                             selected_chapter=chapter_id)

    except Exception as e:
        flash(f'加载小节数据失败: {str(e)}', 'error')
        return render_template('admin/sections.html', sections=[], chapters=[])

# 指标管理
@app.route('/admin/indicators')
@login_required
def indicators():
    """指标管理页面"""
    try:
        chapter_id = request.args.get('chapter_id')
        section_id = request.args.get('section_id')
        search = request.args.get('search', '')
        page = int(request.args.get('page', 1))
        per_page = 20

        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取章节和小节列表用于筛选
        cursor.execute("SELECT * FROM chapters WHERE is_active = 1 ORDER BY code")
        chapters = [dict_from_row(row) for row in cursor.fetchall()]

        cursor.execute("SELECT * FROM sections WHERE is_active = 1 ORDER BY chapter_id, sort_order")
        sections = [dict_from_row(row) for row in cursor.fetchall()]

        # 构建查询条件
        where_conditions = ["i.is_active = 1"]
        params = []

        if chapter_id:
            where_conditions.append("i.chapter_id = ?")
            params.append(chapter_id)

        if section_id:
            where_conditions.append("i.section_id = ?")
            params.append(section_id)

        if search:
            where_conditions.append("(i.name LIKE ? OR i.id LIKE ? OR i.description LIKE ?)")
            params.extend([f"%{search}%", f"%{search}%", f"%{search}%"])

        where_clause = " AND ".join(where_conditions)

        # 获取总数
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM indicators i
            WHERE {where_clause}
        """, params)

        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT i.*,
                   c.name as chapter_name, c.code as chapter_code,
                   s.name as section_name, s.code as section_code,
                   p.name as parent_name,
                   COUNT(DISTINCT child.id) as children_count
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN sections s ON i.section_id = s.id
            LEFT JOIN indicators p ON i.parent_id = p.id
            LEFT JOIN indicators child ON child.parent_id = i.id AND child.is_active = 1
            WHERE {where_clause}
            GROUP BY i.id
            ORDER BY i.id
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])

        indicators = [dict_from_row(row) for row in cursor.fetchall()]

        conn.close()

        return render_template('admin/indicators.html',
                             indicators=indicators,
                             chapters=chapters,
                             sections=sections,
                             selected_chapter=chapter_id,
                             selected_section=section_id,
                             search=search,
                             pagination={
                                 'page': page,
                                 'per_page': per_page,
                                 'total': total,
                                 'pages': (total + per_page - 1) // per_page
                             })

    except Exception as e:
        flash(f'加载指标数据失败: {str(e)}', 'error')
        return render_template('admin/indicators.html', indicators=[], chapters=[], sections=[])

if __name__ == '__main__':
    # 检查数据库文件是否存在
    if not os.path.exists(DB_PATH):
        print(f"错误：数据库文件 {DB_PATH} 不存在")
        print("请先运行主程序创建数据库")
        exit(1)

    print("🔧 医院指标后台管理系统启动中...")
    print("🌐 管理地址: http://localhost:5002/admin")
    print("👤 默认账号: admin / hospital2024")
    print("=" * 50)

    app.run(debug=True, host='0.0.0.0', port=5002)
