#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试指标类型功能
"""

import sqlite3
import json

def test_indicator_types():
    """测试指标类型功能"""
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🧪 测试指标类型功能")
        print("=" * 50)
        
        # 测试1：检查指标类型字段是否存在
        print("\n1️⃣ 检查指标类型字段...")
        cursor.execute("PRAGMA table_info(indicators)")
        columns = cursor.fetchall()
        
        has_indicator_type = any(col[1] == 'indicator_type' for col in columns)
        if has_indicator_type:
            print("✅ indicator_type字段存在")
        else:
            print("❌ indicator_type字段不存在")
            return False
        
        # 测试2：检查指标类型数据
        print("\n2️⃣ 检查指标类型数据...")
        cursor.execute("""
            SELECT indicator_type, COUNT(*) as count
            FROM indicators 
            WHERE is_active = 1
            GROUP BY indicator_type
            ORDER BY count DESC
        """)
        
        type_stats = cursor.fetchall()
        for indicator_type, count in type_stats:
            type_name = "简单指标" if indicator_type == 'simple' else "复合指标"
            print(f"  {type_name}: {count} 个")
        
        # 测试3：检查简单指标示例
        print("\n3️⃣ 简单指标示例...")
        cursor.execute("""
            SELECT i.id, i.name, 
                   COUNT(DISTINCT children.id) as children_count,
                   COUNT(DISTINCT ic.id) as component_count
            FROM indicators i
            LEFT JOIN indicators children ON i.id = children.parent_id AND children.is_active = 1
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id AND ic.is_active = 1
            WHERE i.indicator_type = 'simple' AND i.is_active = 1
            GROUP BY i.id, i.name
            ORDER BY i.id
            LIMIT 5
        """)
        
        simple_indicators = cursor.fetchall()
        for indicator_id, name, children_count, component_count in simple_indicators:
            print(f"  🔹 {indicator_id} - {name} (子指标:{children_count}, 组件:{component_count})")
        
        # 测试4：检查复合指标示例
        print("\n4️⃣ 复合指标示例...")
        cursor.execute("""
            SELECT i.id, i.name, 
                   COUNT(DISTINCT children.id) as children_count,
                   COUNT(DISTINCT ic.id) as component_count
            FROM indicators i
            LEFT JOIN indicators children ON i.id = children.parent_id AND children.is_active = 1
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id AND ic.is_active = 1
            WHERE i.indicator_type = 'composite' AND i.is_active = 1
            GROUP BY i.id, i.name
            HAVING children_count > 0 OR component_count > 0
            ORDER BY i.id
            LIMIT 5
        """)
        
        composite_indicators = cursor.fetchall()
        for indicator_id, name, children_count, component_count in composite_indicators:
            details = []
            if children_count > 0:
                details.append(f"子指标:{children_count}")
            if component_count > 0:
                details.append(f"组件:{component_count}")
            detail_str = ", ".join(details)
            print(f"  🔸 {indicator_id} - {name} ({detail_str})")
        
        # 测试5：API测试
        print("\n5️⃣ 测试API返回...")
        cursor.execute("""
            SELECT i.*, c.name as chapter_name, c.code as chapter_code,
                   s.name as section_name, s.code as section_code,
                   COUNT(DISTINCT children.id) as children_count,
                   COUNT(DISTINCT ic.id) as component_count
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN sections s ON i.section_id = s.id
            LEFT JOIN indicators children ON i.id = children.parent_id AND children.is_active = 1
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id AND ic.is_active = 1
            WHERE i.is_active = 1 AND i.id = '1.1.1'
            GROUP BY i.id
        """)
        
        result = cursor.fetchone()
        if result:
            # 转换为字典
            columns = [description[0] for description in cursor.description]
            indicator_data = dict(zip(columns, result))
            
            print(f"  指标ID: {indicator_data['id']}")
            print(f"  指标名称: {indicator_data['name']}")
            print(f"  指标类型: {indicator_data['indicator_type']}")
            print(f"  子指标数量: {indicator_data['children_count']}")
            print(f"  组件数量: {indicator_data['component_count']}")
            
            # 验证类型判断逻辑
            expected_type = 'simple' if (indicator_data['children_count'] == 0 and indicator_data['component_count'] == 0) else 'composite'
            actual_type = indicator_data['indicator_type']
            
            if expected_type == actual_type:
                print(f"  ✅ 类型判断正确: {actual_type}")
            else:
                print(f"  ❌ 类型判断错误: 期望{expected_type}, 实际{actual_type}")
        
        # 测试6：检查类型判断逻辑的准确性
        print("\n6️⃣ 验证类型判断逻辑...")
        cursor.execute("""
            SELECT 
                i.id,
                i.name,
                i.indicator_type,
                COUNT(DISTINCT children.id) as children_count,
                COUNT(DISTINCT ic.id) as component_count,
                CASE 
                    WHEN COUNT(DISTINCT children.id) = 0 AND COUNT(DISTINCT ic.id) = 0 THEN 'simple'
                    ELSE 'composite'
                END as expected_type
            FROM indicators i
            LEFT JOIN indicators children ON i.id = children.parent_id AND children.is_active = 1
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id AND ic.is_active = 1
            WHERE i.is_active = 1
            GROUP BY i.id, i.name, i.indicator_type
            HAVING i.indicator_type != expected_type
            LIMIT 10
        """)
        
        mismatched = cursor.fetchall()
        if mismatched:
            print(f"  ❌ 发现 {len(mismatched)} 个类型判断不匹配的指标:")
            for indicator_id, name, actual_type, children_count, component_count, expected_type in mismatched:
                print(f"    {indicator_id} - {name}: 实际={actual_type}, 期望={expected_type} (子指标:{children_count}, 组件:{component_count})")
        else:
            print("  ✅ 所有指标的类型判断都正确")
        
        conn.close()
        
        print("\n" + "=" * 50)
        print("🎉 指标类型功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_indicator_types()
