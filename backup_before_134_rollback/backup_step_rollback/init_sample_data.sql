-- 医院等级评审指标管理系统 - 示例数据初始化脚本
-- 执行前请确保已经运行了 database_design.sql
-- 注意：本文件包含示例数据，请根据实际需求修改

USE hospital_indicator_system;

-- ========================================
-- 插入示例数据（请根据实际情况修改）
-- ========================================

-- 注意：以下所有数据都是示例，实际使用时请根据具体需求修改或删除

-- 示例：插入章节数据
/*
INSERT INTO chapters (code, name, description, icon, color, sort_order) VALUES
('1', '资源配置与运行数据', '医院基础资源配置情况和运行效率相关指标', 'fas fa-chart-bar', '#1a73e8', 1),
('2', '医疗服务能力', '医院医疗服务能力和服务质量相关指标', 'fas fa-heartbeat', '#34a853', 2),
('3', '质量控制指标', '医疗质量控制和安全管理相关指标', 'fas fa-check-circle', '#ea4335', 3);
*/

-- 示例：插入小节数据
/*
INSERT INTO sections (chapter_id, code, name, description, sort_order) VALUES
(1, '1.1', '床位配置', '医院床位资源配置相关指标', 1),
(1, '1.2', '人力资源配置', '医院人力资源配置相关指标', 2),
(2, '2.1', '门诊服务', '门诊医疗服务能力相关指标', 1),
(3, '3.1', '医疗质量', '医疗质量控制相关指标', 1);
*/

-- 示例：插入指标数据
/*
INSERT INTO indicators (id, name, description, calculation_method, target_value, chapter_id, section_id, category, data_source, collection_frequency, sort_order, notes) VALUES
('1.1.1', '实际开放床位数', '医院实际开放使用的床位总数', '统计医院各科室实际开放的床位数量', 500, 1, 1, '资源配置与运行数据', '床位管理系统', 'monthly', 1, '不包括备用床位和未启用床位');
*/

-- 示例：更新子指标的父级关系
/*
UPDATE indicators SET parent_id = '父指标ID' WHERE id = '子指标ID';
*/

-- 示例：插入更多科室数据
/*
INSERT INTO departments (name, code, type, director, contact_phone, description) VALUES
('护理部', 'HLB', 'admin', '护理部主任', '010-12345684', '负责全院护理管理'),
('院感科', 'YGK', 'admin', '院感科主任', '010-12345685', '负责医院感染控制'),
('检验科', 'JYK', 'medical_tech', '检验科主任', '010-12345686', '负责临床检验');
*/

-- 示例：插入更多用户数据
/*
INSERT INTO users (username, password, real_name, email, phone, department_id, role) VALUES
('nurse_manager', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '护理部主任', '<EMAIL>', '13800138003', 7, 'manager'),
('infection_control', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '院感专员', '<EMAIL>', '13800138004', 8, 'operator');
*/

-- 示例：插入指标标签关联
/*
INSERT INTO indicator_tags (indicator_id, tag_id) VALUES
('指标ID', 标签ID); -- 示例：('1.1.1', 1)
*/

-- 示例：插入指标科室关联
/*
INSERT INTO indicator_departments (indicator_id, department_id, role_type, responsibility, contact_person) VALUES
('指标ID', 科室ID, '角色类型', '责任描述', '联系人');
*/

-- 示例：插入指标关联关系
/*
INSERT INTO indicator_relations (source_indicator_id, target_indicator_id, relation_type, description) VALUES
('源指标ID', '目标指标ID', '关联类型', '关联描述');
*/

-- 示例：插入历史数据
/*
INSERT INTO indicator_data_history (indicator_id, value, target_value, data_period, data_source, collection_method, collector_id, status, notes) VALUES
('指标ID', 数值, 目标值, '数据周期', '数据来源', '采集方式', 采集人ID, '状态', '备注');
*/

-- 示例：插入审核流程数据
/*
INSERT INTO data_audit_flow (data_history_id, step, auditor_id, audit_result, audit_comment, audit_time) VALUES
(数据历史ID, 审核步骤, 审核人ID, '审核结果', '审核意见', '审核时间');
*/

-- 示例：插入系统配置
/*
INSERT INTO system_config (config_key, config_value, config_type, description, is_system) VALUES
('配置键', '配置值', '配置类型', '配置描述', 是否系统配置);
*/

-- 注意：以下更新语句仅在有实际指标数据时执行
/*
-- 更新指标当前值和完成率
UPDATE indicators i
SET current_value = (
    SELECT idh.value
    FROM indicator_data_history idh
    WHERE idh.indicator_id = i.id
    AND idh.status = 'approved'
    ORDER BY idh.data_period DESC
    LIMIT 1
),
completion_rate = (
    SELECT ROUND(idh.value / i.target_value * 100, 2)
    FROM indicator_data_history idh
    WHERE idh.indicator_id = i.id
    AND idh.status = 'approved'
    AND i.target_value > 0
    ORDER BY idh.data_period DESC
    LIMIT 1
)
WHERE EXISTS (
    SELECT 1 FROM indicator_data_history idh2
    WHERE idh2.indicator_id = i.id
    AND idh2.status = 'approved'
);

-- 更新指标状态
UPDATE indicators
SET status = CASE
    WHEN completion_rate >= 95 THEN 'normal'
    WHEN completion_rate >= 80 THEN 'warning'
    ELSE 'danger'
END
WHERE completion_rate IS NOT NULL;
*/

-- 显示初始化完成信息
SELECT '数据库结构创建完成！请根据实际需求添加数据。' AS message;
