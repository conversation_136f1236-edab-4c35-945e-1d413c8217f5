#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试组件编辑功能
"""

from flask import Flask, request, redirect
import sqlite3
import os

app = Flask(__name__)

# 数据库路径
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DB_PATH = os.path.join(BASE_DIR, 'DATABASE-HOSPITAL', 'hospital_indicator_system.db')

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def dict_from_row(row):
    """将sqlite3.Row转换为字典"""
    return dict(zip(row.keys(), row)) if row else None

@app.route('/')
def index():
    return '''
    <h1>组件编辑功能测试</h1>
    <p><a href="/create_test_component">创建测试组件</a></p>
    <p><a href="/list_components">查看所有组件</a></p>
    '''

@app.route('/create_test_component')
def create_test_component():
    """创建测试组件"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 确保indicator_components表存在
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS indicator_components (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                indicator_id VARCHAR(20) NOT NULL,
                component_type VARCHAR(20) NOT NULL CHECK (component_type IN ('numerator', 'denominator', 'other')),
                name VARCHAR(200) NOT NULL,
                definition TEXT,
                unit VARCHAR(50),
                data_source VARCHAR(200),
                lead_department VARCHAR(100),
                logic_definition TEXT,
                collection_method VARCHAR(50),
                calculation_formula TEXT,
                notes TEXT,
                sort_order INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 插入测试组件
        cursor.execute("""
            INSERT INTO indicator_components 
            (indicator_id, component_type, name, definition, unit, data_source, lead_department, logic_definition)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            '1.3.2.1',
            'numerator',
            '重症医学科开放床位数',
            '医院重症医学科实际开放并可收治患者的床位数量',
            '张',
            'HIS系统',
            '重症医学科',
            '统计期末重症医学科实际开放的床位数'
        ))
        
        component_id = cursor.lastrowid
        
        cursor.execute("""
            INSERT INTO indicator_components 
            (indicator_id, component_type, name, definition, unit, data_source, lead_department, logic_definition)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            '1.3.2.1',
            'denominator',
            '医院开放床位总数',
            '医院实际开放并可收治患者的床位总数',
            '张',
            'HIS系统',
            '医务科',
            '统计期末医院实际开放的床位总数'
        ))
        
        conn.commit()
        conn.close()
        
        return f'''
        <h1>测试组件创建成功！</h1>
        <p>组件ID: {component_id}</p>
        <p><a href="/edit_component/{component_id}">编辑组件 {component_id}</a></p>
        <p><a href="/list_components">查看所有组件</a></p>
        <p><a href="/">返回首页</a></p>
        '''
        
    except Exception as e:
        return f'<h1>错误</h1><p>{str(e)}</p>'

@app.route('/list_components')
def list_components():
    """列出所有组件"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM indicator_components ORDER BY id")
        components = [dict_from_row(row) for row in cursor.fetchall()]
        
        conn.close()
        
        html = '<h1>所有组件</h1><table border="1"><tr><th>ID</th><th>指标ID</th><th>类型</th><th>名称</th><th>操作</th></tr>'
        
        for comp in components:
            html += f'''
            <tr>
                <td>{comp['id']}</td>
                <td>{comp['indicator_id']}</td>
                <td>{comp['component_type']}</td>
                <td>{comp['name']}</td>
                <td><a href="/edit_component/{comp['id']}">编辑</a></td>
            </tr>
            '''
        
        html += '</table><p><a href="/">返回首页</a></p>'
        
        return html
        
    except Exception as e:
        return f'<h1>错误</h1><p>{str(e)}</p>'

@app.route('/edit_component/<component_id>', methods=['GET', 'POST'])
def edit_component(component_id):
    """编辑组件"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if request.method == 'POST':
            # 处理表单提交
            data = request.form
            
            cursor.execute("""
                UPDATE indicator_components 
                SET name = ?, definition = ?, unit = ?, data_source = ?, 
                    lead_department = ?, logic_definition = ?, component_type = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                data.get('name'),
                data.get('definition'),
                data.get('unit'),
                data.get('data_source'),
                data.get('lead_department'),
                data.get('logic_definition'),
                data.get('component_type'),
                component_id
            ))
            
            conn.commit()
            conn.close()
            
            return f'''
            <script>
                alert('组件更新成功！');
                window.location.href = '/list_components';
            </script>
            '''
        
        # GET请求 - 显示编辑表单
        cursor.execute("SELECT * FROM indicator_components WHERE id = ?", (component_id,))
        component = dict_from_row(cursor.fetchone())
        
        if not component:
            return f'<h1>组件不存在</h1><p>组件 {component_id} 不存在</p>'
        
        conn.close()
        
        return f'''
        <!DOCTYPE html>
        <html>
        <head>
            <title>编辑组件 {component_id}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .form-group {{ margin-bottom: 15px; }}
                label {{ display: block; margin-bottom: 5px; font-weight: bold; }}
                input, select, textarea {{ width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }}
                textarea {{ height: 60px; }}
                button {{ padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }}
                button:hover {{ background: #0056b3; }}
            </style>
        </head>
        <body>
            <h1>编辑组件 {component_id}</h1>
            <form method="POST">
                <div class="form-group">
                    <label>组件类型:</label>
                    <select name="component_type">
                        <option value="numerator" {'selected' if component.get('component_type') == 'numerator' else ''}>分子</option>
                        <option value="denominator" {'selected' if component.get('component_type') == 'denominator' else ''}>分母</option>
                        <option value="other" {'selected' if component.get('component_type') == 'other' else ''}>其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>组件名称:</label>
                    <input type="text" name="name" value="{component.get('name', '')}" required>
                </div>
                <div class="form-group">
                    <label>组件定义:</label>
                    <textarea name="definition">{component.get('definition', '')}</textarea>
                </div>
                <div class="form-group">
                    <label>单位:</label>
                    <input type="text" name="unit" value="{component.get('unit', '')}">
                </div>
                <div class="form-group">
                    <label>数据来源:</label>
                    <input type="text" name="data_source" value="{component.get('data_source', '')}">
                </div>
                <div class="form-group">
                    <label>牵头科室:</label>
                    <input type="text" name="lead_department" value="{component.get('lead_department', '')}">
                </div>
                <div class="form-group">
                    <label>逻辑定义:</label>
                    <textarea name="logic_definition">{component.get('logic_definition', '')}</textarea>
                </div>
                <button type="submit">保存更改</button>
                <a href="/list_components" style="margin-left: 10px;">取消</a>
            </form>
        </body>
        </html>
        '''
        
    except Exception as e:
        return f'<h1>错误</h1><p>{str(e)}</p>'

if __name__ == '__main__':
    print("测试组件编辑功能...")
    print("访问: http://localhost:5002")
    app.run(debug=True, host='0.0.0.0', port=5002)
