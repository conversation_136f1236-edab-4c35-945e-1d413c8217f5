#!/usr/bin/env python3
"""
测试前端模态框基本属性和指标参考显示
"""

import requests

def test_api_data_completeness():
    """测试API数据完整性"""
    print("🧪 测试API数据完整性...")
    
    test_cases = [
        ("复合指标", "1.3.1"),
        ("简单指标", "1.1.1"),
        ("底层指标", "*******")  # 如果存在的话
    ]
    
    for case_name, indicator_id in test_cases:
        try:
            response = requests.get(f'http://localhost:5001/api/indicators/{indicator_id}')
            data = response.json()
            
            if data['success']:
                indicator = data['data']['indicator']
                
                print(f"\n📊 {case_name} ({indicator_id}):")
                print(f"   指标名称: {indicator['name']}")
                print(f"   指标类型: {indicator.get('indicator_type', 'composite')}")
                
                # 检查基本属性字段
                basic_attrs = {
                    '单位': indicator.get('unit'),
                    '牵头科室': indicator.get('lead_department'),
                    '数据来源': indicator.get('data_source'),
                    '逻辑定义': indicator.get('logic_definition'),
                    '意义': indicator.get('significance')
                }
                
                print("   📝 基本属性:")
                has_basic_attrs = False
                for attr_name, attr_value in basic_attrs.items():
                    if attr_value:
                        print(f"     ✅ {attr_name}: {attr_value[:50]}{'...' if len(str(attr_value)) > 50 else ''}")
                        has_basic_attrs = True
                    else:
                        print(f"     ❌ {attr_name}: 未设置")
                
                if not has_basic_attrs:
                    print("     ⚠️  无基本属性数据")
                
                # 检查指标参考字段
                reference_attrs = {
                    '指标定义': indicator.get('indicator_definition'),
                    '统计范围': indicator.get('statistical_scope'),
                    '数据来源详细': indicator.get('data_sources'),
                    '统计频率': indicator.get('collection_frequency_detail'),
                    '参考值': indicator.get('reference_value'),
                    '监测分析': indicator.get('monitoring_analysis')
                }
                
                print("   📖 指标参考:")
                has_reference_attrs = False
                for attr_name, attr_value in reference_attrs.items():
                    if attr_value:
                        print(f"     ✅ {attr_name}: {attr_value[:50]}{'...' if len(str(attr_value)) > 50 else ''}")
                        has_reference_attrs = True
                    else:
                        print(f"     ❌ {attr_name}: 未设置")
                
                if not has_reference_attrs:
                    print("     ⚠️  无指标参考数据")
                
                # 检查组件数据
                components = data['data'].get('components', [])
                print(f"   🧮 分子分母组件: {len(components)}个")
                
                # 检查子指标数据
                children = data['data'].get('children', [])
                print(f"   🌳 子指标: {len(children)}个")
                
            else:
                print(f"❌ {case_name} API请求失败: {data.get('error')}")
                
        except Exception as e:
            print(f"❌ {case_name} 测试异常: {e}")

def check_frontend_modal_structure():
    """检查前端模态框结构"""
    print("\n🧪 检查前端模态框结构...")
    
    try:
        with open('templates/base.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 检查基本属性相关元素
        basic_attr_elements = [
            'modalDetailedCard',
            'modalBasicAttributesSection',
            'modalUnit',
            'modalLeadDepartment',
            'modalDataSource',
            'modalLogicDefinition',
            'modalIndicatorSignificance'
        ]
        
        print("   📝 基本属性模态框元素:")
        for element in basic_attr_elements:
            if element in html_content:
                print(f"     ✅ {element}")
            else:
                print(f"     ❌ {element}")
        
        # 检查指标参考相关元素
        reference_elements = [
            'modalReferenceCard',
            'modalRefIndicatorDefinition',
            'modalRefStatisticalScope',
            'modalRefDataSources',
            'modalRefCollectionFrequency',
            'modalRefReferenceValue',
            'modalRefMonitoringAnalysis'
        ]
        
        print("   📖 指标参考模态框元素:")
        for element in reference_elements:
            if element in html_content:
                print(f"     ✅ {element}")
            else:
                print(f"     ❌ {element}")
        
        # 检查分子分母组件元素
        component_elements = [
            'modalComponentsSection',
            'modalComponentsContainer'
        ]
        
        print("   🧮 分子分母组件模态框元素:")
        for element in component_elements:
            if element in html_content:
                print(f"     ✅ {element}")
            else:
                print(f"     ❌ {element}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查前端模态框结构失败: {e}")
        return False

def check_javascript_display_logic():
    """检查JavaScript显示逻辑"""
    print("\n🧪 检查JavaScript显示逻辑...")
    
    try:
        with open('static/js/app.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # 检查基本属性显示函数
        basic_attr_functions = [
            'updateModalBasicAttributes',
            'updateBasicAttributeField',
            'updateModalDetailedInfo'
        ]
        
        print("   📝 基本属性显示函数:")
        for func in basic_attr_functions:
            if func in js_content:
                print(f"     ✅ {func}")
            else:
                print(f"     ❌ {func}")
        
        # 检查指标参考显示函数
        reference_functions = [
            'updateModalReferenceInfo',
            'renderModalAnalysisDimensions'
        ]
        
        print("   📖 指标参考显示函数:")
        for func in reference_functions:
            if func in js_content:
                print(f"     ✅ {func}")
            else:
                print(f"     ❌ {func}")
        
        # 检查组件显示函数
        component_functions = [
            'renderComponentsInModal'
        ]
        
        print("   🧮 分子分母组件显示函数:")
        for func in component_functions:
            if func in js_content:
                print(f"     ✅ {func}")
            else:
                print(f"     ❌ {func}")
        
        # 检查主要调用逻辑
        main_calls = [
            'updateModalDetailedInfo(indicator)',
            'updateModalReferenceInfo(indicator)',
            'renderComponentsInModal(components, indicator)'
        ]
        
        print("   🔄 主要调用逻辑:")
        for call in main_calls:
            if call in js_content:
                print(f"     ✅ {call}")
            else:
                print(f"     ❌ {call}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查JavaScript显示逻辑失败: {e}")
        return False

def suggest_improvements():
    """建议改进措施"""
    print("\n💡 改进建议")
    print("=" * 60)
    
    print("🎯 确保前端模态框正确显示基本属性和指标参考:")
    print("1. ✅ 检查API数据是否包含完整的基本属性和指标参考字段")
    print("2. ✅ 确认前端模态框HTML结构包含所有必要元素")
    print("3. ✅ 验证JavaScript显示逻辑正确调用相关函数")
    print("4. ✅ 测试不同类型指标的模态框显示效果")
    
    print("\n🔧 可能的问题和解决方案:")
    print("1. 📊 数据问题:")
    print("   - 检查数据库中是否有基本属性和指标参考数据")
    print("   - 确认API返回的数据结构正确")
    
    print("2. 🎨 显示问题:")
    print("   - 检查CSS样式是否正确")
    print("   - 确认元素的display属性设置正确")
    
    print("3. 🔄 逻辑问题:")
    print("   - 验证JavaScript函数调用顺序")
    print("   - 检查条件判断逻辑")
    
    print("\n🔗 测试步骤:")
    print("1. 访问前端系统: http://localhost:5001")
    print("2. 点击任意指标打开模态框")
    print("3. 检查是否显示基本属性卡片")
    print("4. 检查是否显示指标参考卡片")
    print("5. 检查分子分母组件是否正确显示/隐藏")

def main():
    """主测试函数"""
    print("🎯 前端模态框基本属性和指标参考显示测试")
    print("=" * 70)
    
    tests = [
        ("API数据完整性", test_api_data_completeness),
        ("前端模态框结构", check_frontend_modal_structure),
        ("JavaScript显示逻辑", check_javascript_display_logic)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 检查完成")
            else:
                print(f"❌ {test_name} 检查失败")
        except Exception as e:
            print(f"❌ {test_name} 检查异常: {e}")
    
    # 显示改进建议
    suggest_improvements()
    
    print("\n" + "=" * 70)
    print(f"📊 检查结果: {passed}/{total} 项检查完成")
    
    if passed == total:
        print("🎉 前端模态框结构和逻辑检查完成！")
        print("请访问前端系统测试实际显示效果")
    else:
        print("⚠️  部分检查发现问题，请根据建议进行修复")
    
    return passed == total

if __name__ == "__main__":
    main()
