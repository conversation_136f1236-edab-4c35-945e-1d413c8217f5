<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>列表视图测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .view-toggle {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }
        
        .view-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .view-btn.active {
            background: #1a73e8;
            color: white;
            border-color: #1a73e8;
        }
        
        .card-view {
            display: block;
        }
        
        .list-view {
            display: none;
        }
        
        .test-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        
        .test-list {
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .list-header {
            background: #f8f9fa;
            padding: 10px;
            font-weight: bold;
            border-bottom: 1px solid #ddd;
            display: flex;
        }
        
        .list-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
        }
        
        .list-item:last-child {
            border-bottom: none;
        }
        
        .list-item:hover {
            background: #f8f9fa;
        }
        
        .col-id { width: 100px; }
        .col-name { flex: 1; }
        .col-desc { flex: 2; }
        .col-action { width: 80px; }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            background: #e8f5e8;
            border-radius: 4px;
            color: #2d5a2d;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>列表视图功能测试</h1>
        
        <div class="view-toggle">
            <button class="view-btn active" data-view="card">卡片视图</button>
            <button class="view-btn" data-view="list">列表视图</button>
        </div>
        
        <!-- 卡片视图 -->
        <div class="card-view" id="cardView">
            <h3>卡片视图</h3>
            <div class="test-card">
                <strong>1.1.1</strong> - 医院功能定位
                <p>医院应当明确功能定位，制定发展规划...</p>
            </div>
            <div class="test-card">
                <strong>1.1.2</strong> - 医院管理体系
                <p>医院应当建立健全管理体系...</p>
            </div>
            <div class="test-card">
                <strong>1.1.3</strong> - 医院文化建设
                <p>医院应当加强文化建设...</p>
            </div>
        </div>
        
        <!-- 列表视图 -->
        <div class="list-view" id="listView">
            <h3>列表视图</h3>
            <div class="test-list">
                <div class="list-header">
                    <div class="col-id">编号</div>
                    <div class="col-name">指标名称</div>
                    <div class="col-desc">描述</div>
                    <div class="col-action">操作</div>
                </div>
                <div class="list-item">
                    <div class="col-id">1.1.1</div>
                    <div class="col-name">医院功能定位</div>
                    <div class="col-desc">医院应当明确功能定位，制定发展规划...</div>
                    <div class="col-action">查看</div>
                </div>
                <div class="list-item">
                    <div class="col-id">1.1.2</div>
                    <div class="col-name">医院管理体系</div>
                    <div class="col-desc">医院应当建立健全管理体系...</div>
                    <div class="col-action">查看</div>
                </div>
                <div class="list-item">
                    <div class="col-id">1.1.3</div>
                    <div class="col-name">医院文化建设</div>
                    <div class="col-desc">医院应当加强文化建设...</div>
                    <div class="col-action">查看</div>
                </div>
            </div>
        </div>
        
        <div class="status" id="status">
            当前视图：卡片视图
        </div>
    </div>

    <script>
        // 测试视图切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const viewButtons = document.querySelectorAll('.view-btn');
            const cardView = document.getElementById('cardView');
            const listView = document.getElementById('listView');
            const status = document.getElementById('status');
            
            viewButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const view = this.dataset.view;
                    
                    // 更新按钮状态
                    viewButtons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 切换视图
                    if (view === 'card') {
                        cardView.style.display = 'block';
                        listView.style.display = 'none';
                        status.textContent = '当前视图：卡片视图';
                    } else {
                        cardView.style.display = 'none';
                        listView.style.display = 'block';
                        status.textContent = '当前视图：列表视图';
                    }
                    
                    console.log('切换到:', view);
                });
            });
        });
    </script>
</body>
</html>
