<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指标详情 - 医院等级评审指标说明系统 v2.1</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .detail-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .detail-header {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .detail-title {
            font-size: 24px;
            font-weight: 600;
            color: #1a73e8;
            margin-bottom: 8px;
        }

        .detail-subtitle {
            color: #5f6368;
            font-size: 16px;
            margin-bottom: 16px;
        }

        .detail-meta {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #5f6368;
        }

        .info-section,
        .values-section,
        .direct-attributes-section,
        .components-section,
        .children-section {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .section-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #202124;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .info-item {
            padding: 16px;
            border: 1px solid #dadce0;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .info-label {
            font-weight: 600;
            color: #202124;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .info-value {
            color: #5f6368;
            font-size: 16px;
            line-height: 1.4;
        }

        .values-grid,
        .attributes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .value-card,
        .attribute-card {
            padding: 20px;
            border: 1px solid #dadce0;
            border-radius: 8px;
            text-align: center;
            background: #f8f9fa;
        }

        .value-number {
            font-size: 32px;
            font-weight: 600;
            color: #1a73e8;
            margin-bottom: 8px;
        }

        .value-label {
            font-size: 14px;
            color: #5f6368;
            font-weight: 500;
        }

        .children-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
        }

        .child-card {
            padding: 16px;
            border: 1px solid #dadce0;
            border-radius: 8px;
            background: #f8f9fa;
            transition: all 0.2s ease;
        }

        .child-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .child-title {
            font-weight: 600;
            color: #1a73e8;
            margin-bottom: 8px;
            text-decoration: none;
        }

        .child-title:hover {
            text-decoration: underline;
        }

        .child-description {
            color: #5f6368;
            font-size: 14px;
            line-height: 1.4;
        }

        .edit-values-btn {
            background: #34a853;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .edit-values-btn:hover {
            background: #2d8f47;
        }

        .cancel-values-btn {
            background: #ea4335;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: 8px;
        }

        .cancel-values-btn:hover {
            background: #d33b2c;
        }

        .value-edit-input {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #1a73e8;
            border-radius: 4px;
            font-size: 24px;
            font-weight: 600;
            text-align: center;
            background: white;
            color: #1a73e8;
        }

        .value-edit-input:focus {
            outline: none;
            border-color: #1557b0;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }

        .add-component-btn,
        .edit-attributes-btn {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .add-component-btn:hover,
        .edit-attributes-btn:hover {
            background: #1557b0;
        }

        .edit-attributes-btn {
            background: #34a853;
        }

        .edit-attributes-btn:hover {
            background: #2d8f47;
        }

        .cancel-attributes-btn {
            background: #ea4335;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
            margin-left: 10px;
            transition: background 0.2s ease;
        }

        .cancel-attributes-btn:hover {
            background: #d33b2c;
        }

        .component-card {
            border: 1px solid #dadce0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
            position: relative;
        }

        .component-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .component-type {
            background: #e8f0fe;
            color: #1a73e8;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .component-type.denominator {
            background: #fce8e6;
            color: #d93025;
        }

        .component-actions {
            display: flex;
            gap: 8px;
        }

        .edit-btn, .delete-btn {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            color: #5f6368;
        }

        .edit-btn:hover {
            background: #f1f3f4;
            color: #1a73e8;
        }

        .delete-btn:hover {
            background: #fce8e6;
            color: #d93025;
        }

        .component-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .field-group {
            margin-bottom: 12px;
        }

        .field-label {
            font-weight: 500;
            color: #202124;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .field-value {
            color: #5f6368;
            font-size: 14px;
            line-height: 1.4;
        }

        .field-value.empty {
            color: #9aa0a6;
            font-style: italic;
        }

        .edit-form {
            display: none;
            grid-template-columns: 1fr;
            gap: 16px;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #dadce0;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: #202124;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .form-input, .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-size: 14px;
            font-family: inherit;
        }

        .form-input:focus, .form-textarea:focus {
            outline: none;
            border-color: #1a73e8;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .save-btn, .cancel-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .save-btn {
            background: #1a73e8;
            color: white;
        }

        .save-btn:hover {
            background: #1557b0;
        }

        .cancel-btn {
            background: #f8f9fa;
            color: #5f6368;
            border: 1px solid #dadce0;
        }

        .cancel-btn:hover {
            background: #f1f3f4;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 16px;
            font-size: 14px;
        }

        .alert-success {
            background: #e6f4ea;
            color: #137333;
            border: 1px solid #34a853;
        }

        .alert-error {
            background: #fce8e6;
            color: #d93025;
            border: 1px solid #ea4335;
        }

        .top-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .back-btn,
        .admin-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            font-size: 14px;
            padding: 12px 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .back-btn {
            background: #1a73e8;
            color: white;
        }

        .back-btn:hover {
            background: #1557b0;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .admin-btn {
            background: #34a853;
            color: white;
        }

        .admin-btn:hover {
            background: #2d8f47;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* 底部操作区样式 */
        .bottom-actions {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
            padding: 24px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-top: 3px solid #1a73e8;
        }

        .bottom-back-btn,
        .bottom-admin-btn {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            padding: 16px 32px;
            border-radius: 8px;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 160px;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .bottom-back-btn {
            background: #1a73e8;
            color: white;
        }

        .bottom-back-btn:hover {
            background: #1557b0;
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .bottom-admin-btn {
            background: #34a853;
            color: white;
        }

        .bottom-admin-btn:hover {
            background: #2d8f47;
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid #dadce0;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #202124;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #5f6368;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
        }

        .modal-close:hover {
            background: #f1f3f4;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
            padding: 16px 24px;
            border-top: 1px solid #dadce0;
        }

        /* 详细信息模块样式 */
        .section-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border: 1px solid #dadce0;
            background: white;
            color: #5f6368;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: #f1f3f4;
            color: #202124;
            border-color: #dadce0;
        }

        .action-btn.edit-btn.active {
            background: #1a73e8;
            color: white;
            border-color: #1a73e8;
        }

        .details-content {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .details-content.collapsed {
            max-height: 0;
            padding: 0;
            margin: 0;
            opacity: 0;
        }

        .detail-item {
            margin-bottom: 24px;
            padding: 16px;
            border: 1px solid #dadce0;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .detail-label {
            font-weight: 600;
            color: #202124;
            margin-bottom: 12px;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .detail-value {
            color: #5f6368;
            font-size: 14px;
            line-height: 1.6;
        }

        .edit-form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            font-weight: 500;
            color: #202124;
            font-size: 14px;
        }

        .form-group input,
        .form-group textarea {
            padding: 12px 16px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-family: inherit;
            font-size: 14px;
            background: white;
            color: #202124;
            resize: vertical;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #1a73e8;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
        }

        @media (max-width: 768px) {
            .detail-container {
                padding: 16px;
            }

            .component-content {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .detail-meta {
                flex-direction: column;
                gap: 12px;
            }

            .edit-form-grid {
                grid-template-columns: 1fr;
            }

            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="detail-container">
        <div class="top-actions">
            <a href="/" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                返回指标列表
            </a>
            <a href="/admin/indicators/{{ indicator_id }}" class="admin-btn" target="_blank">
                <i class="fas fa-cog"></i>
                后台管理
            </a>
        </div>

        <div id="alertContainer"></div>

        <div class="detail-header">
            <div class="detail-title" id="indicatorTitle">加载中...</div>
            <div class="detail-subtitle" id="indicatorDescription"></div>
            <div class="detail-meta" id="indicatorMeta"></div>
        </div>

        <!-- 指标基本信息 -->
        <div class="info-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    指标说明
                </h2>
            </div>
            <div class="info-content" id="indicatorInfo">
                <div class="loading-message">加载中...</div>
            </div>
        </div>

        <!-- 详细信息统一模块 -->
        <div class="info-section" id="detailsSection">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    详细信息
                </h2>
                <div class="section-actions">
                    <button class="action-btn edit-btn" id="editDetailsBtn" onclick="toggleDetailEdit()" title="编辑详细信息">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn collapse-btn" id="collapseDetailsBtn" onclick="toggleDetailCollapse()" title="折叠/展开">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                </div>
            </div>
            <div class="details-content" id="detailsContent">
                <!-- 显示模式 -->
                <div id="detailsDisplayMode">
                    <!-- 基本属性 -->
                    <div class="detail-item" id="basicAttributesItem" style="display: none;">
                        <div class="detail-label">
                            <i class="fas fa-cog"></i>
                            基本属性
                        </div>
                        <div class="detail-value">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                                <div id="unitItem" style="display: none;">
                                    <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; border-left: 3px solid #1a73e8;">
                                        <div style="font-weight: 500; color: #202124; margin-bottom: 4px;">单位</div>
                                        <div id="unitValue" style="color: #5f6368;"></div>
                                    </div>
                                </div>
                                <div id="leadDepartmentItem" style="display: none;">
                                    <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; border-left: 3px solid #34a853;">
                                        <div style="font-weight: 500; color: #202124; margin-bottom: 4px;">牵头科室</div>
                                        <div id="leadDepartmentValue" style="color: #5f6368;"></div>
                                    </div>
                                </div>
                                <div id="dataSourceBasicItem" style="display: none;">
                                    <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; border-left: 3px solid #ea4335;">
                                        <div style="font-weight: 500; color: #202124; margin-bottom: 4px;">数据来源</div>
                                        <div id="dataSourceBasicValue" style="color: #5f6368;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 逻辑定义 -->
                    <div class="detail-item" id="logicDefinitionItem" style="display: none;">
                        <div class="detail-label">
                            <i class="fas fa-code"></i>
                            逻辑定义
                        </div>
                        <div class="detail-value" id="logicDefinitionValue" style="background: #f8f9fa; padding: 16px; border-radius: 8px; border-left: 4px solid #1a73e8; font-family: monospace;"></div>
                    </div>

                    <!-- 指标定义 -->
                    <div class="detail-item" id="definitionItem" style="display: none;">
                        <div class="detail-label">
                            <i class="fas fa-book"></i>
                            指标定义
                        </div>
                        <div class="detail-value" id="indicatorDefinition"></div>
                    </div>

                    <!-- 计算方法 -->
                    <div class="detail-item" id="calculationItem" style="display: none;">
                        <div class="detail-label">
                            <i class="fas fa-calculator"></i>
                            计算方法
                        </div>
                        <div class="detail-value">
                            <div class="calculation-formula" id="calculationFormula" style="background: #f8f9fa; padding: 16px; border-radius: 8px; margin-bottom: 16px; font-family: monospace; font-size: 14px; border-left: 4px solid #1a73e8;"></div>
                            <div class="calculation-details">
                                <div class="calculation-item" id="numeratorSection" style="display: none; margin-bottom: 12px;">
                                    <strong style="color: #1a73e8;">分子：</strong><span id="numeratorDescription"></span>
                                </div>
                                <div class="calculation-item" id="denominatorSection" style="display: none;">
                                    <strong style="color: #ea4335;">分母：</strong><span id="denominatorDescription"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 统计范围 -->
                    <div class="detail-item" id="scopeItem" style="display: none;">
                        <div class="detail-label">
                            <i class="fas fa-bullseye"></i>
                            统计范围
                        </div>
                        <div class="detail-value" id="statisticalScope"></div>
                    </div>

                    <!-- 数据来源详细 -->
                    <div class="detail-item" id="dataSourceItem" style="display: none;">
                        <div class="detail-label">
                            <i class="fas fa-database"></i>
                            数据来源详细
                        </div>
                        <div class="detail-value" id="dataSourcesDetail"></div>
                    </div>

                    <!-- 统计频率 -->
                    <div class="detail-item" id="frequencyItem" style="display: none;">
                        <div class="detail-label">
                            <i class="fas fa-clock"></i>
                            统计频率
                        </div>
                        <div class="detail-value" id="collectionFrequencyDetail"></div>
                    </div>

                    <!-- 标准值/参考值 -->
                    <div class="detail-item" id="referenceItem" style="display: none;">
                        <div class="detail-label">
                            <i class="fas fa-chart-line"></i>
                            标准值/参考值
                        </div>
                        <div class="detail-value" id="referenceValue"></div>
                    </div>

                    <!-- 监测分析 -->
                    <div class="detail-item" id="monitoringItem" style="display: none;">
                        <div class="detail-label">
                            <i class="fas fa-chart-area"></i>
                            监测分析
                        </div>
                        <div class="detail-value">
                            <div id="monitoringAnalysis"></div>
                            <div id="analysisDimensions" style="margin-top: 16px;"></div>
                        </div>
                    </div>
                </div>

                <!-- 编辑模式 -->
                <div id="detailsEditMode" style="display: none;">
                    <form id="detailsEditForm">
                        <div class="edit-form-grid">
                            <!-- 基本属性字段 -->
                            <div class="form-group">
                                <label for="editUnit">单位</label>
                                <input type="text" id="editUnit" name="unit" placeholder="请输入单位">
                            </div>

                            <div class="form-group">
                                <label for="editLeadDepartment">牵头科室</label>
                                <input type="text" id="editLeadDepartment" name="lead_department" placeholder="请输入牵头科室">
                            </div>

                            <div class="form-group">
                                <label for="editDataSource">数据来源</label>
                                <input type="text" id="editDataSource" name="data_source" placeholder="请输入数据来源">
                            </div>

                            <div class="form-group full-width">
                                <label for="editLogicDefinition">逻辑定义</label>
                                <textarea id="editLogicDefinition" name="logic_definition" rows="2" placeholder="请输入逻辑定义"></textarea>
                            </div>

                            <!-- 详细信息字段 -->
                            <div class="form-group full-width">
                                <label for="editIndicatorDefinition">指标定义</label>
                                <textarea id="editIndicatorDefinition" name="indicator_definition" rows="3" placeholder="请输入指标的详细定义"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="editCalculationFormula">计算公式</label>
                                <textarea id="editCalculationFormula" name="calculation_formula" rows="2" placeholder="请输入计算公式"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="editNumeratorDescription">分子说明</label>
                                <textarea id="editNumeratorDescription" name="numerator_description" rows="2" placeholder="请输入分子说明"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="editDenominatorDescription">分母说明</label>
                                <textarea id="editDenominatorDescription" name="denominator_description" rows="2" placeholder="请输入分母说明"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="editStatisticalScope">统计范围</label>
                                <textarea id="editStatisticalScope" name="statistical_scope" rows="2" placeholder="请输入统计范围"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="editDataSources">数据来源详细</label>
                                <textarea id="editDataSources" name="data_sources" rows="2" placeholder="请输入详细数据来源"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="editCollectionFrequency">统计频率</label>
                                <input type="text" id="editCollectionFrequency" name="collection_frequency_detail" placeholder="请输入统计频率">
                            </div>

                            <div class="form-group">
                                <label for="editReferenceValue">标准值/参考值</label>
                                <input type="text" id="editReferenceValue" name="reference_value" placeholder="请输入参考值">
                            </div>

                            <div class="form-group full-width">
                                <label for="editMonitoringAnalysis">监测分析</label>
                                <textarea id="editMonitoringAnalysis" name="monitoring_analysis" rows="3" placeholder="请输入监测分析内容"></textarea>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="cancel-btn" onclick="cancelDetailEdit()">
                                <i class="fas fa-times"></i>
                                取消
                            </button>
                            <button type="submit" class="save-btn">
                                <i class="fas fa-save"></i>
                                保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 指标数值 -->
        <div class="values-section" id="valuesSection" style="display: none;">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-chart-bar"></i>
                    指标数值
                </h2>
                <button class="edit-values-btn" onclick="toggleEditValues()">
                    <i class="fas fa-edit"></i>
                    编辑数值
                </button>
            </div>
            <div class="values-grid" id="valuesGrid">
                <!-- 动态加载 -->
            </div>
        </div>

        <!-- 直接属性（适用于简单指标） -->
        <div class="direct-attributes-section" id="directAttributesSection" style="display: none;">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-cog"></i>
                    指标属性
                </h2>
                <button class="edit-attributes-btn" onclick="toggleEditAttributes()">
                    <i class="fas fa-edit"></i>
                    编辑属性
                </button>
            </div>
            <div class="attributes-grid" id="attributesGrid">
                <!-- 动态加载 -->
            </div>
        </div>

        <div class="components-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-puzzle-piece"></i>
                    计算组成部分
                </h2>
                <button class="add-component-btn" onclick="showAddComponentForm()">
                    <i class="fas fa-plus"></i>
                    添加组件
                </button>
            </div>

            <div id="componentsContainer">
                <div class="loading-message">加载中...</div>
            </div>
        </div>

        <!-- 子指标 -->
        <div class="children-section" id="childrenSection" style="display: none;">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-sitemap"></i>
                    子指标
                </h2>
            </div>
            <div class="children-grid" id="childrenGrid">
                <!-- 动态加载 -->
            </div>
        </div>

        <!-- 底部操作区 -->
        <div class="bottom-actions">
            <a href="/" class="bottom-back-btn">
                <i class="fas fa-list"></i>
                返回列表
            </a>
            <a href="/admin/indicators/{{ indicator_id }}" class="bottom-admin-btn" target="_blank">
                <i class="fas fa-cog"></i>
                后台管理
            </a>
        </div>
    </div>

    <script>
// 通用安全DOM操作函数
function safeSetTextContent(elementId, value, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.textContent = value;
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}

function safeGetElement(elementId, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (!element) {
        console.warn(`Element not found: ${elementId}`);
    }
    return element;
}

function safeSetInnerHTML(elementId, html, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.innerHTML = html;
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}

function safeSetStyle(elementId, property, value, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.style[property] = value;
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}

function safeAddClass(elementId, className, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.classList.add(className);
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}

function safeRemoveClass(elementId, className, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.classList.remove(className);
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}

        const indicatorId = '{{ indicator_id }}';
        let indicatorData = null;

        // 页面加载时获取指标详情
        document.addEventListener('DOMContentLoaded', function() {
            loadIndicatorDetail();
        });

        // 加载指标详情
        async function loadIndicatorDetail() {
            try {
                // 添加时间戳防止缓存
                const timestamp = new Date().getTime();
                const response = await fetch(`/api/indicators/${indicatorId}?t=${timestamp}`);
                const result = await response.json();

                if (result.success) {
                    indicatorData = result.data;
                    console.log('加载的指标数据:', indicatorData);
                    console.log('指标详细字段:', {
                        indicator_definition: indicatorData.indicator.indicator_definition,
                        statistical_scope: indicatorData.indicator.statistical_scope,
                        data_sources: indicatorData.indicator.data_sources,
                        collection_frequency_detail: indicatorData.indicator.collection_frequency_detail,
                        reference_value: indicatorData.indicator.reference_value,
                        monitoring_analysis: indicatorData.indicator.monitoring_analysis,
                        analysis_dimensions: indicatorData.indicator.analysis_dimensions
                    });
                    renderIndicatorDetail();
                    renderComponents();
                } else {
                    showAlert('加载指标详情失败: ' + result.error, 'error');
                }
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'error');
            }
        }

        // 渲染指标基本信息
        function renderIndicatorDetail() {
            const indicator = indicatorData.indicator;
            const children = indicatorData.children || [];

            safeSetTextContent('indicatorTitle', `${indicator.id} - ${indicator.name}`);
            safeSetTextContent('indicatorDescription', indicator.description || '暂无描述');

            const metaHtml = `
                <div class="meta-item">
                    <i class="fas fa-folder"></i>
                    <span>${indicator.chapter_name}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-layer-group"></i>
                    <span>${indicator.section_name}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-tag"></i>
                    <span>${indicator.category || '未分类'}</span>
                </div>
            `;
            document.getElementById('indicatorMeta').innerHTML = metaHtml;

            // 渲染基本信息
            renderIndicatorInfo(indicator);

            // 渲染详细信息部分
            renderDetailedSections(indicator);

            // 渲染指标数值（如果有）
            renderIndicatorValues(indicator);

            // 渲染直接属性（适用于简单指标）
            renderDirectAttributes(indicator);

            // 渲染子指标
            renderChildren(children);
        }

        // 渲染详细信息部分
        function renderDetailedSections(indicator) {
            console.log('渲染详细信息部分', indicator);

            let hasAnyDetail = false;

            // 基本属性
            let hasBasicAttributes = false;
            if (indicator.unit) {
                safeSetTextContent('unitValue', indicator.unit);
                document.getElementById('unitItem').style.display = 'block';
                hasBasicAttributes = true;
            }
            if (indicator.lead_department) {
                safeSetTextContent('leadDepartmentValue', indicator.lead_department);
                document.getElementById('leadDepartmentItem').style.display = 'block';
                hasBasicAttributes = true;
            }
            if (indicator.data_source) {
                safeSetTextContent('dataSourceBasicValue', indicator.data_source);
                document.getElementById('dataSourceBasicItem').style.display = 'block';
                hasBasicAttributes = true;
            }
            if (hasBasicAttributes) {
                document.getElementById('basicAttributesItem').style.display = 'block';
                hasAnyDetail = true;
            }

            // 逻辑定义
            if (indicator.logic_definition) {
                safeSetTextContent('logicDefinitionValue', indicator.logic_definition);
                document.getElementById('logicDefinitionItem').style.display = 'block';
                hasAnyDetail = true;
            }

            // 指标定义
            if (indicator.indicator_definition) {
                console.log('显示指标定义:', indicator.indicator_definition);
                safeSetTextContent('indicatorDefinition', indicator.indicator_definition);
                document.getElementById('definitionItem').style.display = 'block';
                hasAnyDetail = true;
            }

            // 计算方法
            if (indicator.calculation_formula || indicator.numerator_description || indicator.denominator_description) {
                if (indicator.calculation_formula) {
                    safeSetTextContent('calculationFormula', indicator.calculation_formula);
                }
                if (indicator.numerator_description) {
                    safeSetTextContent('numeratorDescription', indicator.numerator_description);
                    document.getElementById('numeratorSection').style.display = 'block';
                }
                if (indicator.denominator_description) {
                    safeSetTextContent('denominatorDescription', indicator.denominator_description);
                    document.getElementById('denominatorSection').style.display = 'block';
                }
                document.getElementById('calculationItem').style.display = 'block';
                hasAnyDetail = true;
            }

            // 统计范围
            if (indicator.statistical_scope) {
                safeSetTextContent('statisticalScope', indicator.statistical_scope);
                document.getElementById('scopeItem').style.display = 'block';
                hasAnyDetail = true;
            }

            // 数据来源详细
            if (indicator.data_sources) {
                safeSetTextContent('dataSourcesDetail', indicator.data_sources);
                document.getElementById('dataSourceItem').style.display = 'block';
                hasAnyDetail = true;
            }

            // 统计频率
            if (indicator.collection_frequency_detail) {
                safeSetTextContent('collectionFrequencyDetail', indicator.collection_frequency_detail);
                document.getElementById('frequencyItem').style.display = 'block';
                hasAnyDetail = true;
            }

            // 标准值/参考值
            if (indicator.reference_value) {
                safeSetTextContent('referenceValue', indicator.reference_value);
                document.getElementById('referenceItem').style.display = 'block';
                hasAnyDetail = true;
            }

            // 监测分析
            if (indicator.monitoring_analysis) {
                safeSetTextContent('monitoringAnalysis', indicator.monitoring_analysis);

                // 如果有分析维度数据，也显示出来
                if (indicator.analysis_dimensions) {
                    renderAnalysisDimensions(indicator.analysis_dimensions);
                }

                document.getElementById('monitoringItem').style.display = 'block';
                hasAnyDetail = true;
            }

            // 显示详细信息模块（如果有任何详细信息）
            if (hasAnyDetail) {
                document.getElementById('detailsSection').style.display = 'block';
            }
        }

        // 渲染分析维度
        function renderAnalysisDimensions(dimensions) {
            if (!dimensions || dimensions.length === 0) return;

            const dimensionsHtml = `
                <div style="margin-top: 20px;">
                    <h4 style="color: #1a73e8; margin-bottom: 12px; font-size: 16px;">建议对数据进行多维度分析：</h4>
                    <div style="display: grid; gap: 12px;">
                        ${dimensions.map(dim => `
                            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; border-left: 3px solid #34a853;">
                                <div style="font-weight: 600; color: #202124; margin-bottom: 4px;">${dim.dimension_name}</div>
                                <div style="color: #5f6368; font-size: 14px;">${dim.analysis_content}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            document.getElementById('analysisDimensions').innerHTML = dimensionsHtml;
        }

        // 渲染指标基本信息
        function renderIndicatorInfo(indicator) {
            const infoHtml = `
                <div class="info-item">
                    <div class="info-label">指标编号</div>
                    <div class="info-value">${indicator.id}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">指标名称</div>
                    <div class="info-value">${indicator.name}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">所属章节</div>
                    <div class="info-value">${indicator.chapter_name || '未设置'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">所属小节</div>
                    <div class="info-value">${indicator.section_name || '未设置'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">指标类型</div>
                    <div class="info-value">${getIndicatorTypeLabel(indicator.indicator_type)}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">计算方法</div>
                    <div class="info-value">${indicator.calculation_method || '未设置'}</div>
                </div>
            `;
            document.getElementById('indicatorInfo').innerHTML = infoHtml;
        }

        // 渲染指标数值
        function renderIndicatorValues(indicator) {
            const targetValue = indicator.target_value || '95';
            const currentValue = indicator.current_value || '88';
            const completionRate = indicator.completion_rate || calculateCompletionRate(currentValue, targetValue);

            const valuesHtml = `
                <div class="value-card" data-field="target_value">
                    <div class="value-number" id="target-value-display">${targetValue}</div>
                    <div class="value-label">目标值/参考范围</div>
                    <input type="text" class="value-edit-input" id="target-value-input" value="${targetValue}" style="display: none;">
                </div>
                <div class="value-card" data-field="current_value">
                    <div class="value-number" id="current-value-display">${currentValue}</div>
                    <div class="value-label">当前值</div>
                    <input type="text" class="value-edit-input" id="current-value-input" value="${currentValue}" style="display: none;">
                </div>
                <div class="value-card">
                    <div class="value-number" id="completion-rate-display">${completionRate}</div>
                    <div class="value-label">完成率</div>
                </div>
                <div class="value-card">
                    <div class="value-number">${indicator.data_source || '医院信息'}</div>
                    <div class="value-label">数据来源</div>
                </div>
            `;
            document.getElementById('valuesGrid').innerHTML = valuesHtml;
            document.getElementById('valuesSection').style.display = 'block';
        }

        // 计算完成率
        function calculateCompletionRate(current, target) {
            if (!current || !target) return '未设置';
            const rate = (parseFloat(current) / parseFloat(target) * 100).toFixed(1);
            return rate + '%';
        }

        // 渲染直接属性
        function renderDirectAttributes(indicator) {
            // 对于简单指标，始终显示属性部分
            if (indicator.indicator_type === 'simple') {
                const attributesHtml = `
                    <div class="attribute-card">
                        <div class="info-label">单位</div>
                        <div class="info-value">${indicator.unit || '未设置'}</div>
                    </div>
                    <div class="attribute-card">
                        <div class="info-label">牵头科室</div>
                        <div class="info-value">${indicator.lead_department || '未设置'}</div>
                    </div>
                    <div class="attribute-card">
                        <div class="info-label">数据来源</div>
                        <div class="info-value">${indicator.data_source || '未设置'}</div>
                    </div>
                    <div class="attribute-card" style="grid-column: 1 / -1;">
                        <div class="info-label">逻辑定义</div>
                        <div class="info-value">${indicator.logic_definition || '未设置'}</div>
                    </div>
                `;
                document.getElementById('attributesGrid').innerHTML = attributesHtml;
                document.getElementById('directAttributesSection').style.display = 'block';

                // 隐藏分子分母部分，因为简单指标不需要
                document.querySelector('.components-section').style.display = 'none';
            } else {
                // 对于复合指标，隐藏直接属性部分，显示分子分母部分
                document.getElementById('directAttributesSection').style.display = 'none';
                document.querySelector('.components-section').style.display = 'block';
            }
        }

        // 渲染子指标
        function renderChildren(children) {
            if (children && children.length > 0) {
                const childrenHtml = children.map(child => `
                    <div class="child-card">
                        <a href="/indicator/${child.id}" class="child-title">${child.id} - ${child.name}</a>
                        <div class="child-description">${child.description || '暂无描述'}</div>
                        <div style="margin-top: 8px; font-size: 12px; color: #5f6368;">
                            组件数量: ${child.component_count || 0}
                        </div>
                    </div>
                `).join('');

                document.getElementById('childrenGrid').innerHTML = childrenHtml;
                document.getElementById('childrenSection').style.display = 'block';
            }
        }

        // 获取指标类型标签
        function getIndicatorTypeLabel(type) {
            const typeMap = {
                'composite': '复合指标（有分子分母）',
                'simple': '简单指标（基础数据）',
                'ratio': '比率指标',
                'count': '计数指标'
            };
            return typeMap[type] || '未设置';
        }

        // 切换编辑数值模式
        let isEditingValues = false;

        function toggleEditValues() {
            isEditingValues = !isEditingValues;
            const editBtn = document.querySelector('.edit-values-btn');

            if (isEditingValues) {
                // 进入编辑模式
                editBtn.innerHTML = '<i class="fas fa-save"></i> 保存数值';
                editBtn.style.background = '#ea4335';
                editBtn.onclick = saveValues;

                // 显示输入框，隐藏显示值
                document.getElementById('target-value-display').style.display = 'none';
                document.getElementById('target-value-input').style.display = 'block';
                document.getElementById('current-value-display').style.display = 'none';
                document.getElementById('current-value-input').style.display = 'block';

                // 添加取消按钮
                const cancelBtn = document.createElement('button');
                cancelBtn.className = 'cancel-values-btn';
                cancelBtn.innerHTML = '<i class="fas fa-times"></i> 取消';
                cancelBtn.onclick = cancelEditValues;
                editBtn.parentNode.appendChild(cancelBtn);

            } else {
                // 退出编辑模式
                resetEditMode();
            }
        }

        function resetEditMode() {
            isEditingValues = false;
            const editBtn = document.querySelector('.edit-values-btn');
            editBtn.innerHTML = '<i class="fas fa-edit"></i> 编辑数值';
            editBtn.style.background = '#34a853';
            editBtn.onclick = toggleEditValues;

            // 隐藏输入框，显示显示值
            document.getElementById('target-value-display').style.display = 'block';
            document.getElementById('target-value-input').style.display = 'none';
            document.getElementById('current-value-display').style.display = 'block';
            document.getElementById('current-value-input').style.display = 'none';

            // 移除取消按钮
            const cancelBtn = document.querySelector('.cancel-values-btn');
            if (cancelBtn) {
                cancelBtn.remove();
            }
        }

        function cancelEditValues() {
            // 恢复原始值
            const indicator = indicatorData.indicator;
            document.getElementById('target-value-input').value = indicator.target_value || '95';
            document.getElementById('current-value-input').value = indicator.current_value || '88';
            resetEditMode();
        }

        async function saveValues() {
            const targetValue = document.getElementById('target-value-input').value;
            const currentValue = document.getElementById('current-value-input').value;

            try {
                const response = await fetch(`/api/indicators/${indicatorId}/values`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        target_value: targetValue,
                        current_value: currentValue
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // 更新显示值
                    safeSetTextContent('target-value-display', targetValue);
                    safeSetTextContent('current-value-display', currentValue);

                    // 重新计算完成率
                    const completionRate = calculateCompletionRate(currentValue, targetValue);
                    safeSetTextContent('completion-rate-display', completionRate);

                    // 更新数据
                    indicatorData.indicator.target_value = targetValue;
                    indicatorData.indicator.current_value = currentValue;

                    resetEditMode();
                    showAlert('数值更新成功', 'success');
                } else {
                    showAlert('更新失败: ' + result.error, 'error');
                }
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'error');
            }
        }

        // 渲染分子分母组件
        function renderComponents() {
            const components = indicatorData.components;
            const container = document.getElementById('componentsContainer');

            if (components.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <p>暂无分子分母信息</p>
                        <p>点击"添加组件"按钮开始添加</p>
                    </div>
                `;
                return;
            }

            const html = components.map(component => `
                <div class="component-card" data-component-id="${component.id}">
                    <div class="component-header">
                        <span class="component-type ${component.component_type}">
                            ${component.component_type === 'numerator' ? '分子' : '分母'}
                        </span>
                        <div class="component-actions">
                            <button class="edit-btn" onclick="editComponent(${component.id})" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="delete-btn" onclick="deleteComponent(${component.id})" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>

                    <div class="component-content" id="content-${component.id}">
                        <div class="field-group">
                            <div class="field-label">名称</div>
                            <div class="field-value ${!component.name ? 'empty' : ''}">${component.name || '未填写'}</div>
                        </div>
                        <div class="field-group">
                            <div class="field-label">单位</div>
                            <div class="field-value ${!component.unit ? 'empty' : ''}">${component.unit || '未填写'}</div>
                        </div>
                        <div class="field-group">
                            <div class="field-label">牵头科室</div>
                            <div class="field-value ${!component.lead_department ? 'empty' : ''}">${component.lead_department || '未填写'}</div>
                        </div>
                        <div class="field-group">
                            <div class="field-label">数据来源</div>
                            <div class="field-value ${!component.data_source ? 'empty' : ''}">${component.data_source || '未填写'}</div>
                        </div>
                        <div class="field-group" style="grid-column: 1 / -1;">
                            <div class="field-label">逻辑定义</div>
                            <div class="field-value ${!component.logic_definition ? 'empty' : ''}">${component.logic_definition || '未填写'}</div>
                        </div>
                    </div>

                    <div class="edit-form" id="form-${component.id}">
                        ${renderEditForm(component)}
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 渲染编辑表单
        function renderEditForm(component) {
            return `
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">名称</label>
                        <input type="text" class="form-input" name="name" value="${component.name || ''}" placeholder="请输入组件名称">
                    </div>
                    <div class="form-group">
                        <label class="form-label">单位</label>
                        <input type="text" class="form-input" name="unit" value="${component.unit || ''}" placeholder="请输入单位">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">牵头科室</label>
                        <input type="text" class="form-input" name="lead_department" value="${component.lead_department || ''}" placeholder="请输入牵头科室">
                    </div>
                    <div class="form-group">
                        <label class="form-label">数据来源</label>
                        <input type="text" class="form-input" name="data_source" value="${component.data_source || ''}" placeholder="请输入数据来源">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">逻辑定义</label>
                    <textarea class="form-textarea" name="logic_definition" placeholder="请输入逻辑定义">${component.logic_definition || ''}</textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">备注</label>
                    <textarea class="form-textarea" name="notes" placeholder="请输入备注信息">${component.notes || ''}</textarea>
                </div>
                <div class="form-actions">
                    <button class="save-btn" onclick="saveComponent(${component.id})">保存</button>
                    <button class="cancel-btn" onclick="cancelEdit(${component.id})">取消</button>
                </div>
            `;
        }

        // 编辑组件
        function editComponent(componentId) {
            const contentDiv = document.getElementById(`content-${componentId}`);
            const formDiv = document.getElementById(`form-${componentId}`);

            contentDiv.style.display = 'none';
            formDiv.style.display = 'grid';
        }

        // 取消编辑
        function cancelEdit(componentId) {
            const contentDiv = document.getElementById(`content-${componentId}`);
            const formDiv = document.getElementById(`form-${componentId}`);

            contentDiv.style.display = 'grid';
            formDiv.style.display = 'none';
        }

        // 保存组件
        async function saveComponent(componentId) {
            const formDiv = document.getElementById(`form-${componentId}`);
            const formData = new FormData();

            // 收集表单数据
            const inputs = formDiv.querySelectorAll('input, textarea');
            const data = {};
            inputs.forEach(input => {
                data[input.name] = input.value;
            });

            try {
                formDiv.classList.add('loading');

                const response = await fetch(`/api/indicators/${indicatorId}/components/${componentId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('组件信息更新成功', 'success');
                    loadIndicatorDetail(); // 重新加载数据
                } else {
                    showAlert('更新失败: ' + result.error, 'error');
                }
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'error');
            } finally {
                formDiv.classList.remove('loading');
            }
        }

        // 删除组件
        async function deleteComponent(componentId) {
            if (!confirm('确定要删除这个组件吗？此操作不可撤销。')) {
                return;
            }

            try {
                const response = await fetch(`/api/indicators/${indicatorId}/components/${componentId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('组件删除成功', 'success');
                    loadIndicatorDetail(); // 重新加载数据
                } else {
                    showAlert('删除失败: ' + result.error, 'error');
                }
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'error');
            }
        }

        // 显示添加组件表单
        function showAddComponentForm() {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>添加新组件</h3>
                        <button class="modal-close" onclick="closeModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="form-label">组件类型</label>
                            <select class="form-input" id="new-component-type">
                                <option value="numerator">分子</option>
                                <option value="denominator">分母</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">组件名称</label>
                            <input type="text" class="form-input" id="new-component-name" placeholder="请输入组件名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label">单位</label>
                            <input type="text" class="form-input" id="new-component-unit" placeholder="请输入单位">
                        </div>
                        <div class="form-group">
                            <label class="form-label">牵头科室</label>
                            <input type="text" class="form-input" id="new-component-department" placeholder="请输入牵头科室">
                        </div>
                        <div class="form-group">
                            <label class="form-label">数据来源</label>
                            <input type="text" class="form-input" id="new-component-source" placeholder="请输入数据来源">
                        </div>
                        <div class="form-group">
                            <label class="form-label">逻辑定义</label>
                            <textarea class="form-textarea" id="new-component-logic" placeholder="请输入逻辑定义"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="save-btn" onclick="saveNewComponent()">保存</button>
                        <button class="cancel-btn" onclick="closeModal()">取消</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function closeModal() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }

        function saveNewComponent() {
            const type = document.getElementById('new-component-type').value;
            const name = document.getElementById('new-component-name').value;
            const unit = document.getElementById('new-component-unit').value;
            const department = document.getElementById('new-component-department').value;
            const source = document.getElementById('new-component-source').value;
            const logic = document.getElementById('new-component-logic').value;

            if (!name.trim()) {
                alert('请输入组件名称');
                return;
            }

            addComponent(type, name, unit, department, source, logic);
        }

        // 添加组件
        async function addComponent(componentType, name, unit = '', department = '', source = '', logic = '') {
            try {
                const response = await fetch(`/api/indicators/${indicatorId}/components`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        component_type: componentType,
                        name: name,
                        unit: unit,
                        lead_department: department,
                        data_source: source,
                        logic_definition: logic
                    })
                });

                const result = await response.json();

                if (result.success) {
                    closeModal();
                    showAlert('组件添加成功', 'success');
                    loadIndicatorDetail(); // 重新加载数据
                } else {
                    showAlert('添加失败: ' + result.error, 'error');
                }
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'error');
            }
        }

        // 显示提示信息
        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            if (alert) alert.textContent = message;

            container.appendChild(alert);

            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // 详细信息编辑功能
        let isDetailEditMode = false;

        // 切换详细信息编辑模式
        function toggleDetailEdit() {
            const editBtn = document.getElementById('editDetailsBtn');
            const displayMode = document.getElementById('detailsDisplayMode');
            const editMode = document.getElementById('detailsEditMode');
            const editIcon = editBtn.querySelector('i');

            if (!isDetailEditMode) {
                // 进入编辑模式
                isDetailEditMode = true;
                displayMode.style.display = 'none';
                editMode.style.display = 'block';
                editBtn.classList.add('active');
                editIcon.className = 'fas fa-times';
                editBtn.title = '取消编辑';

                // 填充编辑表单
                populateEditForm();

                // 设置表单提交事件
                setupEditFormHandler();

            } else {
                // 退出编辑模式
                cancelDetailEdit();
            }
        }

        // 取消详细信息编辑
        function cancelDetailEdit() {
            const editBtn = document.getElementById('editDetailsBtn');
            const displayMode = document.getElementById('detailsDisplayMode');
            const editMode = document.getElementById('detailsEditMode');
            const editIcon = editBtn.querySelector('i');

            isDetailEditMode = false;
            displayMode.style.display = 'block';
            editMode.style.display = 'none';
            editBtn.classList.remove('active');
            editIcon.className = 'fas fa-edit';
            editBtn.title = '编辑详细信息';
        }

        // 填充编辑表单
        function populateEditForm() {
            const indicator = indicatorData.indicator;

            // 基本属性字段
            document.getElementById('editUnit').value = indicator.unit || '';
            document.getElementById('editLeadDepartment').value = indicator.lead_department || '';
            document.getElementById('editDataSource').value = indicator.data_source || '';
            document.getElementById('editLogicDefinition').value = indicator.logic_definition || '';

            // 详细信息字段
            document.getElementById('editIndicatorDefinition').value = indicator.indicator_definition || '';
            document.getElementById('editCalculationFormula').value = indicator.calculation_formula || '';
            document.getElementById('editNumeratorDescription').value = indicator.numerator_description || '';
            document.getElementById('editDenominatorDescription').value = indicator.denominator_description || '';
            document.getElementById('editStatisticalScope').value = indicator.statistical_scope || '';
            document.getElementById('editDataSources').value = indicator.data_sources || '';
            document.getElementById('editCollectionFrequency').value = indicator.collection_frequency_detail || '';
            document.getElementById('editReferenceValue').value = indicator.reference_value || '';
            document.getElementById('editMonitoringAnalysis').value = indicator.monitoring_analysis || '';
        }

        // 设置编辑表单处理器
        function setupEditFormHandler() {
            const form = document.getElementById('detailsEditForm');

            // 移除之前的事件监听器
            form.removeEventListener('submit', handleDetailFormSubmit);

            // 添加新的事件监听器
            form.addEventListener('submit', handleDetailFormSubmit);
        }

        // 处理详细信息表单提交
        async function handleDetailFormSubmit(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const updateData = {};

            // 收集表单数据
            for (const [key, value] of formData.entries()) {
                updateData[key] = value.trim();
            }

            try {
                // 显示保存状态
                const submitBtn = event.target.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';

                const response = await fetch(`/api/indicators/${indicatorId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(updateData)
                });

                const result = await response.json();

                if (result.success) {
                    // 更新本地数据
                    Object.assign(indicatorData.indicator, updateData);

                    // 重新渲染详细信息
                    renderDetailedSections(indicatorData.indicator);

                    // 退出编辑模式
                    cancelDetailEdit();

                    // 显示成功消息
                    showAlert('详细信息保存成功！', 'success');

                } else {
                    throw new Error(result.error || '保存失败');
                }

            } catch (error) {
                console.error('保存详细信息失败:', error);
                showAlert('保存失败：' + error.message, 'error');
            } finally {
                const submitBtn = event.target.querySelector('button[type="submit"]');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        }

        // 折叠/展开详细信息
        function toggleDetailCollapse() {
            const content = document.getElementById('detailsContent');
            const collapseBtn = document.getElementById('collapseDetailsBtn');
            const collapseIcon = collapseBtn.querySelector('i');

            if (content.classList.contains('collapsed')) {
                // 展开
                content.classList.remove('collapsed');
                collapseIcon.className = 'fas fa-chevron-up';
                collapseBtn.title = '折叠';
            } else {
                // 折叠
                content.classList.add('collapsed');
                collapseIcon.className = 'fas fa-chevron-down';
                collapseBtn.title = '展开';
            }
        }
    </script>
</body>
</html>
