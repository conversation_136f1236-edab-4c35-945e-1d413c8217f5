{% extends "admin/base.html" %}

{% block title %}编辑小节 - 后台管理系统{% endblock %}

{% block breadcrumb %}
<span>后台管理</span> > <a href="/admin/sections">小节管理</a> > <span>编辑小节</span>
{% endblock %}

{% block content %}
<h1 class="admin-page-title">✏️ 编辑小节</h1>

<!-- 小节基本信息 -->
<div style="background: var(--white); padding: 24px; border-radius: var(--border-radius-lg); box-shadow: var(--shadow-1); border: 1px solid var(--gray-200); margin-bottom: 24px;">
    <h3 style="margin: 0 0 16px 0; color: var(--gray-900); font-size: 18px; font-weight: 500;">📋 小节信息</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; color: var(--gray-600); font-size: 14px;">
        <div>
            <strong>小节ID:</strong> {{ section.id }}
        </div>
        <div>
            <strong>所属章节:</strong> {{ section.chapter_code }} - {{ section.chapter_name }}
        </div>
        <div>
            <strong>小节编号:</strong> {{ section.code }}
        </div>
    </div>
</div>

<!-- 编辑表单 -->
<div style="background: var(--white); padding: 24px; border-radius: var(--border-radius-lg); box-shadow: var(--shadow-1); border: 1px solid var(--gray-200);">
    <form method="POST">
        <div style="display: grid; gap: 24px;">
            <!-- 小节名称 -->
            <div>
                <label style="display: block; margin-bottom: 8px; font-weight: 500; color: var(--gray-700); font-size: 14px;">
                    小节名称 <span style="color: var(--danger);">*</span>
                </label>
                <input type="text" name="name" value="{{ section.name or '' }}" required
                       style="width: 100%; padding: 12px 16px; border: 1px solid var(--gray-300); border-radius: var(--border-radius); font-size: 14px; transition: var(--transition); background: var(--white);">
            </div>

            <!-- 小节描述 -->
            <div>
                <label style="display: block; margin-bottom: 8px; font-weight: 500; color: var(--gray-700); font-size: 14px;">
                    小节描述
                </label>
                <textarea name="description" rows="4"
                          style="width: 100%; padding: 12px 16px; border: 1px solid var(--gray-300); border-radius: var(--border-radius); font-size: 14px; transition: var(--transition); background: var(--white); resize: vertical;">{{ section.description or '' }}</textarea>
            </div>

            <!-- 排序号 -->
            <div>
                <label style="display: block; margin-bottom: 8px; font-weight: 500; color: var(--gray-700); font-size: 14px;">
                    排序号
                </label>
                <input type="number" name="sort_order" value="{{ section.sort_order or '' }}" min="0" step="1"
                       style="width: 200px; padding: 12px 16px; border: 1px solid var(--gray-300); border-radius: var(--border-radius); font-size: 14px; transition: var(--transition); background: var(--white);"
                       placeholder="用于排序，数字越小越靠前">
                <div style="font-size: 12px; color: var(--gray-500); margin-top: 4px;">
                    数字越小越靠前，相同数字按创建时间排序
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div style="margin-top: 32px; padding-top: 24px; border-top: 1px solid var(--gray-200); display: flex; gap: 12px;">
            <button type="submit" class="admin-btn admin-btn-primary">
                <i class="fas fa-save"></i>
                保存更改
            </button>
            <a href="/admin/sections" class="admin-btn admin-btn-outline">
                <i class="fas fa-times"></i>
                取消
            </a>
            <a href="/admin/indicators?section_id={{ section.id }}" class="admin-btn admin-btn-outline">
                <i class="fas fa-chart-bar"></i>
                查看指标
            </a>
        </div>
    </form>
</div>

<!-- 相关统计信息 -->
<div style="background: var(--white); padding: 24px; border-radius: var(--border-radius-lg); box-shadow: var(--shadow-1); border: 1px solid var(--gray-200); margin-top: 24px;">
    <h3 style="margin: 0 0 16px 0; color: var(--gray-900); font-size: 18px; font-weight: 500;">📊 相关统计</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
        <div style="text-align: center; padding: 16px; background: var(--primary-light); border-radius: var(--border-radius); border: 1px solid var(--primary-lighter);">
            <div style="font-size: 24px; font-weight: 600; color: var(--primary); margin-bottom: 4px;">
                {{ section.indicator_count or 0 }}
            </div>
            <div style="font-size: 12px; color: var(--gray-600);">指标数量</div>
        </div>
        <div style="text-align: center; padding: 16px; background: var(--success-light); border-radius: var(--border-radius); border: 1px solid var(--success-lighter);">
            <div style="font-size: 24px; font-weight: 600; color: var(--success); margin-bottom: 4px;">
                {{ section.sort_order or 0 }}
            </div>
            <div style="font-size: 12px; color: var(--gray-600);">排序号</div>
        </div>
        <div style="text-align: center; padding: 16px; background: var(--warning-light); border-radius: var(--border-radius); border: 1px solid var(--warning-lighter);">
            <div style="font-size: 24px; font-weight: 600; color: var(--warning); margin-bottom: 4px;">
                {{ section.chapter_code }}
            </div>
            <div style="font-size: 12px; color: var(--gray-600);">所属章节</div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 表单验证
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.querySelector('input[name="name"]').value.trim();
    if (!name) {
        alert('请输入小节名称');
        e.preventDefault();
        return false;
    }
    
    // 确认提交
    if (!confirm('确定要保存这些更改吗？')) {
        e.preventDefault();
        return false;
    }
});

// 输入框焦点效果
document.querySelectorAll('input, textarea, select').forEach(element => {
    element.addEventListener('focus', function() {
        this.style.borderColor = 'var(--primary)';
        this.style.boxShadow = '0 0 0 3px var(--primary-light)';
    });
    
    element.addEventListener('blur', function() {
        this.style.borderColor = 'var(--gray-300)';
        this.style.boxShadow = 'none';
    });
});

// 自动保存提示
let autoSaveTimer;
document.querySelectorAll('input, textarea').forEach(element => {
    element.addEventListener('input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(() => {
            // 可以在这里实现自动保存功能
            console.log('可以实现自动保存...');
        }, 2000);
    });
});
</script>
{% endblock %}
