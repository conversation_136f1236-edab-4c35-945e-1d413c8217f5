{% extends "admin/base.html" %}

{% block title %}小节管理 - 医院指标后台管理系统{% endblock %}

{% block breadcrumb %}
<span>后台管理</span> / <span>小节管理</span>
{% endblock %}

{% block content %}
<h1 class="admin-page-title">📋 小节管理</h1>

<!-- 筛选器 -->
<div class="admin-filters">
    <form method="GET" class="filter-row">
        <div class="filter-group">
            <label for="chapter_filter">筛选章节</label>
            <select id="chapter_filter" name="chapter_id" onchange="this.form.submit()">
                <option value="">全部章节</option>
                {% for chapter in chapters %}
                <option value="{{ chapter.id }}" {% if selected_chapter == chapter.id|string %}selected{% endif %}>
                    {{ chapter.code }} - {{ chapter.name }}
                </option>
                {% endfor %}
            </select>
        </div>
        <div class="filter-group">
            <label>&nbsp;</label>
            <button type="button" class="admin-btn admin-btn-primary" onclick="addSection()">
                <i class="fas fa-plus"></i>
                添加小节
            </button>
        </div>
    </form>
</div>

<!-- 小节列表 -->
<div class="admin-table">
    <table>
        <thead>
            <tr>
                <th style="width: 50px;">
                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                </th>
                <th style="width: 100px;">章节</th>
                <th style="width: 80px;">小节编号</th>
                <th>小节名称</th>
                <th style="width: 80px;">排序</th>
                <th style="width: 80px;">指标数</th>
                <th style="width: 120px;">创建时间</th>
                <th style="width: 150px;">操作</th>
            </tr>
        </thead>
        <tbody>
            {% for section in sections %}
            <tr>
                <td>
                    <input type="checkbox" class="section-checkbox" value="{{ section.id }}">
                </td>
                <td>
                    <span class="badge" style="background: #e3f2fd; color: #1976d2; font-size: 11px;">
                        {{ section.chapter_code }}
                    </span>
                </td>
                <td>
                    <span class="badge" style="background: #f3e5f5; color: #7b1fa2;">
                        {{ section.code }}
                    </span>
                </td>
                <td>
                    <div>
                        <div style="font-weight: 500;">{{ section.name }}</div>
                        {% if section.description %}
                        <div style="font-size: 12px; color: #6c757d; margin-top: 4px;">
                            {{ section.description[:60] }}{% if section.description|length > 60 %}...{% endif %}
                        </div>
                        {% endif %}
                    </div>
                </td>
                <td>
                    <span style="font-family: monospace; background: #f8f9fa; padding: 2px 6px; border-radius: 4px;">
                        {{ section.sort_order or 0 }}
                    </span>
                </td>
                <td>
                    <span class="badge" style="background: #e8f5e8; color: #2e7d32;">
                        {{ section.indicator_count or 0 }}
                    </span>
                </td>
                <td style="font-size: 12px; color: #6c757d;">
                    {{ section.created_at[:10] if section.created_at else '-' }}
                </td>
                <td>
                    <div class="admin-actions">
                        <a href="/admin/sections/{{ section.id }}/edit" class="admin-btn admin-btn-warning" title="编辑">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="/admin/indicators?section_id={{ section.id }}" class="admin-btn admin-btn-primary" title="查看指标">
                            <i class="fas fa-chart-bar"></i>
                        </a>
                        <button onclick="deleteSection('{{ section.id }}')" class="admin-btn admin-btn-danger" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            {% endfor %}

            {% if not sections %}
            <tr>
                <td colspan="8" style="text-align: center; padding: 40px; color: #6c757d;">
                    <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <div>暂无小节数据</div>
                    {% if selected_chapter %}
                    <div style="font-size: 12px; margin-top: 8px;">当前章节下没有小节</div>
                    {% else %}
                    <div style="font-size: 12px; margin-top: 8px;">请选择章节或添加新小节</div>
                    {% endif %}
                </td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- 编辑模态框 -->
<div id="editModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 600px;">
        <div class="modal-header">
            <h3 id="modalTitle">编辑小节</h3>
            <button class="modal-close" onclick="closeModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="sectionForm">
                <input type="hidden" id="sectionId" name="id">

                <div class="form-row">
                    <div class="form-group">
                        <label for="sectionChapter">所属章节 *</label>
                        <select id="sectionChapter" name="chapter_id" required>
                            <option value="">请选择章节</option>
                            {% for chapter in chapters %}
                            <option value="{{ chapter.id }}">{{ chapter.code }} - {{ chapter.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="sectionCode">小节编号 *</label>
                        <input type="text" id="sectionCode" name="code" required placeholder="例如：1.1">
                    </div>
                </div>

                <div class="form-group">
                    <label for="sectionName">小节名称 *</label>
                    <input type="text" id="sectionName" name="name" required placeholder="例如：医院设置与功能">
                </div>

                <div class="form-group">
                    <label for="sectionDescription">小节描述</label>
                    <textarea id="sectionDescription" name="description" rows="3" placeholder="请输入小节描述"></textarea>
                </div>

                <div class="form-group">
                    <label for="sectionSort">排序号</label>
                    <input type="number" id="sectionSort" name="sort_order" min="0" placeholder="用于排序，数字越小越靠前">
                </div>

                <div class="form-actions">
                    <button type="submit" class="admin-btn admin-btn-primary">
                        <i class="fas fa-save"></i>
                        保存
                    </button>
                    <button type="button" class="admin-btn" onclick="closeModal()">取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
}

.modal-body {
    padding: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-group {
    flex: 1;
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.section-checkbox');
    checkboxes.forEach(cb => cb.checked = selectAll.checked);
}

function addSection() {
    document.getElementById('modalTitle').textContent = '添加小节';
    document.getElementById('sectionForm').reset();
    document.getElementById('sectionId').value = '';

    // 如果有选中的章节，自动设置
    const chapterFilter = document.getElementById('chapter_filter');
    if (chapterFilter.value) {
        document.getElementById('sectionChapter').value = chapterFilter.value;
    }

    document.getElementById('editModal').style.display = 'flex';
}

function editSection(id) {
    document.getElementById('modalTitle').textContent = '编辑小节';
    document.getElementById('sectionId').value = id;
    document.getElementById('editModal').style.display = 'flex';

    // TODO: 通过AJAX获取小节数据并填充表单
}

function deleteSection(id) {
    if (confirm('确定要删除这个小节吗？此操作不可恢复。\n\n注意：如果该小节有指标，需要先删除指标。')) {
        // 创建表单并提交删除请求
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/sections/${id}/delete`;

        document.body.appendChild(form);
        form.submit();
    }
}

function viewIndicators(sectionId) {
    // TODO: 跳转到指标管理页面，筛选该小节的指标
    console.log('查看小节指标:', sectionId);
}

function closeModal() {
    document.getElementById('editModal').style.display = 'none';
}

// 表单提交
document.getElementById('sectionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    // TODO: 实现保存功能
    console.log('保存小节数据');
});

// 点击模态框外部关闭
document.getElementById('editModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>
{% endblock %}
