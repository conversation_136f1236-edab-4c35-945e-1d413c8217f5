// 医院等级评审指标说明手册 - 完整版本3

class HospitalIndicatorApp {
    constructor() {
        this.currentChapter = null;
        this.currentSection = null;
        this.indicators = [];
        this.chapters = [];
        this.sections = [];
        this.init();
    }

    async init() {
        try {
            await this.loadChapters();
            await this.loadStatistics();
            this.setupEventListeners();
            this.setupSearch();
            console.log('完整版应用初始化完成');
        } catch (error) {
            console.error('应用初始化失败:', error);
        }
    }

    async loadChapters() {
        try {
            const response = await fetch('/api/chapters');
            const data = await response.json();

            if (data.success) {
                this.chapters = data.data;
                this.renderChapters();
            } else {
                console.error('加载章节失败:', data.error);
            }
        } catch (error) {
            console.error('加载章节异常:', error);
        }
    }

    async loadStatistics() {
        try {
            const response = await fetch('/api/statistics');
            const data = await response.json();

            if (data.success) {
                this.renderStatistics(data.data);
            } else {
                console.error('加载统计失败:', data.error);
            }
        } catch (error) {
            console.error('加载统计异常:', error);
        }
    }

    renderChapters() {
        // 尝试多个可能的容器ID
        const chapterGrid = document.getElementById('chapterGrid');
        const chapterList = document.getElementById('chapterList');
        const container = chapterGrid || chapterList;

        if (!container) {
            console.warn('找不到章节容器');
            return;
        }

        container.innerHTML = this.chapters.map(chapter => `
            <div class="chapter-card" data-chapter="${chapter.id}" onclick="viewChapter('${chapter.id}')">
                <div class="chapter-header">
                    <div class="chapter-icon" style="background-color: ${chapter.color || '#1a73e8'}">
                        <i class="${chapter.icon || 'fas fa-book'}"></i>
                    </div>
                    <div class="chapter-info">
                        <h3 class="chapter-title">${chapter.name}</h3>
                        <p class="chapter-description">${chapter.description || ''}</p>
                    </div>
                </div>
                <div class="chapter-stats">
                    <div class="stat-item">
                        <span class="stat-number">${chapter.section_count || 0}</span>
                        <span class="stat-label">小节</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${chapter.indicator_count || 0}</span>
                        <span class="stat-label">指标</span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    renderStatistics(stats) {
        // 更新各个统计数字
        const chapterCount = document.getElementById('chapterCount');
        const sectionCount = document.getElementById('sectionCount');
        const indicatorCount = document.getElementById('indicatorCount');
        const componentCount = document.getElementById('componentCount');

        if (chapterCount) chapterCount.textContent = stats.total_chapters || 0;
        if (sectionCount) sectionCount.textContent = stats.total_sections || 0;
        if (indicatorCount) indicatorCount.textContent = stats.total_indicators || 0;
        if (componentCount) componentCount.textContent = '0'; // 暂时设为0

        // 也尝试更新统计容器（如果存在）
        const statsContainer = document.getElementById('statisticsContainer');
        if (statsContainer) {
            statsContainer.innerHTML = `
                <div class="stat-card">
                    <i class="fas fa-book"></i>
                    <div class="stat-info">
                        <h3>${stats.total_chapters || 0}</h3>
                        <p>总章节数</p>
                    </div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-list"></i>
                    <div class="stat-info">
                        <h3>${stats.total_sections || 0}</h3>
                        <p>总小节数</p>
                    </div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-chart-bar"></i>
                    <div class="stat-info">
                        <h3>${stats.total_indicators || 0}</h3>
                        <p>总指标数</p>
                    </div>
                </div>
            `;
        }
    }

    async selectChapter(chapterId) {
        this.currentChapter = chapterId;

        try {
            // 加载章节的小节
            await this.loadSections(chapterId);
            // 加载章节的指标
            await this.loadIndicators(chapterId);

            // 更新UI
            this.updateChapterSelection();

        } catch (error) {
            console.error('选择章节失败:', error);
        }
    }

    async loadSections(chapterId) {
        try {
            const response = await fetch(`/api/chapters/${chapterId}/sections`);
            const data = await response.json();

            if (data.success) {
                this.sections = data.data;
                this.renderSections();
            } else {
                console.error('加载小节失败:', data.error);
            }
        } catch (error) {
            console.error('加载小节异常:', error);
        }
    }

    async loadIndicators(chapterId, sectionId = null) {
        try {
            let url = `/api/indicators?chapter=${chapterId}`;
            if (sectionId) {
                url += `&section=${sectionId}`;
            }

            const response = await fetch(url);
            const data = await response.json();

            if (data.success) {
                this.indicators = data.data;
                this.renderIndicators();
            } else {
                console.error('加载指标失败:', data.error);
            }
        } catch (error) {
            console.error('加载指标异常:', error);
        }
    }

    renderSections() {
        const sectionList = document.getElementById('sectionList');
        if (!sectionList) return;

        sectionList.innerHTML = this.sections.map(section => `
            <div class="section-item" data-section="${section.id}">
                <h4>${section.name}</h4>
                <p>${section.description || ''}</p>
                <span class="indicator-count">${section.indicator_count || 0} 个指标</span>
            </div>
        `).join('');

        // 添加点击事件
        sectionList.querySelectorAll('.section-item').forEach(item => {
            item.addEventListener('click', () => {
                const sectionId = item.dataset.section;
                this.selectSection(sectionId);
            });
        });
    }

    renderIndicators() {
        // 尝试多个可能的容器
        const indicatorList = document.getElementById('indicatorList');
        const indicatorCards = document.getElementById('indicatorCards');
        const container = indicatorList || indicatorCards;

        if (!container) {
            console.warn('找不到指标容器');
            return;
        }

        // 根据容器类型选择不同的渲染方式
        if (container.id === 'indicatorCards') {
            // 卡片视图
            container.innerHTML = this.indicators.map(indicator => `
                <div class="indicator-card" data-indicator="${indicator.id}">
                    <div class="indicator-header">
                        <div class="indicator-id">${indicator.id}</div>
                        <div class="indicator-type-badge ${indicator.indicator_type || 'simple'}">${indicator.indicator_type || 'simple'}</div>
                    </div>
                    <h3 class="indicator-title">${indicator.name}</h3>
                    <p class="indicator-description">${indicator.description || ''}</p>
                    <div class="indicator-actions">
                        <button onclick="viewIndicatorDetail('${indicator.id}')" class="btn btn-primary">
                            <i class="fas fa-eye"></i> 查看详情
                        </button>
                    </div>
                </div>
            `).join('');
        } else {
            // 列表视图
            container.innerHTML = this.indicators.map(indicator => `
                <div class="list-item" data-indicator="${indicator.id}">
                    <div class="list-col-id">${indicator.id}</div>
                    <div class="list-col-title">${indicator.name}</div>
                    <div class="list-col-type">
                        <span class="type-badge ${indicator.indicator_type || 'simple'}">${indicator.indicator_type || 'simple'}</span>
                    </div>
                    <div class="list-col-desc">${indicator.description || ''}</div>
                    <div class="list-col-tags">
                        <span class="tag">${indicator.chapter_name || ''}</span>
                    </div>
                    <div class="list-col-actions">
                        <button onclick="viewIndicatorDetail('${indicator.id}')" class="btn btn-sm btn-outline">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }
    }

    async showIndicatorDetail(indicatorId) {
        try {
            const response = await fetch(`/api/indicators/${indicatorId}`);
            const data = await response.json();

            if (data.success) {
                this.displayIndicatorModal(data.data.indicator);
            } else {
                console.error('加载指标详情失败:', data.error);
            }
        } catch (error) {
            console.error('加载指标详情异常:', error);
        }
    }

    displayIndicatorModal(indicator) {
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>${indicator.id} ${indicator.name}</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="indicator-detail">
                        <div class="detail-section">
                            <h3>基本信息</h3>
                            <p><strong>指标类型:</strong> ${indicator.indicator_type || 'simple'}</p>
                            <p><strong>描述:</strong> ${indicator.description || '暂无描述'}</p>
                            <p><strong>单位:</strong> ${indicator.unit || '暂无单位'}</p>
                            <p><strong>所属章节:</strong> ${indicator.chapter_name || '未知'}</p>
                            <p><strong>所属小节:</strong> ${indicator.section_name || '未知'}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 添加关闭事件
        const closeBtn = modal.querySelector('.close');
        closeBtn.onclick = () => {
            document.body.removeChild(modal);
        };

        modal.onclick = (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        };
    }

    selectSection(sectionId) {
        this.currentSection = sectionId;
        this.loadIndicators(this.currentChapter, sectionId);
        this.updateSectionSelection();
    }

    updateChapterSelection() {
        document.querySelectorAll('.chapter-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeChapter = document.querySelector(`[data-chapter="${this.currentChapter}"]`);
        if (activeChapter) {
            activeChapter.classList.add('active');
        }
    }

    updateSectionSelection() {
        document.querySelectorAll('.section-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeSection = document.querySelector(`[data-section="${this.currentSection}"]`);
        if (activeSection) {
            activeSection.classList.add('active');
        }
    }

    setupEventListeners() {
        // 菜单切换
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');

        if (menuToggle && sidebar) {
            menuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
            });
        }
    }

    setupSearch() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchIndicators(e.target.value);
            });
        }
    }

    searchIndicators(query) {
        if (!query.trim()) {
            this.renderIndicators();
            return;
        }

        const filteredIndicators = this.indicators.filter(indicator =>
            indicator.name.toLowerCase().includes(query.toLowerCase()) ||
            indicator.id.toLowerCase().includes(query.toLowerCase()) ||
            (indicator.description && indicator.description.toLowerCase().includes(query.toLowerCase()))
        );

        const indicatorList = document.getElementById('indicatorList');
        if (!indicatorList) return;

        indicatorList.innerHTML = filteredIndicators.map(indicator => `
            <div class="indicator-item" data-indicator="${indicator.id}">
                <div class="indicator-header">
                    <h5>${indicator.id} ${indicator.name}</h5>
                    <span class="indicator-type">${indicator.indicator_type || 'simple'}</span>
                </div>
                <p class="indicator-description">${indicator.description || ''}</p>
                <div class="indicator-actions">
                    <button onclick="app.showIndicatorDetail('${indicator.id}')" class="btn-detail">
                        <i class="fas fa-eye"></i> 查看详情
                    </button>
                </div>
            </div>
        `).join('');
    }

    // 添加前端模板需要的方法
    bindHomePageEvents() {
        // 绑定视图切换
        const viewBtns = document.querySelectorAll('.view-btn');
        viewBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                viewBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                const view = btn.dataset.view;
                const cardView = document.getElementById('cardView');
                const listView = document.getElementById('listView');

                if (view === 'card') {
                    cardView.style.display = 'block';
                    listView.style.display = 'none';
                } else {
                    cardView.style.display = 'none';
                    listView.style.display = 'block';
                }
            });
        });

        // 绑定刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadChapters();
                this.loadStatistics();
            });
        }

        // 绑定筛选器
        const chapterFilter = document.getElementById('chapterFilter');
        if (chapterFilter) {
            chapterFilter.addEventListener('change', (e) => {
                if (e.target.value) {
                    this.selectChapter(e.target.value);
                }
            });
        }
    }

    // 更新章节筛选器
    updateChapterFilter() {
        const chapterFilter = document.getElementById('chapterFilter');
        if (chapterFilter && this.chapters.length > 0) {
            chapterFilter.innerHTML = '<option value="">所有章节</option>' +
                this.chapters.map(chapter =>
                    `<option value="${chapter.id}">${chapter.name}</option>`
                ).join('');
        }
    }

    // 重写loadChapters方法以包含筛选器更新
    async loadChapters() {
        try {
            const response = await fetch('/api/chapters');
            const data = await response.json();

            if (data.success) {
                this.chapters = data.data;
                this.renderChapters();
                this.updateChapterFilter();
            } else {
                console.error('加载章节失败:', data.error);
            }
        } catch (error) {
            console.error('加载章节异常:', error);
        }
    }
}

// 全局函数，供模板调用
function viewChapter(chapterCode) {
    if (window.app) {
        window.app.selectChapter(chapterCode);
        // 滚动到指标列表
        const indicatorSection = document.querySelector('.indicator-section');
        if (indicatorSection) {
            indicatorSection.scrollIntoView({
                behavior: 'smooth'
            });
        }
    }
}

function viewIndicatorDetail(indicatorId) {
    if (window.app) {
        window.app.showIndicatorDetail(indicatorId);
    }
}

function toggleFavorite(indicatorId) {
    // 收藏功能暂时留空
    console.log('收藏功能:', indicatorId);
}

function toggleIndicatorChildren(indicatorId) {
    // 子指标切换功能暂时留空
    console.log('子指标切换:', indicatorId);
}

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new HospitalIndicatorApp();
});
