// 全局错误处理脚本

// 安全的DOM操作函数
function safeSetTextContent(element, text) {
    if (element) {
        element.textContent = text || '';
        return true;
    } else {
        console.warn('Element not found for text setting');
        return false;
    }
}

function safeSetInnerHTML(element, html) {
    if (element) {
        element.innerHTML = html || '';
        return true;
    } else {
        console.warn('Element not found for HTML setting');
        return false;
    }
}

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
});

window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled Promise Rejection:', e.reason);
});

// 通知系统
function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    if (notification) {
        const icon = notification.querySelector('.notification-icon');
        const messageEl = notification.querySelector('.notification-message');
        
        if (icon && messageEl) {
            // 设置图标
            icon.className = `notification-icon fas ${type === 'success' ? 'fa-check-circle' : 
                                                    type === 'error' ? 'fa-exclamation-circle' : 
                                                    type === 'warning' ? 'fa-exclamation-triangle' : 
                                                    'fa-info-circle'}`;
            
            // 设置消息
            messageEl.textContent = message;
            
            // 设置样式
            notification.className = `notification ${type}`;
            notification.style.display = 'block';
            
            // 自动隐藏
            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }
    } else {
        console.log(`Notification: ${message}`);
    }
}

console.log('Error handler loaded');
