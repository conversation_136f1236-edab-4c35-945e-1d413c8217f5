/* 后台管理系统样式 */

/* 基础变量 */
:root {
    --primary: #1976d2;
    --primary-light: #e3f2fd;
    --primary-dark: #1565c0;
    --secondary: #424242;
    --success: #4caf50;
    --success-light: #e8f5e8;
    --warning: #ff9800;
    --warning-light: #fff3e0;
    --warning-dark: #f57c00;
    --danger: #f44336;
    --danger-light: #ffebee;
    --info: #2196f3;
    --info-light: #e3f2fd;
    --info-dark: #1565c0;
    --gray-100: #f5f5f5;
    --gray-200: #eeeeee;
    --gray-300: #e0e0e0;
    --gray-400: #bdbdbd;
    --gray-500: #9e9e9e;
    --gray-600: #757575;
    --gray-700: #616161;
    --gray-800: #424242;
    --gray-900: #212121;
}

/* 按钮样式 */
.admin-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.admin-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.admin-btn-primary {
    background: var(--primary);
    color: white;
}

.admin-btn-primary:hover {
    background: var(--primary-dark);
    color: white;
}

.admin-btn-secondary {
    background: var(--gray-500);
    color: white;
}

.admin-btn-secondary:hover {
    background: var(--gray-600);
    color: white;
}

.admin-btn-success {
    background: var(--success);
    color: white;
}

.admin-btn-success:hover {
    background: #45a049;
    color: white;
}

.admin-btn-warning {
    background: var(--warning);
    color: white;
}

.admin-btn-warning:hover {
    background: var(--warning-dark);
    color: white;
}

.admin-btn-danger {
    background: var(--danger);
    color: white;
}

.admin-btn-danger:hover {
    background: #d32f2f;
    color: white;
}

.admin-btn-info {
    background: var(--info);
    color: white;
}

.admin-btn-info:hover {
    background: var(--info-dark);
    color: white;
}

.admin-btn-outline {
    background: transparent;
    color: var(--primary);
    border: 1px solid var(--primary);
}

.admin-btn-outline:hover {
    background: var(--primary);
    color: white;
}

/* 表格样式 */
.admin-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.admin-table table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th {
    background: var(--gray-100);
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-300);
}

.admin-table td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.admin-table tr:hover {
    background: var(--gray-50);
}

/* 操作按钮组 */
.admin-actions {
    display: flex;
    gap: 6px;
    align-items: center;
}

.admin-actions .admin-btn {
    padding: 6px 8px;
    font-size: 12px;
}

/* 筛选器样式 */
.admin-filters {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.filter-row {
    display: flex;
    gap: 16px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
    min-width: 150px;
}

.filter-group label {
    font-weight: 500;
    color: var(--gray-700);
    font-size: 14px;
}

.filter-group input,
.filter-group select {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    font-size: 14px;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}

/* 页面标题 */
.admin-page-title {
    font-size: 28px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .admin-actions {
        flex-direction: column;
        gap: 4px;
    }
    
    .admin-table {
        overflow-x: auto;
    }
    
    .admin-table table {
        min-width: 800px;
    }
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 成功/错误消息 */
.alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    font-size: 14px;
}

.alert.success {
    background: var(--success-light);
    color: #2e7d32;
    border: 1px solid var(--success);
}

.alert.error {
    background: var(--danger-light);
    color: #c62828;
    border: 1px solid var(--danger);
}

.alert.warning {
    background: var(--warning-light);
    color: #f57c00;
    border: 1px solid var(--warning);
}

.alert.info {
    background: var(--info-light);
    color: var(--info-dark);
    border: 1px solid var(--info);
}


/* 暂时隐藏参考范围相关功能 */
.reference-range-section,
.reference-range-card,
.reference-range-button,
[id*="reference-range"],
[class*="reference-range"],
[href*="reference-range"],
.modal-reference-range,
#modalReferenceRangeCard {
    display: none !important;
}

/* 隐藏参考范围相关的表格列 */
th:has-text("参考范围"),
td:has-text("参考范围") {
    display: none !important;
}

/* 隐藏参考范围按钮 */
button[onclick*="reference"],
a[href*="reference-range"] {
    display: none !important;
}
