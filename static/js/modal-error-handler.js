
// 模态框专用错误处理
function handleModalError(error, context = 'modal') {
    console.error(`Modal Error in ${context}:`, error);
    
    // 显示用户友好的错误信息
    const errorMessage = document.createElement('div');
    errorMessage.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: var(--md-error, #d32f2f);
        color: var(--md-on-error, white);
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 400px;
        text-align: center;
    `;
    
    errorMessage.innerHTML = `
        <div style="margin-bottom: 12px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 24px;"></i>
        </div>
        <div style="font-weight: 500; margin-bottom: 8px;">加载失败</div>
        <div style="font-size: 14px; opacity: 0.9;">页面内容加载时出现问题，请刷新页面重试</div>
        <button onclick="this.parentElement.remove()" style="
            margin-top: 16px;
            padding: 8px 16px;
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            border-radius: 4px;
            cursor: pointer;
        ">关闭</button>
    `;
    
    document.body.appendChild(errorMessage);
    
    // 5秒后自动移除
    setTimeout(() => {
        if (errorMessage.parentElement) {
            errorMessage.remove();
        }
    }, 5000);
}

// 包装模态框显示函数
function safeShowModal(modalId) {
    try {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('show');
            return true;
        } else {
            throw new Error(`Modal not found: ${modalId}`);
        }
    } catch (error) {
        handleModalError(error, 'showModal');
        return false;
    }
}

// 包装模态框隐藏函数
function safeHideModal(modalId) {
    try {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('show');
            return true;
        } else {
            console.warn(`Modal not found: ${modalId}`);
            return false;
        }
    } catch (error) {
        handleModalError(error, 'hideModal');
        return false;
    }
}
