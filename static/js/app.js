// 医院等级评审指标说明手册 - 简单稳定版本

class HospitalIndicatorApp {
    constructor() {
        this.currentChapter = null;
        this.indicators = [];
        this.chapters = [];
        this.init();
    }

    async init() {
        try {
            await this.loadChapters();
            await this.loadStatistics();
            this.setupEventListeners();
            console.log('应用初始化完成');
        } catch (error) {
            console.error('应用初始化失败:', error);
        }
    }

    async loadChapters() {
        try {
            const response = await fetch('/api/chapters');
            const data = await response.json();
            
            if (data.success) {
                this.chapters = data.data;
                this.renderChapters();
            }
        } catch (error) {
            console.error('加载章节失败:', error);
        }
    }

    async loadStatistics() {
        try {
            const response = await fetch('/api/statistics');
            const data = await response.json();
            
            if (data.success) {
                this.renderStatistics(data.data);
            }
        } catch (error) {
            console.error('加载统计失败:', error);
        }
    }

    renderChapters() {
        const chapterList = document.getElementById('chapterList');
        if (!chapterList) return;

        chapterList.innerHTML = this.chapters.map(chapter => `
            <div class="chapter-item" data-chapter="${chapter.id}">
                <h3>${chapter.name}</h3>
                <p>${chapter.description || ''}</p>
                <div class="chapter-stats">
                    <span>${chapter.section_count || 0} 个小节</span>
                    <span>${chapter.indicator_count || 0} 个指标</span>
                </div>
            </div>
        `).join('');

        chapterList.querySelectorAll('.chapter-item').forEach(item => {
            item.addEventListener('click', () => {
                const chapterId = item.dataset.chapter;
                this.selectChapter(chapterId);
            });
        });
    }

    renderStatistics(stats) {
        const statsContainer = document.getElementById('statisticsContainer');
        if (!statsContainer) return;

        statsContainer.innerHTML = `
            <div class="stat-card">
                <h3>${stats.total_chapters || 0}</h3>
                <p>总章节数</p>
            </div>
            <div class="stat-card">
                <h3>${stats.total_sections || 0}</h3>
                <p>总小节数</p>
            </div>
            <div class="stat-card">
                <h3>${stats.total_indicators || 0}</h3>
                <p>总指标数</p>
            </div>
        `;
    }

    async selectChapter(chapterId) {
        this.currentChapter = chapterId;
        
        try {
            await this.loadIndicators(chapterId);
            this.updateChapterSelection();
        } catch (error) {
            console.error('选择章节失败:', error);
        }
    }

    async loadIndicators(chapterId) {
        try {
            const response = await fetch(`/api/indicators?chapter=${chapterId}`);
            const data = await response.json();
            
            if (data.success) {
                this.indicators = data.data;
                this.renderIndicators();
            }
        } catch (error) {
            console.error('加载指标失败:', error);
        }
    }

    renderIndicators() {
        const indicatorList = document.getElementById('indicatorList');
        if (!indicatorList) return;

        indicatorList.innerHTML = this.indicators.map(indicator => `
            <div class="indicator-item">
                <h5>${indicator.id} ${indicator.name}</h5>
                <p>${indicator.description || ''}</p>
            </div>
        `).join('');
    }

    updateChapterSelection() {
        document.querySelectorAll('.chapter-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeChapter = document.querySelector(`[data-chapter="${this.currentChapter}"]`);
        if (activeChapter) {
            activeChapter.classList.add('active');
        }
    }

    setupEventListeners() {
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        
        if (menuToggle && sidebar) {
            menuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
            });
        }
    }
}

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new HospitalIndicatorApp();
});
