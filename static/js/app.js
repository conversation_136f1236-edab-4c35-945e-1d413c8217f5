// 医院指标系统 - 版本1 (简化版)

class SimpleIndicatorApp {
    constructor() {
        this.chapters = [];
        this.indicators = [];
        this.init();
    }

    async init() {
        try {
            await this.loadChapters();
            await this.loadStatistics();
            console.log('简化版应用初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
        }
    }

    async loadChapters() {
        try {
            const response = await fetch('/api/chapters');
            const data = await response.json();
            
            if (data.success) {
                this.chapters = data.data;
                this.renderChapters();
            }
        } catch (error) {
            console.error('加载章节失败:', error);
        }
    }

    async loadStatistics() {
        try {
            const response = await fetch('/api/statistics');
            const data = await response.json();
            
            if (data.success) {
                this.renderStatistics(data.data);
            }
        } catch (error) {
            console.error('加载统计失败:', error);
        }
    }

    renderChapters() {
        // 尝试多个可能的容器
        const chapterGrid = document.getElementById('chapterGrid');
        const chapterList = document.getElementById('chapterList');
        const container = chapterGrid || chapterList;
        
        if (!container) return;

        container.innerHTML = this.chapters.map(chapter => `
            <div class="chapter-card" onclick="viewChapter('${chapter.id}')">
                <h3>${chapter.name}</h3>
                <p>${chapter.description || ''}</p>
                <div class="chapter-stats">
                    <span>${chapter.section_count || 0} 个小节</span>
                    <span>${chapter.indicator_count || 0} 个指标</span>
                </div>
            </div>
        `).join('');
    }

    renderStatistics(stats) {
        // 更新统计数字
        const chapterCount = document.getElementById('chapterCount');
        const sectionCount = document.getElementById('sectionCount');
        const indicatorCount = document.getElementById('indicatorCount');
        
        if (chapterCount) chapterCount.textContent = stats.total_chapters || 0;
        if (sectionCount) sectionCount.textContent = stats.total_sections || 0;
        if (indicatorCount) indicatorCount.textContent = stats.total_indicators || 0;
    }

    async loadIndicators(chapterId) {
        try {
            const response = await fetch(`/api/indicators?chapter=${chapterId}`);
            const data = await response.json();
            
            if (data.success) {
                this.indicators = data.data;
                this.renderIndicators();
            }
        } catch (error) {
            console.error('加载指标失败:', error);
        }
    }

    renderIndicators() {
        const indicatorList = document.getElementById('indicatorList');
        const indicatorCards = document.getElementById('indicatorCards');
        const container = indicatorList || indicatorCards;
        
        if (!container) return;

        container.innerHTML = this.indicators.map(indicator => `
            <div class="indicator-item">
                <h5>${indicator.id} ${indicator.name}</h5>
                <p>${indicator.description || ''}</p>
                <span class="indicator-type">${indicator.indicator_type || 'simple'}</span>
            </div>
        `).join('');
    }
}

// 全局函数
function viewChapter(chapterCode) {
    if (window.app) {
        window.app.loadIndicators(chapterCode);
    }
}

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new SimpleIndicatorApp();
});
