#!/usr/bin/env python3
"""
子指标模块分离验证脚本
验证子指标模块已从"指标参考"模块中分离出来，单独显示
"""

import requests
import json
from bs4 import BeautifulSoup

def test_api_response():
    """测试API响应中的子指标数据"""
    print("🧪 测试API响应...")
    
    # 测试有子指标的指标
    response = requests.get('http://localhost:5001/api/indicators/1.3.1')
    data = response.json()
    
    if data['success']:
        indicator = data['data']['indicator']
        children = data['data']['children']
        
        print(f"✅ 指标 {indicator['id']} - {indicator['name']}")
        print(f"   子指标数量: {len(children)}")
        
        for child in children:
            print(f"   - {child['id']}: {child['name']}")
        
        return len(children) > 0
    else:
        print(f"❌ API请求失败: {data.get('error', '未知错误')}")
        return False

def test_html_structure():
    """测试HTML模板结构"""
    print("\n🧪 测试HTML模板结构...")
    
    try:
        with open('templates/base.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 检查子指标模块是否独立
        if 'id="childrenNavCard"' in html_content:
            print("✅ 子指标导航卡片存在")
            
            # 检查是否使用标准的info-card类
            if 'class="info-card" id="childrenNavCard"' in html_content:
                print("✅ 子指标模块使用标准info-card样式")
                
                # 检查是否有折叠功能
                if 'info-card-toggle' in html_content:
                    print("✅ 子指标模块支持折叠功能")
                    return True
                else:
                    print("❌ 子指标模块缺少折叠功能")
                    return False
            else:
                print("❌ 子指标模块未使用标准info-card样式")
                return False
        else:
            print("❌ 子指标导航卡片不存在")
            return False
            
    except FileNotFoundError:
        print("❌ 模板文件不存在")
        return False
    except Exception as e:
        print(f"❌ 读取模板文件失败: {e}")
        return False

def test_separation_logic():
    """测试分离逻辑"""
    print("\n🧪 测试分离逻辑...")
    
    try:
        with open('static/js/app.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # 检查子指标显示逻辑
        if 'childrenNavCard' in js_content:
            print("✅ JavaScript中包含子指标导航逻辑")
            
            # 检查条件显示逻辑
            if 'children.length > 0' in js_content or 'children && children.length' in js_content:
                print("✅ 包含条件显示逻辑")
                
                # 检查简单指标隐藏逻辑
                if 'simple' in js_content and 'display' in js_content:
                    print("✅ 包含简单指标隐藏逻辑")
                    return True
                else:
                    print("⚠️  简单指标隐藏逻辑可能不完整")
                    return True
            else:
                print("❌ 缺少条件显示逻辑")
                return False
        else:
            print("❌ JavaScript中缺少子指标导航逻辑")
            return False
            
    except FileNotFoundError:
        print("❌ JavaScript文件不存在")
        return False
    except Exception as e:
        print(f"❌ 读取JavaScript文件失败: {e}")
        return False

def test_css_styles():
    """测试CSS样式"""
    print("\n🧪 测试CSS样式...")
    
    try:
        with open('static/css/style.css', 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        # 检查子指标相关样式
        required_classes = [
            'children-nav-grid',
            'child-nav-item',
            'child-nav-info',
            'child-nav-id',
            'child-nav-name',
            'child-nav-arrow'
        ]
        
        missing_classes = []
        for class_name in required_classes:
            if class_name not in css_content:
                missing_classes.append(class_name)
        
        if not missing_classes:
            print("✅ 所有必需的CSS类都存在")
            return True
        else:
            print(f"❌ 缺少CSS类: {', '.join(missing_classes)}")
            return False
            
    except FileNotFoundError:
        print("❌ CSS文件不存在")
        return False
    except Exception as e:
        print(f"❌ 读取CSS文件失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 子指标模块分离验证")
    print("=" * 50)
    
    tests = [
        ("API响应测试", test_api_response),
        ("HTML结构测试", test_html_structure),
        ("分离逻辑测试", test_separation_logic),
        ("CSS样式测试", test_css_styles)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！子指标模块已成功分离")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    main()
