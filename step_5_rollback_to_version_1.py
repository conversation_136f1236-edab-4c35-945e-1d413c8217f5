#!/usr/bin/env python3
"""
步骤5: 回退到版本1 (简化版)
"""

import os
import shutil
import subprocess
import sys
import time

def execute_step_5():
    """执行步骤5: 回退到版本1"""
    print("🔄 步骤5: 回退到版本1 (简化版)...")
    
    # 创建更简化的主应用文件
    simplified_app = '''#!/usr/bin/env python3
"""
医院等级评审指标说明手册系统 - 版本1 (简化版)
向checkpoint 134回退的第二步
"""

from flask import Flask, render_template, jsonify, request
import sqlite3
import os
import sys

app = Flask(__name__)
app.config['SECRET_KEY'] = 'hospital_indicator_system_2024'

DATABASE_PATH = 'DATABASE-HOSPITAL/hospital_indicator_system.db'

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def dict_from_row(row):
    """将sqlite3.Row转换为字典"""
    if row is None:
        return None
    return dict(row)

# ========================================
# 前端路由
# ========================================

@app.route('/')
def index():
    """首页"""
    return render_template('index.html')

# ========================================
# 核心API路由
# ========================================

@app.route('/api/chapters', methods=['GET'])
def get_chapters():
    """获取所有章节"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT c.*, 
                   COUNT(DISTINCT s.id) as section_count,
                   COUNT(DISTINCT i.id) as indicator_count
            FROM chapters c
            LEFT JOIN sections s ON c.id = s.chapter_id AND s.is_active = 1
            LEFT JOIN indicators i ON c.id = i.chapter_id AND i.is_active = 1
            WHERE c.is_active = 1
            GROUP BY c.id
            ORDER BY c.id
        """)
        
        chapters = [dict_from_row(row) for row in cursor.fetchall()]
        conn.close()
        
        return jsonify({'success': True, 'data': chapters})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/indicators', methods=['GET'])
def get_indicators():
    """获取指标列表"""
    try:
        chapter = request.args.get('chapter')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        where_conditions = ['i.is_active = 1']
        params = []
        
        if chapter:
            where_conditions.append('i.chapter_id = ?')
            params.append(chapter)
        
        where_clause = ' AND '.join(where_conditions)
        
        cursor.execute(f"""
            SELECT COUNT(*) FROM indicators i WHERE {where_clause}
        """, params)
        total = cursor.fetchone()[0]
        
        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT i.*, c.name as chapter_name, s.name as section_name
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN sections s ON i.section_id = s.id
            WHERE {where_clause}
            ORDER BY i.id
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])
        
        indicators = [dict_from_row(row) for row in cursor.fetchall()]
        conn.close()
        
        return jsonify({
            'success': True,
            'data': indicators,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/indicators/<indicator_id>', methods=['GET'])
def get_indicator(indicator_id):
    """获取单个指标详情"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT i.*, c.name as chapter_name, s.name as section_name
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN sections s ON i.section_id = s.id
            WHERE i.id = ? AND i.is_active = 1
        """, (indicator_id,))
        
        indicator = dict_from_row(cursor.fetchone())
        
        if not indicator:
            return jsonify({'success': False, 'error': '指标不存在'})
        
        conn.close()
        
        return jsonify({'success': True, 'data': {'indicator': indicator}})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/statistics', methods=['GET'])
def get_statistics():
    """获取统计信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM chapters WHERE is_active = 1")
        total_chapters = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM sections WHERE is_active = 1")
        total_sections = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM indicators WHERE is_active = 1")
        total_indicators = cursor.fetchone()[0]
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': {
                'total_chapters': total_chapters,
                'total_sections': total_sections,
                'total_indicators': total_indicators
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ========================================
# 简单后端管理
# ========================================

@app.route('/admin')
def admin_login():
    """后端登录页"""
    return render_template('admin/login.html')

@app.route('/admin/dashboard')
def admin_dashboard():
    """后端仪表板"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM chapters WHERE is_active = 1")
        chapters_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM sections WHERE is_active = 1")
        sections_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM indicators WHERE is_active = 1")
        indicators_count = cursor.fetchone()[0]
        
        conn.close()
        
        stats = {
            'chapters': chapters_count,
            'sections': sections_count,
            'indicators': indicators_count,
            'components': 0,
            'incomplete_indicators': 0,
            'incomplete_components': 0
        }
        
        return render_template('admin/dashboard.html', stats=stats)
        
    except Exception as e:
        return f'<h1>错误</h1><p>加载仪表板失败: {str(e)}</p>'

# ========================================
# 应用启动
# ========================================

if __name__ == '__main__':
    try:
        if not os.path.exists(DATABASE_PATH):
            print(f"❌ 数据库文件不存在: {DATABASE_PATH}")
            print("请确保数据库文件存在后再启动应用")
            sys.exit(1)
        
        print("🏥 医院等级评审指标说明系统启动中... (版本1 - 简化版)")
        print("🔄 正在向checkpoint 134回退... (步骤2/3)")
        
        try:
            conn = get_db_connection()
            conn.close()
            print("📊 数据库连接正常")
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            sys.exit(1)
        
        print("🌐 访问地址: http://localhost:5001")
        print("=" * 50)
        
        app.run(debug=True, host='0.0.0.0', port=5001)
        
    except Exception as e:
        print(f"应用启动失败: {e}")
        sys.exit(1)
'''
    
    with open('hospital_indicator_app.py', 'w', encoding='utf-8') as f:
        f.write(simplified_app)
    
    print("   ✅ 步骤5完成: 已回退到版本1")
    return True

def create_simplified_js():
    """创建简化的JavaScript"""
    print("🔧 创建简化的JavaScript...")
    
    simplified_js = '''// 医院指标系统 - 版本1 (简化版)

class SimpleIndicatorApp {
    constructor() {
        this.chapters = [];
        this.indicators = [];
        this.init();
    }

    async init() {
        try {
            await this.loadChapters();
            await this.loadStatistics();
            console.log('简化版应用初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
        }
    }

    async loadChapters() {
        try {
            const response = await fetch('/api/chapters');
            const data = await response.json();
            
            if (data.success) {
                this.chapters = data.data;
                this.renderChapters();
            }
        } catch (error) {
            console.error('加载章节失败:', error);
        }
    }

    async loadStatistics() {
        try {
            const response = await fetch('/api/statistics');
            const data = await response.json();
            
            if (data.success) {
                this.renderStatistics(data.data);
            }
        } catch (error) {
            console.error('加载统计失败:', error);
        }
    }

    renderChapters() {
        // 尝试多个可能的容器
        const chapterGrid = document.getElementById('chapterGrid');
        const chapterList = document.getElementById('chapterList');
        const container = chapterGrid || chapterList;
        
        if (!container) return;

        container.innerHTML = this.chapters.map(chapter => `
            <div class="chapter-card" onclick="viewChapter('${chapter.id}')">
                <h3>${chapter.name}</h3>
                <p>${chapter.description || ''}</p>
                <div class="chapter-stats">
                    <span>${chapter.section_count || 0} 个小节</span>
                    <span>${chapter.indicator_count || 0} 个指标</span>
                </div>
            </div>
        `).join('');
    }

    renderStatistics(stats) {
        // 更新统计数字
        const chapterCount = document.getElementById('chapterCount');
        const sectionCount = document.getElementById('sectionCount');
        const indicatorCount = document.getElementById('indicatorCount');
        
        if (chapterCount) chapterCount.textContent = stats.total_chapters || 0;
        if (sectionCount) sectionCount.textContent = stats.total_sections || 0;
        if (indicatorCount) indicatorCount.textContent = stats.total_indicators || 0;
    }

    async loadIndicators(chapterId) {
        try {
            const response = await fetch(`/api/indicators?chapter=${chapterId}`);
            const data = await response.json();
            
            if (data.success) {
                this.indicators = data.data;
                this.renderIndicators();
            }
        } catch (error) {
            console.error('加载指标失败:', error);
        }
    }

    renderIndicators() {
        const indicatorList = document.getElementById('indicatorList');
        const indicatorCards = document.getElementById('indicatorCards');
        const container = indicatorList || indicatorCards;
        
        if (!container) return;

        container.innerHTML = this.indicators.map(indicator => `
            <div class="indicator-item">
                <h5>${indicator.id} ${indicator.name}</h5>
                <p>${indicator.description || ''}</p>
                <span class="indicator-type">${indicator.indicator_type || 'simple'}</span>
            </div>
        `).join('');
    }
}

// 全局函数
function viewChapter(chapterCode) {
    if (window.app) {
        window.app.loadIndicators(chapterCode);
    }
}

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new SimpleIndicatorApp();
});
'''
    
    with open('static/js/app.js', 'w', encoding='utf-8') as f:
        f.write(simplified_js)
    
    print("   ✅ 简化的JavaScript创建完成")

def test_step_5():
    """测试步骤5结果"""
    print("\n🧪 测试步骤5结果...")
    
    try:
        import requests
        time.sleep(2)  # 等待服务器重启
        
        response = requests.get('http://localhost:5001/api/chapters')
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"   ✅ API正常 (共{len(data['data'])}章)")
                return True
        
        print("   ⚠️  API测试未通过")
        return False
        
    except Exception as e:
        print(f"   ⚠️  测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 步骤5: 回退到版本1 (简化版)")
    print("=" * 70)
    
    # 执行步骤5
    if execute_step_5():
        print("\n✅ 步骤5完成！")
        
        # 创建简化的JavaScript
        create_simplified_js()
        
        print("\n🔗 下一步:")
        print("1. 重启应用服务器")
        print("2. 测试版本1功能")
        print("3. 确认正常后继续最后一步")
        print("4. 运行最终回退到checkpoint 134的脚本")
        
        # 测试当前步骤
        test_result = test_step_5()
        if test_result:
            print("\n🎉 步骤5测试通过！可以继续最后一步")
        else:
            print("\n⚠️  步骤5测试有问题，建议检查后再继续")
    else:
        print("❌ 步骤5执行失败")

if __name__ == "__main__":
    main()
