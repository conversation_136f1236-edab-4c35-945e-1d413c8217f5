#!/usr/bin/env python3
"""
创建Django版本的医院指标管理系统
"""

import os
import shutil
import subprocess
import sys

def backup_current_system():
    """备份当前Flask系统"""
    print("📦 备份当前Flask系统...")
    
    backup_dir = "flask_system_backup"
    if os.path.exists(backup_dir):
        shutil.rmtree(backup_dir)
    
    os.makedirs(backup_dir)
    
    # 备份关键文件和目录
    items_to_backup = [
        'hospital_indicator_app.py',
        'static/',
        'templates/',
        '医院等级评审指标说明手册 - Google风格.html'
    ]
    
    for item in items_to_backup:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.copytree(item, os.path.join(backup_dir, item))
            else:
                shutil.copy2(item, backup_dir)
    
    print(f"   ✅ Flask系统已备份到 {backup_dir}/")

def create_django_structure():
    """创建Django项目结构"""
    print("🏗️  创建Django项目结构...")
    
    project_name = "hospital_indicators_django"
    
    # 检查是否已存在
    if os.path.exists(project_name):
        print(f"   ⚠️  目录 {project_name} 已存在，将先删除")
        shutil.rmtree(project_name)
    
    # 创建项目目录
    os.makedirs(project_name)
    os.chdir(project_name)
    
    # 创建基本目录结构
    directories = [
        'hospital_indicators',
        'indicators',
        'indicators/templates',
        'indicators/templates/indicators',
        'api',
        'static',
        'static/css',
        'static/js',
        'static/images',
        'templates',
        'DATABASE-HOSPITAL'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print(f"   ✅ Django项目结构已创建: {project_name}/")

def create_requirements():
    """创建requirements.txt"""
    print("📋 创建requirements.txt...")
    
    requirements = """Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-filter==23.3
Pillow==10.0.1
python-decouple==3.8
whitenoise==6.6.0
"""
    
    with open('requirements.txt', 'w') as f:
        f.write(requirements)
    
    print("   ✅ requirements.txt已创建")

def create_django_settings():
    """创建Django设置文件"""
    print("⚙️  创建Django设置文件...")
    
    settings_content = '''"""
Django settings for hospital_indicators project.
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'hospital-indicators-django-secret-key-2024'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'corsheaders',
    'django_filters',
    'indicators',
    'api',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'hospital_indicators.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'hospital_indicators.wsgi.application'

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'DATABASE-HOSPITAL' / 'hospital_indicator_system.db',
    }
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'zh-hans'
TIME_ZONE = 'Asia/Shanghai'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]
STATIC_ROOT = BASE_DIR / 'staticfiles'

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
}

# CORS settings
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
]

CORS_ALLOW_ALL_ORIGINS = True  # 仅用于开发环境

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'
'''
    
    with open('hospital_indicators/settings.py', 'w', encoding='utf-8') as f:
        f.write(settings_content)
    
    print("   ✅ Django设置文件已创建")

def create_django_models():
    """创建Django模型"""
    print("📊 创建Django模型...")
    
    models_content = '''from django.db import models
from django.utils import timezone

class Chapter(models.Model):
    """章节模型"""
    id = models.CharField(max_length=10, primary_key=True)
    name = models.CharField(max_length=200, verbose_name="章节名称")
    description = models.TextField(blank=True, null=True, verbose_name="描述")
    order_num = models.IntegerField(default=0, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        db_table = 'chapters'
        verbose_name = "章节"
        verbose_name_plural = "章节"
        ordering = ['order_num', 'id']
    
    def __str__(self):
        return f"{self.id} - {self.name}"

class Section(models.Model):
    """小节模型"""
    id = models.CharField(max_length=20, primary_key=True)
    chapter = models.ForeignKey(Chapter, on_delete=models.CASCADE, verbose_name="所属章节")
    name = models.CharField(max_length=200, verbose_name="小节名称")
    description = models.TextField(blank=True, null=True, verbose_name="描述")
    order_num = models.IntegerField(default=0, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        db_table = 'sections'
        verbose_name = "小节"
        verbose_name_plural = "小节"
        ordering = ['order_num', 'id']
    
    def __str__(self):
        return f"{self.id} - {self.name}"

class Indicator(models.Model):
    """指标模型"""
    INDICATOR_TYPE_CHOICES = [
        ('simple', '简单指标'),
        ('composite', '复合指标'),
    ]
    
    id = models.CharField(max_length=50, primary_key=True)
    chapter = models.ForeignKey(Chapter, on_delete=models.CASCADE, verbose_name="所属章节")
    section = models.ForeignKey(Section, on_delete=models.CASCADE, null=True, blank=True, verbose_name="所属小节")
    name = models.CharField(max_length=500, verbose_name="指标名称")
    description = models.TextField(blank=True, null=True, verbose_name="指标描述")
    indicator_type = models.CharField(max_length=20, choices=INDICATOR_TYPE_CHOICES, default='simple', verbose_name="指标类型")
    unit = models.CharField(max_length=50, blank=True, null=True, verbose_name="单位")
    data_source = models.CharField(max_length=200, blank=True, null=True, verbose_name="数据来源")
    lead_department = models.CharField(max_length=200, blank=True, null=True, verbose_name="牵头科室")
    logic_definition = models.TextField(blank=True, null=True, verbose_name="逻辑定义")
    numerator = models.TextField(blank=True, null=True, verbose_name="分子")
    denominator = models.TextField(blank=True, null=True, verbose_name="分母")
    parent_id = models.CharField(max_length=50, blank=True, null=True, verbose_name="父指标ID")
    order_num = models.IntegerField(default=0, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        db_table = 'indicators'
        verbose_name = "指标"
        verbose_name_plural = "指标"
        ordering = ['order_num', 'id']
    
    def __str__(self):
        return f"{self.id} - {self.name}"
    
    @property
    def chapter_name(self):
        return self.chapter.name if self.chapter else None
    
    @property
    def section_name(self):
        return self.section.name if self.section else None
'''
    
    with open('indicators/models.py', 'w', encoding='utf-8') as f:
        f.write(models_content)
    
    print("   ✅ Django模型已创建")

def copy_database():
    """复制数据库文件"""
    print("💾 复制数据库文件...")
    
    source_db = "../DATABASE-HOSPITAL/hospital_indicator_system.db"
    target_db = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    if os.path.exists(source_db):
        shutil.copy2(source_db, target_db)
        print("   ✅ 数据库文件已复制")
    else:
        print("   ⚠️  源数据库文件不存在，需要手动复制")

def create_management_command():
    """创建管理命令"""
    print("🔧 创建管理命令...")
    
    # 创建management目录
    os.makedirs('indicators/management', exist_ok=True)
    os.makedirs('indicators/management/commands', exist_ok=True)
    
    # 创建__init__.py文件
    with open('indicators/management/__init__.py', 'w') as f:
        f.write('')
    
    with open('indicators/management/commands/__init__.py', 'w') as f:
        f.write('')
    
    # 创建同步数据命令
    sync_command = '''from django.core.management.base import BaseCommand
from django.db import connection
from indicators.models import Chapter, Section, Indicator

class Command(BaseCommand):
    help = '同步现有数据库数据到Django模型'
    
    def handle(self, *args, **options):
        self.stdout.write('开始同步数据...')
        
        # 由于使用现有数据库，Django模型会自动映射
        # 这里可以添加数据验证和清理逻辑
        
        chapters_count = Chapter.objects.count()
        sections_count = Section.objects.count()
        indicators_count = Indicator.objects.count()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'数据同步完成！\\n'
                f'章节数: {chapters_count}\\n'
                f'小节数: {sections_count}\\n'
                f'指标数: {indicators_count}'
            )
        )
'''
    
    with open('indicators/management/commands/sync_data.py', 'w', encoding='utf-8') as f:
        f.write(sync_command)
    
    print("   ✅ 管理命令已创建")

def generate_project_summary():
    """生成项目总结"""
    print("\n📋 Django项目创建总结")
    print("=" * 60)
    
    print("🎯 项目优势:")
    print("✅ 规范的Django项目结构")
    print("✅ 强大的ORM数据模型")
    print("✅ 内置管理后台")
    print("✅ REST API支持")
    print("✅ 保留完整数据库")
    print("✅ 现代化前端支持")
    
    print("\n📁 项目结构:")
    print("hospital_indicators_django/")
    print("├── manage.py                    # Django管理脚本")
    print("├── requirements.txt             # 依赖包列表")
    print("├── hospital_indicators/         # 项目配置")
    print("├── indicators/                  # 指标应用")
    print("├── api/                         # API应用")
    print("├── static/                      # 静态文件")
    print("├── templates/                   # 模板文件")
    print("└── DATABASE-HOSPITAL/           # 数据库文件")
    
    print("\n🚀 下一步操作:")
    print("1. cd hospital_indicators_django")
    print("2. pip install -r requirements.txt")
    print("3. python manage.py migrate")
    print("4. python manage.py createsuperuser")
    print("5. python manage.py sync_data")
    print("6. python manage.py runserver")
    
    print("\n💡 建议:")
    print("- 使用虚拟环境管理依赖")
    print("- 基于参考HTML文件设计前端")
    print("- 利用Django Admin管理数据")
    print("- 使用Django REST Framework构建API")

def main():
    """主函数"""
    print("🎯 创建Django版本的医院指标管理系统")
    print("=" * 70)
    
    try:
        # 1. 备份当前系统
        backup_current_system()
        
        # 2. 创建Django项目结构
        create_django_structure()
        
        # 3. 创建requirements.txt
        create_requirements()
        
        # 4. 创建Django设置
        create_django_settings()
        
        # 5. 创建Django模型
        create_django_models()
        
        # 6. 复制数据库
        copy_database()
        
        # 7. 创建管理命令
        create_management_command()
        
        # 8. 生成项目总结
        generate_project_summary()
        
        print("\n🎉 Django项目创建完成！")
        
    except Exception as e:
        print(f"\n❌ 创建过程中出现错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
