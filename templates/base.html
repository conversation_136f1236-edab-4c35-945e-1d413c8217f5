<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}医院等级评审指标说明手册{% endblock %}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block extra_css %}{% endblock %}

    <script src="/static/js/modal-error-handler.js"></script>
</head>
<body>
    <div class="container">
        <!-- 顶部导航 -->
        <header class="header">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <a href="/" class="logo">
                    <i class="fas fa-hospital logo-icon"></i>
                    医院等级评审指标说明手册
                </a>
            </div>

            <div class="search-wrapper">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" id="globalSearch" placeholder="搜索指标编号、名称或描述...">
                <div class="search-results" id="searchResults"></div>
            </div>

            <div class="header-right">
                <a href="/admin/dashboard" class="admin-switch-btn" title="跳转到后台管理系统">
                    <i class="fas fa-tools"></i>
                    后台管理
                </a>
                <button class="header-btn" title="设置">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="header-btn" title="帮助">
                    <i class="fas fa-question-circle"></i>
                </button>
                <div class="user-avatar" title="用户">
                    <i class="fas fa-user"></i>
                </div>
            </div>
        </header>

        <div class="main-container">
            <!-- 侧边导航 -->
            <nav class="sidebar" id="sidebar">
                <div class="nav-section">章节导航</div>
                <div id="chapterNav">
                    <!-- 动态加载章节导航 -->
                </div>

                <div class="nav-section">快速访问</div>
                <a href="#" class="nav-item" data-action="statistics">
                    <i class="fas fa-chart-bar nav-icon"></i>
                    <span class="nav-text">统计概览</span>
                </a>
                <a href="#" class="nav-item" data-action="export">
                    <i class="fas fa-download nav-icon"></i>
                    <span class="nav-text">导出数据</span>
                </a>
                <a href="#" class="nav-item" data-action="help">
                    <i class="fas fa-book nav-icon"></i>
                    <span class="nav-text">使用帮助</span>
                </a>
            </nav>

            <!-- 主内容区域 -->
            <main class="content">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- 指标详情弹窗 -->
    <div class="modal-overlay" id="indicatorModal">
        <div class="modal-container">
            <div class="modal-header">
                <div class="modal-header-content">
                    <div class="modal-indicator-badge" id="modalIndicatorBadge">1.1.1</div>
                    <div class="modal-header-text">
                        <h2 class="modal-title" id="modalIndicatorTitle">指标详情</h2>
                        <div class="modal-breadcrumb" id="modalBreadcrumb">
                            <span>第一章</span>
                            <i class="fas fa-chevron-right"></i>
                            <span>床位配置</span>
                        </div>
                    </div>
                </div>
                <div class="modal-header-actions">
                    <button class="modal-action-btn" title="收藏">
                        <i class="fas fa-star"></i>
                    </button>
                    <button class="modal-action-btn" title="导出">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="modal-close" id="closeModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="modal-body" id="modalBody">
                <div id="modalAlertContainer"></div>

                <!-- 指标基本信息卡片 -->
                <div class="info-card">
                    <div class="info-card-header">
                        <h3 class="info-card-title">
                            <i class="fas fa-info-circle"></i>
                            基本信息
                        </h3>
                    </div>
                    <div class="info-card-content">
                        <div class="info-grid">
                            <div class="info-item">
                                <label class="info-label">指标ID</label>
                                <div class="info-value indicator-id" id="modalIndicatorId">加载中...</div>
                            </div>
                            <div class="info-item">
                                <label class="info-label">指标名称</label>
                                <div class="info-value" id="modalIndicatorName">加载中...</div>
                            </div>
                            <div class="info-item">
                                <label class="info-label">指标类型</label>
                                <div class="info-value" id="modalIndicatorType">-</div>
                            </div>
                            <div class="info-item">
                                <label class="info-label">所属章节</label>
                                <div class="info-value" id="modalIndicatorChapter">-</div>
                            </div>
                            <div class="info-item">
                                <label class="info-label">所属小节</label>
                                <div class="info-value" id="modalIndicatorSection">-</div>
                            </div>
                            <div class="info-item">
                                <label class="info-label">父指标</label>
                                <div class="info-value" id="modalIndicatorParent">-</div>
                            </div>
                            <div class="info-item">
                                <label class="info-label">子指标数量</label>
                                <div class="info-value" id="modalChildrenCount">-</div>
                            </div>
                        </div>

                        <!-- 指标描述 -->
                        <div id="modalDescriptionSection" style="display: none; margin-top: 20px;">
                            <div class="info-label">指标描述</div>
                            <div class="info-value" id="modalIndicatorDescription"></div>
                        </div>

                        <!-- 计算方法 -->
                        <div id="modalCalculationSection" style="display: none; margin-top: 20px;">
                            <div class="info-label">计算方法</div>
                            <div class="info-value" id="modalCalculationMethod"></div>
                        </div>
                    </div>
                </div>

                <!-- 详细信息卡片 -->
                <div class="info-card" id="modalDetailedCard" style="display: none;">
                    <div class="info-card-header">
                        <h3 class="info-card-title">
                            <i class="fas fa-clipboard-list"></i>
                            基本属性
                        </h3>
                    </div>
                    <div class="info-card-content">
                        <!-- 基本属性区域（底层指标专用） -->
                        <div id="modalBasicAttributesSection" style="display: none; margin-bottom: 24px;">
                            <div class="basic-attributes-header">
                                <h4 style="margin: 0 0 16px 0; color: var(--primary-color, #1976d2); font-size: 16px; font-weight: 500; display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-cog" style="font-size: 14px;"></i>
                                    基本属性
                                </h4>
                            </div>
                            <div class="basic-attributes-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; background: #f8f9fa; padding: 16px; border-radius: 8px; border-left: 4px solid var(--primary-color, #1976d2);">
                                <!-- 单位 -->
                                <div class="modal-field-row" style="display: none;">
                                    <div class="info-label" style="font-size: 12px; color: #666; margin-bottom: 4px; font-weight: 500;">单位</div>
                                    <div class="info-value" id="modalUnit" style="font-size: 14px; color: #333; font-weight: 500;"></div>
                                </div>
                                <!-- 牵头科室 -->
                                <div class="modal-field-row" style="display: none;">
                                    <div class="info-label" style="font-size: 12px; color: #666; margin-bottom: 4px; font-weight: 500;">牵头科室</div>
                                    <div class="info-value" id="modalLeadDepartment" style="font-size: 14px; color: #333; font-weight: 500;"></div>
                                </div>
                                <!-- 数据来源 -->
                                <div class="modal-field-row" style="display: none;">
                                    <div class="info-label" style="font-size: 12px; color: #666; margin-bottom: 4px; font-weight: 500;">数据来源</div>
                                    <div class="info-value" id="modalDataSource" style="font-size: 14px; color: #333; font-weight: 500;"></div>
                                </div>
                                <!-- 逻辑定义 -->
                                <div class="modal-field-row" style="display: none; grid-column: 1 / -1;">
                                    <div class="info-label" style="font-size: 12px; color: #666; margin-bottom: 4px; font-weight: 500;">逻辑定义</div>
                                    <div class="info-value" id="modalLogicDefinition" style="font-size: 14px; color: #333; line-height: 1.5;"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 意义 -->
                        <div id="modalSignificanceSection" style="display: none; margin-bottom: 20px;">
                            <div class="info-label">意义</div>
                            <div class="info-value" id="modalIndicatorSignificance"></div>
                        </div>

                        <!-- 分子分母组件（整合在基本属性内） -->
                        <div id="modalComponentsSection" style="display: none; margin-top: 20px;">
                            <div class="info-label" style="margin-bottom: 16px;">
                                <i class="fas fa-calculator" style="margin-right: 8px; color: var(--primary-color, #1976d2);"></i>
                                分子分母组件
                                <span style="font-size: 12px; color: #666; font-weight: normal; margin-left: 8px;">(仅复合指标显示)</span>
                            </div>
                            <div class="modal-components-container" style="background: #f8f9fa; border-radius: 8px; overflow: hidden; border: 1px solid #e0e0e0;">
                                <div id="modalComponentsContainer">
                                    <div class="loading-message">加载中...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 指标参考卡片 -->
                <div class="info-card" id="modalReferenceCard" style="display: none;">
                    <div class="info-card-header">
                        <h3 class="info-card-title">
                            <i class="fas fa-book-open"></i>
                            指标参考
                        </h3>
                    </div>
                    <div class="info-card-content">
                        <!-- 指标定义 -->
                        <div id="modalRefDefinitionSection" style="display: none; margin-bottom: 20px;">
                            <div class="info-label">指标定义</div>
                            <div class="info-value" id="modalRefIndicatorDefinition"></div>
                        </div>

                        <!-- 统计范围 -->
                        <div id="modalRefScopeSection" style="display: none; margin-bottom: 20px;">
                            <div class="info-label">统计范围</div>
                            <div class="info-value" id="modalRefStatisticalScope"></div>
                        </div>

                        <!-- 数据来源 -->
                        <div id="modalRefDataSourcesSection" style="display: none; margin-bottom: 20px;">
                            <div class="info-label">数据来源</div>
                            <div class="info-value" id="modalRefDataSources"></div>
                        </div>

                        <!-- 统计频率 -->
                        <div id="modalRefFrequencySection" style="display: none; margin-bottom: 20px;">
                            <div class="info-label">统计频率</div>
                            <div class="info-value" id="modalRefCollectionFrequency"></div>
                        </div>

                        <!-- 标准值/参考值 -->
                        <div id="modalRefReferenceSection" style="display: none; margin-bottom: 20px;">
                            <div class="info-label">标准值/参考值</div>
                            <div class="info-value" id="modalRefReferenceValue"></div>
                        </div>

                        <!-- 监测分析 -->
                        <div id="modalRefMonitoringSection" style="display: none; margin-bottom: 20px;">
                            <div class="info-label">监测分析</div>
                            <div class="info-value" id="modalRefMonitoringAnalysis"></div>
                            <div id="modalRefAnalysisDimensions" style="margin-top: 12px;"></div>
                        </div>
                    </div>
                </div>

                <!-- 参考范围卡片（底层指标专用） -->
                <div class="info-card" id="modalReferenceRangeCard" style="display: none;">
                    <div class="info-card-header">
                        <h3 class="info-card-title">
                            <i class="fas fa-chart-line"></i>
                            目标值与评价标准
                        </h3>
                    </div>
                    <div class="info-card-content">
                        <!-- 目标值与当前值 -->
                        <div id="modalTargetValueSection" style="display: none; margin-bottom: 20px;">
                            <div class="reference-values-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                                <div class="value-item">
                                    <div class="info-label">目标值</div>
                                    <div class="info-value target-value" id="modalTargetValue" style="font-weight: 600; color: #1976d2;"></div>
                                </div>
                                <div class="value-item">
                                    <div class="info-label">当前值</div>
                                    <div class="info-value current-value" id="modalCurrentValue" style="font-weight: 600; color: #f57c00;"></div>
                                </div>
                                <div class="value-item">
                                    <div class="info-label">完成率</div>
                                    <div class="info-value completion-rate" id="modalCompletionRate" style="font-weight: 600; color: #388e3c;"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 计算方法 -->
                        <div id="modalCalculationMethodSection" style="display: none; margin-bottom: 20px;">
                            <div class="info-label">计算方法</div>
                            <div class="info-value" id="modalCalculationMethod" style="background: #f5f5f5; padding: 12px; border-radius: 6px; font-family: monospace;"></div>
                        </div>

                        <!-- 数据来源详细 -->
                        <div id="modalDataSourcesDetailSection" style="display: none; margin-bottom: 20px;">
                            <div class="info-label">数据来源</div>
                            <div class="info-value" id="modalDataSourcesDetail"></div>
                        </div>

                        <!-- 注意事项 -->
                        <div id="modalNotesSection" style="display: none; margin-bottom: 20px;">
                            <div class="info-label">注意事项</div>
                            <div class="info-value" id="modalNotes" style="background: #fff3e0; padding: 12px; border-radius: 6px; border-left: 4px solid #ff9800;"></div>
                        </div>
                    </div>
                </div>

                <!-- 子指标导航卡片 -->
                <div class="info-card" id="childrenNavCard" style="display: none;">
                    <div class="info-card-header">
                        <h3 class="info-card-title">
                            <i class="fas fa-sitemap"></i>
                            子指标
                            <span class="children-count" id="childrenCount">0</span>
                        </h3>
                        <div class="info-card-actions">
                            <button class="info-card-toggle" onclick="toggleInfoCard(this)">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                        </div>
                    </div>
                    <div class="info-card-content">
                        <div class="children-nav-grid" id="childrenNavGrid">
                            <!-- 动态加载子指标导航 -->
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- 搜索结果弹窗 -->
    <div class="search-dropdown" id="searchDropdown">
        <!-- 动态加载搜索结果 -->
    </div>

    <!-- 加载提示 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 通知提示 -->
    <div class="notification" id="notification">
        <div class="notification-content">
            <i class="notification-icon"></i>
            <span class="notification-message"></span>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}?v=20250525-basic-attributes-title"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
