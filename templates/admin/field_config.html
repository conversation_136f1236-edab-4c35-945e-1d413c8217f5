<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段配置管理 - 医院指标系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: #1976d2;
            color: white;
            padding: 16px 24px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 24px;
            font-weight: 500;
        }

        .container {
            max-width: 1200px;
            margin: 24px auto;
            padding: 0 24px;
        }

        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }

        .card-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e0e0e0;
        }

        .card-title {
            font-size: 18px;
            font-weight: 500;
            color: #1976d2;
        }

        .card-content {
            padding: 24px;
        }

        .field-grid {
            display: grid;
            gap: 16px;
        }

        .field-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            background: #fafafa;
        }

        .field-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 12px;
        }

        .field-name {
            font-weight: 500;
            color: #1976d2;
        }

        .field-label {
            font-size: 16px;
            margin-bottom: 8px;
        }

        .field-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
            font-size: 14px;
            color: #666;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .badge.basic {
            background: #e3f2fd;
            color: #1976d2;
        }

        .badge.detailed {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .badge.required {
            background: #ffebee;
            color: #d32f2f;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #1976d2;
            color: white;
        }

        .btn-primary:hover {
            background: #1565c0;
        }

        .section-divider {
            margin: 32px 0;
            border-top: 2px solid #e0e0e0;
            position: relative;
        }

        .section-title {
            position: absolute;
            top: -12px;
            left: 24px;
            background: #f5f5f5;
            padding: 0 16px;
            font-weight: 500;
            color: #1976d2;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>字段配置管理</h1>
    </div>

    <div class="container">
        <div class="card">
            <div class="card-header">
                <div class="card-title">指标字段显示配置</div>
            </div>
            <div class="card-content">
                <div id="loadingState" class="loading">
                    <div>正在加载字段配置...</div>
                </div>

                <div id="fieldConfigContainer" style="display: none;">
                    <!-- 基础信息字段 -->
                    <div class="section-title">基础信息字段</div>
                    <div id="basicFieldsGrid" class="field-grid"></div>

                    <div class="section-divider">
                        <div class="section-title">详细信息字段</div>
                    </div>
                    <div id="detailedFieldsGrid" class="field-grid"></div>
                </div>

                <div id="errorState" class="empty-state" style="display: none;">
                    <div>加载字段配置失败</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class FieldConfigManager {
            constructor() {
                this.init();
            }

            async init() {
                await this.loadFieldConfigs();
            }

            async loadFieldConfigs() {
                try {
                    const response = await fetch('/api/field-config');
                    const result = await response.json();

                    if (result.success) {
                        this.renderFieldConfigs(result.data);
                    } else {
                        this.showError('加载失败: ' + result.error);
                    }
                } catch (error) {
                    this.showError('网络错误: ' + error.message);
                }
            }

            renderFieldConfigs(configs) {
                const basicFields = configs.filter(c => c.display_section === 'basic');
                const detailedFields = configs.filter(c => c.display_section === 'detailed');

                this.renderFieldSection('basicFieldsGrid', basicFields);
                this.renderFieldSection('detailedFieldsGrid', detailedFields);

                document.getElementById('loadingState').style.display = 'none';
                document.getElementById('fieldConfigContainer').style.display = 'block';
            }

            renderFieldSection(containerId, fields) {
                const container = document.getElementById(containerId);
                
                const html = fields.map(field => `
                    <div class="field-item">
                        <div class="field-header">
                            <div class="field-name">${field.field_name}</div>
                            <button class="btn btn-primary" onclick="fieldManager.editField('${field.field_name}')">
                                编辑
                            </button>
                        </div>
                        <div class="field-label">${field.field_label}</div>
                        <div class="field-meta">
                            <div class="meta-item">
                                <span>类型:</span>
                                <span class="badge">${field.field_type}</span>
                            </div>
                            <div class="meta-item">
                                <span>显示区域:</span>
                                <span class="badge ${field.display_section}">${field.display_section === 'basic' ? '基础信息' : '详细信息'}</span>
                            </div>
                            <div class="meta-item">
                                <span>必填:</span>
                                <span class="badge ${field.is_required ? 'required' : ''}">${field.is_required ? '是' : '否'}</span>
                            </div>
                            <div class="meta-item">
                                <span>排序:</span>
                                <span>${field.sort_order}</span>
                            </div>
                        </div>
                        ${field.help_text ? `<div style="margin-top: 8px; font-size: 12px; color: #666;">${field.help_text}</div>` : ''}
                    </div>
                `).join('');

                container.innerHTML = html;
            }

            editField(fieldName) {
                // 简单的编辑功能 - 可以扩展为模态框
                const newSection = prompt('请选择显示区域 (basic/detailed):', 'basic');
                if (newSection && (newSection === 'basic' || newSection === 'detailed')) {
                    this.updateFieldConfig(fieldName, { display_section: newSection });
                }
            }

            async updateFieldConfig(fieldName, updateData) {
                try {
                    const response = await fetch(`/api/field-config/${fieldName}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(updateData)
                    });

                    const result = await response.json();
                    if (result.success) {
                        alert('更新成功');
                        this.loadFieldConfigs(); // 重新加载
                    } else {
                        alert('更新失败: ' + result.error);
                    }
                } catch (error) {
                    alert('网络错误: ' + error.message);
                }
            }

            showError(message) {
                document.getElementById('loadingState').style.display = 'none';
                document.getElementById('errorState').style.display = 'block';
                document.getElementById('errorState').innerHTML = `<div>${message}</div>`;
            }
        }

        // 初始化
        let fieldManager;
        document.addEventListener('DOMContentLoaded', function() {
            fieldManager = new FieldConfigManager();
        });
    </script>
</body>
</html>
