{% extends "admin/base.html" %}

{% block title %}指标管理 - 后台管理系统{% endblock %}

{% block breadcrumb %}
<span>后台管理</span> > <span>指标管理</span>
{% endblock %}

{% block content %}
<h1 class="admin-page-title">📊 指标管理</h1>

<!-- 筛选器 -->
<div class="admin-filters">
    <form method="GET">
        <div class="filter-row">
            <div class="filter-group">
                <label>章节</label>
                <select name="chapter_id" onchange="updateSections()">
                    <option value="">全部章节</option>
                    {% for chapter in chapters %}
                    <option value="{{ chapter.id }}" {% if selected_chapter == chapter.id|string %}selected{% endif %}>
                        {{ chapter.code }} - {{ chapter.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="filter-group">
                <label>小节</label>
                <select name="section_id" id="section_filter">
                    <option value="">全部小节</option>
                    {% for section in sections %}
                    <option value="{{ section.id }}" data-chapter="{{ section.chapter_id }}"
                            {% if selected_section == section.id|string %}selected{% endif %}>
                        {{ section.code }} - {{ section.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="filter-group">
                <label>搜索</label>
                <input type="text" name="search" value="{{ search }}" placeholder="指标ID、名称或描述">
            </div>
            <div class="filter-group">
                <button type="submit" class="admin-btn admin-btn-primary">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
            </div>
        </div>
    </form>
</div>

<!-- 操作栏 -->
<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
    <div style="display: flex; gap: 12px;">
        <button class="admin-btn admin-btn-primary" onclick="addIndicator()">
            <i class="fas fa-plus"></i>
            添加指标
        </button>
        <button class="admin-btn admin-btn-warning" onclick="batchEdit()">
            <i class="fas fa-edit"></i>
            批量编辑
        </button>
        <button class="admin-btn admin-btn-danger" onclick="batchDelete()">
            <i class="fas fa-trash-alt"></i>
            批量删除
        </button>
    </div>
    <div style="color: var(--gray-600); font-size: 14px;">
        共 {{ pagination.total }} 个指标，第 {{ pagination.page }} / {{ pagination.pages }} 页
    </div>
</div>

<!-- 数据表格 -->
<div class="admin-table">
    <table>
        <thead>
            <tr>
                <th style="width: 50px;">
                    <input type="checkbox" onchange="toggleSelectAll(this)">
                </th>
                <th style="width: 120px;">指标ID</th>
                <th>指标名称</th>
                <th style="width: 100px;">指标类型</th>
                <th style="width: 80px; white-space: nowrap;">章节</th>
                <th style="width: 80px; white-space: nowrap;">小节</th>
                <th style="width: 120px;">父指标</th>
                <th style="width: 90px; white-space: nowrap;">子指标</th>
                <th style="width: 80px; text-align: center;">查看</th>
                <th style="width: 120px; text-align: center;">操作</th>
            </tr>
        </thead>
        <tbody>
            {% if indicators %}
                {% for indicator in indicators %}
                <tr>
                    <!-- 暂时隐藏参考范围功能 <td>
                        <input type="checkbox" value="{{ indicator.id }}">
                    </td>
                    <td>
                        <span style="background: var(--primary-light); color: var(--primary); padding: 4px 8px; border-radius: 4px; font-family: monospace; font-size: 12px; font-weight: 500;">
                            {{ indicator.id }}
                        </span>
                    </td>
                    <td>
                        <div style="font-weight: 500; color: var(--gray-900);">{{ indicator.name }}</div>
                        {% if indicator.description %}
                        <div style="font-size: 12px; color: var(--gray-600); margin-top: 4px;">
                            {{ indicator.description[:80] }}{% if indicator.description|length > 80 %}...{% endif %}
                        </div>
                        {% endif %}
                    </td>
                    <td>
                        {% if indicator.indicator_type == 'simple' %}
                        <span style="background: #e8f5e8; color: #2e7d32; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 500; border: 1px solid #4caf50; white-space: nowrap; min-width: 60px; display: inline-block; text-align: center;">
                            简单指标
                        </span>
                        {% else %}
                        <span style="background: #e3f2fd; color: #1565c0; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 500; border: 1px solid #2196f3; white-space: nowrap; min-width: 60px; display: inline-block; text-align: center;">
                            复合指标
                        </span>
                        {% endif %}
                    </td>
                    <td style="white-space: nowrap;">
                        {% if indicator.chapter_code %}
                        <span style="background: var(--gray-100); color: var(--gray-700); padding: 2px 6px; border-radius: 4px; font-size: 11px;">
                            {{ indicator.chapter_code }}
                        </span>
                        {% else %}
                        <span style="color: var(--gray-400);">-</span>
                        {% endif %}
                    </td>
                    <td style="white-space: nowrap;">
                        {% if indicator.section_code %}
                        <span style="background: var(--gray-100); color: var(--gray-700); padding: 2px 6px; border-radius: 4px; font-size: 11px;">
                            {{ indicator.section_code }}
                        </span>
                        {% else %}
                        <span style="color: var(--gray-400);">-</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if indicator.parent_name %}
                        <span style="font-size: 12px; color: var(--gray-600);">
                            {{ indicator.parent_name[:15] }}{% if indicator.parent_name|length > 15 %}...{% endif %}
                        </span>
                        {% else %}
                        <span style="color: var(--gray-400); font-size: 12px;">顶级指标</span>
                        {% endif %}
                    </td>
                    <td style="white-space: nowrap;">
                        <span style="background: var(--warning-light); color: var(--warning-dark); padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 500;">
                            {{ indicator.children_count or 0 }}
                        </span>
                    </td>
                    <!-- 查看列 -->
                    <td style="text-align: center;">
                        <a href="/admin/indicators/{{ indicator.id }}" class="admin-btn admin-btn-primary" title="查看详情" style="padding: 8px 12px;">
                            <i class="fas fa-eye"></i>
                        </a>
                    </td>
                    <!-- 操作列 -->
                    <td style="text-align: center;">
                        <div class="admin-actions" style="display: flex; gap: 6px; justify-content: center;">
                            <a href="/admin/indicators/{{ indicator.id }}/edit" class="admin-btn admin-btn-warning" title="编辑指标" style="padding: 6px 8px;">
                                <i class="fas fa-edit"></i>
                            </a>
                            <!-- 暂时隐藏参考范围功能 <a href="/admin/indicators/{{ indicator.id }}/reference-range" class="admin-btn admin-btn-info" title="参考范围" style="padding: 6px 8px;">
                                <i class="fas fa-chart-line"></i> -->
                            </a>
                            <button onclick="deleteIndicator('{{ indicator.id }}')" class="admin-btn admin-btn-danger" title="删除指标" style="padding: 6px 8px;">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td> -->
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="10" style="text-align: center; padding: 60px; color: var(--gray-500);">
                    <div style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;">📊</div>
                    <div style="font-size: 16px; margin-bottom: 8px;">暂无指标数据</div>
                    <div style="font-size: 14px;">请调整筛选条件或添加新指标</div>
                </td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- 分页 -->
{% if pagination.pages > 1 %}
<div style="display: flex; justify-content: center; align-items: center; margin-top: 24px; gap: 8px;">
    {% if pagination.page > 1 %}
    <a href="?page={{ pagination.page - 1 }}&chapter_id={{ selected_chapter or '' }}&section_id={{ selected_section or '' }}&search={{ search }}"
       class="admin-btn admin-btn-outline">
        <i class="fas fa-chevron-left"></i>
        上一页
    </a>
    {% endif %}

    <span style="padding: 8px 16px; color: var(--gray-600); font-size: 14px;">
        第 {{ pagination.page }} / {{ pagination.pages }} 页
    </span>

    {% if pagination.page < pagination.pages %}
    <a href="?page={{ pagination.page + 1 }}&chapter_id={{ selected_chapter or '' }}&section_id={{ selected_section or '' }}&search={{ search }}"
       class="admin-btn admin-btn-outline">
        下一页
        <i class="fas fa-chevron-right"></i>
    </a>
    {% endif %}
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// 更新小节选项
function updateSections() {
    const chapterSelect = document.querySelector('select[name="chapter_id"]');
    const sectionSelect = document.getElementById('section_filter');
    const selectedChapter = chapterSelect.value;

    const sectionOptions = sectionSelect.querySelectorAll('option[data-chapter]');
    sectionOptions.forEach(option => {
        if (!selectedChapter || option.dataset.chapter === selectedChapter) {
            option.style.display = 'block';
        } else {
            option.style.display = 'none';
        }
    });

    if (selectedChapter && sectionSelect.value) {
        const selectedOption = sectionSelect.querySelector(`option[value="${sectionSelect.value}"]`);
        if (selectedOption && selectedOption.dataset.chapter !== selectedChapter) {
            sectionSelect.value = '';
        }
    }
}

// 全选/取消全选
function toggleSelectAll(checkbox) {
    const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
    checkboxes.forEach(cb => cb.checked = checkbox.checked);
}

// 添加指标
function addIndicator() {
    alert('添加指标功能开发中...');
}

// 编辑指标
function editIndicator(id) {
    alert('编辑指标功能开发中: ' + id);
}

// 删除指标
function deleteIndicator(id) {
    if (confirm('确定要删除这个指标吗？此操作不可恢复。\n\n注意：如果该指标有子指标，需要先删除子指标。')) {
        // 创建表单并提交删除请求
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/indicators/${id}/delete`;

        // 添加CSRF保护（如果需要）
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

// 批量编辑
function batchEdit() {
    const selected = document.querySelectorAll('tbody input[type="checkbox"]:checked');
    if (selected.length === 0) {
        alert('请先选择要编辑的指标');
        return;
    }

    // 跳转到批量编辑页面
    window.location.href = '/admin/indicators/batch-edit';
}

// 批量删除
function batchDelete() {
    const selected = document.querySelectorAll('tbody input[type="checkbox"]:checked');
    if (selected.length === 0) {
        alert('请先选择要删除的指标');
        return;
    }

    // 获取选中的指标ID和名称
    const selectedIndicators = [];
    selected.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const idElement = row.querySelector('td:nth-child(2) span');
        const nameElement = row.querySelector('td:nth-child(3) div');
        if (idElement && nameElement) {
            selectedIndicators.push({
                id: checkbox.value,
                name: nameElement.textContent.trim()
            });
        }
    });

    // 构建确认消息
    let confirmMessage = `确定要删除以下 ${selectedIndicators.length} 个指标吗？此操作不可恢复。\n\n`;
    confirmMessage += '将要删除的指标：\n';
    selectedIndicators.forEach((indicator, index) => {
        if (index < 5) { // 只显示前5个，避免消息过长
            confirmMessage += `• ${indicator.id} - ${indicator.name}\n`;
        } else if (index === 5) {
            confirmMessage += `• ... 还有 ${selectedIndicators.length - 5} 个指标\n`;
        }
    });
    confirmMessage += '\n⚠️ 注意：如果指标有子指标，需要先删除子指标。';

    if (confirm(confirmMessage)) {
        // 创建表单并提交批量删除请求
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/indicators/batch-delete';

        // 添加选中的指标ID
        selectedIndicators.forEach(indicator => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'indicator_ids';
            input.value = indicator.id;
            form.appendChild(input);
        });

        // 添加CSRF保护（如果需要）
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    updateSections();
});
</script>
{% endblock %}
