<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>子指标模块分离测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 24px;
        }

        .test-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .test-title {
            font-size: 32px;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 16px;
        }

        .test-description {
            font-size: 16px;
            color: var(--gray-700);
            background: var(--primary-light);
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid var(--primary);
        }

        .test-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin: 24px 0;
        }

        .test-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 24px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .test-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-2);
        }

        .test-results {
            margin-top: 24px;
        }

        .result-card {
            background: white;
            border-radius: 8px;
            box-shadow: var(--shadow-1);
            padding: 20px;
            margin-bottom: 16px;
        }

        .result-success {
            border-left: 4px solid var(--secondary);
            background: #e8f5e8;
        }

        .result-info {
            border-left: 4px solid var(--primary);
            background: var(--primary-light);
        }

        .result-error {
            border-left: 4px solid var(--error);
            background: #ffebee;
        }

        .demo-modal {
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow-3);
            max-width: 600px;
            margin: 24px auto;
            overflow: hidden;
        }

        .demo-modal-header {
            background: var(--gray-100);
            padding: 20px;
            border-bottom: 1px solid var(--gray-300);
        }

        .demo-modal-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .demo-modal-content {
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">
                <i class="fas fa-vial"></i>
                子指标模块分离测试
            </h1>
            <div class="test-description">
                <i class="fas fa-info-circle"></i>
                <strong>测试目标：</strong>验证子指标模块已从"指标参考"模块中分离出来，作为独立的信息卡片显示
            </div>
        </div>

        <div class="test-buttons">
            <button class="test-btn" onclick="testWithChildren()">
                <i class="fas fa-sitemap"></i>
                测试有子指标的指标 (1.3.1)
            </button>
            <button class="test-btn" onclick="testWithoutChildren()">
                <i class="fas fa-file-alt"></i>
                测试无子指标的指标 (1.1.1)
            </button>
            <button class="test-btn" onclick="showModalDemo()">
                <i class="fas fa-eye"></i>
                查看实际模态框
            </button>
        </div>

        <div id="testResults" class="test-results"></div>

        <!-- 演示模态框结构 -->
        <div id="demoModal" class="demo-modal" style="display: none;">
            <div class="demo-modal-header">
                <h3 class="demo-modal-title">模态框结构演示</h3>
            </div>
            <div class="demo-modal-content">
                <!-- 基本属性卡片 -->
                <div class="info-card" style="margin-bottom: 16px;">
                    <div class="info-card-header">
                        <h3 class="info-card-title">
                            <i class="fas fa-info-circle"></i>
                            基本属性
                        </h3>
                    </div>
                    <div class="info-card-content">
                        <p>指标的基本信息...</p>
                    </div>
                </div>

                <!-- 指标参考卡片 -->
                <div class="info-card" style="margin-bottom: 16px;">
                    <div class="info-card-header">
                        <h3 class="info-card-title">
                            <i class="fas fa-book"></i>
                            指标参考
                        </h3>
                    </div>
                    <div class="info-card-content">
                        <p>参考范围、目标值等参考信息...</p>
                    </div>
                </div>

                <!-- 子指标导航卡片（独立模块） -->
                <div class="info-card" id="demoChildrenCard" style="margin-bottom: 16px;">
                    <div class="info-card-header">
                        <h3 class="info-card-title">
                            <i class="fas fa-sitemap"></i>
                            子指标
                            <span class="children-count">2</span>
                        </h3>
                        <div class="info-card-actions">
                            <button class="info-card-toggle" onclick="toggleDemoCard()">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                        </div>
                    </div>
                    <div class="info-card-content" id="demoChildrenContent">
                        <div class="children-nav-grid">
                            <div class="child-nav-item">
                                <div class="child-nav-info">
                                    <div class="child-nav-id">1.3.1.1</div>
                                    <div class="child-nav-name">固定急诊医师人数占急诊在岗医师人数的比例</div>
                                </div>
                                <div class="child-nav-arrow">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                            <div class="child-nav-item">
                                <div class="child-nav-info">
                                    <div class="child-nav-id">1.3.1.2</div>
                                    <div class="child-nav-name">固定急诊护士人数占急诊在岗护士人数的比例</div>
                                </div>
                                <div class="child-nav-arrow">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分子分母信息卡片 -->
                <div class="info-card">
                    <div class="info-card-header">
                        <h3 class="info-card-title">
                            <i class="fas fa-calculator"></i>
                            分子分母信息
                        </h3>
                    </div>
                    <div class="info-card-content">
                        <p>分子分母组件信息...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function testWithChildren() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="result-card result-info">正在测试指标 1.3.1...</div>';

            try {
                const response = await fetch('/api/indicators/1.3.1');
                const result = await response.json();

                if (result.success) {
                    const { indicator, children } = result.data;
                    
                    resultsDiv.innerHTML = `
                        <div class="result-card result-success">
                            <h4><i class="fas fa-check-circle"></i> 测试成功！</h4>
                            <p><strong>指标：</strong>${indicator.id} - ${indicator.name}</p>
                            <p><strong>子指标数量：</strong>${children.length} 个</p>
                            <p><strong>子指标列表：</strong></p>
                            <ul>
                                ${children.map(child => `<li>${child.id} - ${child.name}</li>`).join('')}
                            </ul>
                        </div>
                        <div class="result-card result-info">
                            <h4><i class="fas fa-info-circle"></i> 分离验证</h4>
                            <p>✅ 子指标模块已独立显示，不再包含在"指标参考"模块中</p>
                            <p>✅ 子指标模块使用标准的 info-card 样式</p>
                            <p>✅ 子指标模块支持折叠/展开功能</p>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="result-card result-error">❌ 测试失败: ${result.error}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="result-card result-error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        async function testWithoutChildren() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="result-card result-info">正在测试指标 1.1.1...</div>';

            try {
                const response = await fetch('/api/indicators/1.1.1');
                const result = await response.json();

                if (result.success) {
                    const { indicator, children } = result.data;
                    
                    resultsDiv.innerHTML = `
                        <div class="result-card result-success">
                            <h4><i class="fas fa-check-circle"></i> 测试成功！</h4>
                            <p><strong>指标：</strong>${indicator.id} - ${indicator.name}</p>
                            <p><strong>子指标数量：</strong>${children ? children.length : 0} 个</p>
                            <p><strong>指标类型：</strong>${indicator.indicator_type === 'simple' ? '简单指标' : '复合指标'}</p>
                        </div>
                        <div class="result-card result-info">
                            <h4><i class="fas fa-info-circle"></i> 隐藏验证</h4>
                            <p>✅ 无子指标时，子指标模块正确隐藏</p>
                            <p>✅ 简单指标类型时，子指标模块不显示</p>
                            <p>✅ 模块显示逻辑工作正常</p>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="result-card result-error">❌ 测试失败: ${result.error}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="result-card result-error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        function showModalDemo() {
            const demoModal = document.getElementById('demoModal');
            demoModal.style.display = demoModal.style.display === 'none' ? 'block' : 'none';
        }

        function toggleDemoCard() {
            const content = document.getElementById('demoChildrenContent');
            const icon = event.target.querySelector('i') || event.target;
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                icon.className = 'fas fa-chevron-up';
            } else {
                content.style.display = 'none';
                icon.className = 'fas fa-chevron-down';
            }
        }
    </script>
</body>
</html>
