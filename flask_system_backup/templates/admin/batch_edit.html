{% extends "admin/base.html" %}

{% block title %}批量编辑指标{% endblock %}

{% block content %}
<div class="batch-edit-container">
    <!-- 页面头部 -->
    <div class="edit-header">
        <div class="breadcrumb">
            <a href="/admin/dashboard">后台管理</a> /
            <a href="/admin/indicators">指标管理</a> /
            <span>批量编辑</span>
        </div>
        <h1>
            <span class="material-symbols-outlined">edit_note</span>
            批量编辑指标
        </h1>
        <p class="header-description">选择多个指标进行批量编辑，可以同时修改指标类型、分类、牵头科室等信息</p>
    </div>

    <!-- 筛选和选择区域 -->
    <div class="filter-section">
        <h3>
            <span class="material-symbols-outlined">filter_list</span>
            筛选指标
        </h3>
        <div class="filter-grid">
            <div class="filter-field">
                <label for="chapter-filter">章节</label>
                <select id="chapter-filter">
                    <option value="">全部章节</option>
                </select>
            </div>
            <div class="filter-field">
                <label for="section-filter">小节</label>
                <select id="section-filter">
                    <option value="">全部小节</option>
                </select>
            </div>
            <div class="filter-field">
                <label for="type-filter">指标类型</label>
                <select id="type-filter">
                    <option value="">全部类型</option>
                    <option value="composite">复合指标</option>
                    <option value="simple">简单指标</option>
                </select>
            </div>
            <div class="filter-field">
                <label for="search-input">搜索</label>
                <input type="text" id="search-input" placeholder="输入指标名称或ID">
            </div>
        </div>
        <div class="filter-actions">
            <button class="md-button outlined" onclick="resetFilters()">
                <span class="material-symbols-outlined">refresh</span>
                <span>重置筛选</span>
            </button>
            <button class="md-button filled" onclick="loadIndicators()">
                <span class="material-symbols-outlined">search</span>
                <span>搜索指标</span>
            </button>
        </div>
    </div>

    <!-- 指标选择区域 -->
    <div class="selection-section">
        <div class="section-header">
            <h3>
                <span class="material-symbols-outlined">checklist</span>
                选择指标
                <span id="selection-count" class="count-badge">0</span>
            </h3>
            <div class="selection-actions">
                <button class="md-button outlined" onclick="selectAll()">
                    <span class="material-symbols-outlined">select_all</span>
                    <span>全选</span>
                </button>
                <button class="md-button outlined" onclick="clearSelection()">
                    <span class="material-symbols-outlined">clear</span>
                    <span>清空</span>
                </button>
            </div>
        </div>

        <!-- 指标列表 -->
        <div class="indicators-table-container">
            <table class="indicators-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <input type="checkbox" id="select-all-checkbox" onchange="toggleSelectAll()">
                        </th>
                        <th style="width: 120px;">指标ID</th>
                        <th>指标名称</th>
                        <th style="width: 100px;">指标类型</th>
                        <th style="width: 120px;">章节</th>
                        <th style="width: 120px;">小节</th>
                    </tr>
                </thead>
                <tbody id="indicators-tbody">
                    <tr>
                        <td colspan="6" class="empty-state">
                            <div class="empty-state-icon">
                                <span class="material-symbols-outlined">search</span>
                            </div>
                            <div>请使用上方筛选条件搜索指标</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 批量编辑表单 -->
    <div class="batch-form-section" id="batch-form-section" style="display: none;">
        <h3>
            <span class="material-symbols-outlined">edit</span>
            批量编辑
            <span id="edit-count" class="count-badge">0</span>
        </h3>

        <form id="batch-edit-form">
            <div class="form-grid">
                <div class="form-field">
                    <label for="batch-indicator-type">指标类型</label>
                    <select id="batch-indicator-type" name="indicator_type">
                        <option value="">不修改</option>
                        <option value="composite">复合指标</option>
                        <option value="simple">简单指标</option>
                    </select>
                </div>
                <div class="form-field">
                    <label for="batch-category">指标分类</label>
                    <select id="batch-category" name="category">
                        <option value="">不修改</option>
                        <option value="结构指标">结构指标</option>
                        <option value="过程指标">过程指标</option>
                        <option value="结果指标">结果指标</option>
                        <option value="安全指标">安全指标</option>
                        <option value="效率指标">效率指标</option>
                        <option value="质量指标">质量指标</option>
                    </select>
                </div>
                <div class="form-field">
                    <label for="batch-lead-department">牵头科室</label>
                    <input type="text" id="batch-lead-department" name="lead_department" placeholder="留空表示不修改">
                </div>
                <div class="form-field">
                    <label for="batch-data-source">数据来源</label>
                    <input type="text" id="batch-data-source" name="data_source" placeholder="留空表示不修改">
                </div>
            </div>

            <div class="form-actions">
                <button type="button" class="md-button outlined" onclick="cancelBatchEdit()">
                    <span class="material-symbols-outlined">cancel</span>
                    <span>取消</span>
                </button>
                <button type="submit" class="md-button filled">
                    <span class="material-symbols-outlined">save</span>
                    <span>批量保存</span>
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .batch-edit-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .edit-header {
        margin-bottom: 32px;
    }

    .edit-header h1 {
        margin: 16px 0 8px 0;
        font-size: 28px;
        font-weight: 400;
        color: var(--md-on-surface);
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .header-description {
        color: var(--md-on-surface-variant);
        margin: 0;
        font-size: 14px;
    }

    .breadcrumb {
        color: var(--md-on-surface-variant);
        font-size: 14px;
    }

    .breadcrumb a {
        color: var(--md-primary);
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .filter-section,
    .selection-section,
    .batch-form-section {
        background: var(--md-surface);
        border-radius: var(--md-corner-lg);
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: var(--md-elevation-1);
        border: 1px solid var(--md-outline-variant);
    }

    .filter-section h3,
    .selection-section h3,
    .batch-form-section h3 {
        margin: 0 0 20px 0;
        font-size: 18px;
        font-weight: 500;
        color: var(--md-on-surface);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .count-badge {
        background: var(--md-primary);
        color: var(--md-on-primary);
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        margin-left: 8px;
    }

    .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 20px;
    }

    .filter-field {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .filter-field label {
        font-weight: 500;
        color: var(--md-on-surface);
        font-size: 14px;
    }

    .filter-field input,
    .filter-field select {
        padding: 12px 16px;
        border: 1px solid var(--md-outline);
        border-radius: var(--md-corner-sm);
        font-family: var(--md-font-family);
        font-size: 14px;
        background: var(--md-surface);
        color: var(--md-on-surface);
    }

    .filter-actions {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .selection-actions {
        display: flex;
        gap: 8px;
    }

    .indicators-table-container {
        border: 1px solid var(--md-outline-variant);
        border-radius: var(--md-corner-sm);
        overflow: hidden;
    }

    .indicators-table {
        width: 100%;
        border-collapse: collapse;
    }

    .indicators-table th {
        background: var(--md-surface-variant);
        padding: 16px;
        text-align: left;
        font-weight: 500;
        color: var(--md-on-surface);
        border-bottom: 1px solid var(--md-outline-variant);
    }

    .indicators-table td {
        padding: 12px 16px;
        border-bottom: 1px solid var(--md-outline-variant);
        color: var(--md-on-surface);
    }

    .indicators-table tr:hover {
        background: var(--md-surface-variant);
    }

    .indicators-table tr.selected {
        background: var(--md-primary-container);
    }

    .empty-state {
        text-align: center;
        padding: 40px;
        color: var(--md-on-surface-variant);
    }

    .empty-state-icon {
        font-size: 48px;
        opacity: 0.5;
        margin-bottom: 16px;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 24px;
    }

    .form-field {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .form-field label {
        font-weight: 500;
        color: var(--md-on-surface);
        font-size: 14px;
    }

    .form-field input,
    .form-field select {
        padding: 12px 16px;
        border: 1px solid var(--md-outline);
        border-radius: var(--md-corner-sm);
        font-family: var(--md-font-family);
        font-size: 14px;
        background: var(--md-surface);
        color: var(--md-on-surface);
    }

    .form-actions {
        display: flex;
        gap: 16px;
        justify-content: flex-end;
        padding-top: 24px;
        border-top: 1px solid var(--md-outline-variant);
    }

    .indicator-type-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
        border: 1px solid;
        white-space: nowrap;
        min-width: 60px;
        text-align: center;
    }

    .indicator-type-badge.simple-indicator {
        background: #e8f5e8;
        color: #2e7d32;
        border-color: #4caf50;
    }

    .indicator-type-badge.composite-indicator {
        background: #e3f2fd;
        color: #1565c0;
        border-color: #2196f3;
    }

    @media (max-width: 768px) {
        .batch-edit-container {
            padding: 16px;
        }

        .filter-grid {
            grid-template-columns: 1fr;
        }

        .section-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 16px;
        }

        .form-actions {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let allIndicators = [];
let selectedIndicators = [];
let chapters = [];
let sections = [];

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadChapters();
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    // 章节变化时加载小节
    document.getElementById('chapter-filter').addEventListener('change', loadSections);

    // 表单提交
    document.getElementById('batch-edit-form').addEventListener('submit', handleBatchSubmit);

    // 搜索输入
    document.getElementById('search-input').addEventListener('input', debounce(loadIndicators, 500));
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 加载章节
async function loadChapters() {
    try {
        const response = await fetch('/api/chapters');
        const result = await response.json();

        if (result.success) {
            chapters = result.data;
            const select = document.getElementById('chapter-filter');
            select.innerHTML = '<option value="">全部章节</option>';

            chapters.forEach(chapter => {
                const option = document.createElement('option');
                option.value = chapter.id;
                option.textContent = `第${chapter.code}章 - ${chapter.name}`;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载章节失败:', error);
    }
}

// 加载小节
async function loadSections() {
    const chapterId = document.getElementById('chapter-filter').value;
    const sectionSelect = document.getElementById('section-filter');

    sectionSelect.innerHTML = '<option value="">全部小节</option>';

    if (!chapterId) return;

    try {
        const response = await fetch(`/api/sections?chapter=${chapterId}`);
        const result = await response.json();

        if (result.success) {
            sections = result.data;

            sections.forEach(section => {
                const option = document.createElement('option');
                option.value = section.id;
                option.textContent = `${section.code} - ${section.name}`;
                sectionSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载小节失败:', error);
    }
}

// 加载指标
async function loadIndicators() {
    try {
        const params = new URLSearchParams();

        const chapterId = document.getElementById('chapter-filter').value;
        const sectionId = document.getElementById('section-filter').value;
        const typeFilter = document.getElementById('type-filter').value;
        const searchText = document.getElementById('search-input').value.trim();

        if (chapterId) params.append('chapter', chapterId);
        if (sectionId) params.append('section', sectionId);
        if (typeFilter) params.append('indicator_type', typeFilter);
        if (searchText) params.append('search', searchText);

        params.append('per_page', '100'); // 批量编辑时加载更多数据

        const response = await fetch(`/api/indicators?${params.toString()}`);
        const result = await response.json();

        if (result.success) {
            allIndicators = result.data;
            renderIndicatorsTable();
        } else {
            throw new Error(result.error || '加载失败');
        }
    } catch (error) {
        console.error('加载指标失败:', error);
        alert('加载指标失败: ' + error.message);
    }
}

// 渲染指标表格
function renderIndicatorsTable() {
    const tbody = document.getElementById('indicators-tbody');

    if (!allIndicators || allIndicators.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="empty-state">
                    <div class="empty-state-icon">
                        <span class="material-symbols-outlined">search_off</span>
                    </div>
                    <div>未找到符合条件的指标</div>
                </td>
            </tr>
        `;
        return;
    }

    const rows = allIndicators.map(indicator => {
        const isSelected = selectedIndicators.includes(indicator.id);
        const typeClass = indicator.indicator_type === 'simple' ? 'simple-indicator' : 'composite-indicator';
        const typeText = indicator.indicator_type === 'simple' ? '简单指标' : '复合指标';

        return `
            <tr class="${isSelected ? 'selected' : ''}" onclick="toggleIndicatorSelection('${indicator.id}')">
                <td onclick="event.stopPropagation()">
                    <input type="checkbox" ${isSelected ? 'checked' : ''}
                           onchange="toggleIndicatorSelection('${indicator.id}')">
                </td>
                <td class="indicator-id">${indicator.id}</td>
                <td>${indicator.name}</td>
                <td>
                    <span class="indicator-type-badge ${typeClass}">${typeText}</span>
                </td>
                <td>${indicator.chapter_name || '-'}</td>
                <td>${indicator.section_name || '-'}</td>
            </tr>
        `;
    }).join('');

    tbody.innerHTML = rows;
    updateSelectionCount();
}

// 切换指标选择
function toggleIndicatorSelection(indicatorId) {
    const index = selectedIndicators.indexOf(indicatorId);

    if (index > -1) {
        selectedIndicators.splice(index, 1);
    } else {
        selectedIndicators.push(indicatorId);
    }

    renderIndicatorsTable();
    updateBatchFormVisibility();
}

// 全选/取消全选
function toggleSelectAll() {
    const checkbox = document.getElementById('select-all-checkbox');

    if (checkbox.checked) {
        selectAll();
    } else {
        clearSelection();
    }
}

// 全选
function selectAll() {
    selectedIndicators = allIndicators.map(indicator => indicator.id);
    document.getElementById('select-all-checkbox').checked = true;
    renderIndicatorsTable();
    updateBatchFormVisibility();
}

// 清空选择
function clearSelection() {
    selectedIndicators = [];
    document.getElementById('select-all-checkbox').checked = false;
    renderIndicatorsTable();
    updateBatchFormVisibility();
}

// 更新选择计数
function updateSelectionCount() {
    document.getElementById('selection-count').textContent = selectedIndicators.length;
    document.getElementById('edit-count').textContent = selectedIndicators.length;

    // 更新全选复选框状态
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    if (selectedIndicators.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (selectedIndicators.length === allIndicators.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

// 更新批量编辑表单可见性
function updateBatchFormVisibility() {
    const formSection = document.getElementById('batch-form-section');

    if (selectedIndicators.length > 0) {
        formSection.style.display = 'block';
    } else {
        formSection.style.display = 'none';
    }
}

// 重置筛选
function resetFilters() {
    document.getElementById('chapter-filter').value = '';
    document.getElementById('section-filter').value = '';
    document.getElementById('type-filter').value = '';
    document.getElementById('search-input').value = '';

    // 清空小节选项
    document.getElementById('section-filter').innerHTML = '<option value="">全部小节</option>';

    // 清空指标列表
    allIndicators = [];
    selectedIndicators = [];
    renderIndicatorsTable();
    updateBatchFormVisibility();
}

// 取消批量编辑
function cancelBatchEdit() {
    clearSelection();
    document.getElementById('batch-edit-form').reset();
}

// 处理批量提交
async function handleBatchSubmit(event) {
    event.preventDefault();

    if (selectedIndicators.length === 0) {
        alert('请先选择要编辑的指标');
        return;
    }

    const formData = new FormData(event.target);
    const updateData = {};

    // 只包含非空值
    for (const [key, value] of formData.entries()) {
        if (value && value.trim() !== '') {
            updateData[key] = value.trim();
        }
    }

    if (Object.keys(updateData).length === 0) {
        alert('请至少选择一个要修改的字段');
        return;
    }

    const confirmMessage = `确定要批量修改 ${selectedIndicators.length} 个指标吗？\n\n修改内容：\n${Object.entries(updateData).map(([key, value]) => `${getFieldLabel(key)}: ${value}`).join('\n')}`;

    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        const response = await fetch('/api/indicators/batch-update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                indicator_ids: selectedIndicators,
                update_data: updateData
            })
        });

        const result = await response.json();

        if (result.success) {
            alert(`✅ 批量更新成功！共更新了 ${result.updated_count} 个指标`);

            // 重新加载指标列表
            await loadIndicators();

            // 清空选择和表单
            clearSelection();
            document.getElementById('batch-edit-form').reset();
        } else {
            throw new Error(result.error || '批量更新失败');
        }
    } catch (error) {
        console.error('批量更新失败:', error);
        alert('❌ 批量更新失败：' + error.message);
    }
}

// 获取字段标签
function getFieldLabel(fieldName) {
    const labels = {
        'indicator_type': '指标类型',
        'category': '指标分类',
        'lead_department': '牵头科室',
        'data_source': '数据来源'
    };
    return labels[fieldName] || fieldName;
}
</script>
{% endblock %}
