// 医院等级评审指标说明手册系统 - Checkpoint 134

class HospitalIndicatorApp {
    constructor() {
        this.currentChapter = null;
        this.currentSection = null;
        this.indicators = [];
        this.chapters = [];
        this.sections = [];
        this.init();
    }

    async init() {
        try {
            await this.loadChapters();
            await this.loadStatistics();
            this.setupEventListeners();
            this.setupSearch();
            console.log('Checkpoint 134应用初始化完成');
        } catch (error) {
            console.error('应用初始化失败:', error);
        }
    }

    async loadChapters() {
        try {
            const response = await fetch('/api/chapters');
            const data = await response.json();
            
            if (data.success) {
                this.chapters = data.data;
                this.renderChapters();
                this.updateChapterFilter();
            } else {
                console.error('加载章节失败:', data.error);
            }
        } catch (error) {
            console.error('加载章节异常:', error);
        }
    }

    async loadStatistics() {
        try {
            const response = await fetch('/api/statistics');
            const data = await response.json();
            
            if (data.success) {
                this.renderStatistics(data.data);
            } else {
                console.error('加载统计失败:', data.error);
            }
        } catch (error) {
            console.error('加载统计异常:', error);
        }
    }

    renderChapters() {
        const chapterGrid = document.getElementById('chapterGrid');
        const chapterList = document.getElementById('chapterList');
        const container = chapterGrid || chapterList;
        
        if (!container) return;

        container.innerHTML = this.chapters.map(chapter => `
            <div class="chapter-card" data-chapter="${chapter.id}" onclick="viewChapter('${chapter.id}')">
                <div class="chapter-header">
                    <div class="chapter-icon" style="background-color: ${this.getChapterColor(chapter.id)}">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="chapter-info">
                        <h3 class="chapter-title">${chapter.name}</h3>
                        <p class="chapter-description">${chapter.description || ''}</p>
                    </div>
                </div>
                <div class="chapter-stats">
                    <div class="stat-item">
                        <span class="stat-number">${chapter.section_count || 0}</span>
                        <span class="stat-label">小节</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${chapter.indicator_count || 0}</span>
                        <span class="stat-label">指标</span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    renderStatistics(stats) {
        const chapterCount = document.getElementById('chapterCount');
        const sectionCount = document.getElementById('sectionCount');
        const indicatorCount = document.getElementById('indicatorCount');
        const componentCount = document.getElementById('componentCount');
        
        if (chapterCount) chapterCount.textContent = stats.total_chapters || 0;
        if (sectionCount) sectionCount.textContent = stats.total_sections || 0;
        if (indicatorCount) indicatorCount.textContent = stats.total_indicators || 0;
        if (componentCount) componentCount.textContent = '0';
    }

    async selectChapter(chapterId) {
        this.currentChapter = chapterId;
        
        try {
            await this.loadSections(chapterId);
            await this.loadIndicators(chapterId);
            this.updateChapterSelection();
        } catch (error) {
            console.error('选择章节失败:', error);
        }
    }

    async loadSections(chapterId) {
        try {
            const response = await fetch(`/api/chapters/${chapterId}/sections`);
            const data = await response.json();
            
            if (data.success) {
                this.sections = data.data;
                this.renderSections();
            } else {
                console.error('加载小节失败:', data.error);
            }
        } catch (error) {
            console.error('加载小节异常:', error);
        }
    }

    async loadIndicators(chapterId, sectionId = null) {
        try {
            let url = `/api/indicators?chapter=${chapterId}`;
            if (sectionId) {
                url += `&section=${sectionId}`;
            }
            
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.success) {
                this.indicators = data.data;
                this.renderIndicators();
            } else {
                console.error('加载指标失败:', data.error);
            }
        } catch (error) {
            console.error('加载指标异常:', error);
        }
    }

    renderSections() {
        const sectionList = document.getElementById('sectionList');
        if (!sectionList) return;

        sectionList.innerHTML = this.sections.map(section => `
            <div class="section-item" data-section="${section.id}" onclick="selectSection('${section.id}')">
                <h4>${section.name}</h4>
                <p>${section.description || ''}</p>
                <span class="indicator-count">${section.indicator_count || 0} 个指标</span>
            </div>
        `).join('');
    }

    renderIndicators() {
        const indicatorList = document.getElementById('indicatorList');
        const indicatorCards = document.getElementById('indicatorCards');
        const container = indicatorList || indicatorCards;
        
        if (!container) return;

        if (container.id === 'indicatorCards') {
            container.innerHTML = this.indicators.map(indicator => `
                <div class="indicator-card" data-indicator="${indicator.id}">
                    <div class="indicator-header">
                        <div class="indicator-id">${indicator.id}</div>
                        <div class="indicator-type-badge ${indicator.indicator_type || 'simple'}">${indicator.indicator_type || 'simple'}</div>
                    </div>
                    <h3 class="indicator-title">${indicator.name}</h3>
                    <p class="indicator-description">${indicator.description || ''}</p>
                    <div class="indicator-actions">
                        <button onclick="viewIndicatorDetail('${indicator.id}')" class="btn btn-primary">
                            <i class="fas fa-eye"></i> 查看详情
                        </button>
                    </div>
                </div>
            `).join('');
        } else {
            container.innerHTML = this.indicators.map(indicator => `
                <div class="list-item" data-indicator="${indicator.id}">
                    <div class="list-col-id">${indicator.id}</div>
                    <div class="list-col-title">${indicator.name}</div>
                    <div class="list-col-type">
                        <span class="type-badge ${indicator.indicator_type || 'simple'}">${indicator.indicator_type || 'simple'}</span>
                    </div>
                    <div class="list-col-desc">${indicator.description || ''}</div>
                    <div class="list-col-tags">
                        <span class="tag">${indicator.chapter_name || ''}</span>
                    </div>
                    <div class="list-col-actions">
                        <button onclick="viewIndicatorDetail('${indicator.id}')" class="btn btn-sm btn-outline">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }
    }

    async showIndicatorDetail(indicatorId) {
        try {
            const response = await fetch(`/api/indicators/${indicatorId}`);
            const data = await response.json();
            
            if (data.success) {
                this.displayIndicatorModal(data.data.indicator);
            } else {
                console.error('加载指标详情失败:', data.error);
            }
        } catch (error) {
            console.error('加载指标详情异常:', error);
        }
    }

    displayIndicatorModal(indicator) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>${indicator.id} ${indicator.name}</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="indicator-detail">
                        <div class="detail-section">
                            <h3>基本信息</h3>
                            <p><strong>指标类型:</strong> ${indicator.indicator_type || 'simple'}</p>
                            <p><strong>描述:</strong> ${indicator.description || '暂无描述'}</p>
                            <p><strong>单位:</strong> ${indicator.unit || '暂无单位'}</p>
                            <p><strong>所属章节:</strong> ${indicator.chapter_name || '未知'}</p>
                            <p><strong>所属小节:</strong> ${indicator.section_name || '未知'}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        const closeBtn = modal.querySelector('.close');
        closeBtn.onclick = () => {
            document.body.removeChild(modal);
        };

        modal.onclick = (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        };
    }

    updateChapterSelection() {
        document.querySelectorAll('.chapter-card').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeChapter = document.querySelector(`[data-chapter="${this.currentChapter}"]`);
        if (activeChapter) {
            activeChapter.classList.add('active');
        }
    }

    updateChapterFilter() {
        const chapterFilter = document.getElementById('chapterFilter');
        if (chapterFilter && this.chapters.length > 0) {
            chapterFilter.innerHTML = '<option value="">所有章节</option>' + 
                this.chapters.map(chapter => 
                    `<option value="${chapter.id}">${chapter.name}</option>`
                ).join('');
        }
    }

    setupEventListeners() {
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        
        if (menuToggle && sidebar) {
            menuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
            });
        }

        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadChapters();
                this.loadStatistics();
            });
        }

        const chapterFilter = document.getElementById('chapterFilter');
        if (chapterFilter) {
            chapterFilter.addEventListener('change', (e) => {
                if (e.target.value) {
                    this.selectChapter(e.target.value);
                }
            });
        }
    }

    setupSearch() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchIndicators(e.target.value);
            });
        }
    }

    async searchIndicators(query) {
        if (!query.trim()) {
            if (this.currentChapter) {
                this.loadIndicators(this.currentChapter, this.currentSection);
            }
            return;
        }

        try {
            let url = `/api/indicators?search=${encodeURIComponent(query)}`;
            if (this.currentChapter) {
                url += `&chapter=${this.currentChapter}`;
            }

            const response = await fetch(url);
            const data = await response.json();
            
            if (data.success) {
                this.indicators = data.data;
                this.renderIndicators();
            }
        } catch (error) {
            console.error('搜索失败:', error);
        }
    }

    getChapterColor(chapterId) {
        const colors = ['#1a73e8', '#34a853', '#ea4335', '#fbbc04', '#9aa0a6'];
        return colors[(chapterId - 1) % colors.length];
    }
}

// 全局函数，供模板调用
function viewChapter(chapterCode) {
    if (window.app) {
        window.app.selectChapter(chapterCode);
    }
}

function viewIndicatorDetail(indicatorId) {
    if (window.app) {
        window.app.showIndicatorDetail(indicatorId);
    }
}

function selectSection(sectionId) {
    if (window.app) {
        window.app.currentSection = sectionId;
        window.app.loadIndicators(window.app.currentChapter, sectionId);
    }
}

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new HospitalIndicatorApp();
});
