#!/usr/bin/env python3
"""
暂时隐藏参考范围功能，确保前后端核心功能正常工作
"""

import os
import re

def hide_frontend_reference_features():
    """隐藏前端参考范围相关功能"""
    print("🔧 隐藏前端参考范围功能...")
    
    try:
        # 读取前端JavaScript文件
        with open('static/js/app.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 注释掉参考范围相关的代码段
        modifications = [
            # 隐藏参考范围卡片显示
            (
                r'(// 更新参考范围信息.*?referenceRangeCard\.style\.display = hasReferenceRangeInfo \? \'block\' : \'none\';)',
                r'/* 暂时隐藏参考范围功能\n\1\n*/',
                re.DOTALL
            ),
            # 隐藏参考范围API调用
            (
                r'(// 更新参考范围信息（底层指标专用）.*?updateModalReferenceRangeInfo\(indicator\);)',
                r'/* 暂时隐藏参考范围功能\n\1\n*/',
                re.DOTALL
            ),
            # 隐藏参考范围数据加载
            (
                r'(async loadAndDisplayReferenceRange.*?}\s*catch.*?}\s*})',
                r'/* 暂时隐藏参考范围功能\n\1\n*/',
                re.DOTALL
            )
        ]
        
        modified_content = content
        changes_made = 0
        
        for pattern, replacement, flags in modifications:
            new_content = re.sub(pattern, replacement, modified_content, flags=flags)
            if new_content != modified_content:
                changes_made += 1
                modified_content = new_content
        
        # 添加简单的隐藏逻辑
        hide_logic = """
// 暂时隐藏参考范围相关功能
function hideReferenceRangeFeatures() {
    // 隐藏参考范围卡片
    const referenceRangeCards = document.querySelectorAll('[id*="ReferenceRange"], [class*="reference-range"]');
    referenceRangeCards.forEach(card => {
        if (card) card.style.display = 'none';
    });
    
    // 隐藏参考范围按钮
    const referenceRangeButtons = document.querySelectorAll('[onclick*="reference"], [href*="reference-range"]');
    referenceRangeButtons.forEach(btn => {
        if (btn) btn.style.display = 'none';
    });
}

// 页面加载完成后执行隐藏
document.addEventListener('DOMContentLoaded', hideReferenceRangeFeatures);
"""
        
        # 在文件末尾添加隐藏逻辑
        if 'hideReferenceRangeFeatures' not in modified_content:
            modified_content += '\n' + hide_logic
            changes_made += 1
        
        # 保存修改后的文件
        if changes_made > 0:
            with open('static/js/app.js', 'w', encoding='utf-8') as f:
                f.write(modified_content)
            print(f"   ✅ 前端修改完成，应用了 {changes_made} 个修改")
        else:
            print("   ℹ️  前端无需修改")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 前端修改失败: {e}")
        return False

def hide_backend_reference_features():
    """隐藏后端参考范围相关功能"""
    print("\n🔧 隐藏后端参考范围功能...")
    
    try:
        # 检查后端模板文件
        template_files = [
            'templates/admin/indicators.html',
            'templates/admin/indicator_detail.html',
            'templates/admin/edit_indicator.html'
        ]
        
        total_changes = 0
        
        for template_file in template_files:
            if os.path.exists(template_file):
                print(f"   处理 {template_file}...")
                
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 隐藏参考范围相关的HTML元素
                modifications = [
                    # 隐藏参考范围按钮
                    (r'(<[^>]*href[^>]*reference-range[^>]*>.*?</[^>]*>)', r'<!-- 暂时隐藏参考范围功能 \1 -->'),
                    # 隐藏参考范围链接
                    (r'(<[^>]*onclick[^>]*reference[^>]*>.*?</[^>]*>)', r'<!-- 暂时隐藏参考范围功能 \1 -->'),
                    # 隐藏参考范围表格列
                    (r'(<th[^>]*>.*?参考范围.*?</th>)', r'<!-- 暂时隐藏参考范围功能 \1 -->'),
                    (r'(<td[^>]*>.*?参考范围.*?</td>)', r'<!-- 暂时隐藏参考范围功能 \1 -->'),
                ]
                
                modified_content = content
                file_changes = 0
                
                for pattern, replacement in modifications:
                    new_content = re.sub(pattern, replacement, modified_content, flags=re.IGNORECASE | re.DOTALL)
                    if new_content != modified_content:
                        file_changes += 1
                        modified_content = new_content
                
                if file_changes > 0:
                    with open(template_file, 'w', encoding='utf-8') as f:
                        f.write(modified_content)
                    print(f"     ✅ 修改了 {file_changes} 处")
                    total_changes += file_changes
                else:
                    print(f"     ℹ️  无需修改")
            else:
                print(f"     ⚠️  文件不存在: {template_file}")
        
        print(f"   ✅ 后端修改完成，总共应用了 {total_changes} 个修改")
        return True
        
    except Exception as e:
        print(f"   ❌ 后端修改失败: {e}")
        return False

def hide_api_reference_endpoints():
    """暂时禁用参考范围API端点"""
    print("\n🔧 暂时禁用参考范围API端点...")
    
    try:
        # 读取主应用文件
        with open('hospital_indicator_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找参考范围相关的路由
        reference_routes = [
            r"@app\.route\('/api/indicators/<indicator_id>/reference-range'.*?\ndef.*?\):",
            r"@app\.route\('/admin/indicators/<indicator_id>/reference-range'.*?\ndef.*?\):"
        ]
        
        modified_content = content
        changes_made = 0
        
        for route_pattern in reference_routes:
            # 查找路由定义
            matches = re.finditer(route_pattern, modified_content, re.DOTALL)
            
            for match in matches:
                route_start = match.start()
                
                # 找到函数的结束位置
                lines = modified_content[route_start:].split('\n')
                function_lines = []
                indent_level = None
                
                for i, line in enumerate(lines):
                    if i == 0 or (indent_level is not None and (line.strip() == '' or line.startswith(' ' * indent_level))):
                        if indent_level is None and line.strip().startswith('def '):
                            # 确定缩进级别
                            indent_level = len(line) - len(line.lstrip())
                        function_lines.append(line)
                    else:
                        break
                
                if function_lines:
                    original_function = '\n'.join(function_lines)
                    
                    # 创建禁用版本
                    disabled_function = f"""# 暂时禁用参考范围功能
{original_function.split('def ')[0]}def {original_function.split('def ')[1].split('(')[0]}(*args, **kwargs):
    return jsonify({{'success': False, 'error': '参考范围功能暂时禁用'}})"""
                    
                    # 替换函数
                    modified_content = modified_content.replace(original_function, disabled_function)
                    changes_made += 1
        
        if changes_made > 0:
            with open('hospital_indicator_app.py', 'w', encoding='utf-8') as f:
                f.write(modified_content)
            print(f"   ✅ API修改完成，禁用了 {changes_made} 个参考范围端点")
        else:
            print("   ℹ️  未找到需要禁用的API端点")
        
        return True
        
    except Exception as e:
        print(f"   ❌ API修改失败: {e}")
        return False

def create_css_hide_rules():
    """创建CSS规则隐藏参考范围元素"""
    print("\n🔧 创建CSS隐藏规则...")
    
    css_rules = """
/* 暂时隐藏参考范围相关功能 */
.reference-range-section,
.reference-range-card,
.reference-range-button,
[id*="reference-range"],
[class*="reference-range"],
[href*="reference-range"],
.modal-reference-range,
#modalReferenceRangeCard {
    display: none !important;
}

/* 隐藏参考范围相关的表格列 */
th:has-text("参考范围"),
td:has-text("参考范围") {
    display: none !important;
}

/* 隐藏参考范围按钮 */
button[onclick*="reference"],
a[href*="reference-range"] {
    display: none !important;
}
"""
    
    try:
        # 添加到前端CSS文件
        css_files = ['static/css/style.css', 'static/css/admin.css']
        
        for css_file in css_files:
            if os.path.exists(css_file):
                with open(css_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if '暂时隐藏参考范围相关功能' not in content:
                    with open(css_file, 'a', encoding='utf-8') as f:
                        f.write('\n' + css_rules)
                    print(f"   ✅ 已添加隐藏规则到 {css_file}")
                else:
                    print(f"   ℹ️  {css_file} 已包含隐藏规则")
            else:
                print(f"   ⚠️  CSS文件不存在: {css_file}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ CSS修改失败: {e}")
        return False

def test_core_functionality():
    """测试核心功能是否正常"""
    print("\n🧪 测试核心功能...")
    
    import requests
    import time
    
    # 等待服务器响应
    time.sleep(1)
    
    try:
        # 测试基本API
        response = requests.get('http://localhost:5001/api/indicators/1.1.1')
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print("   ✅ 基本指标API正常")
            else:
                print(f"   ❌ 指标API失败: {data.get('error')}")
        else:
            print(f"   ❌ 指标API请求失败: {response.status_code}")
        
        # 测试前端页面
        response = requests.get('http://localhost:5001/')
        if response.status_code == 200:
            print("   ✅ 前端首页正常")
        else:
            print(f"   ❌ 前端首页失败: {response.status_code}")
        
        # 测试后端页面
        response = requests.get('http://localhost:5001/admin')
        if response.status_code == 200:
            print("   ✅ 后端首页正常")
        else:
            print(f"   ❌ 后端首页失败: {response.status_code}")
        
        # 测试参考范围API是否被禁用
        response = requests.get('http://localhost:5001/api/indicators/1.1.1/reference-range')
        if response.status_code == 200:
            data = response.json()
            if not data['success'] and '暂时禁用' in data.get('error', ''):
                print("   ✅ 参考范围API已成功禁用")
            else:
                print("   ⚠️  参考范围API仍然活跃")
        else:
            print("   ✅ 参考范围API已禁用（404）")
        
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")

def main():
    """主函数"""
    print("🎯 暂时隐藏参考范围功能，确保核心功能正常")
    print("=" * 70)
    
    # 1. 隐藏前端参考范围功能
    frontend_success = hide_frontend_reference_features()
    
    # 2. 隐藏后端参考范围功能
    backend_success = hide_backend_reference_features()
    
    # 3. 禁用参考范围API端点
    api_success = hide_api_reference_endpoints()
    
    # 4. 创建CSS隐藏规则
    css_success = create_css_hide_rules()
    
    # 5. 测试核心功能
    test_core_functionality()
    
    print("\n" + "=" * 70)
    print("📋 隐藏操作总结:")
    
    if frontend_success:
        print("✅ 前端参考范围功能已隐藏")
    else:
        print("❌ 前端参考范围功能隐藏失败")
    
    if backend_success:
        print("✅ 后端参考范围功能已隐藏")
    else:
        print("❌ 后端参考范围功能隐藏失败")
    
    if api_success:
        print("✅ 参考范围API端点已禁用")
    else:
        print("❌ 参考范围API端点禁用失败")
    
    if css_success:
        print("✅ CSS隐藏规则已添加")
    else:
        print("❌ CSS隐藏规则添加失败")
    
    print("\n🎯 现在系统状态:")
    print("- ✅ 核心指标管理功能正常")
    print("- ✅ 前后端基本功能正常")
    print("- ✅ 参考范围功能已暂时隐藏")
    print("- ✅ 不会再有参考范围相关的JavaScript错误")
    
    print("\n🔗 建议:")
    print("1. 重启应用服务器以应用所有更改")
    print("2. 清除浏览器缓存并刷新页面")
    print("3. 测试核心指标管理功能")
    print("4. 确认不再有JavaScript错误")
    print("5. 需要时可以随时恢复参考范围功能")

if __name__ == "__main__":
    main()
