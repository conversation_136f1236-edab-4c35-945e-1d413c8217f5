#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示第一章完整数据的脚本
"""

import sqlite3
import sys

def show_chapter1_data():
    """
    显示第一章的完整数据
    """
    db_path = "hospital_indicator_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=" * 80)
        print("🏥 医院等级评审指标管理系统 - 第一章完整数据")
        print("=" * 80)
        
        # 获取章节信息
        cursor.execute("""
            SELECT code, name, description, icon, color 
            FROM chapters 
            WHERE code = '1'
        """)
        chapter = cursor.fetchone()
        
        if not chapter:
            print("❌ 未找到第一章数据")
            return
        
        print(f"\n📚 章节信息")
        print(f"编码: {chapter[0]}")
        print(f"名称: {chapter[1]}")
        print(f"描述: {chapter[2]}")
        print(f"图标: {chapter[3]}")
        print(f"颜色: {chapter[4]}")
        
        # 获取小节信息
        cursor.execute("""
            SELECT s.code, s.name, s.description, s.sort_order,
                   COUNT(i.id) as indicator_count
            FROM sections s
            LEFT JOIN indicators i ON s.id = i.section_id
            WHERE s.chapter_id = (SELECT id FROM chapters WHERE code = '1')
            GROUP BY s.id
            ORDER BY s.sort_order
        """)
        sections = cursor.fetchall()
        
        print(f"\n📖 小节概览 (共{len(sections)}个小节)")
        print("-" * 80)
        for section in sections:
            print(f"{section[0]} - {section[1]} ({section[4]}个指标)")
        
        # 详细显示每个小节及其指标
        for section in sections:
            section_code = section[0]
            section_name = section[1]
            
            print(f"\n📋 {section_code} {section_name}")
            print("-" * 60)
            
            # 获取该小节下的所有指标
            cursor.execute("""
                SELECT id, name, description, category, sort_order
                FROM indicators
                WHERE section_id = (
                    SELECT id FROM sections WHERE code = ? AND chapter_id = (
                        SELECT id FROM chapters WHERE code = '1'
                    )
                )
                ORDER BY id
            """, (section_code,))
            
            indicators = cursor.fetchall()
            
            if indicators:
                for i, indicator in enumerate(indicators, 1):
                    print(f"  {indicator[0]} - {indicator[1]}")
                    if indicator[2] and indicator[2] != f"第一章指标：{indicator[1]}":
                        print(f"      描述: {indicator[2]}")
                    if indicator[3]:
                        print(f"      类别: {indicator[3]}")
            else:
                print("  暂无指标数据")
        
        # 统计信息
        cursor.execute("""
            SELECT COUNT(*) FROM sections 
            WHERE chapter_id = (SELECT id FROM chapters WHERE code = '1')
        """)
        section_count = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) FROM indicators 
            WHERE chapter_id = (SELECT id FROM chapters WHERE code = '1')
        """)
        indicator_count = cursor.fetchone()[0]
        
        print(f"\n📊 统计信息")
        print("-" * 40)
        print(f"小节总数: {section_count}")
        print(f"指标总数: {indicator_count}")
        
        # 按小节分组的指标统计
        print(f"\n📈 各小节指标分布")
        print("-" * 40)
        for section in sections:
            print(f"{section[0]} {section[1]}: {section[4]}个指标")
        
        print("\n" + "=" * 80)
        print("✅ 第一章数据展示完成")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 查询数据时出错: {e}")
    finally:
        if conn:
            conn.close()

def show_detailed_indicators():
    """
    显示指标的详细信息
    """
    db_path = "hospital_indicator_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n" + "=" * 80)
        print("📊 第一章指标详细信息")
        print("=" * 80)
        
        cursor.execute("""
            SELECT 
                i.id,
                i.name,
                i.description,
                i.category,
                s.name as section_name,
                c.name as chapter_name
            FROM indicators i
            LEFT JOIN sections s ON i.section_id = s.id
            LEFT JOIN chapters c ON i.chapter_id = c.id
            WHERE c.code = '1'
            ORDER BY i.id
        """)
        
        indicators = cursor.fetchall()
        
        for indicator in indicators:
            print(f"\n🔹 {indicator[0]} - {indicator[1]}")
            print(f"   所属小节: {indicator[4]}")
            print(f"   所属章节: {indicator[5]}")
            if indicator[3]:
                print(f"   指标类别: {indicator[3]}")
            if indicator[2] and not indicator[2].startswith("第一章指标："):
                print(f"   详细描述: {indicator[2]}")
        
    except Exception as e:
        print(f"❌ 查询详细信息时出错: {e}")
    finally:
        if conn:
            conn.close()

def main():
    """
    主函数
    """
    show_chapter1_data()
    
    # 询问是否显示详细信息
    try:
        choice = input("\n是否显示指标详细信息？(y/N): ").strip().lower()
        if choice in ['y', 'yes']:
            show_detailed_indicators()
    except:
        pass  # 如果在脚本环境中运行，跳过交互

if __name__ == "__main__":
    main()
