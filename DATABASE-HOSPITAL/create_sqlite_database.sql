-- 医院等级评审指标管理系统 - SQLite版本数据库创建脚本
-- 创建时间: 2024年
-- 数据库名称: hospital_indicator_system

-- ========================================
-- 删除已存在的表（如果存在）
-- ========================================
DROP TABLE IF EXISTS data_audit_flow;
DROP TABLE IF EXISTS indicator_data_history;
DROP TABLE IF EXISTS indicator_relations;
DROP TABLE IF EXISTS indicator_departments;
DROP TABLE IF EXISTS indicator_tags;
DROP TABLE IF EXISTS indicators;
DROP TABLE IF EXISTS sections;
DROP TABLE IF EXISTS chapters;
DROP TABLE IF EXISTS tags;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS departments;
DROP TABLE IF EXISTS system_config;

-- ========================================
-- 创建表结构
-- ========================================

-- 章节表
CREATE TABLE chapters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(20) DEFAULT '#1a73e8',
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 小节表
CREATE TABLE sections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    chapter_id INTEGER NOT NULL,
    code VARCHAR(10) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE
);

-- 指标表
CREATE TABLE indicators (
    id VARCHAR(20) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    calculation_method TEXT,
    target_value DECIMAL(10,4),
    current_value DECIMAL(10,4),
    completion_rate DECIMAL(5,2),
    data_source VARCHAR(100),
    collection_frequency VARCHAR(20) CHECK (collection_frequency IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'realtime')),
    collection_method VARCHAR(20) CHECK (collection_method IN ('auto', 'manual', 'import')),
    sql_query TEXT,
    parent_id VARCHAR(20),
    chapter_id INTEGER,
    section_id INTEGER,
    category VARCHAR(50),
    status VARCHAR(20) CHECK (status IN ('normal', 'warning', 'danger', 'inactive')) DEFAULT 'normal',
    sort_order INTEGER DEFAULT 0,
    notes TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES indicators(id) ON DELETE SET NULL,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE SET NULL,
    FOREIGN KEY (section_id) REFERENCES sections(id) ON DELETE SET NULL
);

-- 标签表
CREATE TABLE tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    color VARCHAR(20) DEFAULT '#1a73e8',
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 指标标签关联表
CREATE TABLE indicator_tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    indicator_id VARCHAR(20) NOT NULL,
    tag_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
    UNIQUE(indicator_id, tag_id)
);

-- 科室表
CREATE TABLE departments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE,
    type VARCHAR(20) CHECK (type IN ('clinical', 'medical_tech', 'admin', 'support')),
    parent_id INTEGER,
    director VARCHAR(50),
    contact_phone VARCHAR(20),
    description TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES departments(id) ON DELETE SET NULL
);

-- 指标科室关联表
CREATE TABLE indicator_departments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    indicator_id VARCHAR(20) NOT NULL,
    department_id INTEGER NOT NULL,
    role_type VARCHAR(20) CHECK (role_type IN ('lead', 'numerator', 'denominator', 'support')),
    responsibility VARCHAR(200),
    contact_person VARCHAR(50),
    data_description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
);

-- 用户表
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    department_id INTEGER,
    role VARCHAR(20) CHECK (role IN ('admin', 'manager', 'operator', 'viewer')) DEFAULT 'viewer',
    is_active BOOLEAN DEFAULT 1,
    last_login_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
);

-- 指标关联表
CREATE TABLE indicator_relations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_indicator_id VARCHAR(20) NOT NULL,
    target_indicator_id VARCHAR(20) NOT NULL,
    relation_type VARCHAR(20) CHECK (relation_type IN ('related', 'dependent', 'conflict', 'derived')),
    description VARCHAR(200),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_indicator_id) REFERENCES indicators(id) ON DELETE CASCADE,
    FOREIGN KEY (target_indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
);

-- 指标数据历史表
CREATE TABLE indicator_data_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    indicator_id VARCHAR(20) NOT NULL,
    value DECIMAL(15,4),
    target_value DECIMAL(15,4),
    completion_rate DECIMAL(5,2),
    data_period DATE,
    data_source VARCHAR(100),
    collection_method VARCHAR(20) CHECK (collection_method IN ('auto', 'manual', 'import')),
    collector_id INTEGER,
    status VARCHAR(20) CHECK (status IN ('draft', 'submitted', 'approved', 'rejected')) DEFAULT 'draft',
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE,
    FOREIGN KEY (collector_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 数据审核流程表
CREATE TABLE data_audit_flow (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    data_history_id INTEGER NOT NULL,
    step INTEGER NOT NULL,
    auditor_id INTEGER,
    audit_result VARCHAR(20) CHECK (audit_result IN ('pending', 'approved', 'rejected')),
    audit_comment TEXT,
    audit_time DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (data_history_id) REFERENCES indicator_data_history(id) ON DELETE CASCADE,
    FOREIGN KEY (auditor_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 系统配置表
CREATE TABLE system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    config_type VARCHAR(20) CHECK (config_type IN ('string', 'number', 'boolean', 'json')),
    description VARCHAR(200),
    is_system BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- ========================================
-- 创建索引
-- ========================================
CREATE INDEX idx_chapters_code ON chapters(code);
CREATE INDEX idx_chapters_sort_order ON chapters(sort_order);
CREATE INDEX idx_sections_chapter_id ON sections(chapter_id);
CREATE INDEX idx_sections_sort_order ON sections(sort_order);
CREATE INDEX idx_indicators_chapter_id ON indicators(chapter_id);
CREATE INDEX idx_indicators_section_id ON indicators(section_id);
CREATE INDEX idx_indicators_parent_id ON indicators(parent_id);
CREATE INDEX idx_indicators_status ON indicators(status);
CREATE INDEX idx_indicator_data_history_indicator_id ON indicator_data_history(indicator_id);
CREATE INDEX idx_indicator_data_history_data_period ON indicator_data_history(data_period);
CREATE INDEX idx_indicator_data_history_status ON indicator_data_history(status);

-- ========================================
-- 插入基础数据
-- ========================================

-- 从Excel文件提取的章节数据
INSERT INTO chapters (code, name, description, icon, color, sort_order) VALUES
('1', '资源配置与运行数据指标', '第1章：资源配置与运行数据指标', 'fas fa-chart-bar', '#1a73e8', 1),
('2', '医疗服务能力与医院质量安全指标', '第2章：医疗服务能力与医院质量安全指标', 'fas fa-heartbeat', '#34a853', 2),
('3', '重点专业质量控制指标', '第3章：重点专业质量控制指标', 'fas fa-check-circle', '#ea4335', 3),
('4', '单病种质量控制指标', '第4章：单病种质量控制指标', 'fas fa-clipboard-list', '#fbbc04', 4),
('5', '重点医疗技术临床应用质量控制指标', '第5章：重点医疗技术临床应用质量控制指标', 'fas fa-microscope', '#9c27b0', 5);

-- 插入标签数据
INSERT INTO tags (name, color, description) VALUES
('关键指标', '#1a73e8', '用于标记医院重点关注的核心指标'),
('医疗安全', '#ea4335', '与医疗安全相关的指标'),
('患者服务', '#34a853', '与患者服务质量相关的指标'),
('运营效率', '#fbbc04', '与医院运营效率相关的指标'),
('质量控制', '#9c27b0', '与医疗质量控制相关的指标');

-- 插入科室数据
INSERT INTO departments (name, code, type, director, contact_phone, description) VALUES
('医务科', 'YWK', 'admin', '医务科主任', '010-12345678', '负责医疗质量管理和医务行政工作'),
('质控科', 'ZKK', 'admin', '质控科主任', '010-12345679', '负责医疗质量控制和指标监测'),
('信息科', 'XXK', 'admin', '信息科主任', '010-12345680', '负责医院信息化建设和数据管理'),
('内科', 'NK', 'clinical', '内科主任', '010-12345681', '内科医疗服务'),
('外科', 'WK', 'clinical', '外科主任', '010-12345682', '外科医疗服务'),
('急诊科', 'JZK', 'clinical', '急诊科主任', '010-12345683', '急诊医疗服务');

-- 插入用户数据
INSERT INTO users (username, password, real_name, email, phone, department_id, role) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', '<EMAIL>', '13800138000', 1, 'admin'),
('quality_manager', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '质控主管', '<EMAIL>', '13800138001', 2, 'manager'),
('data_operator', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '数据录入员', '<EMAIL>', '13800138002', 3, 'operator');

-- 插入系统配置
INSERT INTO system_config (config_key, config_value, config_type, description, is_system) VALUES
('system_name', '医院等级评审指标管理系统', 'string', '系统名称', 1),
('system_version', '1.0.0', 'string', '系统版本', 1),
('data_backup_enabled', '1', 'boolean', '是否启用数据备份', 0),
('audit_enabled', '1', 'boolean', '是否启用审核流程', 0),
('max_file_size', '10485760', 'number', '最大文件上传大小(字节)', 0);
