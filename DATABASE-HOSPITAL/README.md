# 🏥 医院等级评审指标管理系统数据库

## 📋 项目概述

这是一个完整的医院等级评审指标管理系统数据库，基于SQLite构建，包含了从Excel文件中提取的真实章节数据。

## 📁 文件结构

```
DATABASE-HOSPITAL/
├── hospital_indicator_system.db    # SQLite数据库文件
├── create_sqlite_database.sql      # 数据库创建脚本
├── manage_database.sh              # 数据库管理脚本
└── README.md                       # 说明文档
```

## 🗃️ 数据库结构

### 核心表

| 表名 | 说明 | 记录数 |
|------|------|--------|
| **chapters** | 章节管理 | 5 |
| **sections** | 小节管理 | 0 |
| **indicators** | 指标管理 | 0 |
| **tags** | 标签管理 | 5 |
| **departments** | 科室管理 | 6 |
| **users** | 用户管理 | 3 |
| **system_config** | 系统配置 | 5 |

### 关联表

- `indicator_tags` - 指标标签关联
- `indicator_departments` - 指标科室关联  
- `indicator_relations` - 指标关联关系
- `indicator_data_history` - 指标数据历史
- `data_audit_flow` - 数据审核流程

## 📊 预置数据

### 章节数据（从Excel提取）

| 编码 | 章节名称 | 图标 | 颜色 |
|------|----------|------|------|
| 1 | 资源配置与运行数据指标 | fas fa-chart-bar | #1a73e8 |
| 2 | 医疗服务能力与医院质量安全指标 | fas fa-heartbeat | #34a853 |
| 3 | 重点专业质量控制指标 | fas fa-check-circle | #ea4335 |
| 4 | 单病种质量控制指标 | fas fa-clipboard-list | #fbbc04 |
| 5 | 重点医疗技术临床应用质量控制指标 | fas fa-microscope | #9c27b0 |

### 预置用户

| 用户名 | 角色 | 说明 |
|--------|------|------|
| admin | 管理员 | 系统管理员 |
| quality_manager | 管理者 | 质控主管 |
| data_operator | 操作员 | 数据录入员 |

## 🔧 使用方法

### 1. 数据库管理脚本

```bash
# 查看数据库状态
./manage_database.sh status

# 查看章节数据
./manage_database.sh chapters

# 查看科室数据
./manage_database.sh departments

# 查看用户数据
./manage_database.sh users

# 备份数据库
./manage_database.sh backup

# 进入SQLite交互模式
./manage_database.sh interactive

# 显示帮助
./manage_database.sh help
```

### 2. 直接使用SQLite

```bash
# 连接数据库
sqlite3 hospital_indicator_system.db

# 查看所有表
.tables

# 查看表结构
.schema chapters

# 查询数据
SELECT * FROM chapters;

# 退出
.quit
```

## 📝 常用SQL查询

### 查看章节信息
```sql
SELECT code, name, description, icon, color 
FROM chapters 
ORDER BY sort_order;
```

### 查看科室信息
```sql
SELECT code, name, type, director, contact_phone 
FROM departments 
WHERE is_active = 1;
```

### 查看系统配置
```sql
SELECT config_key, config_value, description 
FROM system_config 
ORDER BY is_system DESC, config_key;
```

## 🔐 安全说明

- 用户密码使用bcrypt加密存储
- 默认密码为: `password`（请在生产环境中修改）
- 建议定期备份数据库文件

## 📈 扩展功能

### 添加小节数据
```sql
INSERT INTO sections (chapter_id, code, name, description, sort_order) VALUES
(1, '1.1', '床位配置', '医院床位资源配置相关指标', 1);
```

### 添加指标数据
```sql
INSERT INTO indicators (id, name, description, chapter_id, section_id, target_value) VALUES
('1.1.1', '实际开放床位数', '医院实际开放使用的床位总数', 1, 1, 500);
```

### 添加指标标签关联
```sql
INSERT INTO indicator_tags (indicator_id, tag_id) VALUES
('1.1.1', 1);  -- 关联到"关键指标"标签
```

## 🛠️ 维护建议

1. **定期备份**: 使用 `./manage_database.sh backup` 备份数据
2. **监控大小**: 定期检查数据库文件大小
3. **清理历史**: 定期清理过期的历史数据
4. **索引优化**: 根据查询需求添加适当索引

## 📞 技术支持

如需技术支持或有问题反馈，请联系系统管理员。

---

**创建时间**: 2024年5月23日  
**数据库版本**: 1.0.0  
**SQLite版本**: 3.43.2
