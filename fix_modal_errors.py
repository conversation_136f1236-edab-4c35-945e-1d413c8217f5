#!/usr/bin/env python3
"""
专门修复前端模态框中的JavaScript错误
"""

import re

def fix_modal_javascript():
    """修复模态框JavaScript中的DOM操作"""
    print("🔧 修复前端模态框JavaScript...")
    
    # 读取模态框模板
    try:
        with open('templates/modal.html', 'r', encoding='utf-8') as f:
            modal_content = f.read()
        
        print("✅ 找到模态框模板")
        
        # 添加安全的DOM操作函数
        safe_modal_js = """
<script>
// 模态框安全DOM操作函数
function safeModalSetText(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
        return true;
    } else {
        console.warn('Modal element not found:', elementId);
        return false;
    }
}

function safeModalSetHTML(elementId, html) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = html;
        return true;
    } else {
        console.warn('Modal element not found:', elementId);
        return false;
    }
}

function safeModalShow(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = 'block';
        return true;
    } else {
        console.warn('Modal element not found:', elementId);
        return false;
    }
}

function safeModalHide(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = 'none';
        return true;
    } else {
        console.warn('Modal element not found:', elementId);
        return false;
    }
}
</script>
"""
        
        # 在模态框模板末尾添加安全函数
        if '</div>' in modal_content:
            insert_pos = modal_content.rfind('</div>') + len('</div>')
            modified_content = modal_content[:insert_pos] + safe_modal_js + modal_content[insert_pos:]
            
            with open('templates/modal.html', 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print("✅ 模态框模板修复完成")
        
    except FileNotFoundError:
        print("⚠️  模态框模板不存在，跳过")
    except Exception as e:
        print(f"❌ 修复模态框模板失败: {e}")

def fix_app_js_modal_functions():
    """修复app.js中的模态框相关函数"""
    print("\n🔧 修复app.js中的模态框函数...")
    
    try:
        with open('static/js/app.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并修复模态框相关的textContent设置
        modal_fixes = [
            # 修复模态框标题设置
            (r"modalTitle\.textContent\s*=\s*([^;]+);", 
             r"if (modalTitle) modalTitle.textContent = \1;"),
            
            # 修复模态框内容设置
            (r"(\w+Element)\.textContent\s*=\s*([^;]+);", 
             r"if (\1) \1.textContent = \2;"),
            
            # 修复getElementById后直接设置textContent
            (r"document\.getElementById\('([^']+)'\)\.textContent\s*=\s*([^;]+);", 
             r"safeSetTextContent('\1', \2);")
        ]
        
        modified_content = content
        changes_made = 0
        
        for pattern, replacement in modal_fixes:
            new_content = re.sub(pattern, replacement, modified_content)
            if new_content != modified_content:
                changes_made += 1
                modified_content = new_content
        
        if changes_made > 0:
            with open('static/js/app.js', 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print(f"✅ app.js修复完成，应用了 {changes_made} 个修复")
        else:
            print("ℹ️  app.js无需修复")
        
    except Exception as e:
        print(f"❌ 修复app.js失败: {e}")

def add_modal_error_handling():
    """为模态框添加专门的错误处理"""
    print("\n🔧 添加模态框错误处理...")
    
    modal_error_handler = """
// 模态框专用错误处理
function handleModalError(error, context = 'modal') {
    console.error(`Modal Error in ${context}:`, error);
    
    // 显示用户友好的错误信息
    const errorMessage = document.createElement('div');
    errorMessage.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: var(--md-error, #d32f2f);
        color: var(--md-on-error, white);
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 400px;
        text-align: center;
    `;
    
    errorMessage.innerHTML = `
        <div style="margin-bottom: 12px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 24px;"></i>
        </div>
        <div style="font-weight: 500; margin-bottom: 8px;">加载失败</div>
        <div style="font-size: 14px; opacity: 0.9;">页面内容加载时出现问题，请刷新页面重试</div>
        <button onclick="this.parentElement.remove()" style="
            margin-top: 16px;
            padding: 8px 16px;
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            border-radius: 4px;
            cursor: pointer;
        ">关闭</button>
    `;
    
    document.body.appendChild(errorMessage);
    
    // 5秒后自动移除
    setTimeout(() => {
        if (errorMessage.parentElement) {
            errorMessage.remove();
        }
    }, 5000);
}

// 包装模态框显示函数
function safeShowModal(modalId) {
    try {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('show');
            return true;
        } else {
            throw new Error(`Modal not found: ${modalId}`);
        }
    } catch (error) {
        handleModalError(error, 'showModal');
        return false;
    }
}

// 包装模态框隐藏函数
function safeHideModal(modalId) {
    try {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('show');
            return true;
        } else {
            console.warn(`Modal not found: ${modalId}`);
            return false;
        }
    } catch (error) {
        handleModalError(error, 'hideModal');
        return false;
    }
}
"""
    
    try:
        with open('static/js/modal-error-handler.js', 'w', encoding='utf-8') as f:
            f.write(modal_error_handler)
        
        print("✅ 创建模态框错误处理脚本: static/js/modal-error-handler.js")
        
    except Exception as e:
        print(f"❌ 创建模态框错误处理脚本失败: {e}")

def update_base_template():
    """更新基础模板以包含模态框错误处理"""
    print("\n🔧 更新基础模板...")
    
    try:
        with open('templates/base.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含错误处理脚本
        if 'modal-error-handler.js' not in content:
            # 在head标签结束前添加脚本引用
            head_end = content.find('</head>')
            if head_end != -1:
                script_tag = '\n    <script src="/static/js/modal-error-handler.js"></script>\n'
                modified_content = content[:head_end] + script_tag + content[head_end:]
                
                with open('templates/base.html', 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                
                print("✅ 基础模板更新完成")
            else:
                print("⚠️  未找到</head>标签")
        else:
            print("ℹ️  基础模板已包含错误处理脚本")
        
    except FileNotFoundError:
        print("⚠️  基础模板不存在")
    except Exception as e:
        print(f"❌ 更新基础模板失败: {e}")

def create_test_script():
    """创建测试脚本"""
    print("\n🔧 创建测试脚本...")
    
    test_script = """
// 模态框功能测试
function testModalFunctionality() {
    console.log('🧪 开始测试模态框功能...');
    
    // 测试安全函数
    const testResults = {
        safeSetTextContent: typeof safeSetTextContent === 'function',
        safeGetElement: typeof safeGetElement === 'function',
        handleModalError: typeof handleModalError === 'function',
        safeShowModal: typeof safeShowModal === 'function',
        safeHideModal: typeof safeHideModal === 'function'
    };
    
    console.log('📊 测试结果:', testResults);
    
    // 测试DOM元素访问
    const testElements = [
        'notification',
        'indicatorModal',
        'modalIndicatorTitle',
        'modalIndicatorBadge'
    ];
    
    console.log('🔍 测试DOM元素:');
    testElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        console.log(`  ${elementId}: ${element ? '✅ 存在' : '❌ 不存在'}`);
    });
    
    return testResults;
}

// 页面加载完成后自动测试
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(testModalFunctionality, 1000);
});
"""
    
    try:
        with open('static/js/modal-test.js', 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        print("✅ 创建测试脚本: static/js/modal-test.js")
        
    except Exception as e:
        print(f"❌ 创建测试脚本失败: {e}")

def main():
    """主修复函数"""
    print("🎯 专门修复前端模态框JavaScript错误")
    print("=" * 60)
    
    # 修复模态框模板
    fix_modal_javascript()
    
    # 修复app.js中的模态框函数
    fix_app_js_modal_functions()
    
    # 添加模态框错误处理
    add_modal_error_handling()
    
    # 更新基础模板
    update_base_template()
    
    # 创建测试脚本
    create_test_script()
    
    print("\n" + "=" * 60)
    print("🎉 模态框修复完成！")
    
    print("\n📋 修复内容:")
    print("1. ✅ 添加了安全的DOM操作函数")
    print("2. ✅ 修复了app.js中的textContent设置")
    print("3. ✅ 创建了模态框专用错误处理")
    print("4. ✅ 更新了基础模板")
    print("5. ✅ 创建了功能测试脚本")
    
    print("\n🔗 测试建议:")
    print("1. 清除浏览器缓存")
    print("2. 刷新页面")
    print("3. 打开浏览器控制台查看测试结果")
    print("4. 尝试打开指标详情模态框")
    print("5. 检查是否还有JavaScript错误")

if __name__ == "__main__":
    main()
