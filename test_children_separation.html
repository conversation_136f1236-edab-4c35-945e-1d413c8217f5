<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>子指标模块分离测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Google Sans', 'Roboto', 'Arial', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
        }

        .test-title {
            font-size: 24px;
            font-weight: 600;
            color: #1a73e8;
            margin-bottom: 20px;
            text-align: center;
        }

        .test-button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.2s ease;
        }

        .test-button:hover {
            background: #1565c0;
            transform: translateY(-1px);
        }

        .info-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .info-card-header {
            background: #f8f9fa;
            padding: 16px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .info-card-title {
            font-size: 16px;
            font-weight: 600;
            color: #202124;
            display: flex;
            align-items: center;
            margin: 0;
        }

        .info-card-title i {
            margin-right: 8px;
            color: #1a73e8;
        }

        .info-card-content {
            padding: 20px;
        }

        .children-count {
            background: #1a73e8;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }

        .children-nav-grid {
            display: grid;
            gap: 12px;
        }

        .child-nav-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .child-nav-item:hover {
            background: #e8f0fe;
            border-color: #1a73e8;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(26, 115, 232, 0.15);
        }

        .child-nav-info {
            flex: 1;
        }

        .child-nav-id {
            font-family: 'Roboto Mono', monospace;
            font-weight: 600;
            font-size: 14px;
            color: #1a73e8;
            margin-bottom: 4px;
        }

        .child-nav-name {
            font-size: 15px;
            color: #202124;
            font-weight: 500;
            line-height: 1.3;
        }

        .child-nav-arrow {
            color: #9aa0a6;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .child-nav-item:hover .child-nav-arrow {
            color: #1a73e8;
            transform: translateX(4px);
        }

        .status {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }

        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }

        .status.info {
            background: #e3f2fd;
            color: #1565c0;
            border: 1px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🧪 子指标模块分离测试</h1>
        
        <div class="status info">
            <i class="fas fa-info-circle"></i>
            测试目标：验证子指标模块已从"指标参考"模块中分离出来，单独显示
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="test-button" onclick="testIndicatorWithChildren()">
                <i class="fas fa-play"></i>
                测试有子指标的指标 (1.3.1)
            </button>
            <button class="test-button" onclick="testIndicatorWithoutChildren()">
                <i class="fas fa-play"></i>
                测试无子指标的指标 (1.1.1)
            </button>
        </div>

        <div id="testResults"></div>

        <!-- 模拟的子指标卡片 -->
        <div class="info-card" id="childrenNavCard" style="display: none;">
            <div class="info-card-header">
                <h3 class="info-card-title">
                    <i class="fas fa-sitemap"></i>
                    子指标
                    <span class="children-count" id="childrenCount">0</span>
                </h3>
            </div>
            <div class="info-card-content">
                <div class="children-nav-grid" id="childrenNavGrid">
                    <!-- 动态加载子指标导航 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        async function testIndicatorWithChildren() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="status info">正在测试指标 1.3.1...</div>';

            try {
                const response = await fetch('http://localhost:5001/api/indicators/1.3.1');
                const result = await response.json();

                if (result.success) {
                    const { indicator, children } = result.data;
                    
                    // 显示子指标模块
                    showChildrenModule(children);
                    
                    resultsDiv.innerHTML = `
                        <div class="status success">
                            <i class="fas fa-check-circle"></i>
                            ✅ 测试成功！指标 ${indicator.id} - ${indicator.name} 有 ${children.length} 个子指标
                        </div>
                        <div class="status info">
                            子指标模块已独立显示在下方，不再包含在"指标参考"模块中
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="status error">❌ 测试失败: ${result.error}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        async function testIndicatorWithoutChildren() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="status info">正在测试指标 1.1.1...</div>';

            try {
                const response = await fetch('http://localhost:5001/api/indicators/1.1.1');
                const result = await response.json();

                if (result.success) {
                    const { indicator, children } = result.data;
                    
                    // 隐藏子指标模块
                    hideChildrenModule();
                    
                    resultsDiv.innerHTML = `
                        <div class="status success">
                            <i class="fas fa-check-circle"></i>
                            ✅ 测试成功！指标 ${indicator.id} - ${indicator.name} 无子指标
                        </div>
                        <div class="status info">
                            子指标模块已隐藏，因为该指标没有子指标
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="status error">❌ 测试失败: ${result.error}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        function showChildrenModule(children) {
            const navCard = document.getElementById('childrenNavCard');
            const countElement = document.getElementById('childrenCount');
            const gridElement = document.getElementById('childrenNavGrid');

            if (children && children.length > 0) {
                navCard.style.display = 'block';
                countElement.textContent = children.length;

                const html = children.map(child => `
                    <div class="child-nav-item" onclick="alert('点击了子指标: ${child.id}')">
                        <div class="child-nav-info">
                            <div class="child-nav-id">${child.id}</div>
                            <div class="child-nav-name">${child.name}</div>
                        </div>
                        <div class="child-nav-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                `).join('');

                gridElement.innerHTML = html;
            } else {
                navCard.style.display = 'none';
            }
        }

        function hideChildrenModule() {
            const navCard = document.getElementById('childrenNavCard');
            navCard.style.display = 'none';
        }
    </script>
</body>
</html>
