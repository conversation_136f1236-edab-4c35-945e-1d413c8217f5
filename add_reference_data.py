#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量为指标添加参考数据的脚本
"""

import sqlite3
import sys

def add_reference_data():
    """为指标添加参考数据"""
    
    # 连接数据库
    conn = sqlite3.connect('hospital_indicator_system.db')
    cursor = conn.cursor()
    
    # 定义指标参考数据
    reference_data = {
        '1.1.2': {
            'indicator_definition': '医院实际开放并投入使用的床位数量，反映医院的实际服务能力和床位利用情况。',
            'statistical_scope': '医院所有科室当前实际开放使用的床位，包括普通病房、ICU、手术观察床等，排除因维修、改造等原因暂停使用的床位。',
            'data_sources': '医院信息系统(HIS)、床位管理系统、护理管理系统、医务科统计报表等。',
            'collection_frequency_detail': '实时监测，每日统计，月度汇总分析',
            'reference_value': '应≥核定床位数的85%，三甲医院建议保持在90%以上',
            'monitoring_analysis': '建议按科室、病区、床位类型、时间趋势等维度进行分析，重点关注床位开放率和使用效率'
        },
        '1.1.3': {
            'indicator_definition': '一定时期内医院床位被患者占用的比例，反映床位资源的利用效率和医院服务能力。',
            'statistical_scope': '统计期内所有开放床位的占用情况，包括普通病房、ICU等各类床位的平均使用率。',
            'data_sources': '医院信息系统(HIS)、住院管理系统、床位管理系统、统计信息系统等。',
            'collection_frequency_detail': '每日统计，月度、季度、年度汇总分析',
            'reference_value': '三级医院≥88%，二级医院≥85%，专科医院根据专业特点确定',
            'monitoring_analysis': '建议按科室、病区、床位类型、季节变化等维度分析，识别床位利用的规律和问题'
        },
        '1.2.1': {
            'indicator_definition': '医院卫生技术人员总数与实际开放床位数的比值，反映医院人力资源配置水平。',
            'statistical_scope': '包括医师、护士、医技人员、药师等所有卫生技术人员与实际开放床位的比例关系。',
            'data_sources': '人力资源管理系统、医院信息系统、统计报表等。',
            'collection_frequency_detail': '月度统计，季度分析，年度评估',
            'reference_value': '三级医院≥1.03:1，二级医院≥0.88:1',
            'monitoring_analysis': '建议按人员类别、科室分布、床位类型等维度分析人力资源配置的合理性'
        },
        '1.2.2': {
            'indicator_definition': '医院执业医师总数与实际开放床位数的比值，反映医院医师人力资源配置水平。',
            'statistical_scope': '包括所有在医院执业的医师（含主治医师、住院医师、进修医师等）与实际开放床位的比例。',
            'data_sources': '人力资源管理系统、医师执业注册系统、医院统计报表等。',
            'collection_frequency_detail': '月度统计，季度分析，年度评估',
            'reference_value': '三级医院≥0.4:1，二级医院≥0.3:1',
            'monitoring_analysis': '建议按科室、职称结构、专业分布等维度分析医师配置的合理性和充足性'
        },
        '1.2.3': {
            'indicator_definition': '医院注册护士总数与实际开放床位数的比值，反映医院护理人力资源配置水平。',
            'statistical_scope': '包括所有在医院注册执业的护士与实际开放床位的比例关系。',
            'data_sources': '人力资源管理系统、护士执业注册系统、护理部统计等。',
            'collection_frequency_detail': '月度统计，季度分析，年度评估',
            'reference_value': '三级医院≥0.4:1，二级医院≥0.4:1，ICU等重点科室≥2.5-3:1',
            'monitoring_analysis': '建议按科室、病区、护理级别、工作强度等维度分析护士配置情况'
        }
    }
    
    # 批量更新数据
    for indicator_id, data in reference_data.items():
        try:
            cursor.execute("""
                UPDATE indicators 
                SET 
                    indicator_definition = ?,
                    statistical_scope = ?,
                    data_sources = ?,
                    collection_frequency_detail = ?,
                    reference_value = ?,
                    monitoring_analysis = ?
                WHERE id = ?
            """, (
                data['indicator_definition'],
                data['statistical_scope'],
                data['data_sources'],
                data['collection_frequency_detail'],
                data['reference_value'],
                data['monitoring_analysis'],
                indicator_id
            ))
            
            if cursor.rowcount > 0:
                print(f"✅ 已更新指标 {indicator_id} 的参考数据")
            else:
                print(f"⚠️  指标 {indicator_id} 不存在或未更新")
                
        except Exception as e:
            print(f"❌ 更新指标 {indicator_id} 时出错: {e}")
    
    # 提交更改
    conn.commit()
    
    # 验证更新结果
    print("\n📊 验证更新结果:")
    cursor.execute("""
        SELECT id, name, 
               CASE WHEN indicator_definition IS NOT NULL AND indicator_definition != '' THEN '✅' ELSE '❌' END as 定义,
               CASE WHEN statistical_scope IS NOT NULL AND statistical_scope != '' THEN '✅' ELSE '❌' END as 范围,
               CASE WHEN reference_value IS NOT NULL AND reference_value != '' THEN '✅' ELSE '❌' END as 参考值
        FROM indicators 
        WHERE id IN ('1.1.1', '1.1.2', '1.1.3', '1.2.1', '1.2.2', '1.2.3')
        ORDER BY id
    """)
    
    results = cursor.fetchall()
    print(f"{'指标ID':<8} {'指标名称':<20} {'定义':<4} {'范围':<4} {'参考值':<6}")
    print("-" * 50)
    for row in results:
        print(f"{row[0]:<8} {row[1]:<20} {row[2]:<4} {row[3]:<4} {row[4]:<6}")
    
    # 关闭连接
    conn.close()
    print(f"\n🎉 批量更新完成！现在有 {len([r for r in results if r[2] == '✅'])} 个指标有完整的参考数据。")

if __name__ == "__main__":
    add_reference_data()
