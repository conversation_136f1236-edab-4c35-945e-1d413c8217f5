#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为缺失分子分母的指标添加默认信息
"""

import sqlite3

def add_missing_components():
    """
    为缺失分子分母的指标添加默认信息
    """
    
    # 定义一些常见指标的分子分母信息
    missing_components = {
        '1.1.1': {
            'name': '实际开放床位数',
            'numerator': {
                'name': '医院实际开放并可收治患者的床位数',
                'unit': '张',
                'lead_department': '医务科',
                'data_source': '医院信息系统',
                'logic_definition': '统计期内医院实际开放并投入使用的床位总数'
            },
            'denominator': {
                'name': '医院编制床位数',
                'unit': '张', 
                'lead_department': '医务科',
                'data_source': '医院基本信息',
                'logic_definition': '医院按照相关标准核定的床位总数'
            }
        },
        '1.1.2': {
            'name': '床位配置',
            'numerator': {
                'name': '各科室实际开放床位数',
                'unit': '张',
                'lead_department': '医务科',
                'data_source': '医院信息系统',
                'logic_definition': '各临床科室实际开放并投入使用的床位数'
            },
            'denominator': {
                'name': '医院总开放床位数',
                'unit': '张',
                'lead_department': '医务科', 
                'data_source': '医院信息系统',
                'logic_definition': '医院所有科室开放床位数的总和'
            }
        },
        '1.1.3': {
            'name': '床位使用率',
            'numerator': {
                'name': '出院患者占用总床日数',
                'unit': '床日',
                'lead_department': '医务科',
                'data_source': '医院信息系统',
                'logic_definition': '统计期内所有出院患者实际占用的床位天数总和'
            },
            'denominator': {
                'name': '同期实际开放总床日数',
                'unit': '床日',
                'lead_department': '医务科',
                'data_source': '医院信息系统', 
                'logic_definition': '统计期内医院实际开放床位数×统计天数'
            }
        },
        '1.2.1': {
            'name': '医师配置',
            'numerator': {
                'name': '医院在岗执业医师数',
                'unit': '人',
                'lead_department': '人事科',
                'data_source': '人事管理系统',
                'logic_definition': '统计时点医院在岗的执业医师总数'
            },
            'denominator': {
                'name': '医院实际开放床位数',
                'unit': '张',
                'lead_department': '医务科',
                'data_source': '医院信息系统',
                'logic_definition': '统计时点医院实际开放的床位总数'
            }
        },
        '1.2.2': {
            'name': '护士配置',
            'numerator': {
                'name': '医院在岗注册护士数',
                'unit': '人', 
                'lead_department': '护理部',
                'data_source': '人事管理系统',
                'logic_definition': '统计时点医院在岗的注册护士总数'
            },
            'denominator': {
                'name': '医院实际开放床位数',
                'unit': '张',
                'lead_department': '医务科',
                'data_source': '医院信息系统',
                'logic_definition': '统计时点医院实际开放的床位总数'
            }
        },
        '1.2.3': {
            'name': '药师配置',
            'numerator': {
                'name': '医院在岗执业药师数',
                'unit': '人',
                'lead_department': '药学部',
                'data_source': '人事管理系统', 
                'logic_definition': '统计时点医院在岗的执业药师总数'
            },
            'denominator': {
                'name': '医院实际开放床位数',
                'unit': '张',
                'lead_department': '医务科',
                'data_source': '医院信息系统',
                'logic_definition': '统计时点医院实际开放的床位总数'
            }
        },
        '1.2.4': {
            'name': '技师配置',
            'numerator': {
                'name': '医院在岗卫生技术人员数',
                'unit': '人',
                'lead_department': '人事科',
                'data_source': '人事管理系统',
                'logic_definition': '统计时点医院在岗的卫生技术人员总数'
            },
            'denominator': {
                'name': '医院实际开放床位数', 
                'unit': '张',
                'lead_department': '医务科',
                'data_source': '医院信息系统',
                'logic_definition': '统计时点医院实际开放的床位总数'
            }
        }
    }
    
    try:
        conn = sqlite3.connect("DATABASE-HOSPITAL/hospital_indicator_system.db")
        cursor = conn.cursor()
        
        print("=" * 80)
        print("🔧 为缺失分子分母的指标添加默认信息")
        print("=" * 80)
        
        added_count = 0
        
        for indicator_id, info in missing_components.items():
            # 检查指标是否存在
            cursor.execute("SELECT id, name FROM indicators WHERE id = ?", (indicator_id,))
            indicator = cursor.fetchone()
            
            if not indicator:
                print(f"⚠️  指标 {indicator_id} 不存在，跳过")
                continue
            
            # 检查是否已有分子分母
            cursor.execute("SELECT COUNT(*) FROM indicator_components WHERE indicator_id = ?", (indicator_id,))
            existing_count = cursor.fetchone()[0]
            
            if existing_count > 0:
                print(f"ℹ️  指标 {indicator_id} 已有分子分母数据，跳过")
                continue
            
            print(f"✅ 为指标 {indicator_id} - {indicator[1]} 添加分子分母数据")
            
            # 添加分子
            cursor.execute("""
                INSERT INTO indicator_components 
                (indicator_id, component_type, name, definition, unit, data_source, lead_department, logic_definition, sort_order, is_active, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            """, (
                indicator_id,
                'numerator',
                info['numerator']['name'],
                info['numerator']['name'],
                info['numerator']['unit'],
                info['numerator']['data_source'],
                info['numerator']['lead_department'],
                info['numerator']['logic_definition'],
                1
            ))
            
            # 添加分母
            cursor.execute("""
                INSERT INTO indicator_components 
                (indicator_id, component_type, name, definition, unit, data_source, lead_department, logic_definition, sort_order, is_active, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            """, (
                indicator_id,
                'denominator', 
                info['denominator']['name'],
                info['denominator']['name'],
                info['denominator']['unit'],
                info['denominator']['data_source'],
                info['denominator']['lead_department'],
                info['denominator']['logic_definition'],
                2
            ))
            
            added_count += 1
            print(f"   📊 分子: {info['numerator']['name']}")
            print(f"   📊 分母: {info['denominator']['name']}")
        
        conn.commit()
        
        print(f"\n✅ 成功为 {added_count} 个指标添加了分子分母数据")
        
        # 验证结果
        print(f"\n=== 验证结果 ===")
        cursor.execute("""
            SELECT i.id, i.name, COUNT(ic.id) as component_count
            FROM indicators i
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id
            WHERE i.id IN ('1.1.1', '1.1.2', '1.1.3', '1.2.1', '1.2.2', '1.2.3', '1.2.4')
            GROUP BY i.id, i.name
            ORDER BY i.id
        """)
        
        results = cursor.fetchall()
        for result in results:
            status = "✅" if result[2] > 0 else "❌"
            print(f"{status} {result[0]} - {result[1]}: {result[2]}个组成部分")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 添加分子分母数据时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    add_missing_components()

if __name__ == "__main__":
    main()
