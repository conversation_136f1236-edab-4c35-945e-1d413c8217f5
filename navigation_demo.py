#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前后端跳转功能演示
"""

from flask import Flask

app = Flask(__name__)

# ==================== 前端系统 ====================

@app.route('/')
def frontend_home():
    """前端主页"""
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>医院指标查看系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .container { max-width: 1000px; margin: 0 auto; }
            .header { background: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; position: relative; }
            .admin-switch { position: absolute; top: 20px; right: 20px; background: #ff6b6b; color: white; padding: 10px 20px; border-radius: 25px; text-decoration: none; font-weight: bold; box-shadow: 0 2px 10px rgba(0,0,0,0.2); transition: all 0.3s ease; }
            .admin-switch:hover { background: #ff5252; transform: scale(1.05); }
            .nav-buttons { display: flex; gap: 15px; justify-content: center; margin-top: 20px; flex-wrap: wrap; }
            .nav-btn { padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 14px; font-weight: 500; transition: all 0.3s ease; }
            .nav-btn-primary { background: #007bff; color: white; }
            .nav-btn-success { background: #28a745; color: white; }
            .nav-btn-warning { background: #ffc107; color: #212529; }
            .nav-btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
            .demo-info { background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-top: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <a href="/admin" class="admin-switch">🔧 后台管理</a>
                <h1 style="color: #333; margin: 0 0 10px 0;">🏥 医院指标查看系统</h1>
                <p style="color: #666; margin: 0; font-size: 16px;">前端系统 - 查看和浏览医院等级评审指标</p>

                <div class="nav-buttons">
                    <a href="/indicators" class="nav-btn nav-btn-primary">📊 浏览指标</a>
                    <a href="/chapters" class="nav-btn nav-btn-success">📚 按章节查看</a>
                    <a href="/search" class="nav-btn nav-btn-warning">🔍 搜索指标</a>
                </div>
            </div>

            <div class="demo-info">
                <h3 style="color: #333; margin-top: 0;">✨ 前后端跳转功能演示</h3>
                <p><strong>当前位置：</strong>🌐 前端系统</p>
                <p><strong>跳转按钮：</strong>右上角的 "🔧 后台管理" 按钮</p>
                <p><strong>功能说明：</strong></p>
                <ul>
                    <li>✅ 前端专注于数据查看和浏览</li>
                    <li>✅ 一键跳转到后台管理系统</li>
                    <li>✅ 保持用户友好的界面设计</li>
                    <li>✅ 响应式布局适配各种设备</li>
                </ul>
                <p style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 8px; color: #1976d2;">
                    💡 <strong>试试看：</strong>点击右上角的 "🔧 后台管理" 按钮，体验前端到后端的跳转！
                </p>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/indicators')
def frontend_indicators():
    """前端指标列表"""
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>指标列表 - 前端系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1000px; margin: 0 auto; }
            .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }
            .admin-switch { background: #ff6b6b; color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }
            .admin-switch:hover { background: #ff5252; }
            .demo-content { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div>
                    <h1 style="margin: 0; color: #333;">📊 指标列表</h1>
                    <p style="margin: 5px 0 0 0; color: #666;">前端系统 - 指标浏览页面</p>
                </div>
                <a href="/admin" class="admin-switch">🔧 后台管理</a>
            </div>

            <div class="demo-content">
                <h2>🎯 前端指标浏览功能</h2>
                <p>这里是前端系统的指标列表页面</p>
                <p>注意右上角的 "🔧 后台管理" 跳转按钮</p>
                <div style="margin-top: 30px;">
                    <a href="/" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">🏠 返回首页</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

# ==================== 后端管理系统 ====================

@app.route('/admin')
@app.route('/admin/')
def admin_home():
    """后端管理主页"""
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>后台管理系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .container { max-width: 1000px; margin: 0 auto; }
            .header { background: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; position: relative; }
            .frontend-switch { position: absolute; top: 20px; right: 20px; background: #28a745; color: white; padding: 10px 20px; border-radius: 25px; text-decoration: none; font-weight: bold; box-shadow: 0 2px 10px rgba(0,0,0,0.2); transition: all 0.3s ease; }
            .frontend-switch:hover { background: #218838; transform: scale(1.05); }
            .management-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
            .management-card { background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
            .management-card h3 { margin-top: 0; color: #333; }
            .btn { padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; font-size: 14px; }
            .btn-primary { background: #007bff; color: white; }
            .btn-success { background: #28a745; color: white; }
            .btn-warning { background: #ffc107; color: #212529; }
            .btn:hover { opacity: 0.9; transform: translateY(-1px); }
            .demo-info { background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <a href="/" class="frontend-switch">🌐 前端系统</a>
                <h1 style="color: #333; margin: 0 0 10px 0;">🔧 后台管理系统</h1>
                <p style="color: #666; margin: 0; font-size: 16px;">后端系统 - 医院指标数据管理和维护</p>
            </div>

            <div class="demo-info">
                <h3 style="color: #333; margin-top: 0;">✨ 前后端跳转功能演示</h3>
                <p><strong>当前位置：</strong>🔧 后台管理系统</p>
                <p><strong>跳转按钮：</strong>右上角的 "🌐 前端系统" 按钮</p>
                <p><strong>功能说明：</strong></p>
                <ul>
                    <li>✅ 后端专注于数据管理和维护</li>
                    <li>✅ 一键跳转到前端查看系统</li>
                    <li>✅ 专业的管理界面设计</li>
                    <li>✅ 完整的CRUD操作功能</li>
                </ul>
                <p style="margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px; color: #2e7d32;">
                    💡 <strong>试试看：</strong>点击右上角的 "🌐 前端系统" 按钮，体验后端到前端的跳转！
                </p>
            </div>

            <div class="management-grid">
                <div class="management-card">
                    <h3>📊 指标管理</h3>
                    <p>管理医院指标的基本信息、层级关系和分子分母组件</p>
                    <a href="/admin/indicators" class="btn btn-primary">指标列表</a>
                    <a href="/admin/components" class="btn btn-warning">组件管理</a>
                </div>
                <div class="management-card">
                    <h3>📚 结构管理</h3>
                    <p>管理指标体系的章节和小节结构</p>
                    <a href="/admin/chapters" class="btn btn-success">章节管理</a>
                    <a href="/admin/sections" class="btn btn-success">小节管理</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/admin/components')
@app.route('/admin/components/')
def admin_components():
    """后端组件管理"""
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>组件管理 - 后台管理系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1000px; margin: 0 auto; }
            .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }
            .nav-switches { display: flex; gap: 10px; }
            .nav-switch { padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }
            .frontend-switch { background: #28a745; color: white; }
            .admin-switch { background: #6c757d; color: white; }
            .nav-switch:hover { opacity: 0.9; }
            .demo-content { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
            .success-message { background: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div>
                    <h1 style="margin: 0; color: #333;">🧮 组件管理</h1>
                    <p style="margin: 5px 0 0 0; color: #666;">后端系统 - 管理指标的分子分母组件</p>
                </div>
                <div class="nav-switches">
                    <a href="/" class="nav-switch frontend-switch">🌐 前端系统</a>
                    <a href="/admin" class="nav-switch admin-switch">🔧 管理首页</a>
                </div>
            </div>

            <div class="demo-content">
                <div class="success-message">
                    ✅ 多重导航跳转功能演示成功！
                </div>

                <h2>🎉 导航功能完整演示</h2>
                <p>您已成功体验了完整的前后端跳转功能：</p>

                <ul style="text-align: left; max-width: 400px; margin: 20px auto;">
                    <li>✅ 前端首页 → 后台管理</li>
                    <li>✅ 后台管理 → 前端系统</li>
                    <li>✅ 组件管理页面的双重导航</li>
                    <li>✅ 跨页面导航保持一致性</li>
                </ul>

                <div style="margin-top: 30px;">
                    <a href="/admin" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px;">🔧 管理首页</a>
                    <a href="/" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px;">🌐 前端首页</a>
                </div>

                <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <h4 style="margin-top: 0; color: #333;">🎯 跳转功能特色</h4>
                    <div style="text-align: left; max-width: 500px; margin: 0 auto;">
                        <p><strong>🎨 视觉设计：</strong></p>
                        <ul>
                            <li>前端跳转按钮：红色圆角设计</li>
                            <li>后端跳转按钮：绿色圆角设计</li>
                            <li>悬停效果：缩放和颜色变化</li>
                        </ul>
                        <p><strong>🔄 功能特性：</strong></p>
                        <ul>
                            <li>固定位置：右上角易于找到</li>
                            <li>一键切换：无需复杂操作</li>
                            <li>多重导航：支持多个跳转选项</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🎯 前后端跳转功能演示系统")
    print("🌐 前端系统: http://localhost:5001")
    print("🔧 后端管理: http://localhost:5001/admin")
    print("🧮 组件管理: http://localhost:5001/admin/components")
    print("✨ 完整的前后端导航演示")
    print("=" * 50)

    app.run(debug=True, host='0.0.0.0', port=5002)
