#!/usr/bin/env python3
"""
诊断后端指标管理页面的JavaScript错误
"""

import requests
from bs4 import BeautifulSoup

def test_backend_pages():
    """测试后端页面的加载情况"""
    print("🔍 测试后端页面加载情况...")
    
    test_urls = [
        ('/admin', '管理首页'),
        ('/admin/indicators', '指标管理页面'),
        ('/admin/indicators/1.1.1', '指标详情页面'),
        ('/admin/chapters', '章节管理页面'),
        ('/admin/sections', '小节管理页面')
    ]
    
    for url, name in test_urls:
        try:
            response = requests.get(f'http://localhost:5001{url}')
            print(f"\n📊 {name} ({url}):")
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 页面加载成功")
                
                # 检查页面内容
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 检查是否有JavaScript错误相关的元素
                scripts = soup.find_all('script')
                print(f"   脚本数量: {len(scripts)}")
                
                # 检查关键DOM元素
                key_elements = [
                    'notification',
                    'notification-icon', 
                    'notification-message',
                    'page-title',
                    'page-subtitle'
                ]
                
                missing_elements = []
                for element_id in key_elements:
                    element = soup.find(id=element_id) or soup.find(class_=element_id)
                    if not element:
                        missing_elements.append(element_id)
                
                if missing_elements:
                    print(f"   ⚠️  缺少元素: {', '.join(missing_elements)}")
                else:
                    print(f"   ✅ 关键元素完整")
                    
            else:
                print(f"   ❌ 页面加载失败")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

def check_javascript_elements():
    """检查JavaScript中可能出错的元素"""
    print("\n🔍 检查JavaScript中可能出错的元素...")
    
    try:
        with open('static/js/app.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # 查找所有textContent设置的地方
        import re
        textcontent_patterns = [
            r'\.textContent\s*=',
            r'textContent\s*=',
            r'getElementById\([\'"][^\'"]*[\'"]\)\.textContent',
            r'querySelector\([\'"][^\'"]*[\'"]\)\.textContent'
        ]
        
        print("📝 JavaScript中设置textContent的位置:")
        for i, line in enumerate(js_content.split('\n'), 1):
            for pattern in textcontent_patterns:
                if re.search(pattern, line):
                    print(f"   行 {i}: {line.strip()}")
        
        # 查找可能的问题元素
        problem_elements = [
            'notification-icon',
            'notification-message', 
            'page-title',
            'page-subtitle',
            'target-value-display',
            'current-value-display',
            'completion-rate-display'
        ]
        
        print(f"\n🎯 检查问题元素的使用:")
        for element in problem_elements:
            count = js_content.count(element)
            if count > 0:
                print(f"   {element}: 使用 {count} 次")
                
                # 查找具体使用位置
                for i, line in enumerate(js_content.split('\n'), 1):
                    if element in line and 'textContent' in line:
                        print(f"     行 {i}: {line.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查JavaScript文件失败: {e}")
        return False

def check_html_templates():
    """检查HTML模板中的元素定义"""
    print("\n🔍 检查HTML模板中的元素定义...")
    
    template_files = [
        'templates/admin/base.html',
        'templates/admin/indicators.html', 
        'templates/admin/indicator_detail.html',
        'templates/admin/dashboard.html'
    ]
    
    required_elements = [
        'notification',
        'notification-icon',
        'notification-message'
    ]
    
    for template_file in template_files:
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📄 {template_file}:")
            
            for element in required_elements:
                # 检查id和class
                id_found = f'id="{element}"' in content or f"id='{element}'" in content
                class_found = f'class="{element}"' in content or f"class='{element}'" in content or f'class=".*{element}.*"' in content
                
                if id_found or class_found:
                    print(f"   ✅ {element}: {'ID' if id_found else ''}{'CLASS' if class_found else ''}")
                else:
                    print(f"   ❌ {element}: 未找到")
                    
        except FileNotFoundError:
            print(f"   ⚠️  文件不存在: {template_file}")
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")

def analyze_error_pattern():
    """分析错误模式"""
    print("\n🎯 错误模式分析")
    print("=" * 60)
    
    print("📋 可能的错误原因:")
    print("1. 🎨 HTML模板缺少必要的DOM元素")
    print("   - notification相关元素")
    print("   - page-title, page-subtitle元素")
    print("   - 指标详情页面的显示元素")
    
    print("\n2. 🔄 JavaScript执行时机问题")
    print("   - DOM元素尚未加载完成就尝试访问")
    print("   - 异步加载导致的时序问题")
    
    print("\n3. 📊 新增指标参考功能的影响")
    print("   - 新增的DOM元素ID冲突")
    print("   - JavaScript逻辑与新HTML结构不匹配")
    print("   - CSS选择器失效")
    
    print("\n4. 🔧 模板继承问题")
    print("   - 基础模板缺少必要元素")
    print("   - 子模板覆盖了重要元素")
    
    print("\n💡 建议的解决步骤:")
    print("1. 检查admin/base.html是否包含notification元素")
    print("2. 确认所有页面都正确继承了基础模板")
    print("3. 添加JavaScript错误处理和元素存在性检查")
    print("4. 在设置textContent前检查元素是否存在")

def suggest_fixes():
    """建议修复方案"""
    print("\n🔧 建议的修复方案")
    print("=" * 60)
    
    print("📝 方案1: 添加元素存在性检查")
    print("""
// 修改前
element.textContent = value;

// 修改后  
if (element) {
    element.textContent = value;
} else {
    console.warn('Element not found:', elementId);
}
""")
    
    print("📝 方案2: 创建安全的DOM操作函数")
    print("""
function safeSetTextContent(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
        return true;
    } else {
        console.warn('Element not found:', elementId);
        return false;
    }
}
""")
    
    print("📝 方案3: 确保基础模板包含必要元素")
    print("""
<!-- 在admin/base.html中添加 -->
<div id="notification" class="notification">
    <i class="notification-icon"></i>
    <span class="notification-message"></span>
</div>
""")

def main():
    """主诊断函数"""
    print("🎯 后端指标管理页面JavaScript错误诊断")
    print("=" * 70)
    
    # 测试后端页面加载
    test_backend_pages()
    
    # 检查JavaScript元素
    check_javascript_elements()
    
    # 检查HTML模板
    check_html_templates()
    
    # 分析错误模式
    analyze_error_pattern()
    
    # 建议修复方案
    suggest_fixes()
    
    print("\n" + "=" * 70)
    print("📊 诊断完成")
    print("请根据上述分析结果修复相关问题")

if __name__ == "__main__":
    main()
