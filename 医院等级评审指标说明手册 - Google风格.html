<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医院等级评审指标说明手册 - Google风格</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #1a73e8;
            --primary-dark: #1765cc;
            --primary-light: #e8f0fe;
            --blue-50: #e8f1fe;
            --secondary: #34a853;
            --warning: #fbbc04;
            --error: #ea4335;
            --gray-50: #f8f9fa;
            --gray-100: #f1f3f4;
            --gray-200: #e8eaed;
            --gray-300: #dadce0;
            --gray-400: #bdc1c6;
            --gray-500: #9aa0a6;
            --gray-600: #80868b;
            --gray-700: #5f6368;
            --gray-800: #3c4043;
            --gray-900: #202124;
            --white: #ffffff;
            --shadow-1: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
            --shadow-2: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
            --shadow-3: 0 1px 3px 0 rgba(60, 64, 67, 0.3), 0 4px 8px 3px rgba(60, 64, 67, 0.15);
            --border-radius: 8px;
            --transition: all 0.2s ease;
        }

        @font-face {
            font-family: 'Google Sans';
            src: local('Google Sans'), url(https://fonts.gstatic.com/s/googlesans/v14/4UaGrENHsxJlGDuGo1OIlL3Owp4.woff2) format('woff2');
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Google Sans', 'Roboto', 'Arial', sans-serif;
        }

        body {
            background-color: var(--gray-50);
            color: var(--gray-900);
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        /* 顶部导航 */
        .header {
            background-color: var(--white);
            border-bottom: 1px solid var(--gray-300);
            padding: 8px 16px;
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .menu-toggle {
            background: none;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--gray-700);
            margin-right: 16px;
            transition: var(--transition);
        }

        .menu-toggle:hover {
            background-color: var(--gray-100);
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: 500;
            color: var(--gray-900);
            text-decoration: none;
            margin-right: 24px;
        }

        .logo-icon {
            color: var(--primary);
            margin-right: 12px;
            font-size: 24px;
        }

        .search-wrapper {
            flex: 1;
            max-width: 720px;
            position: relative;
        }

        .search-input {
            width: 100%;
            height: 48px;
            border: none;
            border-radius: 24px;
            background-color: var(--gray-100);
            padding: 0 24px 0 48px;
            font-size: 16px;
            color: var(--gray-900);
            transition: var(--transition);
        }

        .search-input:focus {
            background-color: var(--white);
            box-shadow: var(--shadow-1);
            outline: none;
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-600);
            font-size: 18px;
        }

        .header-right {
            display: flex;
            align-items: center;
            margin-left: auto;
        }

        .header-btn {
            background: none;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--gray-700);
            margin-left: 8px;
            transition: var(--transition);
        }

        .header-btn:hover {
            background-color: var(--gray-100);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary);
            color: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            margin-left: 16px;
            cursor: pointer;
        }

        /* 主内容区域 */
        .main-container {
            display: flex;
            flex: 1;
        }

        /* 侧边导航 */
        .sidebar {
            width: 256px;
            background-color: var(--white);
            border-right: 1px solid var(--gray-300);
            overflow-y: auto;
            padding: 12px 0;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed {
            width: 72px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: var(--gray-800);
            text-decoration: none;
            cursor: pointer;
            border-radius: 0 24px 24px 0;
            margin-right: 12px;
            transition: var(--transition);
        }

        .nav-item:hover {
            background-color: var(--gray-100);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary);
            font-weight: 500;
        }

        .nav-icon {
            margin-right: 24px;
            width: 24px;
            text-align: center;
            font-size: 18px;
        }

        .sidebar.collapsed .nav-icon {
            margin-right: 0;
        }

        .sidebar.collapsed .nav-text {
            display: none;
        }

        .nav-section {
            margin-top: 24px;
            padding: 0 24px 8px;
            font-size: 14px;
            font-weight: 500;
            color: var(--gray-700);
        }

        .sidebar.collapsed .nav-section {
            display: none;
        }

        /* 主内容 */
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 400;
            color: var(--gray-900);
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: var(--gray-700);
        }

        /* 工具栏 */
        .toolbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
        }

        .view-toggle {
            display: flex;
            background-color: var(--gray-100);
            border-radius: 24px;
            padding: 4px;
        }

        .view-btn {
            background: none;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            color: var(--gray-800);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
        }

        .view-btn i {
            margin-right: 8px;
        }

        .view-btn.active {
            background-color: var(--white);
            color: var(--gray-900);
            box-shadow: var(--shadow-1);
        }

        .action-btns {
            display: flex;
            gap: 12px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 24px;
            border-radius: 24px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            border: none;
        }

        .btn-icon {
            margin-right: 8px;
        }

        .btn-outline {
            background-color: var(--white);
            color: var(--gray-900);
            border: 1px solid var(--gray-300);
        }

        .btn-outline:hover {
            background-color: var(--gray-100);
        }

        .btn-outline.active {
            background-color: var(--primary-light);
            border-color: var(--primary);
            color: var(--primary);
        }

        .filter-badge {
            position: relative;
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--primary);
            margin-left: 5px;
        }

        .btn-primary {
            background-color: var(--primary);
            color: var(--white);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-1);
            overflow: hidden;
            transition: var(--transition);
            height: 100%;
        }

        .card:hover {
            box-shadow: var(--shadow-2);
        }

        .card-body {
            padding: 16px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--gray-900);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .card-icon {
            color: var(--primary);
            margin-right: 12px;
            font-size: 20px;
        }

        .card-content {
            color: var(--gray-700);
            font-size: 14px;
            margin-bottom: 16px;
        }

        .card-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            color: var(--gray-600);
            padding-top: 12px;
            border-top: 1px solid var(--gray-200);
        }

        .card-id {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .id-badge {
            color: var(--primary);
            font-weight: 500;
            font-size: 14px;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .action-icon {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-700);
            cursor: pointer;
            transition: var(--transition);
        }

        .action-icon:hover {
            background-color: var(--gray-100);
            color: var(--gray-900);
        }

        /* 列表视图 */
        .list-view {
            display: none;
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-1);
            overflow: hidden;
            margin-bottom: 32px;
        }

        .list-header {
            display: flex;
            align-items: center;
            padding: 16px;
            background-color: var(--gray-50);
            border-bottom: 1px solid var(--gray-200);
            font-weight: 500;
            color: var(--gray-700);
            font-size: 14px;
        }

        .list-col-id {
            width: 80px;
        }

        .list-col-title {
            flex: 2;
        }

        .list-col-desc {
            flex: 2;
        }

        .list-col-tags {
            flex: 1;
        }

        .list-col-actions {
            width: 80px;
            text-align: right;
        }

        .list-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid var(--gray-200);
            transition: var(--transition);
        }

        .list-item:last-child {
            border-bottom: none;
        }

        .list-item:hover {
            background-color: var(--gray-50);
        }

        .list-item-id {
            width: 80px;
            font-size: 14px;
        }

        .list-item-badge {
            color: var(--primary);
            font-size: 14px;
            font-weight: 500;
        }

        .list-item-title {
            flex: 2;
            font-weight: 500;
            color: var(--gray-900);
            font-size: 14px;
        }

        .list-item-desc {
            flex: 2;
            color: var(--gray-700);
            font-size: 14px;
        }

        .list-item-tags {
            flex: 1;
        }

        .list-item-tags .indicator-tags {
            margin-top: 0;
        }

        .list-item-actions {
            width: 80px;
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        /* 详情面板 */
        .detail-panel {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-1);
            overflow: hidden;
            margin-top: 32px;
        }

        .detail-header {
            padding: 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--gray-200);
        }

        .detail-title {
            font-size: 20px;
            font-weight: 500;
            color: var(--gray-900);
            display: flex;
            align-items: flex-start;
        }

        .detail-title-content {
            display: flex;
            flex-direction: column;
        }

        .detail-title-text {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .detail-title-icon {
            width: 40px;
            height: 40px;
            background-color: var(--primary-light);
            color: var(--primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
        }

        .detail-actions {
            display: flex;
            gap: 12px;
        }

        .detail-body {
            padding: 24px;
        }

        .detail-section {
            margin-bottom: 24px;
        }

        .detail-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--gray-900);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .section-title-icon {
            color: var(--primary);
            margin-right: 8px;
        }

        .section-content {
            background-color: var(--gray-50);
            border-radius: var(--border-radius);
            padding: 16px;
        }

        .section-content p {
            margin-bottom: 12px;
            font-size: 14px;
            color: var(--gray-800);
        }

        .section-content p:last-child {
            margin-bottom: 0;
        }

        .data-row {
            display: flex;
            margin-bottom: 12px;
        }

        .data-row:last-child {
            margin-bottom: 0;
        }

        .data-label {
            width: 120px;
            font-weight: 500;
            color: var(--gray-800);
            font-size: 14px;
        }

        .data-value {
            flex: 1;
            color: var(--gray-700);
            font-size: 14px;
        }

        .progress-container {
            margin-top: 8px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background-color: var(--gray-200);
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--primary);
            border-radius: 2px;
        }

        .progress-fill.warning {
            background-color: var(--warning);
        }

        .progress-fill.error {
            background-color: var(--error);
        }

        .progress-fill.success {
            background-color: var(--secondary);
        }

        /* 表格 */
        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            text-align: left;
            padding: 12px 16px;
            font-size: 14px;
        }

        th {
            background-color: var(--gray-50);
            font-weight: 500;
            color: var(--gray-800);
            border-bottom: 1px solid var(--gray-200);
        }

        td {
            color: var(--gray-700);
            border-bottom: 1px solid var(--gray-200);
        }

        tr:last-child td {
            border-bottom: none;
        }

        /* 子指标样式 */
        .parent-card {
            position: relative;
        }

        .has-children {
            margin-left: 8px;
            color: var(--primary);
            cursor: pointer;
            font-size: 14px;
        }

        .parent-indicator-badge {
            margin-left: 12px;
            color: var(--primary);
            font-size: 14px;
            display: inline-flex;
            align-items: center;
        }

        .parent-indicator-badge i {
            margin-right: 4px;
        }

        .child-indicators {
            display: none;
            background-color: var(--gray-50);
            border-top: 1px solid var(--gray-200);
            padding: 12px;
            margin-top: 8px;
        }

        .parent-card.expanded .child-indicators {
            display: block;
        }

        .child-card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-1);
            margin-bottom: 8px;
            transition: var(--transition);
        }

        .child-card:last-child {
            margin-bottom: 0;
        }

        .child-card:hover {
            box-shadow: var(--shadow-2);
        }

        .child-card-body {
            padding: 12px;
        }

        .child-card-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--gray-900);
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }

        .child-card-icon {
            color: var(--primary);
            margin-right: 8px;
            font-size: 16px;
        }

        .child-card-content {
            color: var(--gray-700);
            font-size: 13px;
            margin-bottom: 8px;
        }

        .child-card-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            color: var(--gray-600);
        }

        /* 列表视图子指标 */
        .parent-list-item {
            background-color: var(--gray-50);
            font-weight: 500;
        }

        .child-list-items {
            display: none;
            background-color: var(--white);
            border-left: 3px solid var(--primary-light);
            margin-left: 40px;
        }

        .child-list-items.expanded {
            display: block;
        }

        .child-list-item {
            padding-left: 24px;
            background-color: var(--white);
            border-bottom: 1px dashed var(--gray-200) !important;
        }

        .child-list-item:last-child {
            border-bottom: none !important;
        }

        .child-badge {
            color: var(--primary);
            font-size: 13px;
        }

        /* 子指标选项卡 */
        .sub-indicators-tabs {
            background-color: var(--white);
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .tab-header {
            display: flex;
            border-bottom: 1px solid var(--gray-200);
            overflow-x: auto;
            white-space: nowrap;
            -webkit-overflow-scrolling: touch;
        }

        .tab-btn {
            padding: 12px 16px;
            background: none;
            border: none;
            font-size: 14px;
            color: var(--gray-700);
            cursor: pointer;
            transition: var(--transition);
            border-bottom: 2px solid transparent;
        }

        .tab-btn.active {
            color: var(--primary);
            border-bottom: 2px solid var(--primary);
            font-weight: 500;
        }

        .tab-content {
            display: none;
            padding: 16px;
        }

        .tab-content.active {
            display: block;
        }

        .sub-indicator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .sub-indicator-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--gray-900);
            display: flex;
            align-items: center;
        }

        .sub-indicator-title .id-badge {
            margin-right: 8px;
            background-color: transparent;
            padding: 0;
            border-radius: 0;
        }

        .sub-indicator-value {
            font-size: 18px;
            font-weight: 500;
            color: var(--primary);
        }

        .sub-indicator-desc {
            margin-bottom: 16px;
            color: var(--gray-700);
            font-size: 14px;
        }

        .sub-indicator-data {
            background-color: var(--gray-50);
            border-radius: var(--border-radius);
            padding: 12px;
        }

        /* 视图模式切换 */
        .view-mode-toggle {
            display: flex;
            margin-right: 16px;
        }

        .view-mode-btn {
            border-radius: 0;
            margin: 0;
            border-right: none;
        }

        .view-mode-btn:first-child {
            border-radius: 24px 0 0 24px;
        }

        .view-mode-btn:last-child {
            border-radius: 0 24px 24px 0;
            border-right: 1px solid var(--gray-300);
        }

        .view-mode-btn.active {
            background-color: var(--primary-light);
            color: var(--primary);
            border-color: var(--primary-light);
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            overflow-y: auto;
            padding: 20px;
        }

        .modal-overlay.active {
            display: flex;
        }

        .modal-container {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-3);
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            animation: modal-appear 0.3s ease;
        }

        @keyframes modal-appear {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            padding: 16px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--gray-200);
            position: sticky;
            top: 0;
            background-color: var(--white);
            z-index: 10;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .modal-title {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: 500;
            color: var(--gray-900);
        }

        .modal-title-text {
            margin-left: 16px;
            display: flex;
            flex-direction: column;
        }

        .modal-title-text > div:first-child {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .modal-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .modal-close-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: none;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--gray-700);
            transition: var(--transition);
            margin-left: 8px;
        }

        .modal-close-btn:hover {
            background-color: var(--gray-100);
            color: var(--gray-900);
        }

        .modal-body {
            padding: 24px;
            overflow-y: auto;
            flex: 1;
        }

        .btn-sm {
            padding: 6px 16px;
            font-size: 13px;
        }

        /* 详情面板显示/隐藏 */
        .detail-panel.hidden {
            display: none;
        }

        /* 弹窗尺寸 */
        .modal-sm {
            max-width: 500px;
        }

        .modal-md {
            max-width: 700px;
        }

        /* 表单样式 */
        .section-subtitle {
            font-size: 14px;
            font-weight: 500;
            color: var(--gray-800);
            margin-bottom: 12px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            font-size: 13px;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 6px;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--gray-300);
            border-radius: 4px;
            font-size: 14px;
            color: var(--gray-800);
            transition: var(--transition);
        }

        .form-control:focus {
            border-color: var(--primary);
            outline: none;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }

        .select-wrapper {
            position: relative;
        }

        .select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-600);
            pointer-events: none;
        }

        .input-group {
            display: flex;
            align-items: center;
        }

        .input-group .form-control {
            flex: 1;
            border-radius: 4px 0 0 4px;
        }

        .input-group-text {
            padding: 8px 12px;
            background-color: var(--gray-100);
            border: 1px solid var(--gray-300);
            border-left: none;
            border-radius: 0 4px 4px 0;
            font-size: 14px;
            color: var(--gray-700);
        }

        .btn-block {
            width: 100%;
        }

        /* 标签管理样式 */
        .tags-container, .dept-container {
            padding: 16px 0;
        }

        .tags-list, .depts-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 24px;
        }

        .tag-item {
            display: inline-flex;
            align-items: center;
            padding: 4px 0;
            color: var(--primary);
            font-size: 13px;
            font-weight: 500;
            position: relative;
            margin-right: 12px;
        }

        .tag-item i.fa-tag {
            margin-right: 6px;
        }

        .tag-actions {
            display: flex;
            align-items: center;
            margin-left: 6px;
        }

        .tag-item .tag-info,
        .tag-item .tag-remove {
            cursor: pointer;
            font-size: 12px;
            padding: 2px 4px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .tag-item .tag-info {
            color: var(--primary);
            margin-right: 2px;
        }

        .tag-item .tag-info:hover {
            background-color: rgba(26, 115, 232, 0.1);
        }

        .tag-item .tag-remove:hover {
            color: var(--error);
        }

        /* 筛选面板样式 */
        .filter-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .filter-section {
            padding-bottom: 16px;
            border-bottom: 1px solid var(--gray-200);
        }

        .filter-section:last-child {
            border-bottom: none;
        }

        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 20px;
            padding-top: 16px;
            border-top: 1px solid var(--gray-200);
        }

        .status-dot {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 6px;
        }

        .status-dot.warning {
            background-color: var(--warning);
        }

        .status-dot.danger {
            background-color: var(--error);
        }

        /* 范围滑块样式 */
        .range-slider-container {
            padding: 0 10px;
            margin-top: 10px;
        }

        .range-values {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
            color: var(--gray-700);
        }

        .range-slider {
            position: relative;
            height: 30px;
        }

        .range-slider-input {
            position: absolute;
            width: 100%;
            height: 5px;
            background: none;
            pointer-events: none;
            -webkit-appearance: none;
            appearance: none;
            z-index: 3;
        }

        .range-slider-input::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--primary);
            cursor: pointer;
            pointer-events: auto;
            margin-top: -7px;
            border: 2px solid var(--white);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .range-slider-track {
            position: absolute;
            width: 100%;
            height: 5px;
            background: var(--gray-300);
            border-radius: 5px;
            z-index: 1;
            top: 50%;
            transform: translateY(-50%);
        }

        .range-slider-fill {
            position: absolute;
            height: 5px;
            background: var(--primary);
            border-radius: 5px;
            z-index: 2;
            top: 50%;
            transform: translateY(-50%);
        }

        /* 指标标签样式 */
        .indicator-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }

        .indicator-tag {
            display: inline-flex;
            align-items: center;
            font-size: 12px;
            color: var(--gray-700);
            white-space: nowrap;
            margin-right: 8px;
        }

        .indicator-tag i {
            margin-right: 4px;
            font-size: 10px;
        }

        /* 标签备注弹窗样式 */
        .tag-description-content {
            background-color: var(--gray-50);
            border-radius: var(--border-radius);
            padding: 16px;
            margin-bottom: 16px;
            border-left: 4px solid var(--primary);
        }

        .tag-description-content p {
            margin: 0;
            line-height: 1.6;
            color: var(--gray-800);
        }

        .color-picker {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .color-option {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            cursor: pointer;
            transition: var(--transition);
            border: 2px solid transparent;
        }

        .color-option.selected {
            border-color: var(--gray-700);
        }

        /* 科室管理样式 */
        .dept-item {
            display: inline-flex;
            align-items: center;
            padding: 4px 10px;
            background-color: var(--gray-100);
            color: var(--gray-800);
            border-radius: 16px;
            font-size: 13px;
            font-weight: 500;
        }

        .dept-item .dept-remove {
            margin-left: 6px;
            cursor: pointer;
            font-size: 14px;
            padding: 2px 4px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .dept-item .dept-remove:hover {
            color: var(--white);
            background-color: var(--error);
        }

        /* 关联指标样式 */
        .related-indicators-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .related-indicators-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            max-height: 200px;
            overflow-y: auto;
            padding: 4px;
            border: 1px solid var(--gray-200);
            border-radius: 4px;
            background-color: var(--white);
        }

        .related-indicator-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            border-radius: 4px;
            background-color: var(--gray-100);
            transition: all 0.2s ease;
        }

        .related-indicator-item.selected {
            background-color: var(--blue-50);
            border-left: 3px solid var(--primary);
        }

        .related-indicator-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .related-indicator-id {
            font-weight: 500;
            color: var(--gray-700);
        }

        .related-indicator-name {
            color: var(--gray-800);
        }

        .related-indicator-remove {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: none;
            background-color: transparent;
            color: var(--gray-500);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .related-indicator-remove:hover {
            background-color: var(--error);
            color: var(--white);
        }

        .related-indicators-actions {
            display: flex;
            justify-content: flex-end;
        }

        /* 关联指标选择弹窗 */
        .related-indicator-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1100;
            overflow-y: auto;
        }

        .related-indicator-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .related-indicator-modal-content {
            width: 600px;
            max-width: 90%;
            background-color: var(--white);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            display: flex;
            flex-direction: column;
            max-height: 80vh;
        }

        .related-indicator-modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            border-bottom: 1px solid var(--gray-200);
        }

        .related-indicator-modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 500;
            color: var(--gray-900);
        }

        .related-indicator-modal-close {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--gray-500);
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .related-indicator-modal-close:hover {
            background-color: var(--gray-100);
            color: var(--gray-700);
        }

        .related-indicator-modal-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .related-indicator-search {
            position: relative;
        }

        .related-indicator-search .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
        }

        .related-indicator-categories {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .related-indicator-category {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .related-indicator-category-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid var(--gray-200);
        }

        .related-indicator-category-name {
            font-weight: 500;
            color: var(--gray-800);
        }

        .related-indicator-category-count {
            background-color: var(--gray-200);
            color: var(--gray-700);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .related-indicator-category-items {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding-left: 8px;
        }

        .related-indicator-select-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .related-indicator-select-item:hover {
            background-color: var(--gray-100);
        }

        .related-indicator-select-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .related-indicator-select-id {
            font-weight: 500;
            color: var(--gray-700);
            min-width: 50px;
        }

        .related-indicator-select-name {
            color: var(--gray-800);
        }

        .related-indicator-modal-footer {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 12px;
            padding: 16px 20px;
            border-top: 1px solid var(--gray-200);
        }

        .tabs-container .tabs-header {
            display: flex;
            border-bottom: 1px solid var(--gray-200);
            margin-bottom: 16px;
        }

        .tabs-container .tab-btn {
            padding: 8px 16px;
            background: none;
            border: none;
            font-size: 14px;
            color: var(--gray-700);
            cursor: pointer;
            transition: var(--transition);
            border-bottom: 2px solid transparent;
        }

        .tabs-container .tab-btn.active {
            color: var(--primary);
            border-bottom: 2px solid var(--primary);
            font-weight: 500;
        }

        /* 操作维护样式 */
        .operation-section {
            margin-bottom: 24px;
            padding: 16px;
            background-color: var(--gray-50);
            border-radius: var(--border-radius);
        }

        .radio-group, .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 16px;
        }

        .radio-item, .checkbox-item {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .radio-label, .checkbox-label {
            margin-left: 8px;
            font-size: 14px;
            color: var(--gray-800);
        }

        /* 复选框样式 */
        .checkbox-wrapper {
            position: relative;
            display: inline-block;
            width: 18px;
            height: 18px;
        }

        .checkbox-wrapper input[type="checkbox"] {
            opacity: 0;
            width: 0;
            height: 0;
            position: absolute;
        }

        .checkbox-wrapper label {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            border-radius: 3px;
            border: 2px solid var(--gray-400);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .checkbox-wrapper label:after {
            content: '';
            position: absolute;
            top: 4px;
            left: 3px;
            width: 8px;
            height: 4px;
            border: 2px solid var(--white);
            border-top: none;
            border-right: none;
            transform: rotate(-45deg);
            opacity: 0;
            transition: all 0.2s ease;
        }

        .checkbox-wrapper input[type="checkbox"]:checked + label {
            background-color: var(--primary);
            border-color: var(--primary);
        }

        .checkbox-wrapper input[type="checkbox"]:checked + label:after {
            opacity: 1;
        }

        .checkbox-wrapper input[type="checkbox"]:focus + label {
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }

        .code-editor {
            font-family: monospace;
            font-size: 13px;
        }

        .verification-flow {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 16px;
        }

        .flow-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-1);
        }

        .flow-step {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: var(--primary);
            color: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            margin-right: 12px;
        }

        .flow-content {
            flex: 1;
        }

        .flow-title {
            font-weight: 500;
            color: var(--gray-900);
            font-size: 14px;
        }

        .flow-desc {
            color: var(--gray-700);
            font-size: 13px;
        }

        .flow-arrow {
            display: flex;
            justify-content: center;
            color: var(--gray-400);
        }

        .threshold-settings {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .threshold-item {
            display: flex;
            align-items: center;
        }

        .threshold-label {
            width: 60px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            margin-right: 12px;
        }

        .threshold-label.warning {
            background-color: var(--warning);
            color: var(--white);
        }

        .threshold-label.danger {
            background-color: var(--error);
            color: var(--white);
        }

        .threshold-inputs {
            display: flex;
            gap: 12px;
            flex: 1;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 24px;
            padding-top: 16px;
            border-top: 1px solid var(--gray-200);
        }

        /* 自定义tooltip样式 */
        .custom-tooltip {
            position: relative;
            display: inline-block;
        }

        .custom-tooltip .tooltip-text {
            visibility: hidden;
            width: 180px;
            background-color: var(--gray-800);
            color: var(--white);
            text-align: left;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1000;
            left: 100%;
            top: -5px;
            margin-left: 5px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            line-height: 1.4;
            box-shadow: var(--shadow-2);
            pointer-events: none;
            word-wrap: break-word;
            overflow-wrap: break-word;
            max-height: 150px;
            overflow-y: auto;
        }

        /* 为左侧面板的tooltip特殊处理 */
        #sidebar-tags-container .custom-tooltip .tooltip-text {
            left: 60px;
            top: 0;
        }

        .custom-tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            top: 15px;
            right: 100%;
            margin-top: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: transparent var(--gray-800) transparent transparent;
        }

        .custom-tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        /* 左侧标签项样式增强 */
        #sidebar-tags-container .nav-item {
            position: relative;
        }

        /* 响应式设计 */
        @media (max-width: 992px) {
            .card-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }

            .search-wrapper {
                max-width: 320px;
            }

            .tab-header {
                flex-wrap: wrap;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -256px;
                top: 60px;
                bottom: 0;
                z-index: 90;
                transition: left 0.3s ease;
            }

            .sidebar.open {
                left: 0;
            }

            .content {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }

            .search-wrapper {
                max-width: none;
            }

            .logo-text {
                display: none;
            }

            .toolbar {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .action-btns {
                width: 100%;
                justify-content: flex-end;
            }

            .child-list-items {
                margin-left: 20px;
            }

            .sub-indicator-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航 -->
        <header class="header">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <a href="#" class="logo">
                    <i class="fas fa-hospital logo-icon"></i>
                    <span class="logo-text">医院评审指标</span>
                </a>
            </div>

            <div class="search-wrapper">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="搜索指标...">
            </div>

            <div class="header-right">
                <button class="header-btn" title="帮助">
                    <i class="fas fa-question-circle"></i>
                </button>
                <button class="header-btn" title="设置">
                    <i class="fas fa-cog"></i>
                </button>
                <div class="user-avatar">
                    <span>管</span>
                </div>
            </div>
        </header>

        <div class="main-container">
            <!-- 侧边导航 -->
            <nav class="sidebar" id="sidebar">
                <a href="#" class="nav-item active">
                    <i class="fas fa-chart-bar nav-icon"></i>
                    <span class="nav-text">资源配置与运行数据</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-heartbeat nav-icon"></i>
                    <span class="nav-text">医疗服务能力</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-check-circle nav-icon"></i>
                    <span class="nav-text">质量控制指标</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-clipboard-list nav-icon"></i>
                    <span class="nav-text">单病种</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-microscope nav-icon"></i>
                    <span class="nav-text">重点医疗技术</span>
                </a>

                <div class="nav-section">标签</div>
                <div id="sidebar-tags-container">
                    <a href="#" class="nav-item" data-tag-id="1" title="用于标记医院重点关注的核心指标，这些指标直接关系到医院的整体评价和等级评审结果。">
                        <i class="fas fa-tag nav-icon" style="color: var(--primary);"></i>
                        <span class="nav-text">关键指标</span>
                    </a>
                    <a href="#" class="nav-item" data-tag-id="2" title="与医疗安全相关的指标，包括医疗质量、患者安全、医院感染等方面的指标。">
                        <i class="fas fa-tag nav-icon" style="color: var(--secondary);"></i>
                        <span class="nav-text">医疗安全</span>
                    </a>
                    <a href="#" class="nav-item" data-tag-id="3" title="与患者服务体验相关的指标，包括就医流程、候诊时间、满意度等方面。">
                        <i class="fas fa-tag nav-icon" style="color: var(--warning);"></i>
                        <span class="nav-text">患者服务</span>
                    </a>
                </div>

                <div class="nav-section">系统</div>
                <a href="#" class="nav-item">
                    <i class="fas fa-cog nav-icon"></i>
                    <span class="nav-text">设置</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-question-circle nav-icon"></i>
                    <span class="nav-text">帮助中心</span>
                </a>
            </nav>

            <!-- 主内容 -->
            <main class="content">
                <div class="page-header">
                    <h1 class="page-title">资源配置与运行数据指标</h1>
                    <p class="page-subtitle">这些指标用于衡量医院资源配置与运行效率的基本情况，是评价医院综合能力的重要依据。</p>
                </div>

                <div class="toolbar">
                    <div class="view-toggle">
                        <button class="view-btn active" id="cardViewBtn">
                            <i class="fas fa-th-large"></i>
                            卡片视图
                        </button>
                        <button class="view-btn" id="listViewBtn">
                            <i class="fas fa-list"></i>
                            列表视图
                        </button>
                    </div>

                    <div class="action-btns">
                        <div class="view-mode-toggle">
                            <button class="btn btn-outline view-mode-btn active" data-mode="inline">
                                <i class="fas fa-list-alt btn-icon"></i>
                                内嵌模式
                            </button>
                            <button class="btn btn-outline view-mode-btn" data-mode="modal">
                                <i class="fas fa-window-maximize btn-icon"></i>
                                弹窗模式
                            </button>
                        </div>
                        <button class="btn btn-outline" id="filterBtn">
                            <i class="fas fa-filter btn-icon"></i>
                            筛选
                        </button>
                        <button class="btn btn-outline">
                            <i class="fas fa-print btn-icon"></i>
                            打印
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-download btn-icon"></i>
                            导出
                        </button>
                    </div>
                </div>

                <!-- 卡片视图 -->
                <div class="card-grid" id="cardView">
                    <!-- 卡片1 - 带子指标的父指标 -->
                    <div class="card parent-card">
                        <div class="card-body">
                            <h3 class="card-title">
                                <i class="fas fa-user-md card-icon"></i>
                                卫生技术人员数与开放床位数比
                                <span class="has-children" title="包含子指标">
                                    <i class="fas fa-chevron-down"></i>
                                </span>
                            </h3>
                            <div class="card-content">
                                该指标衡量医院人力资源与床位资源的配比情况，反映医疗服务能力的基础保障水平。
                            </div>
                            <div class="indicator-tags">
                                <span class="indicator-tag">
                                    <i class="fas fa-tag" style="color: var(--primary);"></i>
                                    关键指标
                                </span>
                                <span class="indicator-tag">
                                    <i class="fas fa-tag" style="color: var(--secondary);"></i>
                                    医疗安全
                                </span>
                            </div>
                            <div class="card-meta">
                                <div class="card-id">
                                    <span class="id-badge">1.2.1</span>
                                </div>
                                <div class="card-actions">
                                    <div class="action-icon" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </div>
                                    <div class="action-icon" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <div class="action-icon" title="更多选项">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 子指标容器 -->
                        <div class="child-indicators">
                            <!-- 子指标1 -->
                            <div class="child-card">
                                <div class="child-card-body">
                                    <h4 class="child-card-title">
                                        <i class="fas fa-stethoscope child-card-icon"></i>
                                        医师人数与开放床位数比
                                    </h4>
                                    <div class="child-card-content">
                                        该指标衡量医师人力资源与床位资源的配比情况。
                                    </div>
                                    <div class="child-card-meta">
                                        <div class="child-card-id">
                                            <span class="id-badge">1.2.1.1</span>
                                        </div>
                                        <div class="child-card-actions">
                                            <div class="action-icon" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 子指标2 -->
                            <div class="child-card">
                                <div class="child-card-body">
                                    <h4 class="child-card-title">
                                        <i class="fas fa-user-md child-card-icon"></i>
                                        主治及以上医师人数与开放床位数比
                                    </h4>
                                    <div class="child-card-content">
                                        该指标衡量高级别医师人力资源与床位资源的配比情况。
                                    </div>
                                    <div class="child-card-meta">
                                        <div class="child-card-id">
                                            <span class="id-badge">1.2.1.2</span>
                                        </div>
                                        <div class="child-card-actions">
                                            <div class="action-icon" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 子指标3 -->
                            <div class="child-card">
                                <div class="child-card-body">
                                    <h4 class="child-card-title">
                                        <i class="fas fa-graduation-cap child-card-icon"></i>
                                        高学历医师占比
                                    </h4>
                                    <div class="child-card-content">
                                        该指标衡量医院医师队伍的学历结构情况。
                                    </div>
                                    <div class="child-card-meta">
                                        <div class="child-card-id">
                                            <span class="id-badge">1.2.1.3</span>
                                        </div>
                                        <div class="child-card-actions">
                                            <div class="action-icon" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 卡片2 -->
                    <div class="card">
                        <div class="card-body">
                            <h3 class="card-title">
                                <i class="fas fa-user-nurse card-icon"></i>
                                全院护士人数与开放床位数比
                            </h3>
                            <div class="card-content">
                                该指标反映医院护理人力资源与床位资源的配比情况，是评价护理质量保障的重要参数。
                            </div>
                            <div class="indicator-tags">
                                <span class="indicator-tag">
                                    <i class="fas fa-tag" style="color: var(--warning);"></i>
                                    患者服务
                                </span>
                            </div>
                            <div class="card-meta">
                                <div class="card-id">
                                    <span class="id-badge">1.2.2</span>
                                </div>
                                <div class="card-actions">
                                    <div class="action-icon" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </div>
                                    <div class="action-icon" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <div class="action-icon" title="更多选项">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 卡片3 -->
                    <div class="card">
                        <div class="card-body">
                            <h3 class="card-title">
                                <i class="fas fa-hospital-user card-icon"></i>
                                病区护士人数与开放床位数比
                            </h3>
                            <div class="card-content">
                                该指标衡量病区护理人力资源配置情况，直接关系到患者的护理质量和安全保障水平。
                            </div>
                            <div class="card-meta">
                                <div class="card-id">
                                    <span class="id-badge">1.2.3</span>
                                </div>
                                <div class="card-actions">
                                    <div class="action-icon" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </div>
                                    <div class="action-icon" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <div class="action-icon" title="更多选项">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 卡片4 -->
                    <div class="card">
                        <div class="card-body">
                            <h3 class="card-title">
                                <i class="fas fa-shield-virus card-icon"></i>
                                医院感染管理专职人员数与开放床位数比
                            </h3>
                            <div class="card-content">
                                该指标反映医院对医院感染管理的重视程度，是医院质量安全管理体系的重要组成部分。
                            </div>
                            <div class="card-meta">
                                <div class="card-id">
                                    <span class="id-badge">1.2.4</span>
                                </div>
                                <div class="card-actions">
                                    <div class="action-icon" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </div>
                                    <div class="action-icon" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <div class="action-icon" title="更多选项">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 列表视图 -->
                <div class="list-view" id="listView">
                    <div class="list-header">
                        <div class="list-col-id">编号</div>
                        <div class="list-col-title">指标名称</div>
                        <div class="list-col-desc">指标描述</div>
                        <div class="list-col-tags">标签</div>
                        <div class="list-col-actions">操作</div>
                    </div>

                    <div class="list-item parent-list-item">
                        <div class="list-item-id">
                            <span class="list-item-badge">1.2.1</span>
                        </div>
                        <div class="list-item-title">
                            卫生技术人员数与开放床位数比
                            <span class="has-children" title="包含子指标">
                                <i class="fas fa-chevron-down"></i>
                            </span>
                        </div>
                        <div class="list-item-desc">该指标衡量医院人力资源与床位资源的配比情况，反映医疗服务能力的基础保障水平。</div>
                        <div class="list-item-tags">
                            <div class="indicator-tags">
                                <span class="indicator-tag">
                                    <i class="fas fa-tag" style="color: var(--primary);"></i>
                                    关键指标
                                </span>
                                <span class="indicator-tag">
                                    <i class="fas fa-tag" style="color: var(--secondary);"></i>
                                    医疗安全
                                </span>
                            </div>
                        </div>
                        <div class="list-item-actions">
                            <div class="action-icon" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="action-icon" title="编辑">
                                <i class="fas fa-edit"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 子指标列表 -->
                    <div class="child-list-items">
                        <!-- 子指标1 -->
                        <div class="list-item child-list-item">
                            <div class="list-item-id">
                                <span class="list-item-badge child-badge">1.2.1.1</span>
                            </div>
                            <div class="list-item-title">医师人数与开放床位数比</div>
                            <div class="list-item-desc">该指标衡量医师人力资源与床位资源的配比情况。</div>
                            <div class="list-item-actions">
                                <div class="action-icon" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 子指标2 -->
                        <div class="list-item child-list-item">
                            <div class="list-item-id">
                                <span class="list-item-badge child-badge">1.2.1.2</span>
                            </div>
                            <div class="list-item-title">主治及以上医师人数与开放床位数比</div>
                            <div class="list-item-desc">该指标衡量高级别医师人力资源与床位资源的配比情况。</div>
                            <div class="list-item-actions">
                                <div class="action-icon" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 子指标3 -->
                        <div class="list-item child-list-item">
                            <div class="list-item-id">
                                <span class="list-item-badge child-badge">1.2.1.3</span>
                            </div>
                            <div class="list-item-title">高学历医师占比</div>
                            <div class="list-item-desc">该指标衡量医院医师队伍的学历结构情况。</div>
                            <div class="list-item-actions">
                                <div class="action-icon" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="list-item">
                        <div class="list-item-id">
                            <span class="list-item-badge">1.2.2</span>
                        </div>
                        <div class="list-item-title">全院护士人数与开放床位数比</div>
                        <div class="list-item-desc">该指标反映医院护理人力资源与床位资源的配比情况，是评价护理质量保障的重要参数。</div>
                        <div class="list-item-tags">
                            <div class="indicator-tags">
                                <span class="indicator-tag">
                                    <i class="fas fa-tag" style="color: var(--warning);"></i>
                                    患者服务
                                </span>
                            </div>
                        </div>
                        <div class="list-item-actions">
                            <div class="action-icon" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="action-icon" title="编辑">
                                <i class="fas fa-edit"></i>
                            </div>
                        </div>
                    </div>

                    <div class="list-item">
                        <div class="list-item-id">
                            <span class="list-item-badge">1.2.3</span>
                        </div>
                        <div class="list-item-title">病区护士人数与开放床位数比</div>
                        <div class="list-item-desc">该指标衡量病区护理人力资源配置情况，直接关系到患者的护理质量和安全保障水平。</div>
                        <div class="list-item-actions">
                            <div class="action-icon" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="action-icon" title="编辑">
                                <i class="fas fa-edit"></i>
                            </div>
                        </div>
                    </div>

                    <div class="list-item">
                        <div class="list-item-id">
                            <span class="list-item-badge">1.2.4</span>
                        </div>
                        <div class="list-item-title">医院感染管理专职人员数与开放床位数比</div>
                        <div class="list-item-desc">该指标反映医院对医院感染管理的重视程度，是医院质量安全管理体系的重要组成部分。</div>
                        <div class="list-item-actions">
                            <div class="action-icon" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="action-icon" title="编辑">
                                <i class="fas fa-edit"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详情面板 -->
                <div class="detail-panel">
                    <div class="detail-header">
                        <div class="detail-title">
                            <div class="detail-title-icon">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="detail-title-content">
                                <div class="detail-title-text">
                                    卫生技术人员数与开放床位数比
                                    <span class="parent-indicator-badge" title="父级指标">
                                        <i class="fas fa-sitemap"></i>
                                    </span>
                                </div>
                                <div class="indicator-tags">
                                    <span class="indicator-tag">
                                        <i class="fas fa-tag" style="color: var(--primary);"></i>
                                        关键指标
                                    </span>
                                    <span class="indicator-tag">
                                        <i class="fas fa-tag" style="color: var(--secondary);"></i>
                                        医疗安全
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="detail-actions">
                            <button class="btn btn-outline btn-sm" id="inlineManageTagsBtn">
                                <i class="fas fa-tags btn-icon"></i>
                                标签管理
                            </button>
                            <button class="btn btn-outline btn-sm" id="inlineManageDeptBtn">
                                <i class="fas fa-sitemap btn-icon"></i>
                                科室管理
                            </button>
                            <button class="btn btn-outline">
                                <i class="fas fa-print btn-icon"></i>
                                打印
                            </button>
                            <button class="btn btn-primary">
                                <i class="fas fa-edit btn-icon"></i>
                                编辑
                            </button>
                        </div>
                    </div>

                    <div class="detail-body">
                        <div class="detail-section">
                            <h3 class="section-title">
                                <i class="fas fa-info-circle section-title-icon"></i>
                                指标定义
                            </h3>
                            <div class="section-content">
                                <p>卫生技术人员数与开放床位数比是指医院卫生技术人员总数与同期开放床位数的比值，反映医院人力资源配置与床位资源的匹配程度。</p>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3 class="section-title">
                                <i class="fas fa-calculator section-title-icon"></i>
                                计算方法
                            </h3>
                            <div class="section-content">
                                <p>卫生技术人员数与开放床位数比 = 卫生技术人员总数 ÷ 开放床位数</p>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3 class="section-title">
                                <i class="fas fa-chart-line section-title-icon"></i>
                                目标值与评价标准
                            </h3>
                            <div class="section-content">
                                <div class="data-row">
                                    <div class="data-label">目标值：</div>
                                    <div class="data-value">≥ 1.03:1</div>
                                </div>
                                <div class="data-row">
                                    <div class="data-label">当前值：</div>
                                    <div class="data-value">0.85:1</div>
                                </div>
                                <div class="data-row">
                                    <div class="data-label">完成率：</div>
                                    <div class="data-value">
                                        82.5%
                                        <div class="progress-container">
                                            <div class="progress-bar">
                                                <div class="progress-fill warning" style="width: 82.5%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3 class="section-title">
                                <i class="fas fa-database section-title-icon"></i>
                                数据来源
                            </h3>
                            <div class="section-content">
                                <p>医院人事管理系统、床位管理系统</p>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3 class="section-title">
                                <i class="fas fa-exclamation-triangle section-title-icon"></i>
                                注意事项
                            </h3>
                            <div class="section-content">
                                <p>1. 卫生技术人员包括执业医师、执业助理医师、注册护士、药师、检验技师、影像技师等持有相应资格证书的专业技术人员。</p>
                                <p>2. 开放床位数指实际开放使用的床位数量，不包括备用床位和未正式启用的床位。</p>
                                <p>3. 统计周期为上一自然年度。</p>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3 class="section-title">
                                <i class="fas fa-history section-title-icon"></i>
                                历史数据
                            </h3>
                            <div class="section-content">
                                <div class="table-container">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>年份</th>
                                                <th>数值</th>
                                                <th>完成率</th>
                                                <th>对比上年</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>2024年</td>
                                                <td>0.85:1</td>
                                                <td>82.5%</td>
                                                <td><span style="color: var(--secondary);">↑ 3.7%</span></td>
                                            </tr>
                                            <tr>
                                                <td>2023年</td>
                                                <td>0.82:1</td>
                                                <td>79.6%</td>
                                                <td><span style="color: var(--secondary);">↑ 5.1%</span></td>
                                            </tr>
                                            <tr>
                                                <td>2022年</td>
                                                <td>0.78:1</td>
                                                <td>75.7%</td>
                                                <td>-</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 子指标部分 -->
                        <div class="detail-section">
                            <h3 class="section-title">
                                <i class="fas fa-sitemap section-title-icon"></i>
                                子指标
                            </h3>
                            <div class="section-content">
                                <div class="sub-indicators-tabs">
                                    <div class="tab-header">
                                        <button class="tab-btn active" data-tab="sub-1">医师人数与开放床位数比</button>
                                        <button class="tab-btn" data-tab="sub-2">主治及以上医师人数与开放床位数比</button>
                                        <button class="tab-btn" data-tab="sub-3">高学历医师占比</button>
                                    </div>

                                    <div class="tab-content active" id="sub-1">
                                        <div class="sub-indicator-info">
                                            <div class="sub-indicator-header">
                                                <div class="sub-indicator-title">
                                                    <span class="id-badge">1.2.1.1</span>
                                                    医师人数与开放床位数比
                                                </div>
                                                <div class="sub-indicator-value">0.42:1</div>
                                            </div>
                                            <div class="sub-indicator-desc">
                                                <p>该指标衡量医师人力资源与床位资源的配比情况，是评价医疗服务能力的重要参数。</p>
                                            </div>
                                            <div class="sub-indicator-data">
                                                <div class="data-row">
                                                    <div class="data-label">目标值：</div>
                                                    <div class="data-value">≥ 0.50:1</div>
                                                </div>
                                                <div class="data-row">
                                                    <div class="data-label">当前值：</div>
                                                    <div class="data-value">0.42:1</div>
                                                </div>
                                                <div class="data-row">
                                                    <div class="data-label">完成率：</div>
                                                    <div class="data-value">
                                                        84.0%
                                                        <div class="progress-container">
                                                            <div class="progress-bar">
                                                                <div class="progress-fill warning" style="width: 84.0%"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="tab-content" id="sub-2">
                                        <div class="sub-indicator-info">
                                            <div class="sub-indicator-header">
                                                <div class="sub-indicator-title">
                                                    <span class="id-badge">1.2.1.2</span>
                                                    主治及以上医师人数与开放床位数比
                                                </div>
                                                <div class="sub-indicator-value">0.28:1</div>
                                            </div>
                                            <div class="sub-indicator-desc">
                                                <p>该指标衡量高级别医师人力资源与床位资源的配比情况，反映医院医疗服务的质量保障水平。</p>
                                            </div>
                                            <div class="sub-indicator-data">
                                                <div class="data-row">
                                                    <div class="data-label">目标值：</div>
                                                    <div class="data-value">≥ 0.30:1</div>
                                                </div>
                                                <div class="data-row">
                                                    <div class="data-label">当前值：</div>
                                                    <div class="data-value">0.28:1</div>
                                                </div>
                                                <div class="data-row">
                                                    <div class="data-label">完成率：</div>
                                                    <div class="data-value">
                                                        93.3%
                                                        <div class="progress-container">
                                                            <div class="progress-bar">
                                                                <div class="progress-fill success" style="width: 93.3%"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="tab-content" id="sub-3">
                                        <div class="sub-indicator-info">
                                            <div class="sub-indicator-header">
                                                <div class="sub-indicator-title">
                                                    <span class="id-badge">1.2.1.3</span>
                                                    高学历医师占比
                                                </div>
                                                <div class="sub-indicator-value">68.5%</div>
                                            </div>
                                            <div class="sub-indicator-desc">
                                                <p>该指标衡量医院医师队伍的学历结构情况，反映医院医师队伍的整体学术水平。</p>
                                            </div>
                                            <div class="sub-indicator-data">
                                                <div class="data-row">
                                                    <div class="data-label">目标值：</div>
                                                    <div class="data-value">≥ 70.0%</div>
                                                </div>
                                                <div class="data-row">
                                                    <div class="data-label">当前值：</div>
                                                    <div class="data-value">68.5%</div>
                                                </div>
                                                <div class="data-row">
                                                    <div class="data-label">完成率：</div>
                                                    <div class="data-value">
                                                        97.9%
                                                        <div class="progress-container">
                                                            <div class="progress-bar">
                                                                <div class="progress-fill success" style="width: 97.9%"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 弹窗详情面板 -->
    <div class="modal-overlay" id="detailModal">
        <div class="modal-container">
            <div class="modal-header">
                <div class="modal-title">
                    <div class="detail-title-icon">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <div class="modal-title-text">
                        <div>
                            卫生技术人员数与开放床位数比
                            <span class="parent-indicator-badge" title="父级指标">
                                <i class="fas fa-sitemap"></i>
                            </span>
                        </div>
                        <div class="indicator-tags">
                            <span class="indicator-tag">
                                <i class="fas fa-tag" style="color: var(--primary);"></i>
                                关键指标
                            </span>
                            <span class="indicator-tag">
                                <i class="fas fa-tag" style="color: var(--secondary);"></i>
                                医疗安全
                            </span>
                        </div>
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="btn btn-outline btn-sm" id="manageTagsBtn">
                        <i class="fas fa-tags btn-icon"></i>
                        标签管理
                    </button>
                    <button class="btn btn-outline btn-sm" id="manageDeptBtn">
                        <i class="fas fa-sitemap btn-icon"></i>
                        科室管理
                    </button>
                    <button class="btn btn-outline btn-sm">
                        <i class="fas fa-print btn-icon"></i>
                        打印
                    </button>
                    <button class="btn btn-primary btn-sm">
                        <i class="fas fa-edit btn-icon"></i>
                        编辑
                    </button>
                    <button class="modal-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="modal-body">
                <!-- 复制详情面板的内容 -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="fas fa-info-circle section-title-icon"></i>
                        指标定义
                    </h3>
                    <div class="section-content">
                        <p>卫生技术人员数与开放床位数比是指医院卫生技术人员总数与同期开放床位数的比值，反映医院人力资源配置与床位资源的匹配程度。</p>
                    </div>
                </div>

                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="fas fa-calculator section-title-icon"></i>
                        计算方法
                    </h3>
                    <div class="section-content">
                        <p>卫生技术人员数与开放床位数比 = 卫生技术人员总数 ÷ 开放床位数</p>
                    </div>
                </div>

                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="fas fa-chart-line section-title-icon"></i>
                        目标值与评价标准
                    </h3>
                    <div class="section-content">
                        <div class="data-row">
                            <div class="data-label">目标值：</div>
                            <div class="data-value">≥ 1.03:1</div>
                        </div>
                        <div class="data-row">
                            <div class="data-label">当前值：</div>
                            <div class="data-value">0.85:1</div>
                        </div>
                        <div class="data-row">
                            <div class="data-label">完成率：</div>
                            <div class="data-value">
                                82.5%
                                <div class="progress-container">
                                    <div class="progress-bar">
                                        <div class="progress-fill warning" style="width: 82.5%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="fas fa-database section-title-icon"></i>
                        数据来源
                    </h3>
                    <div class="section-content">
                        <p>医院人事管理系统、床位管理系统</p>
                    </div>
                </div>

                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="fas fa-exclamation-triangle section-title-icon"></i>
                        注意事项
                    </h3>
                    <div class="section-content">
                        <p>1. 卫生技术人员包括执业医师、执业助理医师、注册护士、药师、检验技师、影像技师等持有相应资格证书的专业技术人员。</p>
                        <p>2. 开放床位数指实际开放使用的床位数量，不包括备用床位和未正式启用的床位。</p>
                        <p>3. 统计周期为上一自然年度。</p>
                    </div>
                </div>

                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="fas fa-history section-title-icon"></i>
                        历史数据
                    </h3>
                    <div class="section-content">
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>年份</th>
                                        <th>数值</th>
                                        <th>完成率</th>
                                        <th>对比上年</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2024年</td>
                                        <td>0.85:1</td>
                                        <td>82.5%</td>
                                        <td><span style="color: var(--secondary);">↑ 3.7%</span></td>
                                    </tr>
                                    <tr>
                                        <td>2023年</td>
                                        <td>0.82:1</td>
                                        <td>79.6%</td>
                                        <td><span style="color: var(--secondary);">↑ 5.1%</span></td>
                                    </tr>
                                    <tr>
                                        <td>2022年</td>
                                        <td>0.78:1</td>
                                        <td>75.7%</td>
                                        <td>-</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 子指标部分 -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="fas fa-sitemap section-title-icon"></i>
                        子指标
                    </h3>
                    <div class="section-content">
                        <div class="sub-indicators-tabs">
                            <div class="tab-header">
                                <button class="tab-btn active" data-tab="modal-sub-1">医师人数与开放床位数比</button>
                                <button class="tab-btn" data-tab="modal-sub-2">主治及以上医师人数与开放床位数比</button>
                                <button class="tab-btn" data-tab="modal-sub-3">高学历医师占比</button>
                            </div>

                            <div class="tab-content active" id="modal-sub-1">
                                <div class="sub-indicator-info">
                                    <div class="sub-indicator-header">
                                        <div class="sub-indicator-title">
                                            <span class="id-badge">1.2.1.1</span>
                                            医师人数与开放床位数比
                                        </div>
                                        <div class="sub-indicator-value">0.42:1</div>
                                    </div>
                                    <div class="sub-indicator-desc">
                                        <p>该指标衡量医师人力资源与床位资源的配比情况，是评价医疗服务能力的重要参数。</p>
                                    </div>
                                    <div class="sub-indicator-data">
                                        <div class="data-row">
                                            <div class="data-label">目标值：</div>
                                            <div class="data-value">≥ 0.50:1</div>
                                        </div>
                                        <div class="data-row">
                                            <div class="data-label">当前值：</div>
                                            <div class="data-value">0.42:1</div>
                                        </div>
                                        <div class="data-row">
                                            <div class="data-label">完成率：</div>
                                            <div class="data-value">
                                                84.0%
                                                <div class="progress-container">
                                                    <div class="progress-bar">
                                                        <div class="progress-fill warning" style="width: 84.0%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-content" id="modal-sub-2">
                                <div class="sub-indicator-info">
                                    <div class="sub-indicator-header">
                                        <div class="sub-indicator-title">
                                            <span class="id-badge">1.2.1.2</span>
                                            主治及以上医师人数与开放床位数比
                                        </div>
                                        <div class="sub-indicator-value">0.28:1</div>
                                    </div>
                                    <div class="sub-indicator-desc">
                                        <p>该指标衡量高级别医师人力资源与床位资源的配比情况，反映医院医疗服务的质量保障水平。</p>
                                    </div>
                                    <div class="sub-indicator-data">
                                        <div class="data-row">
                                            <div class="data-label">目标值：</div>
                                            <div class="data-value">≥ 0.30:1</div>
                                        </div>
                                        <div class="data-row">
                                            <div class="data-label">当前值：</div>
                                            <div class="data-value">0.28:1</div>
                                        </div>
                                        <div class="data-row">
                                            <div class="data-label">完成率：</div>
                                            <div class="data-value">
                                                93.3%
                                                <div class="progress-container">
                                                    <div class="progress-bar">
                                                        <div class="progress-fill success" style="width: 93.3%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-content" id="modal-sub-3">
                                <div class="sub-indicator-info">
                                    <div class="sub-indicator-header">
                                        <div class="sub-indicator-title">
                                            <span class="id-badge">1.2.1.3</span>
                                            高学历医师占比
                                        </div>
                                        <div class="sub-indicator-value">68.5%</div>
                                    </div>
                                    <div class="sub-indicator-desc">
                                        <p>该指标衡量医院医师队伍的学历结构情况，反映医院医师队伍的整体学术水平。</p>
                                    </div>
                                    <div class="sub-indicator-data">
                                        <div class="data-row">
                                            <div class="data-label">目标值：</div>
                                            <div class="data-value">≥ 70.0%</div>
                                        </div>
                                        <div class="data-row">
                                            <div class="data-label">当前值：</div>
                                            <div class="data-value">68.5%</div>
                                        </div>
                                        <div class="data-row">
                                            <div class="data-label">完成率：</div>
                                            <div class="data-value">
                                                97.9%
                                                <div class="progress-container">
                                                    <div class="progress-bar">
                                                        <div class="progress-fill success" style="width: 97.9%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 标签管理弹窗 -->
    <div class="modal-overlay" id="tagsManageModal">
        <div class="modal-container modal-sm">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-tags section-title-icon"></i>
                    <div class="modal-title-text">标签管理</div>
                </div>
                <button class="modal-close-btn" id="closeTagsModalBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="tags-container">
                    <div class="current-tags">
                        <h4 class="section-subtitle">当前标签</h4>
                        <div class="tags-list">
                            <span class="tag-item" data-tag-id="1" data-tag-desc="用于标记医院重点关注的核心指标，这些指标直接关系到医院的整体评价和等级评审结果。">
                                <i class="fas fa-tag" style="color: var(--primary);"></i>
                                关键指标
                                <div class="tag-actions">
                                    <i class="fas fa-info-circle tag-info" title="查看备注"></i>
                                    <i class="fas fa-times tag-remove" title="移除标签"></i>
                                </div>
                            </span>
                            <span class="tag-item" data-tag-id="2" data-tag-desc="与医疗安全相关的指标，包括医疗质量、患者安全、医院感染等方面的指标。">
                                <i class="fas fa-tag" style="color: var(--secondary);"></i>
                                医疗安全
                                <div class="tag-actions">
                                    <i class="fas fa-info-circle tag-info" title="查看备注"></i>
                                    <i class="fas fa-times tag-remove" title="移除标签"></i>
                                </div>
                            </span>
                        </div>
                    </div>

                    <div class="add-tag-section">
                        <h4 class="section-subtitle">添加标签</h4>
                        <div class="add-tag-form">
                            <div class="form-group">
                                <label>选择现有标签</label>
                                <div class="select-wrapper">
                                    <select class="form-control" id="existingTagSelect">
                                        <option value="">-- 选择标签 --</option>
                                        <option value="3">患者服务</option>
                                        <option value="4">医疗质量</option>
                                        <option value="5">运营效率</option>
                                        <option value="6">重点监控</option>
                                    </select>
                                    <i class="fas fa-chevron-down select-arrow"></i>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>或创建新标签</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="newTagInput" placeholder="输入新标签名称">
                                    <div class="color-picker">
                                        <span class="color-option selected" style="background-color: var(--primary);"></span>
                                        <span class="color-option" style="background-color: var(--secondary);"></span>
                                        <span class="color-option" style="background-color: var(--warning);"></span>
                                        <span class="color-option" style="background-color: var(--error);"></span>
                                        <span class="color-option" style="background-color: #9c27b0;"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>标签备注说明</label>
                                <textarea class="form-control" id="tagDescription" rows="3" placeholder="输入标签的用途、适用范围等说明信息"></textarea>
                            </div>

                            <button class="btn btn-primary btn-block" id="addTagBtn">
                                <i class="fas fa-plus btn-icon"></i>
                                添加标签
                            </button>

                            <div class="form-actions" style="margin-top: 16px;">
                                <button class="btn btn-outline btn-block" id="saveTagsToDbBtn">
                                    <i class="fas fa-save btn-icon"></i>
                                    保存标签设置到数据库
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科室管理弹窗 -->
    <div class="modal-overlay" id="deptManageModal">
        <div class="modal-container modal-sm">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-sitemap section-title-icon"></i>
                    <div class="modal-title-text">科室管理</div>
                </div>
                <div class="modal-actions">
                    <button class="btn btn-outline btn-sm" id="operationManageBtn">
                        <i class="fas fa-cogs btn-icon"></i>
                        操作维护
                    </button>
                </div>
                <button class="modal-close-btn" id="closeDeptModalBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="tabs-container">
                    <div class="tabs-header">
                        <button class="tab-btn active" data-tab="leadDept">牵头科室</button>
                        <button class="tab-btn" data-tab="numeratorDept">分子科室</button>
                        <button class="tab-btn" data-tab="denominatorDept">分母科室</button>
                    </div>

                    <div class="tab-content active" id="leadDept">
                        <div class="dept-container">
                            <div class="current-depts">
                                <h4 class="section-subtitle">当前牵头科室</h4>
                                <div class="depts-list">
                                    <span class="dept-item" data-dept-id="1">
                                        医务科
                                        <i class="fas fa-times dept-remove"></i>
                                    </span>
                                    <span class="dept-item" data-dept-id="2">
                                        质控办
                                        <i class="fas fa-times dept-remove"></i>
                                    </span>
                                </div>
                            </div>

                            <div class="add-dept-section">
                                <h4 class="section-subtitle">添加牵头科室</h4>
                                <div class="add-dept-form">
                                    <div class="form-group">
                                        <label>选择科室</label>
                                        <div class="select-wrapper">
                                            <select class="form-control" id="leadDeptSelect">
                                                <option value="">-- 选择科室 --</option>
                                                <option value="3">护理部</option>
                                                <option value="4">信息科</option>
                                                <option value="5">药剂科</option>
                                                <option value="6">检验科</option>
                                                <option value="7">放射科</option>
                                                <option value="8">内科</option>
                                                <option value="9">外科</option>
                                            </select>
                                            <i class="fas fa-chevron-down select-arrow"></i>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label>责任人</label>
                                        <input type="text" class="form-control" placeholder="输入责任人姓名">
                                    </div>

                                    <button class="btn btn-primary btn-block" id="addLeadDeptBtn">
                                        <i class="fas fa-plus btn-icon"></i>
                                        添加牵头科室
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="numeratorDept">
                        <div class="dept-container">
                            <div class="current-depts">
                                <h4 class="section-subtitle">当前分子科室</h4>
                                <div class="depts-list">
                                    <span class="dept-item" data-dept-id="5">
                                        药剂科
                                        <i class="fas fa-times dept-remove"></i>
                                    </span>
                                </div>
                            </div>

                            <div class="add-dept-section">
                                <h4 class="section-subtitle">添加分子科室</h4>
                                <div class="add-dept-form">
                                    <div class="form-group">
                                        <label>选择科室</label>
                                        <div class="select-wrapper">
                                            <select class="form-control" id="numeratorDeptSelect">
                                                <option value="">-- 选择科室 --</option>
                                                <option value="1">医务科</option>
                                                <option value="2">质控办</option>
                                                <option value="3">护理部</option>
                                                <option value="4">信息科</option>
                                                <option value="6">检验科</option>
                                                <option value="7">放射科</option>
                                                <option value="8">内科</option>
                                                <option value="9">外科</option>
                                            </select>
                                            <i class="fas fa-chevron-down select-arrow"></i>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label>数据说明</label>
                                        <textarea class="form-control" placeholder="输入数据说明" rows="3"></textarea>
                                    </div>

                                    <button class="btn btn-primary btn-block" id="addNumeratorDeptBtn">
                                        <i class="fas fa-plus btn-icon"></i>
                                        添加分子科室
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="denominatorDept">
                        <div class="dept-container">
                            <div class="current-depts">
                                <h4 class="section-subtitle">当前分母科室</h4>
                                <div class="depts-list">
                                    <span class="dept-item" data-dept-id="4">
                                        信息科
                                        <i class="fas fa-times dept-remove"></i>
                                    </span>
                                </div>
                            </div>

                            <div class="add-dept-section">
                                <h4 class="section-subtitle">添加分母科室</h4>
                                <div class="add-dept-form">
                                    <div class="form-group">
                                        <label>选择科室</label>
                                        <div class="select-wrapper">
                                            <select class="form-control" id="denominatorDeptSelect">
                                                <option value="">-- 选择科室 --</option>
                                                <option value="1">医务科</option>
                                                <option value="2">质控办</option>
                                                <option value="3">护理部</option>
                                                <option value="5">药剂科</option>
                                                <option value="6">检验科</option>
                                                <option value="7">放射科</option>
                                                <option value="8">内科</option>
                                                <option value="9">外科</option>
                                            </select>
                                            <i class="fas fa-chevron-down select-arrow"></i>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label>数据说明</label>
                                        <textarea class="form-control" placeholder="输入数据说明" rows="3"></textarea>
                                    </div>

                                    <button class="btn btn-primary btn-block" id="addDenominatorDeptBtn">
                                        <i class="fas fa-plus btn-icon"></i>
                                        添加分母科室
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选面板 -->
    <div class="modal-overlay" id="filterModal">
        <div class="modal-container modal-md">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-filter section-title-icon"></i>
                    <div class="modal-title-text">指标筛选</div>
                </div>
                <button class="modal-close-btn" id="closeFilterModalBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="filter-container">
                    <div class="filter-section">
                        <h4 class="section-subtitle">指标类别</h4>
                        <div class="checkbox-group">
                            <label class="checkbox-item">
                                <input type="checkbox" name="category" value="resource" checked>
                                <span class="checkbox-label">资源配置与运行数据</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="category" value="service" checked>
                                <span class="checkbox-label">医疗服务能力</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="category" value="quality" checked>
                                <span class="checkbox-label">医疗质量</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="category" value="safety" checked>
                                <span class="checkbox-label">医疗安全</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="category" value="satisfaction" checked>
                                <span class="checkbox-label">患者满意度</span>
                            </label>
                        </div>
                    </div>

                    <div class="filter-section">
                        <h4 class="section-subtitle">指标标签</h4>
                        <div class="checkbox-group">
                            <label class="checkbox-item">
                                <input type="checkbox" name="tag" value="key" checked>
                                <span class="checkbox-label">
                                    <i class="fas fa-tag" style="color: var(--primary);"></i>
                                    关键指标
                                </span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="tag" value="safety" checked>
                                <span class="checkbox-label">
                                    <i class="fas fa-tag" style="color: var(--secondary);"></i>
                                    医疗安全
                                </span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="tag" value="service" checked>
                                <span class="checkbox-label">
                                    <i class="fas fa-tag" style="color: var(--warning);"></i>
                                    患者服务
                                </span>
                            </label>
                        </div>
                    </div>

                    <div class="filter-section">
                        <h4 class="section-subtitle">指标状态</h4>
                        <div class="checkbox-group">
                            <label class="checkbox-item">
                                <input type="checkbox" name="status" value="normal" checked>
                                <span class="checkbox-label">正常</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="status" value="warning" checked>
                                <span class="checkbox-label">
                                    <span class="status-dot warning"></span>
                                    警告
                                </span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="status" value="danger" checked>
                                <span class="checkbox-label">
                                    <span class="status-dot danger"></span>
                                    危险
                                </span>
                            </label>
                        </div>
                    </div>

                    <div class="filter-section">
                        <h4 class="section-subtitle">数据来源</h4>
                        <div class="checkbox-group">
                            <label class="checkbox-item">
                                <input type="checkbox" name="source" value="his" checked>
                                <span class="checkbox-label">HIS系统</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="source" value="lis" checked>
                                <span class="checkbox-label">LIS系统</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="source" value="pacs" checked>
                                <span class="checkbox-label">PACS系统</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="source" value="emr" checked>
                                <span class="checkbox-label">电子病历</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="source" value="manual" checked>
                                <span class="checkbox-label">手工台账</span>
                            </label>
                        </div>
                    </div>

                    <div class="filter-section">
                        <h4 class="section-subtitle">责任科室</h4>
                        <div class="select-wrapper">
                            <select class="form-control" id="filterDeptSelect">
                                <option value="">全部科室</option>
                                <option value="1">医务科</option>
                                <option value="2">质控办</option>
                                <option value="3">护理部</option>
                                <option value="4">信息科</option>
                                <option value="5">药剂科</option>
                                <option value="6">检验科</option>
                                <option value="7">放射科</option>
                                <option value="8">内科</option>
                                <option value="9">外科</option>
                            </select>
                            <i class="fas fa-chevron-down select-arrow"></i>
                        </div>
                    </div>

                    <div class="filter-section">
                        <h4 class="section-subtitle">完成率范围</h4>
                        <div class="range-slider-container">
                            <div class="range-values">
                                <span id="completionRateMin">0%</span>
                                <span id="completionRateMax">100%</span>
                            </div>
                            <div class="range-slider">
                                <input type="range" id="completionRateMinSlider" min="0" max="100" value="0" class="range-slider-input">
                                <input type="range" id="completionRateMaxSlider" min="0" max="100" value="100" class="range-slider-input">
                                <div class="range-slider-track"></div>
                                <div class="range-slider-fill"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="filter-actions">
                    <button class="btn btn-outline" id="resetFilterBtn">
                        <i class="fas fa-undo btn-icon"></i>
                        重置筛选
                    </button>
                    <button class="btn btn-primary" id="applyFilterBtn">
                        <i class="fas fa-check btn-icon"></i>
                        应用筛选
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 标签备注弹窗 -->
    <div class="modal-overlay" id="tagDescriptionModal">
        <div class="modal-container modal-sm">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-tag section-title-icon" id="tagDescIcon"></i>
                    <div class="modal-title-text">
                        <span id="tagDescTitle">标签备注</span>
                    </div>
                </div>
                <button class="modal-close-btn" id="closeTagDescModalBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- 查看模式 -->
                <div id="tagDescViewMode">
                    <div class="tag-description-content">
                        <p id="tagDescContent">标签备注内容将显示在这里。</p>
                    </div>
                    <div class="form-actions">
                        <button class="btn btn-outline" id="editTagDescBtn">
                            <i class="fas fa-edit btn-icon"></i>
                            编辑备注
                        </button>
                        <button class="btn btn-primary" id="closeTagDescBtn">
                            <i class="fas fa-check btn-icon"></i>
                            确定
                        </button>
                    </div>
                </div>

                <!-- 编辑模式 -->
                <div id="tagDescEditMode" style="display: none;">
                    <div class="form-group">
                        <label>标签备注说明</label>
                        <textarea class="form-control" id="tagDescEditContent" rows="5" placeholder="输入标签的用途、适用范围等说明信息"></textarea>
                    </div>
                    <div class="form-actions">
                        <button class="btn btn-outline" id="cancelTagDescEditBtn">
                            <i class="fas fa-times btn-icon"></i>
                            取消
                        </button>
                        <button class="btn btn-primary" id="saveTagDescBtn">
                            <i class="fas fa-save btn-icon"></i>
                            保存备注
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作维护弹窗 -->
    <div class="modal-overlay" id="operationManageModal">
        <div class="modal-container modal-md">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-cogs section-title-icon"></i>
                    <div class="modal-title-text">指标操作维护</div>
                </div>
                <button class="modal-close-btn" id="closeOperationModalBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="operation-container">
                    <div class="operation-tabs">
                        <div class="tabs-header">
                            <button class="tab-btn active" data-tab="dataCollection">数据采集</button>
                            <button class="tab-btn" data-tab="dataVerification">数据审核</button>
                            <button class="tab-btn" data-tab="dataAnalysis">数据分析</button>
                            <button class="tab-btn" data-tab="dataReport">数据上报</button>
                        </div>

                        <!-- 数据采集 -->
                        <div class="tab-content active" id="dataCollection">
                            <div class="operation-section">
                                <h4 class="section-subtitle">数据采集方式</h4>
                                <div class="radio-group">
                                    <label class="radio-item">
                                        <input type="radio" name="collectionMethod" value="auto" checked>
                                        <span class="radio-label">自动采集</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="collectionMethod" value="manual">
                                        <span class="radio-label">手动录入</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="collectionMethod" value="mixed">
                                        <span class="radio-label">混合模式</span>
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label>数据来源</label>
                                    <div class="select-wrapper">
                                        <select class="form-control" id="dataSourceSelect">
                                            <option value="his">HIS系统</option>
                                            <option value="lis">LIS系统</option>
                                            <option value="pacs">PACS系统</option>
                                            <option value="emr">电子病历</option>
                                            <option value="manual">手工台账</option>
                                        </select>
                                        <i class="fas fa-chevron-down select-arrow"></i>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>采集频率</label>
                                    <div class="select-wrapper">
                                        <select class="form-control" id="frequencySelect">
                                            <option value="daily">每日</option>
                                            <option value="weekly">每周</option>
                                            <option value="monthly" selected>每月</option>
                                            <option value="quarterly">每季度</option>
                                            <option value="yearly">每年</option>
                                        </select>
                                        <i class="fas fa-chevron-down select-arrow"></i>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>SQL查询语句（自动采集）</label>
                                    <textarea class="form-control code-editor" rows="5" placeholder="输入SQL查询语句">SELECT COUNT(*) FROM patient_records WHERE admission_date BETWEEN :start_date AND :end_date</textarea>
                                </div>

                                <div class="form-group">
                                    <label>采集说明</label>
                                    <textarea class="form-control" rows="3" placeholder="输入采集说明">数据每月1日自动从HIS系统采集上月数据，如遇系统故障需手动补录。</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 数据审核 -->
                        <div class="tab-content" id="dataVerification">
                            <div class="operation-section">
                                <h4 class="section-subtitle">审核流程</h4>

                                <div class="verification-flow">
                                    <div class="flow-item">
                                        <div class="flow-step">1</div>
                                        <div class="flow-content">
                                            <div class="flow-title">科室初审</div>
                                            <div class="flow-desc">由数据提供科室进行初步审核</div>
                                        </div>
                                    </div>
                                    <div class="flow-arrow"><i class="fas fa-chevron-down"></i></div>
                                    <div class="flow-item">
                                        <div class="flow-step">2</div>
                                        <div class="flow-content">
                                            <div class="flow-title">质控办复核</div>
                                            <div class="flow-desc">由质控办进行数据复核</div>
                                        </div>
                                    </div>
                                    <div class="flow-arrow"><i class="fas fa-chevron-down"></i></div>
                                    <div class="flow-item">
                                        <div class="flow-step">3</div>
                                        <div class="flow-content">
                                            <div class="flow-title">医务科终审</div>
                                            <div class="flow-desc">由医务科进行最终审核</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>审核规则</label>
                                    <textarea class="form-control" rows="3" placeholder="输入审核规则">1. 数据不得为空；2. 数值必须大于0；3. 环比波动不超过20%；4. 异常值需提供书面说明。</textarea>
                                </div>

                                <div class="form-group">
                                    <label>审核期限</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" value="5">
                                        <span class="input-group-text">工作日内</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 数据分析 -->
                        <div class="tab-content" id="dataAnalysis">
                            <div class="operation-section">
                                <h4 class="section-subtitle">分析方法</h4>

                                <div class="checkbox-group">
                                    <label class="checkbox-item">
                                        <input type="checkbox" name="analysisMethod" value="trend" checked>
                                        <span class="checkbox-label">趋势分析</span>
                                    </label>
                                    <label class="checkbox-item">
                                        <input type="checkbox" name="analysisMethod" value="compare" checked>
                                        <span class="checkbox-label">同比环比</span>
                                    </label>
                                    <label class="checkbox-item">
                                        <input type="checkbox" name="analysisMethod" value="benchmark">
                                        <span class="checkbox-label">标杆对比</span>
                                    </label>
                                    <label class="checkbox-item">
                                        <input type="checkbox" name="analysisMethod" value="correlation">
                                        <span class="checkbox-label">相关性分析</span>
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label>预警阈值</label>
                                    <div class="threshold-settings">
                                        <div class="threshold-item">
                                            <span class="threshold-label warning">警告</span>
                                            <div class="threshold-inputs">
                                                <div class="input-group">
                                                    <span class="input-group-text">下限</span>
                                                    <input type="number" class="form-control" value="0.8">
                                                </div>
                                                <div class="input-group">
                                                    <span class="input-group-text">上限</span>
                                                    <input type="number" class="form-control" value="0.9">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="threshold-item">
                                            <span class="threshold-label danger">危险</span>
                                            <div class="threshold-inputs">
                                                <div class="input-group">
                                                    <span class="input-group-text">下限</span>
                                                    <input type="number" class="form-control" value="0">
                                                </div>
                                                <div class="input-group">
                                                    <span class="input-group-text">上限</span>
                                                    <input type="number" class="form-control" value="0.8">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>关联指标</label>
                                    <div class="related-indicators-container">
                                        <div class="related-indicators-list">
                                            <div class="related-indicator-item" data-indicator-id="1.2.2">
                                                <div class="related-indicator-info">
                                                    <span class="related-indicator-id">1.2.2</span>
                                                    <span class="related-indicator-name">全院护士人数与开放床位数比</span>
                                                </div>
                                                <button type="button" class="related-indicator-remove" title="移除关联">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            <div class="related-indicator-item" data-indicator-id="1.2.3">
                                                <div class="related-indicator-info">
                                                    <span class="related-indicator-id">1.2.3</span>
                                                    <span class="related-indicator-name">病区护士人数与开放床位数比</span>
                                                </div>
                                                <button type="button" class="related-indicator-remove" title="移除关联">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            <div class="related-indicator-item selected" data-indicator-id="2.1.1">
                                                <div class="related-indicator-info">
                                                    <span class="related-indicator-id">2.1.1</span>
                                                    <span class="related-indicator-name">门诊人次数</span>
                                                </div>
                                                <button type="button" class="related-indicator-remove" title="移除关联">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            <div class="related-indicator-item selected" data-indicator-id="2.1.2">
                                                <div class="related-indicator-info">
                                                    <span class="related-indicator-id">2.1.2</span>
                                                    <span class="related-indicator-name">出院人次数</span>
                                                </div>
                                                <button type="button" class="related-indicator-remove" title="移除关联">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="related-indicators-actions">
                                            <button type="button" class="btn btn-outline btn-sm" id="addRelatedIndicatorBtn">
                                                <i class="fas fa-plus btn-icon"></i>
                                                添加关联指标
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 添加关联指标弹窗 -->
                                <div class="related-indicator-modal" id="relatedIndicatorModal">
                                    <div class="related-indicator-modal-content">
                                        <div class="related-indicator-modal-header">
                                            <h3>选择关联指标</h3>
                                            <button type="button" class="related-indicator-modal-close">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="related-indicator-modal-body">
                                            <div class="related-indicator-search">
                                                <input type="text" class="form-control" placeholder="搜索指标..." id="indicatorSearchInput">
                                                <i class="fas fa-search search-icon"></i>
                                            </div>
                                            <div class="related-indicator-categories">
                                                <div class="related-indicator-category">
                                                    <div class="related-indicator-category-header">
                                                        <span class="related-indicator-category-name">资源配置与运行数据</span>
                                                        <span class="related-indicator-category-count">4</span>
                                                    </div>
                                                    <div class="related-indicator-category-items">
                                                        <div class="related-indicator-select-item" data-indicator-id="1.2.1">
                                                            <div class="checkbox-wrapper">
                                                                <input type="checkbox" id="indicator-1.2.1">
                                                                <label for="indicator-1.2.1"></label>
                                                            </div>
                                                            <div class="related-indicator-select-info">
                                                                <span class="related-indicator-select-id">1.2.1</span>
                                                                <span class="related-indicator-select-name">卫生技术人员数与开放床位数比</span>
                                                            </div>
                                                        </div>
                                                        <div class="related-indicator-select-item" data-indicator-id="1.2.2">
                                                            <div class="checkbox-wrapper">
                                                                <input type="checkbox" id="indicator-1.2.2" checked>
                                                                <label for="indicator-1.2.2"></label>
                                                            </div>
                                                            <div class="related-indicator-select-info">
                                                                <span class="related-indicator-select-id">1.2.2</span>
                                                                <span class="related-indicator-select-name">全院护士人数与开放床位数比</span>
                                                            </div>
                                                        </div>
                                                        <div class="related-indicator-select-item" data-indicator-id="1.2.3">
                                                            <div class="checkbox-wrapper">
                                                                <input type="checkbox" id="indicator-1.2.3" checked>
                                                                <label for="indicator-1.2.3"></label>
                                                            </div>
                                                            <div class="related-indicator-select-info">
                                                                <span class="related-indicator-select-id">1.2.3</span>
                                                                <span class="related-indicator-select-name">病区护士人数与开放床位数比</span>
                                                            </div>
                                                        </div>
                                                        <div class="related-indicator-select-item" data-indicator-id="1.2.4">
                                                            <div class="checkbox-wrapper">
                                                                <input type="checkbox" id="indicator-1.2.4">
                                                                <label for="indicator-1.2.4"></label>
                                                            </div>
                                                            <div class="related-indicator-select-info">
                                                                <span class="related-indicator-select-id">1.2.4</span>
                                                                <span class="related-indicator-select-name">医院感染管理专职人员数与开放床位数比</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="related-indicator-category">
                                                    <div class="related-indicator-category-header">
                                                        <span class="related-indicator-category-name">医疗服务能力</span>
                                                        <span class="related-indicator-category-count">2</span>
                                                    </div>
                                                    <div class="related-indicator-category-items">
                                                        <div class="related-indicator-select-item" data-indicator-id="2.1.1">
                                                            <div class="checkbox-wrapper">
                                                                <input type="checkbox" id="indicator-2.1.1" checked>
                                                                <label for="indicator-2.1.1"></label>
                                                            </div>
                                                            <div class="related-indicator-select-info">
                                                                <span class="related-indicator-select-id">2.1.1</span>
                                                                <span class="related-indicator-select-name">门诊人次数</span>
                                                            </div>
                                                        </div>
                                                        <div class="related-indicator-select-item" data-indicator-id="2.1.2">
                                                            <div class="checkbox-wrapper">
                                                                <input type="checkbox" id="indicator-2.1.2" checked>
                                                                <label for="indicator-2.1.2"></label>
                                                            </div>
                                                            <div class="related-indicator-select-info">
                                                                <span class="related-indicator-select-id">2.1.2</span>
                                                                <span class="related-indicator-select-name">出院人次数</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="related-indicator-modal-footer">
                                            <button type="button" class="btn btn-outline" id="cancelRelatedIndicatorBtn">取消</button>
                                            <button type="button" class="btn btn-primary" id="confirmRelatedIndicatorBtn">确认</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 数据上报 -->
                        <div class="tab-content" id="dataReport">
                            <div class="operation-section">
                                <h4 class="section-subtitle">上报设置</h4>

                                <div class="form-group">
                                    <label>上报单位</label>
                                    <div class="checkbox-group">
                                        <label class="checkbox-item">
                                            <input type="checkbox" name="reportTo" value="health_bureau" checked>
                                            <span class="checkbox-label">卫健委</span>
                                        </label>
                                        <label class="checkbox-item">
                                            <input type="checkbox" name="reportTo" value="medical_insurance">
                                            <span class="checkbox-label">医保局</span>
                                        </label>
                                        <label class="checkbox-item">
                                            <input type="checkbox" name="reportTo" value="quality_control">
                                            <span class="checkbox-label">质控中心</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>上报频率</label>
                                    <div class="select-wrapper">
                                        <select class="form-control" id="reportFrequencySelect">
                                            <option value="monthly">每月</option>
                                            <option value="quarterly" selected>每季度</option>
                                            <option value="yearly">每年</option>
                                        </select>
                                        <i class="fas fa-chevron-down select-arrow"></i>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>上报方式</label>
                                    <div class="radio-group">
                                        <label class="radio-item">
                                            <input type="radio" name="reportMethod" value="platform" checked>
                                            <span class="radio-label">平台直报</span>
                                        </label>
                                        <label class="radio-item">
                                            <input type="radio" name="reportMethod" value="file">
                                            <span class="radio-label">文件上传</span>
                                        </label>
                                        <label class="radio-item">
                                            <input type="radio" name="reportMethod" value="api">
                                            <span class="radio-label">接口对接</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>上报截止日期</label>
                                    <div class="input-group">
                                        <span class="input-group-text">每季度后</span>
                                        <input type="number" class="form-control" value="15">
                                        <span class="input-group-text">天内</span>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>上报负责人</label>
                                    <input type="text" class="form-control" value="张医师">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button class="btn btn-outline" id="cancelOperationBtn">
                            <i class="fas fa-times btn-icon"></i>
                            取消
                        </button>
                        <button class="btn btn-primary" id="saveOperationBtn">
                            <i class="fas fa-save btn-icon"></i>
                            保存设置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 侧边栏切换
        document.getElementById('menuToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        });

        // 视图切换
        const cardViewBtn = document.getElementById('cardViewBtn');
        const listViewBtn = document.getElementById('listViewBtn');
        const cardView = document.getElementById('cardView');
        const listView = document.getElementById('listView');

        cardViewBtn.addEventListener('click', function() {
            cardViewBtn.classList.add('active');
            listViewBtn.classList.remove('active');
            cardView.style.display = 'grid';
            listView.style.display = 'none';
        });

        listViewBtn.addEventListener('click', function() {
            listViewBtn.classList.add('active');
            cardViewBtn.classList.remove('active');
            listView.style.display = 'block';
            cardView.style.display = 'none';
        });

        // 卡片和列表项点击事件
        const cards = document.querySelectorAll('.card:not(.child-card)');
        cards.forEach(card => {
            card.addEventListener('click', function(e) {
                // 如果点击的是展开/折叠图标，则不触发详情面板滚动
                if (e.target.closest('.has-children')) {
                    return;
                }

                document.querySelector('.detail-panel').scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        const listItems = document.querySelectorAll('.list-item:not(.child-list-item)');
        listItems.forEach(item => {
            item.addEventListener('click', function(e) {
                // 如果点击的是展开/折叠图标，则不触发详情面板滚动
                if (e.target.closest('.has-children')) {
                    return;
                }

                document.querySelector('.detail-panel').scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // 子指标卡片点击事件
        const childCards = document.querySelectorAll('.child-card');
        childCards.forEach(card => {
            card.addEventListener('click', function() {
                // 获取子指标ID
                const idBadge = card.querySelector('.id-badge');
                const subIndicatorId = idBadge ? idBadge.textContent.trim() : '';

                // 根据ID找到对应的选项卡并激活
                if (subIndicatorId === '1.2.1.1') {
                    activateTab('sub-1');
                } else if (subIndicatorId === '1.2.1.2') {
                    activateTab('sub-2');
                } else if (subIndicatorId === '1.2.1.3') {
                    activateTab('sub-3');
                }

                // 滚动到详情面板
                document.querySelector('.detail-panel').scrollIntoView({
                    behavior: 'smooth'
                });

                // 滚动到子指标部分
                setTimeout(() => {
                    document.querySelector('.sub-indicators-tabs').scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }, 500);
            });
        });

        // 子指标列表项点击事件
        const childListItems = document.querySelectorAll('.child-list-item');
        childListItems.forEach(item => {
            item.addEventListener('click', function() {
                // 获取子指标ID
                const idBadge = item.querySelector('.list-item-badge');
                const subIndicatorId = idBadge ? idBadge.textContent.trim() : '';

                // 根据ID找到对应的选项卡并激活
                if (subIndicatorId === '1.2.1.1') {
                    activateTab('sub-1');
                } else if (subIndicatorId === '1.2.1.2') {
                    activateTab('sub-2');
                } else if (subIndicatorId === '1.2.1.3') {
                    activateTab('sub-3');
                }

                // 滚动到详情面板
                document.querySelector('.detail-panel').scrollIntoView({
                    behavior: 'smooth'
                });

                // 滚动到子指标部分
                setTimeout(() => {
                    document.querySelector('.sub-indicators-tabs').scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }, 500);
            });
        });

        // 点击查看详情图标
        const viewIcons = document.querySelectorAll('.action-icon[title="查看详情"]');
        viewIcons.forEach(icon => {
            icon.addEventListener('click', function(e) {
                e.stopPropagation();

                // 如果是子指标的查看详情图标
                const childCard = icon.closest('.child-card');
                const childListItem = icon.closest('.child-list-item');

                if (childCard || childListItem) {
                    const parent = childCard || childListItem;
                    const idBadge = parent.querySelector('.id-badge') || parent.querySelector('.list-item-badge');
                    const subIndicatorId = idBadge ? idBadge.textContent.trim() : '';

                    // 根据ID找到对应的选项卡并激活
                    if (subIndicatorId === '1.2.1.1') {
                        activateTab('sub-1');
                    } else if (subIndicatorId === '1.2.1.2') {
                        activateTab('sub-2');
                    } else if (subIndicatorId === '1.2.1.3') {
                        activateTab('sub-3');
                    }
                }

                document.querySelector('.detail-panel').scrollIntoView({
                    behavior: 'smooth'
                });

                // 如果是子指标，滚动到子指标部分
                if (childCard || childListItem) {
                    setTimeout(() => {
                        document.querySelector('.sub-indicators-tabs').scrollIntoView({
                            behavior: 'smooth',
                            block: 'center'
                        });
                    }, 500);
                }
            });
        });

        // 展开/折叠子指标
        const hasChildrenIcons = document.querySelectorAll('.has-children');
        hasChildrenIcons.forEach(icon => {
            icon.addEventListener('click', function(e) {
                e.stopPropagation();

                const parentCard = icon.closest('.parent-card');
                const parentListItem = icon.closest('.parent-list-item');

                if (parentCard) {
                    parentCard.classList.toggle('expanded');
                    const iconElement = icon.querySelector('i');
                    if (parentCard.classList.contains('expanded')) {
                        iconElement.className = 'fas fa-chevron-up';
                    } else {
                        iconElement.className = 'fas fa-chevron-down';
                    }
                }

                if (parentListItem) {
                    const childListItems = parentListItem.nextElementSibling;
                    if (childListItems && childListItems.classList.contains('child-list-items')) {
                        childListItems.classList.toggle('expanded');
                        const iconElement = icon.querySelector('i');
                        if (childListItems.classList.contains('expanded')) {
                            iconElement.className = 'fas fa-chevron-up';
                        } else {
                            iconElement.className = 'fas fa-chevron-down';
                        }
                    }
                }
            });
        });

        // 子指标选项卡切换
        const tabBtns = document.querySelectorAll('.tab-btn');
        tabBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const tabId = btn.getAttribute('data-tab');
                const isModalTab = tabId.startsWith('modal-');

                if (isModalTab) {
                    activateModalTab(tabId);
                } else {
                    activateTab(tabId);
                }
            });
        });

        function activateTab(tabId) {
            // 移除所有选项卡的激活状态
            document.querySelectorAll('.tab-btn:not([data-tab^="modal-"])').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelectorAll('.tab-content:not([id^="modal-"])').forEach(content => {
                content.classList.remove('active');
            });

            // 激活选中的选项卡
            document.querySelector(`.tab-btn[data-tab="${tabId}"]`).classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }

        function activateModalTab(tabId) {
            // 移除所有模态框选项卡的激活状态
            document.querySelectorAll('.tab-btn[data-tab^="modal-"]').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelectorAll('.tab-content[id^="modal-"]').forEach(content => {
                content.classList.remove('active');
            });

            // 激活选中的选项卡
            document.querySelector(`.tab-btn[data-tab="${tabId}"]`).classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }

        // 详情查看模式切换
        const viewModeBtns = document.querySelectorAll('.view-mode-btn');
        const detailPanel = document.querySelector('.detail-panel');
        const detailModal = document.getElementById('detailModal');
        let currentViewMode = 'inline'; // 默认为内嵌模式

        viewModeBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const mode = btn.getAttribute('data-mode');

                // 更新按钮状态
                viewModeBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                // 保存当前模式
                currentViewMode = mode;
            });
        });

        // 内嵌模式下的标签管理和科室管理按钮
        const inlineManageTagsBtn = document.getElementById('inlineManageTagsBtn');
        const inlineManageDeptBtn = document.getElementById('inlineManageDeptBtn');

        if (inlineManageTagsBtn) {
            inlineManageTagsBtn.addEventListener('click', function() {
                tagsManageModal.classList.add('active');
                document.body.style.overflow = 'hidden';
            });
        }

        if (inlineManageDeptBtn) {
            inlineManageDeptBtn.addEventListener('click', function() {
                deptManageModal.classList.add('active');
                document.body.style.overflow = 'hidden';

                // 确保所有科室删除按钮都有正确的事件监听器
                setTimeout(() => {
                    const deptRemoveBtns = deptManageModal.querySelectorAll('.dept-remove');
                    deptRemoveBtns.forEach(btn => {
                        // 移除旧的事件监听器（避免重复）
                        const newBtn = btn.cloneNode(true);
                        btn.parentNode.replaceChild(newBtn, btn);

                        // 添加新的事件监听器
                        newBtn.addEventListener('click', function(e) {
                            e.stopPropagation();
                            e.preventDefault();
                            const deptItem = this.closest('.dept-item');
                            if (deptItem) {
                                deptItem.remove();
                            }
                        });
                    });
                }, 100);
            });
        }

        // 打开详情面板（根据当前模式）
        function openDetailView() {
            if (currentViewMode === 'modal') {
                // 弹窗模式
                detailPanel.classList.add('hidden');
                detailModal.classList.add('active');
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            } else {
                // 内嵌模式
                detailPanel.classList.remove('hidden');
                detailModal.classList.remove('active');
                document.body.style.overflow = '';

                // 滚动到详情面板
                detailPanel.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        }

        // 关闭弹窗
        const modalCloseBtn = document.querySelector('.modal-close-btn');
        modalCloseBtn.addEventListener('click', function() {
            detailModal.classList.remove('active');
            document.body.style.overflow = '';
        });

        // 点击弹窗外部关闭
        detailModal.addEventListener('click', function(e) {
            if (e.target === detailModal) {
                detailModal.classList.remove('active');
                document.body.style.overflow = '';
            }
        });

        // 修改所有查看详情的点击事件，使用新的openDetailView函数
        const allViewDetailElements = [
            ...document.querySelectorAll('.card:not(.child-card)'),
            ...document.querySelectorAll('.list-item:not(.child-list-item)'),
            ...document.querySelectorAll('.action-icon[title="查看详情"]')
        ];

        allViewDetailElements.forEach(element => {
            const originalClickHandler = element.onclick;
            element.onclick = function(e) {
                // 如果点击的是展开/折叠图标，则不触发详情面板
                if (e.target.closest('.has-children')) {
                    return;
                }

                // 阻止事件冒泡
                e.stopPropagation();

                // 打开详情面板
                openDetailView();

                // 如果是子指标的查看详情图标
                const childCard = element.closest('.child-card');
                const childListItem = element.closest('.child-list-item');

                if (childCard || childListItem) {
                    const parent = childCard || childListItem;
                    const idBadge = parent.querySelector('.id-badge') || parent.querySelector('.list-item-badge');
                    const subIndicatorId = idBadge ? idBadge.textContent.trim() : '';

                    // 根据ID找到对应的选项卡并激活
                    if (subIndicatorId === '1.2.1.1') {
                        if (currentViewMode === 'modal') {
                            activateModalTab('modal-sub-1');
                        } else {
                            activateTab('sub-1');
                        }
                    } else if (subIndicatorId === '1.2.1.2') {
                        if (currentViewMode === 'modal') {
                            activateModalTab('modal-sub-2');
                        } else {
                            activateTab('sub-2');
                        }
                    } else if (subIndicatorId === '1.2.1.3') {
                        if (currentViewMode === 'modal') {
                            activateModalTab('modal-sub-3');
                        } else {
                            activateTab('sub-3');
                        }
                    }

                    // 如果是内嵌模式，滚动到子指标部分
                    if (currentViewMode === 'inline') {
                        setTimeout(() => {
                            document.querySelector('.sub-indicators-tabs').scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });
                        }, 500);
                    }
                }
            };
        });

        // 标签管理功能
        const tagsManageModal = document.getElementById('tagsManageModal');
        const manageTagsBtn = document.getElementById('manageTagsBtn');
        const closeTagsModalBtn = document.getElementById('closeTagsModalBtn');
        const tagDescriptionModal = document.getElementById('tagDescriptionModal');
        const closeTagDescModalBtn = document.getElementById('closeTagDescModalBtn');
        const closeTagDescBtn = document.getElementById('closeTagDescBtn');
        const editTagDescBtn = document.getElementById('editTagDescBtn');
        const tagDescTitle = document.getElementById('tagDescTitle');
        const tagDescContent = document.getElementById('tagDescContent');
        const tagDescIcon = document.getElementById('tagDescIcon');
        const saveTagsToDbBtn = document.getElementById('saveTagsToDbBtn');
        const tagDescription = document.getElementById('tagDescription');

        // 打开标签管理弹窗
        manageTagsBtn.addEventListener('click', function() {
            tagsManageModal.classList.add('active');
            document.body.style.overflow = 'hidden';
        });

        // 关闭标签管理弹窗
        closeTagsModalBtn.addEventListener('click', function() {
            tagsManageModal.classList.remove('active');
            document.body.style.overflow = '';
        });

        // 点击弹窗外部关闭
        tagsManageModal.addEventListener('click', function(e) {
            if (e.target === tagsManageModal) {
                tagsManageModal.classList.remove('active');
                document.body.style.overflow = '';
            }
        });

        // 关闭标签备注弹窗
        if (closeTagDescModalBtn) {
            closeTagDescModalBtn.addEventListener('click', function() {
                tagDescriptionModal.classList.remove('active');
            });
        }

        if (closeTagDescBtn) {
            closeTagDescBtn.addEventListener('click', function() {
                tagDescriptionModal.classList.remove('active');
            });
        }

        // 编辑标签备注 - 切换到编辑模式
        if (editTagDescBtn) {
            editTagDescBtn.addEventListener('click', function() {
                const tagId = tagDescriptionModal.getAttribute('data-tag-id');
                const tagItem = document.querySelector(`.tag-item[data-tag-id="${tagId}"]`);

                if (tagItem) {
                    // 获取当前标签的备注
                    const tagDesc = tagItem.getAttribute('data-tag-desc') || '';

                    // 填充到编辑框
                    const tagDescEditContent = document.getElementById('tagDescEditContent');
                    tagDescEditContent.value = tagDesc;

                    // 切换到编辑模式
                    document.getElementById('tagDescViewMode').style.display = 'none';
                    document.getElementById('tagDescEditMode').style.display = 'block';

                    // 聚焦到编辑框
                    tagDescEditContent.focus();
                }
            });
        }

        // 取消编辑标签备注
        const cancelTagDescEditBtn = document.getElementById('cancelTagDescEditBtn');
        if (cancelTagDescEditBtn) {
            cancelTagDescEditBtn.addEventListener('click', function() {
                // 切换回查看模式
                document.getElementById('tagDescEditMode').style.display = 'none';
                document.getElementById('tagDescViewMode').style.display = 'block';
            });
        }

        // 保存标签备注
        const saveTagDescBtn = document.getElementById('saveTagDescBtn');
        if (saveTagDescBtn) {
            saveTagDescBtn.addEventListener('click', function() {
                const tagId = tagDescriptionModal.getAttribute('data-tag-id');
                const tagItem = document.querySelector(`.tag-item[data-tag-id="${tagId}"]`);
                const tagDescEditContent = document.getElementById('tagDescEditContent');
                const newDesc = tagDescEditContent.value.trim();

                if (tagItem) {
                    // 更新标签的备注属性
                    tagItem.setAttribute('data-tag-desc', newDesc);

                    // 更新左侧面板中的标签备注
                    const sidebarTag = document.querySelector(`#sidebar-tags-container .nav-item[data-tag-id="${tagId}"]`);
                    if (sidebarTag) {
                        // 更新tooltip
                        const tooltip = sidebarTag.querySelector('.tooltip-text');
                        if (tooltip) {
                            tooltip.textContent = newDesc;
                        } else {
                            // 如果没有tooltip，添加一个
                            enhanceWithTooltip(sidebarTag, newDesc);
                        }
                    }

                    // 更新查看模式的内容
                    document.getElementById('tagDescContent').textContent = newDesc || '该标签暂无备注说明';

                    // 模拟与数据库的交互 - 保存单个标签备注
                    saveTagDescriptionToDatabase(tagId, newDesc);

                    // 切换回查看模式
                    document.getElementById('tagDescEditMode').style.display = 'none';
                    document.getElementById('tagDescViewMode').style.display = 'block';

                    // 显示保存成功提示
                    const originalText = saveTagDescBtn.innerHTML;
                    saveTagDescBtn.innerHTML = '<i class="fas fa-check btn-icon"></i> 保存成功';
                    saveTagDescBtn.disabled = true;

                    setTimeout(() => {
                        saveTagDescBtn.innerHTML = originalText;
                        saveTagDescBtn.disabled = false;
                    }, 1000);
                }
            });
        }

        // 模拟与数据库的交互 - 保存单个标签备注
        function saveTagDescriptionToDatabase(tagId, description) {
            console.log(`保存标签 ID: ${tagId} 的备注到数据库:`, description);

            // 完整数据库设计方案：
            //
            // 1. 指标表(indicators)结构：
            //    - id: VARCHAR(20) PRIMARY KEY, 指标编号(如1.2.1)
            //    - name: VARCHAR(200) NOT NULL, 指标名称
            //    - description: TEXT, 指标定义说明
            //    - calculation_method: TEXT, 计算方法
            //    - target_value: DECIMAL(10,4), 目标值
            //    - current_value: DECIMAL(10,4), 当前值
            //    - completion_rate: DECIMAL(5,2), 完成率(%)
            //    - data_source: VARCHAR(100), 数据来源
            //    - collection_frequency: ENUM('daily','weekly','monthly','quarterly','yearly'), 采集频率
            //    - collection_method: ENUM('auto','manual','mixed'), 采集方式
            //    - sql_query: TEXT, SQL查询语句(自动采集用)
            //    - parent_id: VARCHAR(20), 父指标ID(用于子指标)
            //    - category: VARCHAR(50), 指标类别
            //    - status: ENUM('normal','warning','danger'), 指标状态
            //    - warning_threshold_min: DECIMAL(10,4), 警告阈值下限
            //    - warning_threshold_max: DECIMAL(10,4), 警告阈值上限
            //    - danger_threshold_min: DECIMAL(10,4), 危险阈值下限
            //    - danger_threshold_max: DECIMAL(10,4), 危险阈值上限
            //    - notes: TEXT, 注意事项
            //    - is_active: BOOLEAN DEFAULT TRUE, 是否启用
            //    - created_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP, 创建时间
            //    - updated_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, 更新时间
            //
            // 2. 标签表(tags)结构：
            //    - id: INT AUTO_INCREMENT PRIMARY KEY, 标签ID
            //    - name: VARCHAR(50) NOT NULL UNIQUE, 标签名称
            //    - color: VARCHAR(20) DEFAULT '#1a73e8', 标签颜色
            //    - description: TEXT, 标签备注说明
            //    - created_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP, 创建时间
            //    - updated_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, 更新时间
            //
            // 3. 指标标签关联表(indicator_tags)结构：
            //    - id: INT AUTO_INCREMENT PRIMARY KEY, 主键
            //    - indicator_id: VARCHAR(20) NOT NULL, 外键，关联指标表
            //    - tag_id: INT NOT NULL, 外键，关联标签表
            //    - created_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP, 创建时间
            //    - FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
            //    - FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
            //    - UNIQUE KEY unique_indicator_tag (indicator_id, tag_id)
            //
            // 4. 科室表(departments)结构：
            //    - id: INT AUTO_INCREMENT PRIMARY KEY, 科室ID
            //    - name: VARCHAR(100) NOT NULL UNIQUE, 科室名称
            //    - code: VARCHAR(20) UNIQUE, 科室编码
            //    - type: ENUM('clinical','medical_tech','admin','support'), 科室类型
            //    - parent_id: INT, 上级科室ID
            //    - director: VARCHAR(50), 科室主任
            //    - contact_phone: VARCHAR(20), 联系电话
            //    - description: TEXT, 科室描述
            //    - is_active: BOOLEAN DEFAULT TRUE, 是否启用
            //    - created_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP, 创建时间
            //    - updated_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, 更新时间
            //    - FOREIGN KEY (parent_id) REFERENCES departments(id)
            //
            // 5. 指标科室关联表(indicator_departments)结构：
            //    - id: INT AUTO_INCREMENT PRIMARY KEY, 主键
            //    - indicator_id: VARCHAR(20) NOT NULL, 外键，关联指标表
            //    - department_id: INT NOT NULL, 外键，关联科室表
            //    - role_type: ENUM('lead','numerator','denominator'), 科室角色类型
            //    - responsibility: VARCHAR(200), 责任描述
            //    - contact_person: VARCHAR(50), 联系人
            //    - data_description: TEXT, 数据说明
            //    - created_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP, 创建时间
            //    - FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
            //    - FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
            //
            // 6. 指标关联表(indicator_relations)结构：
            //    - id: INT AUTO_INCREMENT PRIMARY KEY, 主键
            //    - source_indicator_id: VARCHAR(20) NOT NULL, 源指标ID
            //    - target_indicator_id: VARCHAR(20) NOT NULL, 目标指标ID
            //    - relation_type: ENUM('related','dependent','similar'), 关联类型
            //    - description: VARCHAR(200), 关联描述
            //    - created_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP, 创建时间
            //    - FOREIGN KEY (source_indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
            //    - FOREIGN KEY (target_indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
            //    - UNIQUE KEY unique_relation (source_indicator_id, target_indicator_id)
            //
            // 7. 指标数据历史表(indicator_data_history)结构：
            //    - id: INT AUTO_INCREMENT PRIMARY KEY, 主键
            //    - indicator_id: VARCHAR(20) NOT NULL, 外键，关联指标表
            //    - value: DECIMAL(15,4), 指标值
            //    - target_value: DECIMAL(15,4), 目标值
            //    - completion_rate: DECIMAL(5,2), 完成率
            //    - data_period: DATE, 数据周期(年月日)
            //    - data_source: VARCHAR(100), 数据来源
            //    - collection_method: ENUM('auto','manual'), 采集方式
            //    - collector_id: INT, 采集人ID
            //    - status: ENUM('draft','submitted','approved','rejected'), 数据状态
            //    - notes: TEXT, 备注
            //    - created_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP, 创建时间
            //    - FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
            //
            // 8. 数据审核流程表(data_audit_flow)结构：
            //    - id: INT AUTO_INCREMENT PRIMARY KEY, 主键
            //    - data_history_id: INT NOT NULL, 外键，关联指标数据历史表
            //    - step: INT NOT NULL, 审核步骤(1:科室初审,2:质控办复核,3:医务科终审)
            //    - auditor_id: INT, 审核人ID
            //    - audit_result: ENUM('pending','approved','rejected'), 审核结果
            //    - audit_comment: TEXT, 审核意见
            //    - audit_time: TIMESTAMP, 审核时间
            //    - created_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP, 创建时间
            //    - FOREIGN KEY (data_history_id) REFERENCES indicator_data_history(id) ON DELETE CASCADE
            //
            // 9. 用户表(users)结构：
            //    - id: INT AUTO_INCREMENT PRIMARY KEY, 用户ID
            //    - username: VARCHAR(50) NOT NULL UNIQUE, 用户名
            //    - password: VARCHAR(255) NOT NULL, 密码(加密)
            //    - real_name: VARCHAR(50) NOT NULL, 真实姓名
            //    - email: VARCHAR(100), 邮箱
            //    - phone: VARCHAR(20), 电话
            //    - department_id: INT, 所属科室ID
            //    - role: ENUM('admin','manager','operator','viewer'), 用户角色
            //    - is_active: BOOLEAN DEFAULT TRUE, 是否启用
            //    - last_login_at: TIMESTAMP, 最后登录时间
            //    - created_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP, 创建时间
            //    - updated_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, 更新时间
            //    - FOREIGN KEY (department_id) REFERENCES departments(id)
            //
            // 10. 系统配置表(system_config)结构：
            //    - id: INT AUTO_INCREMENT PRIMARY KEY, 主键
            //    - config_key: VARCHAR(100) NOT NULL UNIQUE, 配置键
            //    - config_value: TEXT, 配置值
            //    - config_type: ENUM('string','number','boolean','json'), 配置类型
            //    - description: VARCHAR(200), 配置描述
            //    - is_system: BOOLEAN DEFAULT FALSE, 是否系统配置
            //    - created_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP, 创建时间
            //    - updated_at: TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, 更新时间
            //
            // SQL示例 - 更新标签备注：
            //    UPDATE tags SET description = '备注内容', updated_at = NOW() WHERE id = 'tag_id';

            // 实际实现时，这里应该是AJAX请求将数据发送到服务器
            // 例如：
            /*
            fetch('/api/tags/' + tagId, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    description: description
                }),
            })
            .then(response => response.json())
            .then(data => {
                console.log('标签备注更新成功:', data);
            })
            .catch((error) => {
                console.error('标签备注更新失败:', error);
            });
            */
        }

        // 保存标签设置到数据库
        if (saveTagsToDbBtn) {
            saveTagsToDbBtn.addEventListener('click', function() {
                // 收集所有标签数据
                const tags = [];
                const tagItems = document.querySelectorAll('.tag-item');

                tagItems.forEach(item => {
                    const tagId = item.getAttribute('data-tag-id');
                    const tagName = item.textContent.trim().replace('查看备注', '').replace('移除标签', '').trim();
                    const tagDesc = item.getAttribute('data-tag-desc') || '';
                    const tagColor = item.querySelector('.fa-tag').style.color;

                    tags.push({
                        id: tagId,
                        name: tagName,
                        description: tagDesc,
                        color: tagColor
                    });
                });

                // 模拟与数据库的交互
                console.log('保存标签到数据库:', tags);

                // 实际实现时，这里应该是AJAX请求将数据发送到服务器
                // 例如：
                /*
                fetch('/api/tags/batch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        tags: tags
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    console.log('标签批量更新成功:', data);
                })
                .catch((error) => {
                    console.error('标签批量更新失败:', error);
                });
                */

                // 同步更新左侧面板中的所有标签
                syncTagsWithSidebar(tags);

                // 显示保存成功提示
                const saveBtn = saveTagsToDbBtn;
                const originalText = saveBtn.innerHTML;

                saveBtn.innerHTML = '<i class="fas fa-check btn-icon"></i> 保存成功';
                saveBtn.disabled = true;

                setTimeout(() => {
                    saveBtn.innerHTML = originalText;
                    saveBtn.disabled = false;
                }, 2000);
            });
        }

        // 同步所有标签到左侧面板
        function syncTagsWithSidebar(tags) {
            // 清空左侧面板中的所有标签
            const sidebarTagsContainer = document.getElementById('sidebar-tags-container');
            sidebarTagsContainer.innerHTML = '';

            // 重新添加所有标签
            tags.forEach(tag => {
                addTagToSidebar(tag.id, tag.name, tag.color, tag.description);
            });
        }

        // 标签颜色选择
        const colorOptions = document.querySelectorAll('.color-option');
        let selectedColor = colorOptions[0].style.backgroundColor;

        colorOptions.forEach(option => {
            option.addEventListener('click', function() {
                colorOptions.forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedColor = this.style.backgroundColor;
            });
        });

        // 添加标签
        const addTagBtn = document.getElementById('addTagBtn');
        const existingTagSelect = document.getElementById('existingTagSelect');
        const newTagInput = document.getElementById('newTagInput');
        const tagsList = document.querySelector('.tags-list');

        addTagBtn.addEventListener('click', function() {
            if (existingTagSelect.value) {
                // 添加现有标签
                const selectedOption = existingTagSelect.options[existingTagSelect.selectedIndex];
                const tagText = selectedOption.text;
                const tagId = selectedOption.value;

                // 检查是否已存在
                const existingTags = document.querySelectorAll('.tag-item');
                let tagExists = false;
                existingTags.forEach(tag => {
                    if (tag.getAttribute('data-tag-id') === tagId) {
                        tagExists = true;
                    }
                });

                if (!tagExists) {
                    addTagToList(tagId, tagText, getRandomTagColor(), '');
                    existingTagSelect.value = '';
                }
            } else if (newTagInput.value.trim()) {
                // 添加新标签
                const tagText = newTagInput.value.trim();
                const tagId = Date.now().toString(); // 生成临时ID
                const tagDesc = tagDescription ? tagDescription.value.trim() : '';

                addTagToList(tagId, tagText, selectedColor, tagDesc);
                newTagInput.value = '';
                if (tagDescription) tagDescription.value = '';
            }
        });

        // 添加标签到列表
        function addTagToList(id, text, color, description) {
            const tagItem = document.createElement('span');
            tagItem.className = 'tag-item';
            tagItem.setAttribute('data-tag-id', id);
            if (description) {
                tagItem.setAttribute('data-tag-desc', description);
            }

            tagItem.innerHTML = `
                <i class="fas fa-tag" style="color: ${color};"></i>
                ${text}
                <div class="tag-actions">
                    <i class="fas fa-info-circle tag-info" title="查看备注"></i>
                    <i class="fas fa-times tag-remove" title="移除标签"></i>
                </div>
            `;

            // 添加删除事件
            const removeBtn = tagItem.querySelector('.tag-remove');
            removeBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                e.preventDefault();
                tagItem.remove();

                // 同步删除左侧面板中的标签
                removeTagFromSidebar(id);
            });

            // 添加查看备注事件
            const infoBtn = tagItem.querySelector('.tag-info');
            infoBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                e.preventDefault();
                showTagDescription(tagItem);
            });

            tagsList.appendChild(tagItem);

            // 同步添加到左侧面板
            addTagToSidebar(id, text, color, description);
        }

        // 添加标签到左侧面板
        function addTagToSidebar(id, text, color, description) {
            // 检查标签是否已存在
            const existingTag = document.querySelector(`#sidebar-tags-container .nav-item[data-tag-id="${id}"]`);
            if (existingTag) {
                // 如果已存在，只更新描述
                existingTag.setAttribute('title', description || '');
                return;
            }

            // 创建新标签项
            const sidebarTagsContainer = document.getElementById('sidebar-tags-container');
            const tagItem = document.createElement('a');
            tagItem.href = '#';
            tagItem.className = 'nav-item';
            tagItem.setAttribute('data-tag-id', id);
            tagItem.setAttribute('title', description || '');

            tagItem.innerHTML = `
                <i class="fas fa-tag nav-icon" style="color: ${color};"></i>
                <span class="nav-text">${text}</span>
            `;

            // 添加自定义tooltip
            enhanceWithTooltip(tagItem, description);

            sidebarTagsContainer.appendChild(tagItem);
        }

        // 从左侧面板删除标签
        function removeTagFromSidebar(id) {
            const sidebarTag = document.querySelector(`#sidebar-tags-container .nav-item[data-tag-id="${id}"]`);
            if (sidebarTag) {
                sidebarTag.remove();
            }
        }

        // 为元素添加自定义tooltip
        function enhanceWithTooltip(element, tooltipText) {
            if (!tooltipText) return;

            // 移除title属性，避免显示两个tooltip
            element.removeAttribute('title');

            // 添加自定义tooltip类
            element.classList.add('custom-tooltip');

            // 创建tooltip文本元素
            const tooltip = document.createElement('span');
            tooltip.className = 'tooltip-text';

            // 处理长文本，确保适合显示
            const maxLength = 150; // 最大字符数
            let displayText = tooltipText;

            if (tooltipText.length > maxLength) {
                // 如果文本太长，截断并添加省略号
                displayText = tooltipText.substring(0, maxLength) + '...';
            }

            tooltip.textContent = displayText;

            // 添加到元素中
            element.appendChild(tooltip);
        }

        // 显示标签备注
        function showTagDescription(tagItem) {
            const tagId = tagItem.getAttribute('data-tag-id');
            const tagName = tagItem.textContent.trim().replace('查看备注', '').replace('移除标签', '').trim();
            const tagDesc = tagItem.getAttribute('data-tag-desc') || '该标签暂无备注说明';
            const tagColor = tagItem.querySelector('.fa-tag').style.color;

            // 设置弹窗内容
            tagDescTitle.textContent = tagName;
            tagDescContent.textContent = tagDesc;
            tagDescIcon.style.color = tagColor;

            // 保存当前标签ID，用于编辑功能
            tagDescriptionModal.setAttribute('data-tag-id', tagId);

            // 确保显示查看模式，隐藏编辑模式
            document.getElementById('tagDescViewMode').style.display = 'block';
            document.getElementById('tagDescEditMode').style.display = 'none';

            // 显示弹窗
            tagDescriptionModal.classList.add('active');
        }

        // 获取随机标签颜色
        function getRandomTagColor() {
            const colors = [
                'var(--primary)',
                'var(--secondary)',
                'var(--warning)',
                'var(--error)',
                '#9c27b0'
            ];
            return colors[Math.floor(Math.random() * colors.length)];
        }

        // 确保现有的标签也能被删除和查看备注
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有标签删除按钮添加事件
            const allTagRemoveBtns = document.querySelectorAll('.tag-remove');
            allTagRemoveBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    e.preventDefault();
                    const tagItem = this.closest('.tag-item');
                    if (tagItem) {
                        const tagId = tagItem.getAttribute('data-tag-id');
                        tagItem.remove();
                        // 同步删除左侧面板中的标签
                        removeTagFromSidebar(tagId);
                    }
                });
            });

            // 为所有标签信息按钮添加事件
            const allTagInfoBtns = document.querySelectorAll('.tag-info');
            allTagInfoBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    e.preventDefault();
                    const tagItem = this.closest('.tag-item');
                    if (tagItem) {
                        showTagDescription(tagItem);
                    }
                });
            });

            // 为左侧面板的标签添加自定义tooltip
            const sidebarTags = document.querySelectorAll('#sidebar-tags-container .nav-item');
            sidebarTags.forEach(tag => {
                const description = tag.getAttribute('title');
                if (description) {
                    enhanceWithTooltip(tag, description);
                }
            });

            // 初始化时同步标签数据
            initSyncTags();
        });

        // 初始化同步标签数据
        function initSyncTags() {
            // 收集所有标签数据
            const tags = [];
            const tagItems = document.querySelectorAll('.tag-item');

            tagItems.forEach(item => {
                const tagId = item.getAttribute('data-tag-id');
                const tagName = item.textContent.trim().replace('查看备注', '').replace('移除标签', '').trim();
                const tagDesc = item.getAttribute('data-tag-desc') || '';
                const tagColor = item.querySelector('.fa-tag').style.color;

                tags.push({
                    id: tagId,
                    name: tagName,
                    description: tagDesc,
                    color: tagColor
                });
            });

            // 同步到左侧面板
            syncTagsWithSidebar(tags);
        }

        // 筛选功能
        const filterModal = document.getElementById('filterModal');
        const filterBtn = document.getElementById('filterBtn');
        const closeFilterModalBtn = document.getElementById('closeFilterModalBtn');
        const resetFilterBtn = document.getElementById('resetFilterBtn');
        const applyFilterBtn = document.getElementById('applyFilterBtn');
        const completionRateMinSlider = document.getElementById('completionRateMinSlider');
        const completionRateMaxSlider = document.getElementById('completionRateMaxSlider');
        const completionRateMin = document.getElementById('completionRateMin');
        const completionRateMax = document.getElementById('completionRateMax');

        // 打开筛选面板
        filterBtn.addEventListener('click', function() {
            filterModal.classList.add('active');
            document.body.style.overflow = 'hidden';

            // 初始化范围滑块
            updateRangeSlider();
        });

        // 关闭筛选面板
        closeFilterModalBtn.addEventListener('click', function() {
            filterModal.classList.remove('active');
            document.body.style.overflow = '';
        });

        // 点击筛选面板外部关闭
        filterModal.addEventListener('click', function(e) {
            if (e.target === filterModal) {
                filterModal.classList.remove('active');
                document.body.style.overflow = '';
            }
        });

        // 重置筛选条件
        resetFilterBtn.addEventListener('click', function() {
            // 重置所有复选框
            const checkboxes = filterModal.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });

            // 重置科室选择
            const deptSelect = document.getElementById('filterDeptSelect');
            deptSelect.value = '';

            // 重置完成率范围
            completionRateMinSlider.value = 0;
            completionRateMaxSlider.value = 100;
            updateRangeSlider();

            // 显示所有指标
            showAllIndicators();
        });

        // 应用筛选
        applyFilterBtn.addEventListener('click', function() {
            // 收集筛选条件
            const filters = {
                categories: [],
                tags: [],
                statuses: [],
                sources: [],
                department: document.getElementById('filterDeptSelect').value,
                completionRate: {
                    min: parseInt(completionRateMinSlider.value),
                    max: parseInt(completionRateMaxSlider.value)
                }
            };

            // 收集类别筛选
            filterModal.querySelectorAll('input[name="category"]:checked').forEach(checkbox => {
                filters.categories.push(checkbox.value);
            });

            // 收集标签筛选
            filterModal.querySelectorAll('input[name="tag"]:checked').forEach(checkbox => {
                filters.tags.push(checkbox.value);
            });

            // 收集状态筛选
            filterModal.querySelectorAll('input[name="status"]:checked').forEach(checkbox => {
                filters.statuses.push(checkbox.value);
            });

            // 收集数据来源筛选
            filterModal.querySelectorAll('input[name="source"]:checked').forEach(checkbox => {
                filters.sources.push(checkbox.value);
            });

            // 应用筛选
            applyFilters(filters);

            // 关闭筛选面板
            filterModal.classList.remove('active');
            document.body.style.overflow = '';

            // 显示筛选状态
            showFilterStatus(filters);
        });

        // 更新范围滑块
        function updateRangeSlider() {
            const minVal = parseInt(completionRateMinSlider.value);
            const maxVal = parseInt(completionRateMaxSlider.value);

            // 确保最小值不大于最大值
            if (minVal > maxVal) {
                completionRateMinSlider.value = maxVal;
                completionRateMin.textContent = maxVal + '%';
            } else {
                completionRateMin.textContent = minVal + '%';
            }

            // 确保最大值不小于最小值
            if (maxVal < minVal) {
                completionRateMaxSlider.value = minVal;
                completionRateMax.textContent = minVal + '%';
            } else {
                completionRateMax.textContent = maxVal + '%';
            }

            // 更新填充条
            const rangeSliderFill = document.querySelector('.range-slider-fill');
            const percent1 = (minVal / 100) * 100;
            const percent2 = (maxVal / 100) * 100;
            rangeSliderFill.style.left = percent1 + '%';
            rangeSliderFill.style.width = (percent2 - percent1) + '%';
        }

        // 监听滑块变化
        completionRateMinSlider.addEventListener('input', updateRangeSlider);
        completionRateMaxSlider.addEventListener('input', updateRangeSlider);

        // 应用筛选条件
        function applyFilters(filters) {
            // 获取所有指标卡片和列表项
            const cards = document.querySelectorAll('.card');
            const listItems = document.querySelectorAll('.list-item:not(.child-list-item)');

            // 筛选卡片视图
            cards.forEach(card => {
                // 这里应该根据实际数据结构进行筛选
                // 以下是模拟筛选逻辑
                const shouldShow = Math.random() > 0.3; // 随机筛选，实际应用中应根据filters条件判断

                if (shouldShow) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });

            // 筛选列表视图
            listItems.forEach(item => {
                // 这里应该根据实际数据结构进行筛选
                // 以下是模拟筛选逻辑
                const shouldShow = Math.random() > 0.3; // 随机筛选，实际应用中应根据filters条件判断

                if (shouldShow) {
                    item.style.display = '';

                    // 如果是父指标，同时显示其子指标
                    const nextElement = item.nextElementSibling;
                    if (nextElement && nextElement.classList.contains('child-list-items')) {
                        nextElement.style.display = '';
                    }
                } else {
                    item.style.display = 'none';

                    // 如果是父指标，同时隐藏其子指标
                    const nextElement = item.nextElementSibling;
                    if (nextElement && nextElement.classList.contains('child-list-items')) {
                        nextElement.style.display = 'none';
                    }
                }
            });

            // 显示筛选结果数量
            const visibleCards = document.querySelectorAll('.card[style=""]').length;
            console.log(`筛选结果: 显示 ${visibleCards} 个指标`);
        }

        // 显示所有指标
        function showAllIndicators() {
            const cards = document.querySelectorAll('.card');
            const listItems = document.querySelectorAll('.list-item, .child-list-items');

            cards.forEach(card => {
                card.style.display = '';
            });

            listItems.forEach(item => {
                item.style.display = '';
            });
        }

        // 显示筛选状态
        function showFilterStatus(filters) {
            // 在实际应用中，可以在界面上显示当前筛选条件
            console.log('应用筛选条件:', filters);

            // 更新筛选按钮状态，显示有筛选条件激活
            const hasActiveFilters =
                filters.categories.length < 5 ||
                filters.tags.length < 3 ||
                filters.statuses.length < 3 ||
                filters.sources.length < 5 ||
                filters.department !== '' ||
                filters.completionRate.min > 0 ||
                filters.completionRate.max < 100;

            if (hasActiveFilters) {
                filterBtn.classList.add('active');
                filterBtn.innerHTML = '<i class="fas fa-filter btn-icon"></i> 筛选 <span class="filter-badge"></span>';
            } else {
                filterBtn.classList.remove('active');
                filterBtn.innerHTML = '<i class="fas fa-filter btn-icon"></i> 筛选';
            }
        }

        // 科室管理功能
        const deptManageModal = document.getElementById('deptManageModal');
        const manageDeptBtn = document.getElementById('manageDeptBtn');
        const closeDeptModalBtn = document.getElementById('closeDeptModalBtn');

        // 打开科室管理弹窗
        manageDeptBtn.addEventListener('click', function() {
            deptManageModal.classList.add('active');
            document.body.style.overflow = 'hidden';

            // 确保所有科室删除按钮都有正确的事件监听器
            setTimeout(() => {
                const deptRemoveBtns = deptManageModal.querySelectorAll('.dept-remove');
                deptRemoveBtns.forEach(btn => {
                    // 移除旧的事件监听器（避免重复）
                    const newBtn = btn.cloneNode(true);
                    btn.parentNode.replaceChild(newBtn, btn);

                    // 添加新的事件监听器
                    newBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        e.preventDefault();
                        const deptItem = this.closest('.dept-item');
                        if (deptItem) {
                            deptItem.remove();
                        }
                    });
                });
            }, 100);
        });

        // 关闭科室管理弹窗
        closeDeptModalBtn.addEventListener('click', function() {
            deptManageModal.classList.remove('active');
            document.body.style.overflow = '';
        });

        // 点击弹窗外部关闭
        deptManageModal.addEventListener('click', function(e) {
            if (e.target === deptManageModal) {
                deptManageModal.classList.remove('active');
                document.body.style.overflow = '';
            }
        });

        // 科室管理选项卡切换
        const deptTabBtns = document.querySelectorAll('.tabs-container .tab-btn');
        deptTabBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const tabId = btn.getAttribute('data-tab');

                // 移除所有选项卡的激活状态
                deptTabBtns.forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tabs-container .tab-content').forEach(content => {
                    content.classList.remove('active');
                });

                // 激活选中的选项卡
                btn.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // 添加科室
        const addLeadDeptBtn = document.getElementById('addLeadDeptBtn');
        const addNumeratorDeptBtn = document.getElementById('addNumeratorDeptBtn');
        const addDenominatorDeptBtn = document.getElementById('addDenominatorDeptBtn');

        const leadDeptSelect = document.getElementById('leadDeptSelect');
        const numeratorDeptSelect = document.getElementById('numeratorDeptSelect');
        const denominatorDeptSelect = document.getElementById('denominatorDeptSelect');

        const deptsLists = document.querySelectorAll('.depts-list');

        // 添加牵头科室
        addLeadDeptBtn.addEventListener('click', function() {
            if (leadDeptSelect.value) {
                const selectedOption = leadDeptSelect.options[leadDeptSelect.selectedIndex];
                const deptText = selectedOption.text;
                const deptId = selectedOption.value;

                // 检查是否已存在
                const existingDepts = deptsLists[0].querySelectorAll('.dept-item');
                let deptExists = false;
                existingDepts.forEach(dept => {
                    if (dept.getAttribute('data-dept-id') === deptId) {
                        deptExists = true;
                    }
                });

                if (!deptExists) {
                    addDeptToList(deptsLists[0], deptId, deptText);
                    leadDeptSelect.value = '';
                }
            }
        });

        // 添加分子科室
        addNumeratorDeptBtn.addEventListener('click', function() {
            if (numeratorDeptSelect.value) {
                const selectedOption = numeratorDeptSelect.options[numeratorDeptSelect.selectedIndex];
                const deptText = selectedOption.text;
                const deptId = selectedOption.value;

                // 检查是否已存在
                const existingDepts = deptsLists[1].querySelectorAll('.dept-item');
                let deptExists = false;
                existingDepts.forEach(dept => {
                    if (dept.getAttribute('data-dept-id') === deptId) {
                        deptExists = true;
                    }
                });

                if (!deptExists) {
                    addDeptToList(deptsLists[1], deptId, deptText);
                    numeratorDeptSelect.value = '';
                }
            }
        });

        // 添加分母科室
        addDenominatorDeptBtn.addEventListener('click', function() {
            if (denominatorDeptSelect.value) {
                const selectedOption = denominatorDeptSelect.options[denominatorDeptSelect.selectedIndex];
                const deptText = selectedOption.text;
                const deptId = selectedOption.value;

                // 检查是否已存在
                const existingDepts = deptsLists[2].querySelectorAll('.dept-item');
                let deptExists = false;
                existingDepts.forEach(dept => {
                    if (dept.getAttribute('data-dept-id') === deptId) {
                        deptExists = true;
                    }
                });

                if (!deptExists) {
                    addDeptToList(deptsLists[2], deptId, deptText);
                    denominatorDeptSelect.value = '';
                }
            }
        });

        // 添加科室到列表
        function addDeptToList(list, id, text) {
            const deptItem = document.createElement('span');
            deptItem.className = 'dept-item';
            deptItem.setAttribute('data-dept-id', id);

            deptItem.innerHTML = `
                ${text}
                <i class="fas fa-times dept-remove"></i>
            `;

            // 添加删除事件
            const removeBtn = deptItem.querySelector('.dept-remove');
            removeBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                e.preventDefault();
                deptItem.remove();
            });

            list.appendChild(deptItem);
        }

        // 确保现有的科室项也能被删除
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有现有的科室删除按钮添加事件监听器
            const allDeptRemoveBtns = document.querySelectorAll('.dept-remove');
            allDeptRemoveBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    e.preventDefault();
                    const deptItem = this.closest('.dept-item');
                    if (deptItem) {
                        deptItem.remove();
                    }
                });
            });

            // 关联指标功能
            initRelatedIndicators();
        });

        // 初始化关联指标功能
        function initRelatedIndicators() {
            const addRelatedIndicatorBtn = document.getElementById('addRelatedIndicatorBtn');
            const relatedIndicatorModal = document.getElementById('relatedIndicatorModal');
            const cancelRelatedIndicatorBtn = document.getElementById('cancelRelatedIndicatorBtn');
            const confirmRelatedIndicatorBtn = document.getElementById('confirmRelatedIndicatorBtn');
            const closeRelatedIndicatorModalBtn = document.querySelector('.related-indicator-modal-close');
            const indicatorSearchInput = document.getElementById('indicatorSearchInput');

            // 为所有关联指标的删除按钮添加事件
            const allRelatedIndicatorRemoveBtns = document.querySelectorAll('.related-indicator-remove');
            allRelatedIndicatorRemoveBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    e.preventDefault();
                    const indicatorItem = this.closest('.related-indicator-item');
                    if (indicatorItem) {
                        // 获取指标ID，用于在选择列表中取消选中
                        const indicatorId = indicatorItem.getAttribute('data-indicator-id');
                        const checkbox = document.getElementById(`indicator-${indicatorId}`);
                        if (checkbox) {
                            checkbox.checked = false;
                        }
                        indicatorItem.remove();
                    }
                });
            });

            // 打开关联指标选择弹窗
            if (addRelatedIndicatorBtn) {
                addRelatedIndicatorBtn.addEventListener('click', function() {
                    relatedIndicatorModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            // 关闭关联指标选择弹窗
            if (cancelRelatedIndicatorBtn) {
                cancelRelatedIndicatorBtn.addEventListener('click', function() {
                    relatedIndicatorModal.classList.remove('active');
                    document.body.style.overflow = '';
                });
            }

            if (closeRelatedIndicatorModalBtn) {
                closeRelatedIndicatorModalBtn.addEventListener('click', function() {
                    relatedIndicatorModal.classList.remove('active');
                    document.body.style.overflow = '';
                });
            }

            // 确认选择的关联指标
            if (confirmRelatedIndicatorBtn) {
                confirmRelatedIndicatorBtn.addEventListener('click', function() {
                    // 获取所有选中的指标
                    const selectedCheckboxes = document.querySelectorAll('.related-indicator-select-item input[type="checkbox"]:checked');
                    const relatedIndicatorsList = document.querySelector('.related-indicators-list');

                    // 清空当前列表
                    relatedIndicatorsList.innerHTML = '';

                    // 添加选中的指标到列表
                    selectedCheckboxes.forEach(checkbox => {
                        const indicatorItem = checkbox.closest('.related-indicator-select-item');
                        const indicatorId = indicatorItem.getAttribute('data-indicator-id');
                        const indicatorName = indicatorItem.querySelector('.related-indicator-select-name').textContent;

                        // 创建新的指标项
                        const newIndicatorItem = document.createElement('div');
                        newIndicatorItem.className = 'related-indicator-item';
                        newIndicatorItem.setAttribute('data-indicator-id', indicatorId);

                        newIndicatorItem.innerHTML = `
                            <div class="related-indicator-info">
                                <span class="related-indicator-id">${indicatorId}</span>
                                <span class="related-indicator-name">${indicatorName}</span>
                            </div>
                            <button type="button" class="related-indicator-remove" title="移除关联">
                                <i class="fas fa-times"></i>
                            </button>
                        `;

                        // 添加删除事件
                        const removeBtn = newIndicatorItem.querySelector('.related-indicator-remove');
                        removeBtn.addEventListener('click', function(e) {
                            e.stopPropagation();
                            e.preventDefault();
                            const checkbox = document.getElementById(`indicator-${indicatorId}`);
                            if (checkbox) {
                                checkbox.checked = false;
                            }
                            newIndicatorItem.remove();
                        });

                        relatedIndicatorsList.appendChild(newIndicatorItem);
                    });

                    // 关闭弹窗
                    relatedIndicatorModal.classList.remove('active');
                    document.body.style.overflow = '';
                });
            }

            // 搜索功能
            if (indicatorSearchInput) {
                indicatorSearchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const indicatorItems = document.querySelectorAll('.related-indicator-select-item');

                    indicatorItems.forEach(item => {
                        const indicatorId = item.querySelector('.related-indicator-select-id').textContent;
                        const indicatorName = item.querySelector('.related-indicator-select-name').textContent;
                        const matchesSearch =
                            indicatorId.toLowerCase().includes(searchTerm) ||
                            indicatorName.toLowerCase().includes(searchTerm);

                        item.style.display = matchesSearch ? '' : 'none';
                    });

                    // 更新类别显示/隐藏
                    const categories = document.querySelectorAll('.related-indicator-category');
                    categories.forEach(category => {
                        const visibleItems = category.querySelectorAll('.related-indicator-select-item[style=""]').length;
                        category.style.display = visibleItems > 0 ? '' : 'none';
                    });
                });
            }

            // 为指标选择项添加点击事件（整行可点击）
            const indicatorSelectItems = document.querySelectorAll('.related-indicator-select-item');
            indicatorSelectItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // 如果点击的是复选框本身，不做处理
                    if (e.target.tagName === 'INPUT' || e.target.tagName === 'LABEL') {
                        return;
                    }

                    const checkbox = this.querySelector('input[type="checkbox"]');
                    checkbox.checked = !checkbox.checked;
                });
            });
        }

        // 操作维护功能
        const operationManageModal = document.getElementById('operationManageModal');
        const operationManageBtn = document.getElementById('operationManageBtn');
        const closeOperationModalBtn = document.getElementById('closeOperationModalBtn');
        const cancelOperationBtn = document.getElementById('cancelOperationBtn');
        const saveOperationBtn = document.getElementById('saveOperationBtn');

        // 打开操作维护弹窗
        operationManageBtn.addEventListener('click', function() {
            operationManageModal.classList.add('active');
            document.body.style.overflow = 'hidden';
        });

        // 关闭操作维护弹窗
        function closeOperationModal() {
            operationManageModal.classList.remove('active');
            document.body.style.overflow = '';
        }

        closeOperationModalBtn.addEventListener('click', closeOperationModal);
        cancelOperationBtn.addEventListener('click', closeOperationModal);

        // 点击弹窗外部关闭
        operationManageModal.addEventListener('click', function(e) {
            if (e.target === operationManageModal) {
                closeOperationModal();
            }
        });

        // 操作维护选项卡切换
        const operationTabBtns = document.querySelectorAll('.operation-tabs .tab-btn');
        operationTabBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const tabId = btn.getAttribute('data-tab');

                // 移除所有选项卡的激活状态
                operationTabBtns.forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.operation-tabs .tab-content').forEach(content => {
                    content.classList.remove('active');
                });

                // 激活选中的选项卡
                btn.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // 保存操作设置
        saveOperationBtn.addEventListener('click', function() {
            // 这里可以添加保存逻辑
            alert('操作设置已保存！');
            closeOperationModal();
        });
    </script>
</body>
</html>