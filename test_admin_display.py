#!/usr/bin/env python3
"""
后端管理界面显示效果测试
"""

import requests
import time

def test_admin_page_access():
    """测试后端管理页面访问"""
    print("🧪 测试后端管理页面访问...")
    
    try:
        # 测试指标详情页面
        response = requests.get('http://localhost:5001/admin/indicators/1.3.1')
        
        if response.status_code == 200:
            print("✅ 后端管理页面访问成功")
            
            # 检查页面内容
            content = response.text
            
            # 检查关键元素
            checks = [
                ('childrenNavCard', '子指标导航卡片'),
                ('children-count', '子指标数量徽章'),
                ('children-nav-grid', '子指标导航网格'),
                ('toggleChildrenCollapse', '折叠功能'),
                ('renderChildrenTable', '渲染函数')
            ]
            
            for element, description in checks:
                if element in content:
                    print(f"✅ {description} 存在")
                else:
                    print(f"❌ {description} 缺失")
            
            return True
        else:
            print(f"❌ 页面访问失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 页面访问异常: {e}")
        return False

def test_api_data():
    """测试API数据"""
    print("\n🧪 测试API数据...")
    
    try:
        response = requests.get('http://localhost:5001/api/indicators/1.3.1')
        data = response.json()
        
        if data['success']:
            indicator = data['data']['indicator']
            children = data['data']['children']
            
            print(f"✅ API数据获取成功")
            print(f"   指标ID: {indicator['id']}")
            print(f"   指标名称: {indicator['name']}")
            print(f"   指标类型: {indicator.get('indicator_type', 'composite')}")
            print(f"   子指标数量: {len(children)}")
            
            if len(children) > 0:
                print("   子指标列表:")
                for child in children:
                    print(f"     - {child['id']}: {child['name']}")
            
            return True
        else:
            print(f"❌ API请求失败: {data.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ API请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 后端管理界面显示效果测试")
    print("=" * 50)
    
    tests = [
        ("后端管理页面访问测试", test_admin_page_access),
        ("API数据测试", test_api_data)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 后端管理界面基本功能正常")
        print("\n📝 建议手动验证以下内容:")
        print("1. 访问 http://localhost:5001/admin/indicators/1.3.1")
        print("2. 检查子指标模块是否显示为卡片式")
        print("3. 检查子指标数量徽章是否正确")
        print("4. 检查折叠/展开功能是否工作")
        print("5. 检查子指标点击导航是否正常")
        return True
    else:
        print("⚠️  部分功能异常，请检查相关配置")
        return False

if __name__ == "__main__":
    main()
