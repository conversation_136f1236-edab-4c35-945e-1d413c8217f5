-- 医院等级评审指标管理系统 - 数据库清理脚本
-- 警告：此脚本将清空所有表中的数据，请谨慎使用！

USE hospital_indicator_system;

-- ========================================
-- 清空所有表数据（保留表结构）
-- ========================================

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 清空数据表（按依赖关系顺序）
TRUNCATE TABLE data_audit_flow;
TRUNCATE TABLE indicator_data_history;
TRUNCATE TABLE indicator_relations;
TRUNCATE TABLE indicator_departments;
TRUNCATE TABLE indicator_tags;
TRUNCATE TABLE indicators;
TRUNCATE TABLE sections;
TRUNCATE TABLE chapters;
TRUNCATE TABLE tags;
TRUNCATE TABLE users;
TRUNCATE TABLE departments;
TRUNCATE TABLE system_config;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 重置自增ID
ALTER TABLE chapters AUTO_INCREMENT = 1;
ALTER TABLE sections AUTO_INCREMENT = 1;
ALTER TABLE tags AUTO_INCREMENT = 1;
ALTER TABLE departments AUTO_INCREMENT = 1;
ALTER TABLE users AUTO_INCREMENT = 1;
ALTER TABLE indicator_tags AUTO_INCREMENT = 1;
ALTER TABLE indicator_departments AUTO_INCREMENT = 1;
ALTER TABLE indicator_relations AUTO_INCREMENT = 1;
ALTER TABLE indicator_data_history AUTO_INCREMENT = 1;
ALTER TABLE data_audit_flow AUTO_INCREMENT = 1;
ALTER TABLE system_config AUTO_INCREMENT = 1;

-- 显示清理完成信息
SELECT '数据库清理完成！所有表数据已清空，表结构保留。' AS message;

-- 显示各表记录数（应该都为0）
SELECT 
    'chapters' AS table_name, 
    COUNT(*) AS record_count 
FROM chapters
UNION ALL
SELECT 
    'sections' AS table_name, 
    COUNT(*) AS record_count 
FROM sections
UNION ALL
SELECT 
    'indicators' AS table_name, 
    COUNT(*) AS record_count 
FROM indicators
UNION ALL
SELECT 
    'tags' AS table_name, 
    COUNT(*) AS record_count 
FROM tags
UNION ALL
SELECT 
    'departments' AS table_name, 
    COUNT(*) AS record_count 
FROM departments
UNION ALL
SELECT 
    'users' AS table_name, 
    COUNT(*) AS record_count 
FROM users
UNION ALL
SELECT 
    'indicator_tags' AS table_name, 
    COUNT(*) AS record_count 
FROM indicator_tags
UNION ALL
SELECT 
    'indicator_departments' AS table_name, 
    COUNT(*) AS record_count 
FROM indicator_departments
UNION ALL
SELECT 
    'indicator_relations' AS table_name, 
    COUNT(*) AS record_count 
FROM indicator_relations
UNION ALL
SELECT 
    'indicator_data_history' AS table_name, 
    COUNT(*) AS record_count 
FROM indicator_data_history
UNION ALL
SELECT 
    'data_audit_flow' AS table_name, 
    COUNT(*) AS record_count 
FROM data_audit_flow
UNION ALL
SELECT 
    'system_config' AS table_name, 
    COUNT(*) AS record_count 
FROM system_config;
