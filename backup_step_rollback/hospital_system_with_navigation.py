#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院指标管理系统 - 包含前端后端跳转功能
"""

from flask import Flask, request, jsonify
import sqlite3
import os

app = Flask(__name__)

# 数据库路径
DB_PATH = 'DATABASE-HOSPITAL/hospital_indicator_system.db'

def get_db_connection():
    """获取数据库连接"""
    if not os.path.exists(DB_PATH):
        raise Exception(f"数据库文件不存在: {DB_PATH}")
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def dict_from_row(row):
    """将sqlite3.Row转换为字典"""
    return dict(zip(row.keys(), row)) if row else None

# ==================== 前端系统 ====================

@app.route('/')
def frontend_home():
    """前端主页 - 指标查看系统"""
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>医院指标查看系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .container { max-width: 1200px; margin: 0 auto; }
            .header { background: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; }
            .nav-buttons { display: flex; gap: 15px; justify-content: center; margin-top: 20px; flex-wrap: wrap; }
            .nav-btn { padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 14px; font-weight: 500; transition: all 0.3s ease; }
            .nav-btn-primary { background: #007bff; color: white; }
            .nav-btn-success { background: #28a745; color: white; }
            .nav-btn-warning { background: #ffc107; color: #212529; }
            .nav-btn-danger { background: #dc3545; color: white; }
            .nav-btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
            .admin-switch { position: fixed; top: 20px; right: 20px; z-index: 1000; }
            .admin-switch a { background: #ff6b6b; color: white; padding: 10px 20px; border-radius: 25px; text-decoration: none; font-weight: bold; box-shadow: 0 2px 10px rgba(0,0,0,0.2); transition: all 0.3s ease; }
            .admin-switch a:hover { background: #ff5252; transform: scale(1.05); }
            .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 30px; }
            .feature-card { background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; }
            .feature-icon { font-size: 48px; margin-bottom: 15px; }
        </style>
    </head>
    <body>
        <div class="admin-switch">
            <a href="/admin">🔧 后台管理</a>
        </div>

        <div class="container">
            <div class="header">
                <h1 style="color: #333; margin: 0 0 10px 0;">🏥 医院指标查看系统</h1>
                <p style="color: #666; margin: 0; font-size: 16px;">查看和浏览医院等级评审指标</p>

                <div class="nav-buttons">
                    <a href="/indicators" class="nav-btn nav-btn-primary">📊 浏览指标</a>
                    <a href="/chapters" class="nav-btn nav-btn-success">📚 按章节查看</a>
                    <a href="/search" class="nav-btn nav-btn-warning">🔍 搜索指标</a>
                    <a href="/help" class="nav-btn nav-btn-danger">❓ 使用帮助</a>
                </div>
            </div>

            <div class="features">
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>指标浏览</h3>
                    <p>按章节、小节浏览所有医院指标，查看详细的分子分母信息</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <h3>智能搜索</h3>
                    <p>快速搜索指标名称、编号或描述，精准定位所需指标</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📋</div>
                    <h3>详细信息</h3>
                    <p>查看指标的完整信息，包括计算方法、数据来源、责任科室</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🌳</div>
                    <h3>层级关系</h3>
                    <p>清晰展示指标的父子关系，支持多级指标结构</p>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/indicators')
def frontend_indicators():
    """前端指标列表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取指标数据
        cursor.execute("""
            SELECT i.*, c.name as chapter_name, c.code as chapter_code,
                   s.name as section_name, s.code as section_code,
                   COUNT(child.id) as children_count
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN sections s ON i.section_id = s.id
            LEFT JOIN indicators child ON child.parent_id = i.id AND child.is_active = 1
            WHERE i.is_active = 1
            GROUP BY i.id
            ORDER BY i.id
            LIMIT 20
        """)

        indicators = [dict_from_row(row) for row in cursor.fetchall()]
        conn.close()

        indicators_html = ""
        for indicator in indicators:
            indicators_html += f'''
            <tr>
                <td style="padding: 12px; font-family: monospace; font-weight: bold; color: #007bff;">{indicator['id']}</td>
                <td style="padding: 12px;">{indicator['name']}</td>
                <td style="padding: 12px; text-align: center;">{indicator.get('chapter_code', '-')}</td>
                <td style="padding: 12px; text-align: center;">{indicator.get('section_code', '-')}</td>
                <td style="padding: 12px; text-align: center;">{indicator.get('children_count', 0)}</td>
                <td style="padding: 12px; text-align: center;">
                    <a href="/indicator/{indicator['id']}" style="background: #007bff; color: white; padding: 6px 12px; text-decoration: none; border-radius: 4px; font-size: 12px;">查看详情</a>
                </td>
            </tr>
            '''

        return f'''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>指标列表 - 医院指标查看系统</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                .header {{ background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }}
                .admin-switch {{ background: #ff6b6b; color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }}
                .admin-switch:hover {{ background: #ff5252; }}
                table {{ width: 100%; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                th {{ background: #f8f9fa; padding: 15px; text-align: left; font-weight: 600; border-bottom: 1px solid #dee2e6; }}
                td {{ padding: 12px; border-bottom: 1px solid #f1f3f4; }}
                tr:hover {{ background: #f8f9fa; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div>
                        <h1 style="margin: 0; color: #333;">📊 指标列表</h1>
                        <p style="margin: 5px 0 0 0; color: #666;">浏览医院等级评审指标</p>
                    </div>
                    <a href="/admin" class="admin-switch">🔧 后台管理</a>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>指标ID</th>
                            <th>指标名称</th>
                            <th>章节</th>
                            <th>小节</th>
                            <th>子指标</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {indicators_html}
                    </tbody>
                </table>

                <div style="margin-top: 20px; text-align: center;">
                    <a href="/" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">🏠 返回首页</a>
                </div>
            </div>
        </body>
        </html>
        '''

    except Exception as e:
        return f'<h1>错误</h1><p>{str(e)}</p>'

# ==================== 后端管理系统 ====================

@app.route('/admin')
def admin_home():
    """后端管理主页"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取统计数据
        cursor.execute("SELECT COUNT(*) FROM chapters WHERE is_active = 1")
        chapters_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM sections WHERE is_active = 1")
        sections_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM indicators WHERE is_active = 1")
        indicators_count = cursor.fetchone()[0]

        # 检查组件表是否存在
        try:
            cursor.execute("SELECT COUNT(*) FROM indicator_components WHERE is_active = 1")
            components_count = cursor.fetchone()[0]
        except:
            components_count = 0

        conn.close()

        return f'''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>后台管理系统</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                .header {{ background: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; position: relative; }}
                .frontend-switch {{ position: absolute; top: 20px; right: 20px; background: #28a745; color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }}
                .frontend-switch:hover {{ background: #218838; }}
                .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }}
                .stat-card {{ background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; }}
                .stat-number {{ font-size: 36px; font-weight: bold; margin-bottom: 10px; }}
                .stat-label {{ color: #666; font-size: 14px; }}
                .management-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
                .management-card {{ background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }}
                .management-card h3 {{ margin-top: 0; color: #333; }}
                .btn {{ padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; font-size: 14px; }}
                .btn-primary {{ background: #007bff; color: white; }}
                .btn-success {{ background: #28a745; color: white; }}
                .btn-warning {{ background: #ffc107; color: #212529; }}
                .btn-danger {{ background: #dc3545; color: white; }}
                .btn:hover {{ opacity: 0.9; transform: translateY(-1px); }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <a href="/" class="frontend-switch">🌐 前端系统</a>
                    <h1 style="color: #333; margin: 0 0 10px 0;">🔧 后台管理系统</h1>
                    <p style="color: #666; margin: 0; font-size: 16px;">医院指标数据管理和维护</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" style="color: #007bff;">{chapters_count}</div>
                        <div class="stat-label">章节总数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" style="color: #28a745;">{sections_count}</div>
                        <div class="stat-label">小节总数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" style="color: #ffc107;">{indicators_count}</div>
                        <div class="stat-label">指标总数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" style="color: #dc3545;">{components_count}</div>
                        <div class="stat-label">组件总数</div>
                    </div>
                </div>

                <div class="management-grid">
                    <div class="management-card">
                        <h3>📊 指标管理</h3>
                        <p>管理医院指标的基本信息、层级关系和分子分母组件</p>
                        <a href="/admin/indicators" class="btn btn-primary">指标列表</a>
                        <a href="/admin/components" class="btn btn-warning">组件管理</a>
                    </div>
                    <div class="management-card">
                        <h3>📚 结构管理</h3>
                        <p>管理指标体系的章节和小节结构</p>
                        <a href="/admin/chapters" class="btn btn-success">章节管理</a>
                        <a href="/admin/sections" class="btn btn-success">小节管理</a>
                    </div>
                    <div class="management-card">
                        <h3>🔧 系统工具</h3>
                        <p>数据验证、批量操作和系统维护工具</p>
                        <a href="/admin/validate" class="btn btn-danger">数据验证</a>
                        <a href="/admin/tools" class="btn btn-warning">系统工具</a>
                    </div>
                    <div class="management-card">
                        <h3>📈 数据分析</h3>
                        <p>查看系统使用情况和数据质量报告</p>
                        <a href="/admin/reports" class="btn btn-primary">数据报告</a>
                        <a href="/admin/logs" class="btn btn-warning">操作日志</a>
                    </div>
                </div>
            </div>
        </body>
        </html>
        '''

    except Exception as e:
        return f'<h1>错误</h1><p>{str(e)}</p>'

@app.route('/admin/components')
def admin_components():
    """后端组件管理"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 确保indicator_components表存在
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS indicator_components (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                indicator_id VARCHAR(20) NOT NULL,
                component_type VARCHAR(20) NOT NULL CHECK (component_type IN ('numerator', 'denominator', 'other')),
                name VARCHAR(200) NOT NULL,
                definition TEXT,
                unit VARCHAR(50),
                data_source VARCHAR(200),
                lead_department VARCHAR(100),
                logic_definition TEXT,
                collection_method VARCHAR(50),
                calculation_formula TEXT,
                notes TEXT,
                sort_order INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 检查是否有数据，如果没有则创建示例数据
        cursor.execute("SELECT COUNT(*) FROM indicator_components")
        count = cursor.fetchone()[0]

        if count == 0:
            # 插入示例数据
            demo_components = [
                ('1.3.2.1', 'numerator', '重症医学科开放床位数', '医院重症医学科实际开放并可收治患者的床位数量', '张', 'HIS系统', '重症医学科', '统计期末重症医学科实际开放的床位数'),
                ('1.3.2.1', 'denominator', '医院开放床位总数', '医院实际开放并可收治患者的床位总数', '张', 'HIS系统', '医务科', '统计期末医院实际开放的床位总数'),
                ('2.1.1', 'numerator', '门诊患者数', '统计期内门诊就诊的患者总数', '人次', 'HIS系统', '门诊部', '统计期内所有门诊科室的就诊人次总和'),
                ('2.1.1', 'denominator', '门诊工作日数', '统计期内门诊开放的工作日总数', '天', '人事系统', '医务科', '统计期内门诊正常开放的工作日数量')
            ]

            for comp in demo_components:
                cursor.execute("""
                    INSERT INTO indicator_components
                    (indicator_id, component_type, name, definition, unit, data_source, lead_department, logic_definition)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, comp)

            conn.commit()

        # 获取组件数据
        cursor.execute("SELECT * FROM indicator_components ORDER BY indicator_id, component_type")
        components = [dict_from_row(row) for row in cursor.fetchall()]

        conn.close()

        components_html = ""
        for comp in components:
            type_label = {'numerator': '🔢 分子', 'denominator': '🔣 分母', 'other': '📊 其他'}.get(comp['component_type'], '❓ 未知')
            components_html += f'''
            <tr>
                <td style="padding: 12px; text-align: center;">{comp['id']}</td>
                <td style="padding: 12px; text-align: center; font-family: monospace; font-weight: bold;">{comp['indicator_id']}</td>
                <td style="padding: 12px; text-align: center;">{type_label}</td>
                <td style="padding: 12px;">{comp['name']}</td>
                <td style="padding: 12px; text-align: center;">{comp.get('unit', '-')}</td>
                <td style="padding: 12px;">{comp.get('lead_department', '-')}</td>
                <td style="padding: 12px; text-align: center;">
                    <a href="/admin/components/{comp['id']}/edit" style="background: #ffc107; color: white; padding: 6px 12px; text-decoration: none; border-radius: 4px; font-size: 12px;">✏️ 编辑</a>
                </td>
            </tr>
            '''

        return f'''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>组件管理 - 后台管理系统</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                .header {{ background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }}
                .nav-switches {{ display: flex; gap: 10px; }}
                .nav-switch {{ padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }}
                .frontend-switch {{ background: #28a745; color: white; }}
                .admin-switch {{ background: #6c757d; color: white; }}
                .nav-switch:hover {{ opacity: 0.9; }}
                table {{ width: 100%; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                th {{ background: #f8f9fa; padding: 15px; text-align: left; font-weight: 600; border-bottom: 1px solid #dee2e6; }}
                td {{ padding: 12px; border-bottom: 1px solid #f1f3f4; }}
                tr:hover {{ background: #f8f9fa; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div>
                        <h1 style="margin: 0; color: #333;">🧮 组件管理</h1>
                        <p style="margin: 5px 0 0 0; color: #666;">管理指标的分子分母组件</p>
                    </div>
                    <div class="nav-switches">
                        <a href="/" class="nav-switch frontend-switch">🌐 前端系统</a>
                        <a href="/admin" class="nav-switch admin-switch">🔧 管理首页</a>
                    </div>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>指标ID</th>
                            <th>类型</th>
                            <th>名称</th>
                            <th>单位</th>
                            <th>牵头科室</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {components_html}
                    </tbody>
                </table>
            </div>
        </body>
        </html>
        '''

    except Exception as e:
        return f'<h1>错误</h1><p>{str(e)}</p>'

@app.route('/admin/components/<component_id>/edit', methods=['GET', 'POST'])
def edit_component(component_id):
    """编辑组件"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        if request.method == 'POST':
            # 处理表单提交
            data = request.form

            cursor.execute("""
                UPDATE indicator_components
                SET name = ?, definition = ?, unit = ?, data_source = ?,
                    lead_department = ?, logic_definition = ?, component_type = ?,
                    collection_method = ?, calculation_formula = ?, notes = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                data.get('name'),
                data.get('definition'),
                data.get('unit'),
                data.get('data_source'),
                data.get('lead_department'),
                data.get('logic_definition'),
                data.get('component_type'),
                data.get('collection_method'),
                data.get('calculation_formula'),
                data.get('notes'),
                component_id
            ))

            conn.commit()
            conn.close()

            return '''
            <script>
                alert('✅ 组件更新成功！');
                window.location.href = '/admin/components';
            </script>
            '''

        # GET请求 - 显示编辑表单
        cursor.execute("SELECT * FROM indicator_components WHERE id = ?", (component_id,))
        component = dict_from_row(cursor.fetchone())

        if not component:
            return f'<h1>❌ 组件不存在</h1><p>组件 {component_id} 不存在</p><p><a href="/admin/components">返回组件列表</a></p>'

        conn.close()

        return f'''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>编辑组件 {component_id}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
                .container {{ max-width: 800px; margin: 0 auto; }}
                .header {{ background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }}
                .nav-switches {{ display: flex; gap: 10px; }}
                .nav-switch {{ padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }}
                .frontend-switch {{ background: #28a745; color: white; }}
                .admin-switch {{ background: #6c757d; color: white; }}
                .nav-switch:hover {{ opacity: 0.9; }}
                .form-container {{ background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .form-group {{ margin-bottom: 20px; }}
                .form-group label {{ display: block; margin-bottom: 5px; font-weight: 600; color: #333; }}
                .form-group input, .form-group select, .form-group textarea {{ width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }}
                .form-group textarea {{ height: 80px; resize: vertical; }}
                .form-row {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }}
                .btn {{ padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin-right: 10px; font-size: 14px; }}
                .btn-primary {{ background: #007bff; color: white; }}
                .btn-secondary {{ background: #6c757d; color: white; }}
                .btn:hover {{ opacity: 0.9; }}
                .info {{ background: #e3f2fd; padding: 15px; border-radius: 4px; margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div>
                        <h1 style="margin: 0; color: #333;">🧮 编辑组件 #{component_id}</h1>
                        <p style="margin: 5px 0 0 0; color: #666;">修改组件的详细信息</p>
                    </div>
                    <div class="nav-switches">
                        <a href="/" class="nav-switch frontend-switch">🌐 前端系统</a>
                        <a href="/admin" class="nav-switch admin-switch">🔧 管理首页</a>
                    </div>
                </div>

                <div class="form-container">
                    <div class="info">
                        <strong>📊 指标：</strong>{component['indicator_id']} |
                        <strong>🏷️ 类型：</strong>{'🔢 分子' if component.get('component_type') == 'numerator' else '🔣 分母' if component.get('component_type') == 'denominator' else '📊 其他'}
                    </div>

                    <form method="POST">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="component_type">🏷️ 组件类型 *</label>
                                <select id="component_type" name="component_type" required>
                                    <option value="numerator" {'selected' if component.get('component_type') == 'numerator' else ''}>🔢 分子</option>
                                    <option value="denominator" {'selected' if component.get('component_type') == 'denominator' else ''}>🔣 分母</option>
                                    <option value="other" {'selected' if component.get('component_type') == 'other' else ''}>📊 其他</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="name">📝 组件名称 *</label>
                                <input type="text" id="name" name="name" value="{component.get('name', '')}" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="definition">📋 组件定义</label>
                            <textarea id="definition" name="definition" placeholder="详细描述组件的含义和计算逻辑">{component.get('definition', '')}</textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="unit">📏 单位</label>
                                <input type="text" id="unit" name="unit" value="{component.get('unit', '')}" placeholder="例如：人、张、次、%">
                            </div>
                            <div class="form-group">
                                <label for="lead_department">🏥 牵头科室</label>
                                <input type="text" id="lead_department" name="lead_department" value="{component.get('lead_department', '')}" placeholder="负责此数据的科室">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="data_source">📊 数据来源</label>
                            <input type="text" id="data_source" name="data_source" value="{component.get('data_source', '')}" placeholder="例如：HIS系统、手工统计、第三方系统">
                        </div>

                        <div class="form-group">
                            <label for="logic_definition">🧮 逻辑定义</label>
                            <textarea id="logic_definition" name="logic_definition" placeholder="详细的计算逻辑和业务规则">{component.get('logic_definition', '')}</textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="collection_method">📥 采集方式</label>
                                <input type="text" id="collection_method" name="collection_method" value="{component.get('collection_method', '')}" placeholder="例如：自动采集、手工录入、定期导入">
                            </div>
                            <div class="form-group">
                                <label for="calculation_formula">🔢 计算公式</label>
                                <input type="text" id="calculation_formula" name="calculation_formula" value="{component.get('calculation_formula', '')}" placeholder="具体的计算公式">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">📝 备注</label>
                            <textarea id="notes" name="notes" placeholder="其他需要说明的信息">{component.get('notes', '')}</textarea>
                        </div>

                        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
                            <button type="submit" class="btn btn-primary">💾 保存更改</button>
                            <a href="/admin/components" class="btn btn-secondary">❌ 取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </body>
        </html>
        '''

    except Exception as e:
        return f'<h1>❌ 错误</h1><p>{str(e)}</p><p><a href="/admin/components">返回组件列表</a></p>'

if __name__ == '__main__':
    print("🏥 医院指标管理系统启动中...")
    print("🌐 前端系统: http://localhost:5001")
    print("🔧 后端管理: http://localhost:5001/admin")
    print("🧮 组件管理: http://localhost:5001/admin/components")
    print("=" * 50)

    app.run(debug=True, host='0.0.0.0', port=5001)
