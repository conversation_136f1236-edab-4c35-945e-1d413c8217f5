#!/usr/bin/env python3
"""
为指标添加完整的基本属性和指标参考数据
确保前端模态框能够正确显示所有信息
"""

import sqlite3

def add_complete_indicator_data():
    """为指标添加完整数据"""

    # 连接数据库
    conn = sqlite3.connect('DATABASE-HOSPITAL/hospital_indicator_system.db')
    cursor = conn.cursor()

    # 定义完整的指标数据
    complete_data = {
        '1.1.1': {
            # 基本属性（已有部分数据，补充完整）
            'unit': '张',
            'lead_department': '医务科',
            'data_source': '医院信息系统',
            'logic_definition': '医院经卫生行政部门核准的床位数量',
            'significance': '反映医院规模和服务能力的基础指标，是医院资源配置和管理的重要依据',

            # 指标参考
            'indicator_definition': '医院经卫生行政部门核准设置的床位总数，是医院规模和服务能力的基础指标。',
            'statistical_scope': '医院所有经核准设置的床位，包括普通病房、ICU、手术室观察床等各类床位。',
            'data_sources': '医院执业许可证、卫生行政部门核准文件、医院基本信息登记表等。',
            'collection_frequency_detail': '年度统计，有变更时及时更新',
            'reference_value': '按医院等级和功能定位确定，三甲医院通常≥500张',
            'monitoring_analysis': '作为其他指标计算的基础数据，需确保数据准确性和及时性'
        },
        '1.3.1': {
            # 基本属性
            'unit': '个',
            'lead_department': '相关科室',
            'data_source': '科室管理系统',
            'logic_definition': '急诊医学科的综合评价指标',
            'significance': '反映急诊医学科的整体运营水平和服务能力',

            # 指标参考
            'indicator_definition': '急诊医学科综合指标，包含多个子指标的综合评价体系。',
            'statistical_scope': '急诊医学科所有相关业务和管理指标的综合评价。',
            'data_sources': '急诊科信息系统、医院信息系统、质量管理系统等。',
            'collection_frequency_detail': '月度统计，季度分析，年度评估',
            'reference_value': '根据急诊科规模和功能定位确定具体标准',
            'monitoring_analysis': '建议按急诊量、救治效率、患者满意度等维度进行综合分析'
        },
        '1.1.2': {
            # 基本属性
            'unit': '张',
            'lead_department': '医务科',
            'data_source': '床位管理系统',
            'logic_definition': '医院实际开放并投入使用的床位数量',
            'significance': '反映医院实际服务能力和床位资源利用情况的重要指标',

            # 指标参考
            'indicator_definition': '医院实际开放并投入使用的床位数量，反映医院的实际服务能力和床位利用情况。',
            'statistical_scope': '医院所有科室当前实际开放使用的床位，包括普通病房、ICU、手术观察床等，排除因维修、改造等原因暂停使用的床位。',
            'data_sources': '医院信息系统(HIS)、床位管理系统、护理管理系统、医务科统计报表等。',
            'collection_frequency_detail': '实时监测，每日统计，月度汇总分析',
            'reference_value': '应≥核定床位数的85%，三甲医院建议保持在90%以上',
            'monitoring_analysis': '建议按科室、病区、床位类型、时间趋势等维度进行分析，重点关注床位开放率和使用效率'
        },
        '1.2.1': {
            # 基本属性
            'unit': '比值',
            'lead_department': '人力资源科',
            'data_source': '人力资源管理系统',
            'logic_definition': '卫生技术人员总数与实际开放床位数的比值',
            'significance': '反映医院人力资源配置水平和医疗服务保障能力的核心指标',

            # 指标参考
            'indicator_definition': '医院卫生技术人员总数与实际开放床位数的比值，反映医院人力资源配置水平。',
            'statistical_scope': '包括医师、护士、医技人员、药师等所有卫生技术人员与实际开放床位的比例关系。',
            'data_sources': '人力资源管理系统、医院信息系统、统计报表等。',
            'collection_frequency_detail': '月度统计，季度分析，年度评估',
            'reference_value': '三级医院≥1.03:1，二级医院≥0.88:1',
            'monitoring_analysis': '建议按人员类别、科室分布、床位类型等维度分析人力资源配置的合理性'
        }
    }

    # 批量更新数据
    for indicator_id, data in complete_data.items():
        try:
            cursor.execute("""
                UPDATE indicators
                SET
                    unit = ?,
                    lead_department = ?,
                    data_source = ?,
                    logic_definition = ?,
                    significance = ?,
                    indicator_definition = ?,
                    statistical_scope = ?,
                    data_sources = ?,
                    collection_frequency_detail = ?,
                    reference_value = ?,
                    monitoring_analysis = ?
                WHERE id = ?
            """, (
                data['unit'],
                data['lead_department'],
                data['data_source'],
                data['logic_definition'],
                data['significance'],
                data['indicator_definition'],
                data['statistical_scope'],
                data['data_sources'],
                data['collection_frequency_detail'],
                data['reference_value'],
                data['monitoring_analysis'],
                indicator_id
            ))

            if cursor.rowcount > 0:
                print(f"✅ 已更新指标 {indicator_id} 的完整数据")
            else:
                print(f"⚠️  指标 {indicator_id} 不存在或未更新")

        except Exception as e:
            print(f"❌ 更新指标 {indicator_id} 时出错: {e}")

    # 提交更改
    conn.commit()

    # 验证更新结果
    print("\n📊 验证更新结果:")
    cursor.execute("""
        SELECT id, name,
               CASE WHEN unit IS NOT NULL AND unit != '' THEN '✅' ELSE '❌' END as 单位,
               CASE WHEN lead_department IS NOT NULL AND lead_department != '' THEN '✅' ELSE '❌' END as 科室,
               CASE WHEN indicator_definition IS NOT NULL AND indicator_definition != '' THEN '✅' ELSE '❌' END as 定义,
               CASE WHEN reference_value IS NOT NULL AND reference_value != '' THEN '✅' ELSE '❌' END as 参考值
        FROM indicators
        WHERE id IN ('1.1.1', '1.1.2', '1.2.1', '1.3.1')
        ORDER BY id
    """)

    results = cursor.fetchall()
    print(f"{'指标ID':<8} {'指标名称':<15} {'单位':<4} {'科室':<4} {'定义':<4} {'参考值':<6}")
    print("-" * 55)
    for row in results:
        print(f"{row[0]:<8} {row[1]:<15} {row[2]:<4} {row[3]:<4} {row[4]:<4} {row[5]:<6}")

    # 关闭连接
    conn.close()

    print("\n🎉 指标数据更新完成！")
    print("现在前端模态框应该能够正确显示基本属性和指标参考信息了。")

def verify_api_data():
    """验证API数据"""
    print("\n🧪 验证API数据...")

    import requests

    test_indicators = ['1.1.1', '1.1.2', '1.2.1', '1.3.1']

    for indicator_id in test_indicators:
        try:
            response = requests.get(f'http://localhost:5001/api/indicators/{indicator_id}')
            data = response.json()

            if data['success']:
                indicator = data['data']['indicator']
                print(f"\n📊 {indicator_id} - {indicator['name']}:")

                # 检查基本属性
                basic_attrs = ['unit', 'lead_department', 'data_source', 'logic_definition']
                basic_complete = all(indicator.get(attr) for attr in basic_attrs)
                print(f"   基本属性: {'✅ 完整' if basic_complete else '❌ 不完整'}")

                # 检查指标参考
                ref_attrs = ['indicator_definition', 'statistical_scope', 'reference_value']
                ref_complete = all(indicator.get(attr) for attr in ref_attrs)
                print(f"   指标参考: {'✅ 完整' if ref_complete else '❌ 不完整'}")

            else:
                print(f"❌ {indicator_id} API请求失败")

        except Exception as e:
            print(f"❌ {indicator_id} 验证异常: {e}")

if __name__ == "__main__":
    print("🎯 为指标添加完整的基本属性和指标参考数据")
    print("=" * 60)

    add_complete_indicator_data()
    verify_api_data()

    print("\n🔗 测试链接:")
    print("- 前端系统: http://localhost:5001")
    print("- 点击指标查看模态框中的基本属性和指标参考信息")
    print("- 后端管理: http://localhost:5001/admin/indicators/1.1.1")
