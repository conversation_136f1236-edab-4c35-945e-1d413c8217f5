# 医院等级评审指标说明手册

一个基于Google风格设计的现代化医院等级评审指标管理与查询系统。

## 🌟 功能特性

### 📊 数据管理
- **完整的指标体系**: 支持多级指标层次结构（一级、二级、三级）
- **详细的分子分母**: 包含单位、牵头科室、数据来源、逻辑定义
- **章节分类管理**: 按章节和小节组织指标
- **数据完整性**: 高质量的数据导入和验证

### 🎨 用户界面
- **Google风格设计**: 现代化、简洁的Material Design风格
- **响应式布局**: 完美支持桌面端和移动端
- **直观的导航**: 侧边栏章节导航和面包屑导航
- **优雅的动画**: 流畅的交互动画和过渡效果

### 🔍 搜索与筛选
- **全局搜索**: 支持指标编号、名称、描述的模糊搜索
- **实时搜索**: 输入即搜索，无需等待
- **多维筛选**: 按章节、小节筛选指标
- **搜索建议**: 智能搜索建议和结果高亮

### 📋 指标展示
- **多种视图**: 卡片视图和列表视图自由切换
- **详情弹窗**: 完整的指标详情展示
- **层级关系**: 清晰展示父子指标关系
- **组成部分**: 详细的分子分母信息展示

### 💫 交互功能
- **收藏功能**: 收藏常用指标，快速访问
- **快速操作**: 回到顶部、随机指标等快捷功能
- **键盘快捷键**: 支持Ctrl+K快速搜索等
- **状态保持**: 自动保存用户偏好设置

## 🚀 快速开始

### 1. 环境要求
- Python 3.7+
- Flask 2.0+
- SQLite 3

### 2. 数据准备
确保已运行数据导入脚本，创建完整的数据库：

```bash
# 创建数据库和基础结构
python3 create_database.py

# 导入各章节数据
python3 process_chapter1.py
python3 process_chapter2.py
python3 process_chapter3_improved.py
python3 process_chapter5.py
```

### 3. 启动应用
使用启动脚本（推荐）：

```bash
python3 start_app.py
```

或直接运行Flask应用：

```bash
python3 hospital_indicator_app.py
```

### 4. 访问系统
- 本地访问: http://localhost:5000
- 局域网访问: http://[本机IP]:5000

## 📊 数据覆盖

### 第1章 - 医院功能与任务 (51个指标)
- 5个小节，涵盖医院基本功能和任务要求
- 包含床位配置、科室设置、人员配备等指标

### 第2章 - 医院服务 (64个指标)
- 3个小节，涵盖医疗服务质量和效率
- 包含门诊服务、住院服务、急诊服务等指标

### 第3章 - 重点专业质量控制指标 (491个指标)
- 18个小节，涵盖18个重点专业
- 包含重症、急诊、检验、病理、感染管理等专业指标

### 第5章 - 重点医疗技术临床应用质量控制指标 (400+个指标)
- 3个小节，涵盖限制类医疗技术
- 包含国家限制类、省限制类、器官移植技术等指标

## 🎯 使用指南

### 基本操作
1. **浏览指标**: 在主页查看章节概览和指标列表
2. **搜索指标**: 使用顶部搜索框输入关键词
3. **筛选指标**: 使用章节和小节筛选器
4. **查看详情**: 点击指标卡片查看完整信息

### 高级功能
1. **切换视图**: 在卡片视图和列表视图间切换
2. **收藏指标**: 点击星标收藏常用指标
3. **快速导航**: 使用侧边栏章节导航
4. **键盘快捷键**: Ctrl+K快速搜索，ESC关闭弹窗

### 移动端使用
- 响应式设计，完美适配手机和平板
- 触摸友好的交互设计
- 优化的移动端导航

## 🔧 技术架构

### 后端技术
- **Flask**: 轻量级Web框架
- **SQLite**: 嵌入式数据库
- **Python**: 数据处理和API服务

### 前端技术
- **HTML5**: 语义化标记
- **CSS3**: 现代化样式和动画
- **JavaScript**: 交互逻辑和AJAX
- **Font Awesome**: 图标库

### 设计理念
- **Material Design**: Google设计语言
- **响应式设计**: 移动优先
- **渐进增强**: 基础功能优先
- **用户体验**: 直观易用

## 📈 性能特性

### 数据库优化
- 索引优化，快速查询
- 分页加载，减少内存占用
- 缓存机制，提升响应速度

### 前端优化
- 懒加载，按需加载内容
- 防抖搜索，减少请求频率
- 本地存储，保存用户偏好

### 用户体验
- 加载状态提示
- 错误处理和友好提示
- 键盘导航支持

## 🛠️ 自定义配置

### 修改端口
在 `hospital_indicator_app.py` 中修改：
```python
app.run(debug=True, host='0.0.0.0', port=8080)  # 改为8080端口
```

### 修改样式
编辑 `static/css/style.css` 文件：
```css
:root {
    --primary: #1a73e8;  /* 修改主色调 */
    --secondary: #34a853; /* 修改辅助色 */
}
```

## 🐛 故障排除

### 常见问题

**Q: 启动时提示数据库文件不存在**
A: 请先运行数据导入脚本创建数据库

**Q: 页面显示不正常**
A: 检查静态文件是否完整，清除浏览器缓存

**Q: 搜索功能不工作**
A: 检查JavaScript控制台是否有错误，确保API正常

**Q: 移动端显示异常**
A: 确保使用现代浏览器，检查CSS媒体查询

### 调试模式
启用Flask调试模式查看详细错误信息：
```python
app.run(debug=True)
```

## 📞 联系方式

如有问题或建议，请通过GitHub Issue联系。

---

**医院等级评审指标说明手册** - 让医院质量管理更简单、更高效！
