#!/usr/bin/env python3
"""
验证分子分母组件已整合到基本属性模块内
"""

import requests
from bs4 import BeautifulSoup

def test_html_structure():
    """测试HTML结构变化"""
    print("🧪 测试HTML结构变化...")
    
    try:
        with open('templates/admin/indicator_detail.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 检查独立的分子分母组件模块是否已删除
        if 'id="components-container"' not in html_content:
            print("✅ 独立的分子分母组件模块已删除")
            
            # 检查基本属性模块内是否包含分子分母组件部分
            if 'id="components-section"' in html_content:
                print("✅ 基本属性模块内包含分子分母组件部分")
                
                # 检查组件表格是否在基本属性模块内
                if 'components-table-container' in html_content:
                    print("✅ 组件表格容器存在于基本属性模块内")
                    
                    # 检查表格结构
                    if 'components-table' in html_content and 'components-table' in html_content:
                        print("✅ 组件表格结构完整")
                        return True
                    else:
                        print("❌ 组件表格结构不完整")
                        return False
                else:
                    print("❌ 组件表格容器缺失")
                    return False
            else:
                print("❌ 基本属性模块内缺少分子分母组件部分")
                return False
        else:
            print("❌ 独立的分子分母组件模块仍然存在")
            return False
            
    except FileNotFoundError:
        print("❌ 模板文件不存在")
        return False
    except Exception as e:
        print(f"❌ 读取模板文件失败: {e}")
        return False

def test_javascript_logic():
    """测试JavaScript逻辑更新"""
    print("\n🧪 测试JavaScript逻辑更新...")
    
    try:
        with open('templates/admin/indicator_detail.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 检查updateComponentsVisibility函数是否更新
        if 'components-section' in html_content and 'updateComponentsVisibility' in html_content:
            print("✅ updateComponentsVisibility函数已更新")
            
            # 检查renderDetailedFields函数是否包含组件渲染逻辑
            if 'renderComponentsTable(indicatorData.components)' in html_content:
                print("✅ renderDetailedFields函数包含组件渲染逻辑")
                
                # 检查组件显示条件
                if "indicator.indicator_type === 'composite'" in html_content:
                    print("✅ 组件显示条件正确")
                    return True
                else:
                    print("❌ 组件显示条件缺失")
                    return False
            else:
                print("❌ renderDetailedFields函数缺少组件渲染逻辑")
                return False
        else:
            print("❌ updateComponentsVisibility函数未更新")
            return False
            
    except FileNotFoundError:
        print("❌ 模板文件不存在")
        return False
    except Exception as e:
        print(f"❌ 读取模板文件失败: {e}")
        return False

def test_admin_page_access():
    """测试后端管理页面访问"""
    print("\n🧪 测试后端管理页面访问...")
    
    try:
        # 测试复合指标页面
        response = requests.get('http://localhost:5001/admin/indicators/1.3.1')
        
        if response.status_code == 200:
            print("✅ 复合指标页面访问成功")
            
            content = response.text
            
            # 检查页面是否包含基本属性模块
            if '基本属性' in content:
                print("✅ 页面包含基本属性模块")
                
                # 检查是否包含分子分母组件相关元素
                if 'components-section' in content:
                    print("✅ 页面包含分子分母组件部分")
                    
                    # 检查是否不包含独立的组件容器
                    if 'components-container' not in content:
                        print("✅ 页面不包含独立的组件容器")
                        return True
                    else:
                        print("❌ 页面仍包含独立的组件容器")
                        return False
                else:
                    print("❌ 页面缺少分子分母组件部分")
                    return False
            else:
                print("❌ 页面缺少基本属性模块")
                return False
        else:
            print(f"❌ 页面访问失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 页面访问异常: {e}")
        return False

def test_api_data():
    """测试API数据"""
    print("\n🧪 测试API数据...")
    
    try:
        response = requests.get('http://localhost:5001/api/indicators/1.3.1')
        data = response.json()
        
        if data['success']:
            indicator = data['data']['indicator']
            components = data['data'].get('components', [])
            
            print(f"✅ API数据获取成功")
            print(f"   指标ID: {indicator['id']}")
            print(f"   指标名称: {indicator['name']}")
            print(f"   指标类型: {indicator.get('indicator_type', 'composite')}")
            print(f"   组件数量: {len(components)}")
            
            if len(components) > 0:
                print("   组件列表:")
                for component in components:
                    comp_type = component.get('component_type', 'unknown')
                    comp_name = component.get('name', '未命名')
                    print(f"     - {comp_type}: {comp_name}")
            
            return True
        else:
            print(f"❌ API请求失败: {data.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ API请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 分子分母组件整合到基本属性模块验证")
    print("=" * 60)
    
    tests = [
        ("HTML结构变化测试", test_html_structure),
        ("JavaScript逻辑更新测试", test_javascript_logic),
        ("后端管理页面访问测试", test_admin_page_access),
        ("API数据测试", test_api_data)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 分子分母组件已成功整合到基本属性模块内！")
        print("\n✨ 整合成果:")
        print("1. ✅ 删除了独立的分子分母组件模块")
        print("2. ✅ 在基本属性模块内添加了分子分母组件部分")
        print("3. ✅ 更新了JavaScript显示控制逻辑")
        print("4. ✅ 保持了组件表格的完整功能")
        print("5. ✅ 复合指标时显示，简单指标时隐藏")
        
        print("\n🔗 验证链接:")
        print("- 复合指标: http://localhost:5001/admin/indicators/1.3.1")
        print("- 简单指标: http://localhost:5001/admin/indicators/1.1.1")
        
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    main()
