<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .debug-result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>前端功能调试页面</h1>
        
        <div class="debug-section">
            <div class="debug-title">1. API连接测试</div>
            <button class="test-button" onclick="testAPI()">测试API连接</button>
            <div id="api-result" class="debug-result"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">2. 指标数据加载测试</div>
            <button class="test-button" onclick="testIndicators()">加载指标数据</button>
            <button class="test-button" onclick="testChapter1()">加载第1章指标</button>
            <div id="indicators-result" class="debug-result"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">3. 层级结构测试</div>
            <button class="test-button" onclick="testHierarchy()">测试层级构建</button>
            <div id="hierarchy-result" class="debug-result"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">4. 视图切换测试</div>
            <button class="test-button" onclick="testViewSwitch()">测试视图切换</button>
            <div id="view-result" class="debug-result"></div>
        </div>
    </div>

    <script>
        // 测试API连接
        async function testAPI() {
            const result = document.getElementById('api-result');
            result.innerHTML = '正在测试API连接...';
            
            try {
                const response = await fetch('/api/statistics');
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `✅ API连接正常\n统计数据: ${JSON.stringify(data.data, null, 2)}`;
                    result.className = 'debug-result success';
                } else {
                    result.innerHTML = `❌ API返回错误: ${data.error}`;
                    result.className = 'debug-result error';
                }
            } catch (error) {
                result.innerHTML = `❌ 网络错误: ${error.message}`;
                result.className = 'debug-result error';
            }
        }

        // 测试指标数据加载
        async function testIndicators() {
            const result = document.getElementById('indicators-result');
            result.innerHTML = '正在加载指标数据...';
            
            try {
                const response = await fetch('/api/indicators?per_page=5');
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `✅ 指标数据加载成功\n数据量: ${data.data.length}\n前3个指标:\n${JSON.stringify(data.data.slice(0, 3), null, 2)}`;
                    result.className = 'debug-result success';
                } else {
                    result.innerHTML = `❌ 指标数据加载失败: ${data.error}`;
                    result.className = 'debug-result error';
                }
            } catch (error) {
                result.innerHTML = `❌ 网络错误: ${error.message}`;
                result.className = 'debug-result error';
            }
        }

        // 测试第1章指标
        async function testChapter1() {
            const result = document.getElementById('indicators-result');
            result.innerHTML = '正在加载第1章指标...';
            
            try {
                const response = await fetch('/api/indicators?chapter=1&per_page=10');
                const data = await response.json();
                
                if (data.success) {
                    const indicators = data.data;
                    const parentIndicators = indicators.filter(i => !i.parent_id);
                    const childIndicators = indicators.filter(i => i.parent_id);
                    
                    result.innerHTML = `✅ 第1章指标加载成功
总数量: ${indicators.length}
父指标: ${parentIndicators.length}
子指标: ${childIndicators.length}

父指标列表:
${parentIndicators.map(i => `${i.id} - ${i.name}`).join('\n')}

子指标列表:
${childIndicators.map(i => `${i.id} - ${i.name} (父级: ${i.parent_id})`).join('\n')}`;
                    result.className = 'debug-result success';
                } else {
                    result.innerHTML = `❌ 第1章指标加载失败: ${data.error}`;
                    result.className = 'debug-result error';
                }
            } catch (error) {
                result.innerHTML = `❌ 网络错误: ${error.message}`;
                result.className = 'debug-result error';
            }
        }

        // 测试层级结构构建
        async function testHierarchy() {
            const result = document.getElementById('hierarchy-result');
            result.innerHTML = '正在测试层级结构构建...';
            
            try {
                const response = await fetch('/api/indicators?chapter=1&per_page=20');
                const data = await response.json();
                
                if (data.success) {
                    const indicators = data.data;
                    
                    // 模拟buildIndicatorHierarchy函数
                    const indicatorMap = new Map();
                    const rootIndicators = [];

                    // 创建指标映射
                    indicators.forEach(indicator => {
                        indicatorMap.set(indicator.id, { ...indicator, children: [] });
                    });

                    // 构建层级关系
                    indicators.forEach(indicator => {
                        const indicatorNode = indicatorMap.get(indicator.id);
                        if (indicator.parent_id && indicatorMap.has(indicator.parent_id)) {
                            indicatorMap.get(indicator.parent_id).children.push(indicatorNode);
                        } else {
                            rootIndicators.push(indicatorNode);
                        }
                    });
                    
                    result.innerHTML = `✅ 层级结构构建成功
原始指标数量: ${indicators.length}
根指标数量: ${rootIndicators.length}

层级结构:
${rootIndicators.map(root => {
    let str = `${root.id} - ${root.name}`;
    if (root.children.length > 0) {
        str += `\n  子指标 (${root.children.length}个):`;
        root.children.forEach(child => {
            str += `\n    ${child.id} - ${child.name}`;
        });
    }
    return str;
}).join('\n\n')}`;
                    result.className = 'debug-result success';
                } else {
                    result.innerHTML = `❌ 数据加载失败: ${data.error}`;
                    result.className = 'debug-result error';
                }
            } catch (error) {
                result.innerHTML = `❌ 错误: ${error.message}`;
                result.className = 'debug-result error';
            }
        }

        // 测试视图切换
        function testViewSwitch() {
            const result = document.getElementById('view-result');
            
            // 检查主页面的DOM元素
            const cardView = window.parent.document?.getElementById('cardView');
            const listView = window.parent.document?.getElementById('listView');
            const viewButtons = window.parent.document?.querySelectorAll('.view-btn');
            
            let status = '视图切换测试结果:\n';
            status += `卡片视图容器: ${cardView ? '✅ 存在' : '❌ 不存在'}\n`;
            status += `列表视图容器: ${listView ? '✅ 存在' : '❌ 不存在'}\n`;
            status += `视图切换按钮: ${viewButtons ? `✅ 找到${viewButtons.length}个` : '❌ 未找到'}\n`;
            
            if (cardView && listView) {
                status += `\n当前显示状态:\n`;
                status += `卡片视图: ${cardView.style.display !== 'none' ? '显示' : '隐藏'}\n`;
                status += `列表视图: ${listView.style.display !== 'none' ? '显示' : '隐藏'}\n`;
            }
            
            result.innerHTML = status;
            result.className = 'debug-result success';
        }
    </script>
</body>
</html>
