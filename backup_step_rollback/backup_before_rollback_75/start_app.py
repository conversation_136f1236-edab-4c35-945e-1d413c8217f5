#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院等级评审指标说明程序启动脚本
"""

import os
import sys
import subprocess
import sqlite3
from pathlib import Path

def check_requirements():
    """检查运行环境和依赖"""
    print("🔍 检查运行环境...")

    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return False

    print(f"✅ Python版本: {sys.version}")

    # 检查Flask是否安装
    try:
        import flask
        print(f"✅ Flask版本: {flask.__version__}")
    except ImportError:
        print("❌ Flask未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "flask"])
            print("✅ Flask安装成功")
        except subprocess.CalledProcessError:
            print("❌ Flask安装失败，请手动安装: pip install flask")
            return False

    return True

def check_database():
    """检查数据库文件"""
    print("🗃️ 检查数据库...")

    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"

    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        print("请先运行数据导入脚本创建数据库")
        return False

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 检查主要表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]

        required_tables = ['chapters', 'sections', 'indicators', 'indicator_components']
        missing_tables = [table for table in required_tables if table not in tables]

        if missing_tables:
            print(f"❌ 缺少数据表: {', '.join(missing_tables)}")
            conn.close()
            return False

        # 检查数据量
        cursor.execute("SELECT COUNT(*) FROM indicators")
        indicator_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM indicator_components")
        component_count = cursor.fetchone()[0]

        print(f"✅ 数据库检查通过")
        print(f"   - 指标数量: {indicator_count}")
        print(f"   - 分子分母数量: {component_count}")

        conn.close()
        return True

    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def check_static_files():
    """检查静态文件"""
    print("📁 检查静态文件...")

    required_files = [
        "templates/base.html",
        "templates/index.html",
        "static/css/style.css",
        "static/js/app.js"
    ]

    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)

    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False

    print("✅ 静态文件检查通过")
    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        "templates",
        "static/css",
        "static/js",
        "static/images"
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

def show_startup_info():
    """显示启动信息"""
    print("\n" + "=" * 80)
    print("🏥 医院等级评审指标说明手册")
    print("=" * 80)
    print("📋 功能特性:")
    print("   • Google风格的现代化界面设计")
    print("   • 完整的指标层级结构展示")
    print("   • 强大的搜索和筛选功能")
    print("   • 详细的分子分母信息展示")
    print("   • 响应式设计，支持移动端")
    print("   • 收藏和快速访问功能")
    print("\n📊 数据覆盖:")
    print("   • 第1章: 医院功能与任务")
    print("   • 第2章: 医院服务")
    print("   • 第3章: 重点专业质量控制指标")
    print("   • 第5章: 重点医疗技术临床应用质量控制指标")
    print("\n🌐 访问方式:")
    print("   • 本地访问: http://localhost:5001")
    print("   • 局域网访问: http://[本机IP]:5001")
    print("\n⌨️ 快捷键:")
    print("   • Ctrl+K / Cmd+K: 快速搜索")
    print("   • ESC: 关闭弹窗")
    print("\n💡 使用提示:")
    print("   • 点击章节卡片快速筛选指标")
    print("   • 使用搜索框查找特定指标")
    print("   • 点击指标卡片查看详细信息")
    print("   • 收藏常用指标便于快速访问")
    print("=" * 80)

def main():
    """主函数"""
    print("🚀 启动医院等级评审指标说明程序...")
    print()

    # 创建必要目录
    create_directories()

    # 检查运行环境
    if not check_requirements():
        print("\n❌ 环境检查失败，程序退出")
        return False

    # 检查数据库
    if not check_database():
        print("\n❌ 数据库检查失败，程序退出")
        print("\n💡 解决方案:")
        print("   1. 确保已运行数据导入脚本")
        print("   2. 检查DATABASE-HOSPITAL目录是否存在")
        print("   3. 检查数据库文件是否完整")
        return False

    # 检查静态文件
    if not check_static_files():
        print("\n❌ 静态文件检查失败，程序退出")
        print("\n💡 解决方案:")
        print("   1. 确保所有模板和静态文件已创建")
        print("   2. 检查文件路径是否正确")
        return False

    # 显示启动信息
    show_startup_info()

    # 启动Flask应用
    print("\n🌟 所有检查通过，正在启动Web服务器...")
    print("📝 按 Ctrl+C 停止服务器")
    print()

    try:
        # 导入并运行Flask应用
        from hospital_indicator_app import app
        app.run(debug=True, host='0.0.0.0', port=5001)
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
        return True
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
