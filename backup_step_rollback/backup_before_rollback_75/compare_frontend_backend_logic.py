#!/usr/bin/env python3
"""
前后端指标详情显示逻辑对比分析
"""

import requests
import json

def analyze_frontend_logic():
    """分析前端显示逻辑"""
    print("🔍 分析前端显示逻辑...")
    
    try:
        with open('static/js/app.js', 'r', encoding='utf-8') as f:
            frontend_js = f.read()
        
        with open('templates/base.html', 'r', encoding='utf-8') as f:
            frontend_html = f.read()
        
        frontend_logic = {
            "基本属性显示": {
                "条件": "有基本属性数据时显示",
                "字段": ["单位", "牵头科室", "数据来源", "逻辑定义"],
                "位置": "模态框基本信息卡片内",
                "函数": "updateModalBasicInfo()"
            },
            "指标参考显示": {
                "条件": "有参考数据时显示",
                "字段": ["指标定义", "统计范围", "数据来源", "统计频率", "参考值", "监测分析"],
                "位置": "独立的指标参考卡片",
                "函数": "updateModalReferenceInfo()"
            },
            "子指标显示": {
                "条件": "children.length > 0 && indicator.indicator_type !== 'simple'",
                "位置": "独立的子指标导航卡片",
                "函数": "updateModalChildrenNav()",
                "隐藏条件": "简单指标时隐藏"
            },
            "分子分母组件显示": {
                "条件": "indicator.indicator_type !== 'simple'",
                "位置": "独立的分子分母信息卡片",
                "函数": "renderComponentsInModal()",
                "隐藏条件": "简单指标时隐藏整个卡片"
            }
        }
        
        return frontend_logic
        
    except Exception as e:
        print(f"❌ 分析前端逻辑失败: {e}")
        return None

def analyze_backend_logic():
    """分析后端显示逻辑"""
    print("🔍 分析后端显示逻辑...")
    
    try:
        with open('templates/admin/indicator_detail.html', 'r', encoding='utf-8') as f:
            backend_html = f.read()
        
        backend_logic = {
            "基本属性显示": {
                "条件": "有基本属性数据时显示",
                "字段": ["单位", "牵头科室", "数据来源", "逻辑定义", "意义", "计算公式", "分子分母组件"],
                "位置": "基本属性卡片内（已整合分子分母组件）",
                "函数": "renderDetailedFields()"
            },
            "指标参考显示": {
                "条件": "有参考数据时显示",
                "字段": ["指标定义", "统计范围", "数据来源", "统计频率", "参考值", "监测分析"],
                "位置": "独立的指标参考卡片",
                "函数": "renderDetailedFields() 中的参考部分"
            },
            "子指标显示": {
                "条件": "children.length > 0 && indicator.indicator_type !== 'simple'",
                "位置": "独立的子指标导航卡片",
                "函数": "renderChildrenTable()",
                "隐藏条件": "简单指标时隐藏"
            },
            "分子分母组件显示": {
                "条件": "indicator.indicator_type === 'composite' && components.length > 0",
                "位置": "基本属性卡片内（已整合）",
                "函数": "renderComponentsTable() + renderDetailedFields()",
                "隐藏条件": "简单指标时隐藏组件部分"
            }
        }
        
        return backend_logic
        
    except Exception as e:
        print(f"❌ 分析后端逻辑失败: {e}")
        return None

def test_api_consistency():
    """测试API数据一致性"""
    print("🧪 测试API数据一致性...")
    
    test_cases = [
        ("复合指标", "1.3.1"),
        ("简单指标", "1.1.1")
    ]
    
    results = {}
    
    for case_name, indicator_id in test_cases:
        try:
            response = requests.get(f'http://localhost:5001/api/indicators/{indicator_id}')
            data = response.json()
            
            if data['success']:
                indicator = data['data']['indicator']
                components = data['data'].get('components', [])
                children = data['data'].get('children', [])
                
                results[case_name] = {
                    "indicator_id": indicator['id'],
                    "indicator_type": indicator.get('indicator_type', 'composite'),
                    "components_count": len(components),
                    "children_count": len(children),
                    "has_basic_attrs": bool(indicator.get('unit') or indicator.get('lead_department') or indicator.get('data_source')),
                    "has_reference_info": bool(indicator.get('indicator_definition') or indicator.get('reference_value')),
                    "api_success": True
                }
            else:
                results[case_name] = {"api_success": False, "error": data.get('error')}
                
        except Exception as e:
            results[case_name] = {"api_success": False, "error": str(e)}
    
    return results

def compare_display_logic(frontend_logic, backend_logic):
    """对比前后端显示逻辑"""
    print("\n📊 前后端显示逻辑对比分析")
    print("=" * 80)
    
    comparison_results = []
    
    # 对比各个模块
    modules = ["基本属性显示", "指标参考显示", "子指标显示", "分子分母组件显示"]
    
    for module in modules:
        print(f"\n🔍 {module}")
        print("-" * 50)
        
        frontend = frontend_logic.get(module, {})
        backend = backend_logic.get(module, {})
        
        # 对比显示条件
        frontend_condition = frontend.get("条件", "未定义")
        backend_condition = backend.get("条件", "未定义")
        
        print(f"📋 显示条件:")
        print(f"   前端: {frontend_condition}")
        print(f"   后端: {backend_condition}")
        
        condition_consistent = frontend_condition == backend_condition
        if condition_consistent:
            print("   ✅ 显示条件一致")
        else:
            print("   ❌ 显示条件不一致")
        
        # 对比显示位置
        frontend_position = frontend.get("位置", "未定义")
        backend_position = backend.get("位置", "未定义")
        
        print(f"📍 显示位置:")
        print(f"   前端: {frontend_position}")
        print(f"   后端: {backend_position}")
        
        position_consistent = "基本属性" in frontend_position and "基本属性" in backend_position if module == "分子分母组件显示" else frontend_position == backend_position
        if position_consistent:
            print("   ✅ 显示位置一致")
        else:
            print("   ❌ 显示位置不一致")
        
        # 对比隐藏条件
        frontend_hide = frontend.get("隐藏条件", "无")
        backend_hide = backend.get("隐藏条件", "无")
        
        print(f"🚫 隐藏条件:")
        print(f"   前端: {frontend_hide}")
        print(f"   后端: {backend_hide}")
        
        hide_consistent = frontend_hide == backend_hide
        if hide_consistent:
            print("   ✅ 隐藏条件一致")
        else:
            print("   ❌ 隐藏条件不一致")
        
        # 记录对比结果
        module_result = {
            "module": module,
            "condition_consistent": condition_consistent,
            "position_consistent": position_consistent,
            "hide_consistent": hide_consistent,
            "overall_consistent": condition_consistent and position_consistent and hide_consistent
        }
        comparison_results.append(module_result)
    
    return comparison_results

def analyze_differences(comparison_results):
    """分析差异和建议"""
    print("\n🎯 差异分析和建议")
    print("=" * 80)
    
    consistent_modules = [r for r in comparison_results if r["overall_consistent"]]
    inconsistent_modules = [r for r in comparison_results if not r["overall_consistent"]]
    
    print(f"✅ 一致的模块: {len(consistent_modules)}/{len(comparison_results)}")
    for module in consistent_modules:
        print(f"   - {module['module']}")
    
    if inconsistent_modules:
        print(f"\n❌ 不一致的模块: {len(inconsistent_modules)}/{len(comparison_results)}")
        for module in inconsistent_modules:
            print(f"   - {module['module']}")
            if not module["condition_consistent"]:
                print(f"     ⚠️  显示条件不一致")
            if not module["position_consistent"]:
                print(f"     ⚠️  显示位置不一致")
            if not module["hide_consistent"]:
                print(f"     ⚠️  隐藏条件不一致")
    
    # 特别关注分子分母组件
    components_module = next((r for r in comparison_results if r["module"] == "分子分母组件显示"), None)
    if components_module:
        print(f"\n🧮 分子分母组件特别分析:")
        if components_module["overall_consistent"]:
            print("   ✅ 前后端分子分母组件显示逻辑一致")
            print("   ✅ 后端已成功整合到基本属性模块内")
            print("   ✅ 前端保持独立卡片显示")
            print("   ✅ 两者都正确处理简单指标隐藏逻辑")
        else:
            print("   ❌ 前后端分子分母组件显示逻辑存在差异")
    
    return len(inconsistent_modules) == 0

def main():
    """主分析函数"""
    print("🎯 前后端指标详情显示逻辑对比分析")
    print("=" * 80)
    
    # 分析前后端逻辑
    frontend_logic = analyze_frontend_logic()
    backend_logic = analyze_backend_logic()
    
    if not frontend_logic or not backend_logic:
        print("❌ 无法完成分析，请检查文件是否存在")
        return False
    
    # 测试API数据一致性
    api_results = test_api_consistency()
    print(f"\n📡 API数据测试结果:")
    for case_name, result in api_results.items():
        if result.get("api_success"):
            print(f"   ✅ {case_name}: {result['indicator_id']} ({result['indicator_type']})")
            print(f"      组件: {result['components_count']}个, 子指标: {result['children_count']}个")
        else:
            print(f"   ❌ {case_name}: {result.get('error', '未知错误')}")
    
    # 对比显示逻辑
    comparison_results = compare_display_logic(frontend_logic, backend_logic)
    
    # 分析差异
    is_consistent = analyze_differences(comparison_results)
    
    print("\n" + "=" * 80)
    print("📊 总体一致性评估")
    
    if is_consistent:
        print("🎉 恭喜！前后端指标详情显示逻辑完全一致！")
        print("\n✨ 一致性亮点:")
        print("1. ✅ 基本属性显示逻辑一致")
        print("2. ✅ 指标参考显示逻辑一致")
        print("3. ✅ 子指标显示逻辑一致")
        print("4. ✅ 分子分母组件显示逻辑一致")
        print("5. ✅ 简单指标隐藏逻辑一致")
        print("6. ✅ API数据结构一致")
        
        print("\n🎯 架构特点:")
        print("- 前端: 模态框中各模块独立显示")
        print("- 后端: 分子分母组件整合到基本属性模块内")
        print("- 两者都正确处理指标类型相关的显示逻辑")
        
    else:
        print("⚠️  前后端指标详情显示逻辑存在部分差异")
        print("建议检查不一致的模块并进行调整")
    
    return is_consistent

if __name__ == "__main__":
    main()
