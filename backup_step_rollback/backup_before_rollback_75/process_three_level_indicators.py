#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理三级指标的完整方案
检查并处理Excel文件中的三级指标数据
"""

import pandas as pd
import sqlite3
import re
import sys
import os

def analyze_three_level_indicators():
    """
    分析Excel文件中的三级指标结构
    """
    file_path = "chapter1.xlsx"
    
    try:
        print("=" * 80)
        print("🔍 三级指标分析")
        print("=" * 80)
        
        df = pd.read_excel(file_path, sheet_name=0)
        
        print(f"数据维度: {df.shape[0]}行 × {df.shape[1]}列")
        
        # 检查三级指标列
        three_level_columns = ['三级指标编号', '三级指标名称']
        
        print(f"\n📋 三级指标相关列:")
        for col in three_level_columns:
            if col in df.columns:
                print(f"  ✅ {col}")
            else:
                print(f"  ❌ {col} (不存在)")
        
        # 统计三级指标数据
        if '三级指标编号' in df.columns:
            three_level_data = []
            
            for index, row in df.iterrows():
                level3_id = row.get('三级指标编号', '')
                level3_name = row.get('三级指标名称', '')
                level2_id = row.get('二级指标编号', '')
                level1_id = row.get('一级指标编号', '')
                
                if pd.notna(level3_id) and str(level3_id).strip():
                    three_level_data.append({
                        'level3_id': str(level3_id).strip(),
                        'level3_name': str(level3_name).strip() if pd.notna(level3_name) else '',
                        'level2_id': str(level2_id).strip() if pd.notna(level2_id) else '',
                        'level1_id': str(level1_id).strip() if pd.notna(level1_id) else '',
                        'row': index + 1
                    })
            
            print(f"\n📊 三级指标统计:")
            print(f"  发现三级指标: {len(three_level_data)}个")
            
            if three_level_data:
                print(f"\n📝 三级指标列表:")
                for item in three_level_data[:10]:  # 只显示前10个
                    print(f"  {item['level3_id']} - {item['level3_name']} (父级: {item['level2_id']})")
                
                if len(three_level_data) > 10:
                    print(f"  ... 还有 {len(three_level_data) - 10} 个三级指标")
                
                # 分析三级指标的层级结构
                level_structure = {}
                for item in three_level_data:
                    level1 = item['level1_id']
                    level2 = item['level2_id']
                    level3 = item['level3_id']
                    
                    if level1 not in level_structure:
                        level_structure[level1] = {}
                    if level2 not in level_structure[level1]:
                        level_structure[level1][level2] = []
                    level_structure[level1][level2].append(level3)
                
                print(f"\n🌳 三级指标层级结构:")
                for level1, level2_dict in level_structure.items():
                    print(f"  {level1}")
                    for level2, level3_list in level2_dict.items():
                        print(f"    └── {level2}")
                        for level3 in level3_list:
                            print(f"        └── {level3}")
            
            return three_level_data
        else:
            print("❌ 未找到三级指标编号列")
            return []
            
    except Exception as e:
        print(f"❌ 分析三级指标时出错: {e}")
        import traceback
        traceback.print_exc()
        return []

def check_current_database_structure():
    """
    检查当前数据库中的指标层级结构
    """
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    try:
        print(f"\n" + "=" * 80)
        print("🗃️ 当前数据库指标层级分析")
        print("=" * 80)
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 统计各级指标数量
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN LENGTH(id) - LENGTH(REPLACE(id, '.', '')) = 2 THEN '一级指标'
                    WHEN LENGTH(id) - LENGTH(REPLACE(id, '.', '')) = 3 THEN '二级指标'
                    WHEN LENGTH(id) - LENGTH(REPLACE(id, '.', '')) = 4 THEN '三级指标'
                    ELSE '其他级别'
                END as level,
                COUNT(*) as count
            FROM indicators 
            WHERE id LIKE '1.%'
            GROUP BY level
            ORDER BY level
        """)
        
        level_stats = cursor.fetchall()
        
        print(f"📊 当前数据库指标级别统计:")
        for level, count in level_stats:
            print(f"  {level}: {count}个")
        
        # 显示三级指标示例
        cursor.execute("""
            SELECT id, name, parent_id
            FROM indicators 
            WHERE id LIKE '1.%.%.%' AND LENGTH(id) - LENGTH(REPLACE(id, '.', '')) = 4
            ORDER BY id
            LIMIT 5
        """)
        
        three_level_examples = cursor.fetchall()
        
        if three_level_examples:
            print(f"\n📝 三级指标示例:")
            for indicator_id, name, parent_id in three_level_examples:
                print(f"  {indicator_id} - {name} (父级: {parent_id})")
        
        # 检查是否有四级指标的可能性
        cursor.execute("""
            SELECT id, name, parent_id
            FROM indicators 
            WHERE LENGTH(id) - LENGTH(REPLACE(id, '.', '')) > 4
            ORDER BY id
        """)
        
        deeper_levels = cursor.fetchall()
        
        if deeper_levels:
            print(f"\n⚠️  发现更深层级的指标:")
            for indicator_id, name, parent_id in deeper_levels:
                level_count = len(indicator_id.split('.')) - 1
                print(f"  {indicator_id} - {name} ({level_count}级指标)")
        
    except Exception as e:
        print(f"❌ 检查数据库结构时出错: {e}")
    finally:
        if conn:
            conn.close()

def process_three_level_indicators_from_excel():
    """
    从Excel文件处理三级指标数据
    """
    file_path = "chapter1.xlsx"
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    try:
        print(f"\n" + "=" * 80)
        print("🔄 处理Excel中的三级指标")
        print("=" * 80)
        
        df = pd.read_excel(file_path, sheet_name=0)
        
        three_level_indicators = []
        
        for index, row in df.iterrows():
            try:
                # 获取三级指标信息
                level3_id = row.get('三级指标编号', '')
                level3_name = row.get('三级指标名称', '')
                level2_id = row.get('二级指标编号', '')
                level1_id = row.get('一级指标编号', '')
                
                if pd.notna(level3_id) and str(level3_id).strip():
                    level3_id_str = str(level3_id).strip()
                    level3_name_str = str(level3_name).strip() if pd.notna(level3_name) else ''
                    level2_id_str = str(level2_id).strip() if pd.notna(level2_id) else ''
                    
                    if level3_id_str.startswith('1.') and level3_name_str:
                        # 确定所属小节
                        section_code = '.'.join(level3_id_str.split('.')[:2])
                        
                        three_level_indicators.append({
                            'id': level3_id_str,
                            'name': level3_name_str,
                            'parent_id': level2_id_str,
                            'section_code': section_code,
                            'level': 3,
                            'row': index + 1
                        })
                        
                        print(f"发现三级指标: {level3_id_str} - {level3_name_str} (父级: {level2_id_str})")
                        
            except Exception as e:
                print(f"处理第{index+1}行时出错: {e}")
                continue
        
        # 去重处理
        unique_indicators = []
        seen = set()
        for indicator in three_level_indicators:
            if indicator['id'] not in seen:
                seen.add(indicator['id'])
                unique_indicators.append(indicator)
            else:
                print(f"发现重复三级指标: {indicator['id']}，已跳过")
        
        print(f"\n📊 处理结果:")
        print(f"  提取到 {len(unique_indicators)} 个唯一三级指标")
        
        # 插入数据库
        if unique_indicators:
            insert_three_level_indicators(unique_indicators, db_path)
        else:
            print("未找到新的三级指标数据")
            
    except Exception as e:
        print(f"❌ 处理三级指标时出错: {e}")
        import traceback
        traceback.print_exc()

def insert_three_level_indicators(indicators, db_path):
    """
    将三级指标插入数据库
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取第一章的ID
        cursor.execute("SELECT id FROM chapters WHERE code = '1'")
        chapter_result = cursor.fetchone()
        if not chapter_result:
            print("错误：数据库中未找到第一章数据")
            return
        
        chapter_id = chapter_result[0]
        
        print(f"\n开始插入三级指标...")
        
        for i, indicator in enumerate(indicators):
            # 查找对应的小节ID
            cursor.execute("SELECT id FROM sections WHERE code = ? AND chapter_id = ?", 
                         (indicator['section_code'], chapter_id))
            section_result = cursor.fetchone()
            section_id = section_result[0] if section_result else None
            
            # 检查指标是否已存在
            cursor.execute("SELECT id FROM indicators WHERE id = ?", (indicator['id'],))
            existing = cursor.fetchone()
            
            if existing:
                print(f"三级指标已存在: {indicator['id']} - {indicator['name']}")
                continue
            
            # 插入三级指标
            cursor.execute("""
                INSERT INTO indicators 
                (id, name, description, parent_id, chapter_id, section_id, category, sort_order, is_active, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            """, (
                indicator['id'],
                indicator['name'],
                f"第一章{indicator['level']}级指标：{indicator['name']}",
                indicator['parent_id'],
                chapter_id,
                section_id,
                '资源配置与运行数据',
                i + 1
            ))
            
            print(f"插入三级指标: {indicator['id']} - {indicator['name']} (父级: {indicator['parent_id']})")
        
        conn.commit()
        print(f"成功插入 {len(indicators)} 个三级指标")
        
    except Exception as e:
        print(f"插入三级指标时出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if conn:
            conn.close()

def show_complete_hierarchy():
    """
    显示完整的指标层级结构
    """
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    try:
        print(f"\n" + "=" * 80)
        print("🌳 完整指标层级结构")
        print("=" * 80)
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有指标的层级关系
        cursor.execute("""
            WITH RECURSIVE indicator_tree AS (
                -- 根节点（一级指标）
                SELECT id, name, parent_id, 1 as level, id as path
                FROM indicators 
                WHERE parent_id IS NULL AND id LIKE '1.%'
                
                UNION ALL
                
                -- 递归获取子节点
                SELECT i.id, i.name, i.parent_id, it.level + 1, it.path || ' -> ' || i.id
                FROM indicators i
                JOIN indicator_tree it ON i.parent_id = it.id
            )
            SELECT level, id, name, parent_id, path
            FROM indicator_tree
            ORDER BY path, level, id
            LIMIT 20
        """)
        
        hierarchy = cursor.fetchall()
        
        current_level = 0
        for level, indicator_id, name, parent_id, path in hierarchy:
            indent = "  " * (level - 1)
            level_icon = "🔹" if level == 1 else "🔸" if level == 2 else "🔺"
            print(f"{indent}{level_icon} {indicator_id} - {name}")
        
        # 统计各级指标数量
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN parent_id IS NULL THEN '一级指标'
                    WHEN id IN (SELECT DISTINCT parent_id FROM indicators WHERE parent_id IS NOT NULL) THEN '二级指标'
                    ELSE '三级指标'
                END as level_type,
                COUNT(*) as count
            FROM indicators 
            WHERE id LIKE '1.%'
            GROUP BY level_type
        """)
        
        level_counts = cursor.fetchall()
        
        print(f"\n📊 指标层级统计:")
        for level_type, count in level_counts:
            print(f"  {level_type}: {count}个")
        
    except Exception as e:
        print(f"❌ 显示层级结构时出错: {e}")
    finally:
        if conn:
            conn.close()

def main():
    print("=" * 100)
    print("🏥 医院等级评审指标管理系统 - 三级指标处理方案")
    print("=" * 100)
    
    # 检查文件是否存在
    if not os.path.exists("chapter1.xlsx"):
        print("错误：文件 chapter1.xlsx 不存在")
        return
    
    if not os.path.exists("DATABASE-HOSPITAL/hospital_indicator_system.db"):
        print("错误：数据库文件不存在")
        return
    
    # 1. 分析Excel中的三级指标
    three_level_data = analyze_three_level_indicators()
    
    # 2. 检查当前数据库结构
    check_current_database_structure()
    
    # 3. 处理Excel中的三级指标（如果有新的）
    if three_level_data:
        process_three_level_indicators_from_excel()
    
    # 4. 显示完整的层级结构
    show_complete_hierarchy()
    
    print(f"\n" + "=" * 100)
    print("✅ 三级指标处理完成")
    print("=" * 100)

if __name__ == "__main__":
    main()
