// 医院等级评审指标说明手册 - 主应用脚本


// 安全的文本内容设置函数（处理null值）
function safeSetTextContentWithDefault(element, value, defaultText = '暂无数据') {
    if (element) {
        element.textContent = value || defaultText;
        return true;
    } else {
        console.warn('Element not found for text setting');
        return false;
    }
}

class HospitalIndicatorApp {
    constructor() {
        this.currentChapter = null;
        this.currentSection = null;
        this.searchTimeout = null;
        this.favorites = this.loadFavorites();

        this.init();
    }

    init() {
        this.bindEvents();
        this.loadChapterNavigation();
        this.setupSearch();
        this.loadUserPreferences();
        this.bindHomePageEvents(); // 确保绑定首页事件
        this.setupAdminUpdateListener(); // 监听后端更新
    }

    // 安全的DOM操作函数
    safeSetTextContent(elementId, value, isSelector = false) {
        const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
        if (element) {
            element.textContent = value;
            return true;
        } else {
            console.warn(`Element not found: ${elementId}`);
            return false;
        }
    }

    safeGetElement(elementId, isSelector = false) {
        const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
        if (!element) {
            console.warn(`Element not found: ${elementId}`);
        }
        return element;
    }

    loadUserPreferences() {
        // 加载用户偏好的视图
        const preferredView = localStorage.getItem('preferred_view') || 'card';
        this.switchView(preferredView);
    }

    // 加载首页数据
    async loadHomePageData() {
        try {
            // 加载统计数据
            await this.loadStatistics();

            // 加载章节数据
            await this.loadChapters();

            // 加载指标数据
            await this.loadIndicators();

            // 绑定首页特有的事件
            this.bindHomePageEvents();
        } catch (error) {
            console.error('加载首页数据失败:', error);
            this.showNotification('加载数据失败', 'error');
        }
    }

    // 加载统计数据
    async loadStatistics() {
        try {
            const response = await fetch('/api/statistics');
            const data = await response.json();

            if (data.success) {
                const stats = data.data.overview;
                const chapterCountEl = document.getElementById('chapterCount');
                const sectionCountEl = document.getElementById('sectionCount');
                const indicatorCountEl = document.getElementById('indicatorCount');
                const componentCountEl = document.getElementById('componentCount');

                if (chapterCountEl) chapterCountEl.textContent = stats.chapters;
                if (sectionCountEl) sectionCountEl.textContent = stats.sections;
                if (indicatorCountEl) indicatorCountEl.textContent = stats.indicators;
                if (componentCountEl) componentCountEl.textContent = stats.components;
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    // 加载章节数据
    async loadChapters() {
        try {
            const response = await fetch('/api/chapters');
            const data = await response.json();

            if (data.success) {
                this.renderChapterGrid(data.data);
                this.populateChapterFilter(data.data);
            }
        } catch (error) {
            console.error('加载章节数据失败:', error);
        }
    }

    // 渲染章节网格
    renderChapterGrid(chapters) {
        const grid = document.getElementById('chapterGrid');
        if (!grid) return;

        grid.innerHTML = '';

        chapters.forEach(chapter => {
            const card = document.createElement('div');
            card.className = 'chapter-card';
            card.innerHTML = `
                <div class="chapter-card-header" style="background-color: ${chapter.color}20; border-left: 4px solid ${chapter.color}">
                    <div class="chapter-card-icon" style="color: ${chapter.color}">
                        <i class="${chapter.icon}"></i>
                    </div>
                    <div class="chapter-card-title">
                        <div class="chapter-code">第${chapter.code}章</div>
                        <div class="chapter-name">${chapter.name}</div>
                    </div>
                </div>
                <div class="chapter-card-body">
                    <div class="chapter-stats">
                        <div class="chapter-stat">
                            <span class="stat-value">${chapter.section_count}</span>
                            <span class="stat-label">小节</span>
                        </div>
                        <div class="chapter-stat">
                            <span class="stat-value">${chapter.indicator_count}</span>
                            <span class="stat-label">指标</span>
                        </div>
                        <div class="chapter-stat">
                            <span class="stat-value">${chapter.component_count}</span>
                            <span class="stat-label">组成部分</span>
                        </div>
                    </div>
                    <div class="chapter-actions">
                        <button class="btn btn-outline btn-sm" onclick="viewChapter('${chapter.code}')">
                            <i class="fas fa-eye btn-icon"></i>
                            查看详情
                        </button>
                    </div>
                </div>
            `;
            grid.appendChild(card);
        });
    }

    // 填充章节筛选器
    populateChapterFilter(chapters) {
        const filter = document.getElementById('chapterFilter');
        if (!filter) return;

        filter.innerHTML = '<option value="">所有章节</option>';

        chapters.forEach(chapter => {
            const option = document.createElement('option');
            option.value = chapter.code;
            option.textContent = `第${chapter.code}章 - ${chapter.name}`;
            filter.appendChild(option);
        });
    }

    // 绑定首页特有的事件
    bindHomePageEvents() {
        // 筛选器变化
        const chapterFilter = document.getElementById('chapterFilter');
        const sectionFilter = document.getElementById('sectionFilter');
        const refreshBtn = document.getElementById('refreshBtn');

        if (chapterFilter) {
            chapterFilter.addEventListener('change', () => {
                const chapterCode = chapterFilter.value;
                this.loadSections(chapterCode);
                this.loadIndicators({ chapter: chapterCode });
            });
        }

        if (sectionFilter) {
            sectionFilter.addEventListener('change', () => {
                const sectionCode = sectionFilter.value;
                const chapterCode = chapterFilter ? chapterFilter.value : '';
                this.loadIndicators({ chapter: chapterCode, section: sectionCode });
            });
        }

        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                location.reload();
            });
        }

        // 展开/收起按钮
        const expandAllBtn = document.getElementById('expandAllBtn');
        const collapseAllBtn = document.getElementById('collapseAllBtn');

        console.log('绑定展开/收起按钮事件:');
        console.log('expandAllBtn:', expandAllBtn ? '找到' : '未找到');
        console.log('collapseAllBtn:', collapseAllBtn ? '找到' : '未找到');

        if (expandAllBtn) {
            expandAllBtn.addEventListener('click', () => {
                console.log('点击展开全部按钮');
                this.expandAllIndicators();
            });
        } else {
            // 如果按钮不存在，延迟重试
            setTimeout(() => {
                const retryExpandBtn = document.getElementById('expandAllBtn');
                if (retryExpandBtn) {
                    console.log('延迟找到展开按钮，绑定事件');
                    retryExpandBtn.addEventListener('click', () => {
                        console.log('点击展开全部按钮');
                        this.expandAllIndicators();
                    });
                }
            }, 1000);
        }

        if (collapseAllBtn) {
            collapseAllBtn.addEventListener('click', () => {
                console.log('点击收起全部按钮');
                this.collapseAllIndicators();
            });
        } else {
            // 如果按钮不存在，延迟重试
            setTimeout(() => {
                const retryCollapseBtn = document.getElementById('collapseAllBtn');
                if (retryCollapseBtn) {
                    console.log('延迟找到收起按钮，绑定事件');
                    retryCollapseBtn.addEventListener('click', () => {
                        console.log('点击收起全部按钮');
                        this.collapseAllIndicators();
                    });
                }
            }, 1000);
        }
    }

    // 加载小节数据
    async loadSections(chapterCode) {
        const sectionFilter = document.getElementById('sectionFilter');
        if (!sectionFilter) return;

        sectionFilter.innerHTML = '<option value="">所有小节</option>';

        if (!chapterCode) return;

        try {
            const response = await fetch(`/api/chapters/${chapterCode}/sections`);
            const data = await response.json();

            if (data.success) {
                data.data.forEach(section => {
                    const option = document.createElement('option');
                    option.value = section.code;
                    option.textContent = `${section.code} - ${section.name}`;
                    sectionFilter.appendChild(option);
                });
            }
        } catch (error) {
            console.error('加载小节数据失败:', error);
        }
    }

    // 显示指标详情模态框
    showIndicatorDetailModal(indicatorId) {
        const modal = document.getElementById('indicatorModal');
        if (!modal) return;

        // 显示模态框
        modal.classList.add('show');

        // 绑定关闭事件
        this.bindModalCloseEvents();

        // 加载指标详情
        this.loadIndicatorDetailForModal(indicatorId);
    }

    // 绑定模态框关闭事件
    bindModalCloseEvents() {
        const modal = document.getElementById('indicatorModal');
        const closeBtn = document.getElementById('closeModal');

        // 点击关闭按钮
        if (closeBtn) {
            closeBtn.onclick = () => this.closeIndicatorDetailModal();
        }

        // 点击遮罩层关闭
        if (modal) {
            modal.onclick = (e) => {
                if (e.target === modal) {
                    this.closeIndicatorDetailModal();
                }
            };
        }

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.classList.contains('show')) {
                this.closeIndicatorDetailModal();
            }
        });
    }

    // 关闭指标详情模态框
    closeIndicatorDetailModal() {
        const modal = document.getElementById('indicatorModal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    // 为模态框加载指标详情
    async loadIndicatorDetailForModal(indicatorId) {
        try {
            const response = await fetch(`/api/indicators/${indicatorId}`);
            const result = await response.json();

            if (result.success) {
                this.renderIndicatorDetailInModal(result.data);
            } else {
                this.showModalAlert('加载指标详情失败: ' + result.error, 'error');
            }
        } catch (error) {
            this.showModalAlert('网络错误: ' + error.message, 'error');
        }
    }

    // 在模态框中渲染指标详情
    renderIndicatorDetailInModal(data) {
        try {
            const indicator = data.indicator;
            const components = data.components;
            const children = data.children || [];
            const parent = data.parent;

            // 更新头部信息
            this.updateModalHeader(indicator);

            // 更新基本信息
            this.updateModalBasicInfo(indicator, parent, children);

            // 更新子指标导航（如果有子指标）
            this.updateModalChildrenNav(children, indicator.id, indicator);

            // 根据指标类型决定是否显示分子分母组件
            this.renderComponentsInModal(components, indicator);
        } catch (error) {
            console.error('渲染指标详情时出错:', error);
            this.showModalAlert('渲染指标详情时出错: ' + error.message, 'error');
        }
    }

    // 更新模态框头部
    updateModalHeader(indicator) {
        const badgeElement = document.getElementById('modalIndicatorBadge');
        const titleElement = document.getElementById('modalIndicatorTitle');
        const breadcrumbElement = document.getElementById('modalBreadcrumb');

        if (badgeElement) {
            if (badgeElement) badgeElement.textContent = indicator.id;
        }

        if (titleElement) {
            if (titleElement) titleElement.textContent = indicator.name;
        }

        if (breadcrumbElement) {
            breadcrumbElement.innerHTML = `
                <span>${indicator.chapter_name || '未知章节'}</span>
                <i class="fas fa-chevron-right"></i>
                <span>${indicator.section_name || '未知小节'}</span>
            `;
        }
    }

    // 更新基本信息
    async updateModalBasicInfo(indicator, parent, children) {
        // 基本信息字段
        const idElement = document.getElementById('modalIndicatorId');
        const nameElement = document.getElementById('modalIndicatorName');
        const typeElement = document.getElementById('modalIndicatorType');
        const chapterElement = document.getElementById('modalIndicatorChapter');
        const sectionElement = document.getElementById('modalIndicatorSection');
        const parentElement = document.getElementById('modalIndicatorParent');
        const childrenCountElement = document.getElementById('modalChildrenCount');

        if (idElement) {
            if (idElement) idElement.textContent = indicator.id;
        }

        if (nameElement) {
            if (nameElement) nameElement.textContent = indicator.name || '未命名';
        }

        if (typeElement) {
            const indicatorType = indicator.indicator_type || 'composite';
            const typeText = indicatorType === 'simple' ? '简单指标' : '复合指标';
            const typeClass = indicatorType === 'simple' ? 'simple-indicator' : 'composite-indicator';
            typeElement.innerHTML = `<span class="indicator-type-badge ${typeClass}">${typeText}</span>`;
        }

        if (chapterElement) {
            if (chapterElement) chapterElement.textContent = indicator.chapter_name ?
                `${indicator.chapter_code} - ${indicator.chapter_name}` : '未设置';
        }

        if (sectionElement) {
            if (sectionElement) sectionElement.textContent = indicator.section_name ?
                `${indicator.section_code} - ${indicator.section_name}` : '未设置';
        }

        if (parentElement) {
            if (parentElement) parentElement.textContent = parent ?
                `${parent.id} - ${parent.name}` : '顶级指标';
        }

        if (childrenCountElement) {
            if (childrenCountElement) childrenCountElement.textContent = `${children ? children.length : 0} 个`;
        }

        // 加载并显示基础信息字段
        await this.loadAndDisplayBasicFields(indicator.id);

        // 描述信息
        const descSection = document.getElementById('modalDescriptionSection');
        const descElement = document.getElementById('modalIndicatorDescription');
        if (indicator.description) {
            descSection.style.display = 'block';
            if (descElement) descElement.textContent = indicator.description;
        } else {
            descSection.style.display = 'none';
        }

        // 计算方法
        const calcSection = document.getElementById('modalCalculationSection');
        const calcElement = document.getElementById('modalCalculationMethod');
        if (indicator.calculation_method) {
            calcSection.style.display = 'block';
            if (calcElement) calcElement.textContent = indicator.calculation_method;
        } else {
            calcSection.style.display = 'none';
        }

        // 显示详细信息
        this.updateModalDetailedInfo(indicator);
    }

    // 加载并显示基础信息字段
    async loadAndDisplayBasicFields(indicatorId) {
        try {
            const response = await fetch(`/api/indicators/${indicatorId}/basic-fields`);
            const result = await response.json();

            if (result.success) {
                this.displayBasicFields(result.data);
            } else {
                console.error('加载基础字段失败:', result.error);
            }
        } catch (error) {
            console.error('加载基础字段错误:', error);
        }
    }

    // 显示基础信息字段
    displayBasicFields(basicFields) {
        // 查找基础信息容器
        const basicInfoContainer = document.querySelector('.modal-basic-info-grid');
        if (!basicInfoContainer) return;

        // 生成基础字段HTML
        const fieldsHtml = basicFields.map(field => {
            const value = field.field_value || '未填写';
            const isEmpty = !field.field_value || field.field_value.trim() === '';

            return `
                <div class="modal-field-group">
                    <div class="modal-field-label">${field.field_label}</div>
                    <div class="modal-field-value ${isEmpty ? 'empty' : ''}">${value}</div>
                </div>
            `;
        }).join('');

        // 查找现有的基础字段容器或创建新的
        let basicFieldsContainer = basicInfoContainer.querySelector('.basic-fields-container');
        if (!basicFieldsContainer) {
            basicFieldsContainer = document.createElement('div');
            basicFieldsContainer.className = 'basic-fields-container';
            basicInfoContainer.appendChild(basicFieldsContainer);
        }

        basicFieldsContainer.innerHTML = fieldsHtml;
    }

    // 更新详细信息
    updateModalDetailedInfo(indicator) {
        const detailedCard = document.getElementById('modalDetailedCard');
        if (!detailedCard) return;

        let hasDetailedInfo = false;

        // 基本属性区域（底层指标专用）
        this.updateModalBasicAttributes(indicator);

        // 意义
        const sigSection = document.getElementById('modalSignificanceSection');
        const sigElement = document.getElementById('modalIndicatorSignificance');
        if (indicator.significance && sigSection && sigElement) {
            sigSection.style.display = 'block';
            if (sigElement) sigElement.textContent = indicator.significance;
            hasDetailedInfo = true;
        } else if (sigSection) {
            sigSection.style.display = 'none';
        }

        // 检查是否有基本属性信息
        const basicAttrsSection = document.getElementById('modalBasicAttributesSection');
        if (basicAttrsSection && basicAttrsSection.style.display !== 'none') {
            hasDetailedInfo = true;
        }

        // 检查是否有任何详细信息数据
        const hasAnyDetailedData = indicator.significance ||
                                 indicator.unit ||
                                 indicator.lead_department ||
                                 indicator.data_source ||
                                 indicator.logic_definition;

        // 显示或隐藏详细信息卡片
        detailedCard.style.display = (hasDetailedInfo && hasAnyDetailedData) ? 'block' : 'none';

        // 更新指标参考信息
        this.updateModalReferenceInfo(indicator);

        /* 暂时隐藏参考范围功能
/* 暂时隐藏参考范围功能
// 更新参考范围信息（底层指标专用）
        this.updateModalReferenceRangeInfo(indicator);
*/
    }

    // 更新指标参考信息
    updateModalReferenceInfo(indicator) {
        try {
            const referenceCard = document.getElementById('modalReferenceCard');
            if (!referenceCard) {
                console.warn('找不到modalReferenceCard元素');
                return;
            }

            let hasReferenceInfo = false;

            // 检查是否有任何参考数据
            const hasAnyReferenceData = indicator.indicator_definition ||
                                      indicator.statistical_scope ||
                                      indicator.data_sources ||
                                      indicator.collection_frequency_detail ||
                                      indicator.reference_value ||
                                      indicator.reference_range ||
                                      indicator.monitoring_analysis;

            if (!hasAnyReferenceData) {
                referenceCard.style.display = 'none';
                return;
            }

        // 指标定义
        const defSection = document.getElementById('modalRefDefinitionSection');
        const defElement = document.getElementById('modalRefIndicatorDefinition');
        if (indicator.indicator_definition && defSection && defElement) {
            defSection.style.display = 'block';
            if (defElement) defElement.textContent = indicator.indicator_definition || "暂无定义";
            hasReferenceInfo = true;
        } else if (defSection) {
            defSection.style.display = 'none';
        }

        // 统计范围
        const scopeSection = document.getElementById('modalRefScopeSection');
        const scopeElement = document.getElementById('modalRefStatisticalScope');
        if (indicator.statistical_scope && scopeSection && scopeElement) {
            scopeSection.style.display = 'block';
            if (scopeElement) scopeElement.textContent = indicator.statistical_scope || "暂无统计范围";
            hasReferenceInfo = true;
        } else if (scopeSection) {
            scopeSection.style.display = 'none';
        }

        // 数据来源
        const dataSourcesSection = document.getElementById('modalRefDataSourcesSection');
        const dataSourcesElement = document.getElementById('modalRefDataSources');
        if (indicator.data_sources && dataSourcesSection && dataSourcesElement) {
            dataSourcesSection.style.display = 'block';
            if (dataSourcesElement) dataSourcesElement.textContent = indicator.data_sources || "暂无数据来源";
            hasReferenceInfo = true;
        } else if (dataSourcesSection) {
            dataSourcesSection.style.display = 'none';
        }

        // 统计频率
        const freqSection = document.getElementById('modalRefFrequencySection');
        const freqElement = document.getElementById('modalRefCollectionFrequency');
        if (indicator.collection_frequency_detail && freqSection && freqElement) {
            freqSection.style.display = 'block';
            if (freqElement) freqElement.textContent = indicator.collection_frequency_detail;
            hasReferenceInfo = true;
        } else if (freqSection) {
            freqSection.style.display = 'none';
        }

        // 标准值/参考值
        const refSection = document.getElementById('modalRefReferenceSection');
        const refElement = document.getElementById('modalRefReferenceValue');
        if ((indicator.reference_value || indicator.reference_range) && refSection && refElement) {
            refSection.style.display = 'block';
            if (refElement) refElement.textContent = indicator.reference_value || indicator.reference_range || "暂无参考值";
            hasReferenceInfo = true;
        } else if (refSection) {
            refSection.style.display = 'none';
        }

        // 监测分析
        const monitoringSection = document.getElementById('modalRefMonitoringSection');
        const monitoringElement = document.getElementById('modalRefMonitoringAnalysis');
        const dimensionsElement = document.getElementById('modalRefAnalysisDimensions');
        if (indicator.monitoring_analysis && monitoringSection && monitoringElement) {
            monitoringSection.style.display = 'block';
            if (monitoringElement) monitoringElement.textContent = indicator.monitoring_analysis || "暂无监测分析";

            // 分析维度
            if (indicator.analysis_dimensions && indicator.analysis_dimensions.length > 0) {
                this.renderModalAnalysisDimensions(indicator.analysis_dimensions, dimensionsElement);
            } else if (dimensionsElement) {
                dimensionsElement.innerHTML = '';
            }
            hasReferenceInfo = true;
        } else if (monitoringSection) {
            monitoringSection.style.display = 'none';
        }

            // 显示或隐藏指标参考卡片
            referenceCard.style.display = hasReferenceInfo ? 'block' : 'none';
        } catch (error) {
            console.error('更新指标参考信息时出错:', error);
        }
    }

    // 更新参考范围信息（底层指标专用）
    updateModalReferenceRangeInfo(indicator) {
        const referenceRangeCard = document.getElementById('modalReferenceRangeCard');
        if (!referenceRangeCard) return;

        // 检查是否有参考范围数据
        const referenceRangeData = indicator.reference_range_data;

        if (!referenceRangeData) {
            referenceRangeCard.style.display = 'none';
            return;
        }

        let hasReferenceRangeInfo = false;

        // 目标值与当前值
        const targetValueSection = document.getElementById('modalTargetValueSection');
        const targetValueElement = document.getElementById('modalTargetValue');
        const currentValueElement = document.getElementById('modalCurrentValue');
        const completionRateElement = document.getElementById('modalCompletionRate');

        if (referenceRangeData.target_value || referenceRangeData.current_value || referenceRangeData.completion_rate) {
            if (targetValueSection) {
                targetValueSection.style.display = 'block';
                hasReferenceRangeInfo = true;
            }

            if (targetValueElement) {
                if (targetValueElement) targetValueElement.textContent = referenceRangeData.target_value || '未设置';
            }
            if (currentValueElement) {
                if (currentValueElement) currentValueElement.textContent = referenceRangeData.current_value || '未设置';
            }
            if (completionRateElement) {
                const rate = referenceRangeData.completion_rate;
                if (completionRateElement) completionRateElement.textContent = rate ? `${rate}%` : '未设置';
            }
        } else if (targetValueSection) {
            targetValueSection.style.display = 'none';
        }

        // 计算方法
        const calcMethodSection = document.getElementById('modalCalculationMethodSection');
        const calcMethodElement = document.getElementById('modalCalculationMethod');
        if (referenceRangeData.calculation_method && calcMethodSection && calcMethodElement) {
            calcMethodSection.style.display = 'block';
            if (calcMethodElement) calcMethodElement.textContent = referenceRangeData.calculation_method;
            hasReferenceRangeInfo = true;
        } else if (calcMethodSection) {
            calcMethodSection.style.display = 'none';
        }

        // 数据来源详细
        const dataSourcesDetailSection = document.getElementById('modalDataSourcesDetailSection');
        const dataSourcesDetailElement = document.getElementById('modalDataSourcesDetail');
        if (referenceRangeData.data_sources && dataSourcesDetailSection && dataSourcesDetailElement) {
            dataSourcesDetailSection.style.display = 'block';
            if (dataSourcesDetailElement) dataSourcesDetailElement.textContent = referenceRangeData.data_sources;
            hasReferenceRangeInfo = true;
        } else if (dataSourcesDetailSection) {
            dataSourcesDetailSection.style.display = 'none';
        }

        // 注意事项
        const notesSection = document.getElementById('modalNotesSection');
        const notesElement = document.getElementById('modalNotes');
        if (referenceRangeData.notes && notesSection && notesElement) {
            notesSection.style.display = 'block';
            notesElement.innerHTML = this.formatNotes(referenceRangeData.notes);
            hasReferenceRangeInfo = true;
        } else if (notesSection) {
            notesSection.style.display = 'none';
        }

        // 显示或隐藏参考范围卡片
        referenceRangeCard.style.display = hasReferenceRangeInfo ? 'block' : 'none';
*/
    }

    // 格式化注意事项文本
    formatNotes(notes) {
        if (!notes) return '';

        // 将数字开头的行转换为列表项
        return notes.replace(/^(\d+\.\s)/gm, '<br>$1').replace(/^<br>/, '');
    }

    // 更新基本属性信息（底层指标专用）
    updateModalBasicAttributes(indicator) {
        const basicAttrsSection = document.getElementById('modalBasicAttributesSection');
        if (!basicAttrsSection) return;

        // 检查是否为底层指标（有基本属性字段）
        const hasBasicAttrs = indicator.unit || indicator.lead_department ||
                             indicator.data_source || indicator.logic_definition;

        if (!hasBasicAttrs) {
            basicAttrsSection.style.display = 'none';
            return;
        }

        // 显示基本属性区域
        basicAttrsSection.style.display = 'block';

        // 更新各个基本属性字段
        this.updateBasicAttributeField('modalUnit', '单位', indicator.unit);
        this.updateBasicAttributeField('modalLeadDepartment', '牵头科室', indicator.lead_department);
        this.updateBasicAttributeField('modalDataSource', '数据来源', indicator.data_source);
        this.updateBasicAttributeField('modalLogicDefinition', '逻辑定义', indicator.logic_definition);
    }

    // 更新单个基本属性字段
    updateBasicAttributeField(elementId, label, value) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const container = element.closest('.modal-field-row');
        if (!container) return;

        if (value && value.trim() !== '') {
            container.style.display = 'block';
            element.textContent = value;
        } else {
            container.style.display = 'none';
        }
    }

    // 渲染分析维度
    renderModalAnalysisDimensions(dimensions, container) {
        if (!container) return;

        if (!dimensions || dimensions.length === 0) {
            container.innerHTML = '';
            return;
        }

        const dimensionsHtml = `
            <div style="margin-top: 16px;">
                <h4 style="color: var(--primary-color, #1976d2); margin-bottom: 12px; font-size: 16px;">建议对数据进行多维度分析：</h4>
                <div style="display: grid; gap: 12px;">
                    ${dimensions.map(dim => `
                        <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; border-left: 3px solid var(--primary-color, #1976d2);">
                            <div style="font-weight: 600; color: #333; margin-bottom: 4px;">${dim.dimension_name}</div>
                            <div style="color: #666; font-size: 14px;">${dim.analysis_content}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        container.innerHTML = dimensionsHtml;
    }

    // 更新子指标导航
    updateModalChildrenNav(children, parentId, indicator) {
        const navCard = document.getElementById('childrenNavCard');
        const countElement = document.getElementById('childrenCount');
        const gridElement = document.getElementById('childrenNavGrid');

        if (!navCard || !countElement || !gridElement) return;

        // 如果是简单指标，隐藏子指标模块
        if (indicator && indicator.indicator_type === 'simple') {
            navCard.style.display = 'none';
            return;
        }

        if (children.length > 0) {
            navCard.style.display = 'block';
            if (countElement) countElement.textContent = children.length;

            const html = children.map(child => `
                <div class="child-nav-item" onclick="app.showIndicatorDetailInModal('${child.id}')">
                    <div class="child-nav-info">
                        <div class="child-nav-id">${child.id}</div>
                        <div class="child-nav-name">${child.name}</div>
                    </div>
                    <div class="child-nav-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            `).join('');

            gridElement.innerHTML = html;
        } else {
            navCard.style.display = 'none';
        }
    }

    // 在模态框中渲染分子分母组件（整合在基本属性内）
    renderComponentsInModal(components, indicator) {
        const container = document.getElementById('modalComponentsContainer');
        const componentsSection = document.getElementById('modalComponentsSection');

        if (!container || !componentsSection) return;

        // 如果是简单指标，隐藏分子分母组件部分
        if (indicator.indicator_type === 'simple') {
            componentsSection.style.display = 'none';
            return;
        }

        // 显示分子分母组件部分（复合指标且有组件数据时）
        if (indicator.indicator_type === 'composite' && components.length > 0) {
            componentsSection.style.display = 'block';
        } else {
            componentsSection.style.display = 'none';
            return;
        }

        if (components.length === 0) {
            container.innerHTML = `
                <div class="empty-state" style="padding: 20px; text-align: center; color: #666;">
                    <p>暂无分子分母信息</p>
                </div>
            `;
            return;
        }

        // 渲染为表格形式，与后端保持一致
        const tableHtml = `
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f5f5f5;">
                        <th style="padding: 12px; text-align: left; font-weight: 500; color: #333; border-bottom: 1px solid #e0e0e0; width: 80px;">类型</th>
                        <th style="padding: 12px; text-align: left; font-weight: 500; color: #333; border-bottom: 1px solid #e0e0e0;">组件名称</th>
                        <th style="padding: 12px; text-align: left; font-weight: 500; color: #333; border-bottom: 1px solid #e0e0e0; width: 80px;">单位</th>
                        <th style="padding: 12px; text-align: left; font-weight: 500; color: #333; border-bottom: 1px solid #e0e0e0; width: 120px;">牵头科室</th>
                        <th style="padding: 12px; text-align: left; font-weight: 500; color: #333; border-bottom: 1px solid #e0e0e0; width: 120px;">数据来源</th>
                        <th style="padding: 12px; text-align: left; font-weight: 500; color: #333; border-bottom: 1px solid #e0e0e0; width: 80px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${components.map(component => `
                        <tr style="border-bottom: 1px solid #f0f0f0;">
                            <td style="padding: 12px; vertical-align: top;">
                                <span style="background: ${component.component_type === 'numerator' ? '#e3f2fd' : '#fff3e0'}; color: ${component.component_type === 'numerator' ? '#1976d2' : '#f57c00'}; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                                    ${component.component_type === 'numerator' ? '分子' : '分母'}
                                </span>
                            </td>
                            <td style="padding: 12px; vertical-align: top;">
                                <div style="font-weight: 500; margin-bottom: 4px;">${component.name || '未填写'}</div>
                                ${component.logic_definition ? `<div style="font-size: 12px; color: #666; line-height: 1.4;">${component.logic_definition.substring(0, 60)}${component.logic_definition.length > 60 ? '...' : ''}</div>` : ''}
                            </td>
                            <td style="padding: 12px; vertical-align: top;">${component.unit || '-'}</td>
                            <td style="padding: 12px; vertical-align: top;">${component.lead_department || '-'}</td>
                            <td style="padding: 12px; vertical-align: top;">${component.data_source || '-'}</td>
                            <td style="padding: 12px; vertical-align: top;">
                                <button onclick="app.editComponentInModal(${component.id})" style="background: #f5f5f5; border: 1px solid #ddd; border-radius: 4px; padding: 4px 8px; margin-right: 4px; cursor: pointer; font-size: 12px;" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="app.deleteComponentInModal('${indicator.id}', ${component.id})" style="background: #ffebee; border: 1px solid #ffcdd2; border-radius: 4px; padding: 4px 8px; cursor: pointer; font-size: 12px; color: #d32f2f;" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        container.innerHTML = tableHtml;
    }

    // 渲染模态框中的编辑表单
    renderEditFormForModal(component) {
        return `
            <div class="modal-form-row">
                <div class="modal-form-group">
                    <label class="modal-form-label">名称</label>
                    <input type="text" class="modal-form-input" name="name" value="${component.name || ''}" placeholder="请输入组件名称">
                </div>
                <div class="modal-form-group">
                    <label class="modal-form-label">单位</label>
                    <input type="text" class="modal-form-input" name="unit" value="${component.unit || ''}" placeholder="请输入单位">
                </div>
            </div>
            <div class="modal-form-row">
                <div class="modal-form-group">
                    <label class="modal-form-label">牵头科室</label>
                    <input type="text" class="modal-form-input" name="lead_department" value="${component.lead_department || ''}" placeholder="请输入牵头科室">
                </div>
                <div class="modal-form-group">
                    <label class="modal-form-label">数据来源</label>
                    <input type="text" class="modal-form-input" name="data_source" value="${component.data_source || ''}" placeholder="请输入数据来源">
                </div>
            </div>
            <div class="modal-form-group">
                <label class="modal-form-label">逻辑定义</label>
                <textarea class="modal-form-textarea" name="logic_definition" placeholder="请输入逻辑定义">${component.logic_definition || ''}</textarea>
            </div>
            <div class="modal-form-group">
                <label class="modal-form-label">备注</label>
                <textarea class="modal-form-textarea" name="notes" placeholder="请输入备注信息">${component.notes || ''}</textarea>
            </div>
            <div class="modal-form-actions">
                <button class="modal-save-btn" onclick="app.saveComponentInModal('${component.indicator_id}', ${component.id})">保存</button>
                <button class="modal-cancel-btn" onclick="app.cancelEditInModal(${component.id})">取消</button>
            </div>
        `;
    }

    // 在模态框中编辑组件
    editComponentInModal(componentId) {
        const contentDiv = document.getElementById(`modal-content-${componentId}`);
        const formDiv = document.getElementById(`modal-form-${componentId}`);

        if (contentDiv && formDiv) {
            contentDiv.style.display = 'none';
            formDiv.style.display = 'grid';
        }
    }

    // 在模态框中取消编辑
    cancelEditInModal(componentId) {
        const contentDiv = document.getElementById(`modal-content-${componentId}`);
        const formDiv = document.getElementById(`modal-form-${componentId}`);

        if (contentDiv && formDiv) {
            contentDiv.style.display = 'grid';
            formDiv.style.display = 'none';
        }
    }

    // 在模态框中保存组件
    async saveComponentInModal(indicatorId, componentId) {
        const formDiv = document.getElementById(`modal-form-${componentId}`);
        if (!formDiv) return;

        // 收集表单数据
        const inputs = formDiv.querySelectorAll('input, textarea');
        const data = {};
        inputs.forEach(input => {
            data[input.name] = input.value;
        });

        try {
            formDiv.classList.add('loading');

            const response = await fetch(`/api/indicators/${indicatorId}/components/${componentId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showModalAlert('组件信息更新成功', 'success');
                // 重新加载指标详情
                this.loadIndicatorDetailForModal(indicatorId);
            } else {
                this.showModalAlert('更新失败: ' + result.error, 'error');
            }
        } catch (error) {
            this.showModalAlert('网络错误: ' + error.message, 'error');
        } finally {
            formDiv.classList.remove('loading');
        }
    }

    // 在模态框中删除组件
    async deleteComponentInModal(indicatorId, componentId) {
        if (!confirm('确定要删除这个组件吗？此操作不可撤销。')) {
            return;
        }

        try {
            const response = await fetch(`/api/indicators/${indicatorId}/components/${componentId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                this.showModalAlert('组件删除成功', 'success');
                // 重新加载指标详情
                this.loadIndicatorDetailForModal(indicatorId);
            } else {
                this.showModalAlert('删除失败: ' + result.error, 'error');
            }
        } catch (error) {
            this.showModalAlert('网络错误: ' + error.message, 'error');
        }
    }

    // 显示模态框中的提示信息
    showModalAlert(message, type) {
        const container = document.getElementById('modalAlertContainer');
        if (!container) return;

        const alert = document.createElement('div');
        alert.className = `modal-alert modal-alert-${type}`;
        alert.textContent = message;

        container.appendChild(alert);

        setTimeout(() => {
            alert.remove();
        }, 5000);
    }

    // 在模态框中显示指标详情（支持子指标导航）
    showIndicatorDetailInModal(indicatorId) {
        // 直接调用现有的模态框显示方法
        this.showIndicatorDetailModal(indicatorId);
    }

    bindEvents() {
        // 菜单切换
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');

        if (menuToggle && sidebar) {
            menuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
            });
        }

        // 视图切换按钮
        this.bindViewToggle();

        // 弹窗关闭
        const closeModal = document.getElementById('closeModal');
        const modal = document.getElementById('indicatorModal');

        if (closeModal && modal) {
            closeModal.addEventListener('click', () => {
                this.hideModal();
            });

            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideModal();
                }
            });
        }

        // 快速操作按钮
        const backToTop = document.getElementById('backToTop');
        if (backToTop) {
            backToTop.addEventListener('click', () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        }

        const randomIndicator = document.getElementById('randomIndicator');
        if (randomIndicator) {
            randomIndicator.addEventListener('click', () => {
                this.showRandomIndicator();
            });
        }

        const favorites = document.getElementById('favorites');
        if (favorites) {
            favorites.addEventListener('click', () => {
                this.showFavorites();
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideModal();
                this.hideSearchDropdown();
            }

            if (e.ctrlKey || e.metaKey) {
                if (e.key === 'k') {
                    e.preventDefault();
                    const searchInput = document.getElementById('globalSearch');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }
            }
        });

        // 滚动显示/隐藏快速操作按钮
        window.addEventListener('scroll', () => {
            const backToTop = document.getElementById('backToTop');
            if (backToTop) {
                if (window.scrollY > 300) {
                    backToTop.style.display = 'flex';
                } else {
                    backToTop.style.display = 'none';
                }
            }
        });
    }

    bindViewToggle() {
        // 绑定视图切换按钮事件
        const viewButtons = document.querySelectorAll('.view-btn');
        console.log('找到视图切换按钮:', viewButtons.length);

        viewButtons.forEach((btn, index) => {
            console.log(`按钮${index + 1}:`, btn.dataset.view, btn.textContent.trim());
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const view = btn.dataset.view;
                console.log('点击视图切换按钮:', view);
                this.switchView(view);
            });
        });

        // 如果没有找到按钮，延迟重试
        if (viewButtons.length === 0) {
            console.log('未找到视图切换按钮，1秒后重试...');
            setTimeout(() => {
                this.bindViewToggle();
            }, 1000);
        }
    }

    switchView(view) {
        console.log('switchView被调用，目标视图:', view);

        // 更新按钮状态
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        const activeBtn = document.querySelector(`[data-view="${view}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
            console.log('按钮状态已更新');
        } else {
            console.log('未找到目标按钮:', `[data-view="${view}"]`);
        }

        // 切换视图显示
        const cardView = document.getElementById('cardView');
        const listView = document.getElementById('listView');

        console.log('DOM元素检查:');
        console.log('cardView:', cardView ? '存在' : '不存在');
        console.log('listView:', listView ? '存在' : '不存在');

        if (view === 'card') {
            if (cardView) {
                cardView.style.display = 'block';
                console.log('卡片视图已显示');
            }
            if (listView) {
                listView.style.display = 'none';
                console.log('列表视图已隐藏');
            }
        } else if (view === 'list') {
            if (cardView) {
                cardView.style.display = 'none';
                console.log('卡片视图已隐藏');
            }
            if (listView) {
                listView.style.display = 'block';
                console.log('列表视图已显示');
            }
        }

        // 保存用户偏好
        localStorage.setItem('preferred_view', view);

        console.log(`✅ 成功切换到${view === 'card' ? '卡片' : '列表'}视图`);
    }

    setupSearch() {
        const searchInput = document.getElementById('globalSearch');
        const searchDropdown = document.getElementById('searchDropdown');

        if (!searchInput || !searchDropdown) return;

        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();

            clearTimeout(this.searchTimeout);

            if (query.length < 2) {
                this.hideSearchDropdown();
                return;
            }

            this.searchTimeout = setTimeout(() => {
                this.performSearch(query);
            }, 300);
        });

        searchInput.addEventListener('focus', () => {
            if (searchInput.value.trim().length >= 2) {
                this.showSearchDropdown();
            }
        });

        searchInput.addEventListener('blur', () => {
            // 延迟隐藏，允许点击搜索结果
            setTimeout(() => {
                this.hideSearchDropdown();
            }, 200);
        });
    }

    async performSearch(query) {
        try {
            const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
            const data = await response.json();

            if (data.success) {
                this.renderSearchResults(data.data);
                this.showSearchDropdown();
            }
        } catch (error) {
            console.error('搜索失败:', error);
        }
    }

    renderSearchResults(results) {
        const dropdown = document.getElementById('searchDropdown');
        if (!dropdown) return;

        if (results.length === 0) {
            dropdown.innerHTML = '<div class="search-no-results">未找到相关指标</div>';
            return;
        }

        dropdown.innerHTML = results.map(result => `
            <div class="search-result-item" onclick="app.showIndicatorDetail('${result.id}')">
                <div class="search-result-title">${result.id} - ${result.name}</div>
                <div class="search-result-desc">${result.description || '暂无描述'}</div>
                <div class="search-result-meta">${result.chapter_name} > ${result.section_name || '未分类'}</div>
            </div>
        `).join('');
    }

    showSearchDropdown() {
        const dropdown = document.getElementById('searchDropdown');
        if (dropdown) {
            dropdown.classList.add('active');
        }
    }

    hideSearchDropdown() {
        const dropdown = document.getElementById('searchDropdown');
        if (dropdown) {
            dropdown.classList.remove('active');
        }
    }

    // 设置监听后端更新
    setupAdminUpdateListener() {
        // 监听 localStorage 变化（跨页面通信）
        window.addEventListener('storage', (event) => {
            if (event.key === 'indicator_type_update') {
                this.handleAdminUpdate(event.newValue);
            }
        });

        // 监听自定义事件（同页面通信）
        window.addEventListener('storage', (event) => {
            if (event.key === 'indicator_type_update') {
                this.handleAdminUpdate(event.newValue);
            }
        });

        console.log('已设置后端更新监听器');
    }

    // 处理后端更新
    handleAdminUpdate(updateDataStr) {
        try {
            if (!updateDataStr) return;

            const updateData = JSON.parse(updateDataStr);
            const { indicatorId, indicatorType, timestamp, source } = updateData;

            // 检查更新时间（避免处理过期的更新）
            if (Date.now() - timestamp > 30000) { // 30秒内的更新才处理
                return;
            }

            console.log('收到后端更新通知:', indicatorId, indicatorType);

            // 如果当前模态框显示的是被更新的指标，重新加载
            const modal = document.getElementById('indicatorModal');
            if (modal && modal.classList.contains('active')) {
                const currentIndicatorId = this.getCurrentModalIndicatorId();
                if (currentIndicatorId === indicatorId) {
                    console.log('重新加载当前指标详情');
                    this.loadIndicatorDetailForModal(indicatorId);

                    // 显示更新通知
                    this.showNotification(
                        `指标 ${indicatorId} 的类型已更新为：${indicatorType === 'simple' ? '简单指标' : '复合指标'}`,
                        'info'
                    );
                }
            }

            // 清除更新标记（避免重复处理）
            localStorage.removeItem('indicator_type_update');

        } catch (error) {
            console.error('处理后端更新失败:', error);
        }
    }

    // 获取当前模态框显示的指标ID
    getCurrentModalIndicatorId() {
        const badgeElement = document.getElementById('modalIndicatorBadge');
        return badgeElement ? badgeElement.textContent.trim() : null;
    }

    async loadChapterNavigation() {
        try {
            const response = await fetch('/api/chapters');
            const data = await response.json();

            if (data.success) {
                this.renderTreeNavigation(data.data);
            }
        } catch (error) {
            console.error('加载章节导航失败:', error);
        }
    }

    renderTreeNavigation(chapters) {
        const nav = document.getElementById('chapterNav');
        if (!nav) return;

        const html = `
            <ul class="nav-tree">
                ${chapters.map(chapter => `
                    <li class="nav-tree-item">
                        <button class="nav-tree-toggle" data-chapter="${chapter.code}" onclick="app.toggleChapter('${chapter.code}')">
                            <i class="fas fa-chevron-right nav-tree-icon"></i>
                            <i class="${chapter.icon}" style="margin-right: 8px; color: ${chapter.color};"></i>
                            <span>第${chapter.code}章</span>
                        </button>
                        <div class="nav-tree-children" id="sections-${chapter.code}">
                            <!-- 节信息将动态加载 -->
                        </div>
                    </li>
                `).join('')}
            </ul>
        `;

        nav.innerHTML = html;
    }

    async toggleChapter(chapterCode) {
        const toggleBtn = document.querySelector(`[data-chapter="${chapterCode}"]`);
        const sectionsContainer = document.getElementById(`sections-${chapterCode}`);

        if (!toggleBtn || !sectionsContainer) return;

        const isExpanded = toggleBtn.classList.contains('expanded');

        if (isExpanded) {
            // 折叠
            toggleBtn.classList.remove('expanded');
            sectionsContainer.classList.remove('expanded');
        } else {
            // 展开
            toggleBtn.classList.add('expanded');
            sectionsContainer.classList.add('expanded');

            // 如果还没有加载节信息，则加载
            if (sectionsContainer.children.length === 0) {
                await this.loadSectionsForChapter(chapterCode);
            }
        }
    }

    async loadSectionsForChapter(chapterCode) {
        try {
            const response = await fetch(`/api/sections?chapter=${chapterCode}`);
            const result = await response.json();

            if (result.success) {
                this.renderSectionsInTree(chapterCode, result.data);
            }
        } catch (error) {
            console.error('加载节信息失败:', error);
        }
    }

    renderSectionsInTree(chapterCode, sections) {
        const container = document.getElementById(`sections-${chapterCode}`);
        if (!container) return;

        const html = sections.map(section => `
            <a href="#" class="nav-tree-child" data-chapter="${chapterCode}" data-section="${section.code}" onclick="app.selectSection('${chapterCode}', '${section.code}')">
                <i class="fas fa-layer-group" style="margin-right: 8px; font-size: 12px;"></i>
                ${section.name}
            </a>
        `).join('');

        container.innerHTML = html;
    }

    selectSection(chapterCode, sectionCode) {
        // 更新导航状态
        document.querySelectorAll('.nav-tree-child').forEach(item => {
            item.classList.remove('active');
        });

        const selectedItem = document.querySelector(`[data-chapter="${chapterCode}"][data-section="${sectionCode}"]`);
        if (selectedItem) {
            selectedItem.classList.add('active');
        }

        // 保存当前选择的章节和小节
        this.currentChapter = chapterCode;
        this.currentSection = sectionCode;

        // 直接加载该小节的指标
        this.loadSectionIndicators(chapterCode, sectionCode);

        // 更新筛选器（但不触发change事件，避免重复加载）
        const chapterFilter = document.getElementById('chapterFilter');
        const sectionFilter = document.getElementById('sectionFilter');

        if (chapterFilter) {
            chapterFilter.value = chapterCode;
        }

        if (sectionFilter) {
            sectionFilter.value = sectionCode;
        }
    }

    // 加载特定小节的指标
    async loadSectionIndicators(chapterCode, sectionCode) {
        this.showLoading();

        try {
            const response = await fetch(`/api/indicators?chapter=${chapterCode}&section=${sectionCode}`);
            const data = await response.json();

            if (data.success) {
                this.allIndicators = data.data;
                this.filteredIndicators = data.data;
                this.renderIndicators();

                // 更新页面标题显示当前选择的小节
                this.updatePageTitle(chapterCode, sectionCode);
            } else {
                this.showNotification('加载指标失败', 'error');
            }
        } catch (error) {
            console.error('加载小节指标失败:', error);
            this.showNotification('加载指标失败', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // 更新页面标题
    updatePageTitle(chapterCode, sectionCode) {
        const titleElement = document.querySelector('.page-title');
        const subtitleElement = document.querySelector('.page-subtitle');

        if (titleElement && subtitleElement) {
            // 获取章节和小节名称
            const chapterName = this.getChapterName(chapterCode);
            const sectionName = this.getSectionName(chapterCode, sectionCode);

            if (titleElement) titleElement.textContent = `第${chapterCode}章 - ${sectionName}`;
            if (subtitleElement) subtitleElement.textContent = `${chapterName} > ${sectionName}`;
        } else {
            console.warn('Page title elements not found');
        }
    }

    // 获取章节名称
    getChapterName(chapterCode) {
        const chapterNames = {
            '1': '医院功能与任务',
            '2': '医院服务',
            '3': '患者安全',
            '4': '医疗质量安全管理与持续改进',
            '5': '护理管理与质量持续改进',
            '6': '医院管理',
            '7': '信息管理'
        };
        return chapterNames[chapterCode] || `第${chapterCode}章`;
    }

    // 获取小节名称
    getSectionName(chapterCode, sectionCode) {
        // 这里可以根据实际数据结构来获取小节名称
        // 暂时返回一个通用格式
        return `第${sectionCode}节`;
    }

    // 渲染指标（主要方法）
    renderIndicators() {
        if (!this.filteredIndicators) {
            console.log('没有指标数据需要渲染');
            return;
        }

        console.log('开始渲染指标，数量:', this.filteredIndicators.length);

        // 渲染卡片视图
        this.renderIndicatorCards();

        // 渲染列表视图
        this.renderIndicatorList();
    }

    // 渲染指标卡片
    renderIndicatorCards() {
        const container = document.getElementById('indicatorCards');
        if (!container) {
            console.log('未找到指标卡片容器');
            return;
        }

        console.log('渲染指标卡片，容器已找到');
        container.innerHTML = '';

        if (!this.filteredIndicators || this.filteredIndicators.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无指标数据</div>';
            return;
        }

        // 构建层级结构
        const hierarchicalIndicators = this.buildIndicatorHierarchy(this.filteredIndicators);
        console.log('层级结构构建完成，根指标数量:', hierarchicalIndicators.length);

        // 渲染层级结构
        this.renderHierarchicalCards(container, hierarchicalIndicators, 0);
    }

    // 构建指标层级结构
    buildIndicatorHierarchy(indicators) {
        const indicatorMap = new Map();
        const rootIndicators = [];

        // 创建指标映射
        indicators.forEach(indicator => {
            indicatorMap.set(indicator.id, { ...indicator, children: [] });
        });

        // 构建层级关系
        indicators.forEach(indicator => {
            const indicatorNode = indicatorMap.get(indicator.id);
            if (indicator.parent_id && indicatorMap.has(indicator.parent_id)) {
                indicatorMap.get(indicator.parent_id).children.push(indicatorNode);
            } else {
                rootIndicators.push(indicatorNode);
            }
        });

        return rootIndicators;
    }

    // 渲染层级卡片（优化的卡片样式）
    renderHierarchicalCards(container, indicators, level) {
        indicators.forEach(indicator => {
            const card = document.createElement('div');
            card.className = `indicator-card level-${level}`;

            // 检查是否有子指标（使用children_count或children数组）
            const hasChildren = (indicator.children && indicator.children.length > 0) ||
                               (indicator.children_count && indicator.children_count > 0);

            // 计算缩进
            const indentLevel = level * 20;

            // 为有子指标的父指标添加连接线样式类
            const parentConnectorClass = hasChildren ? 'parent-connector' : '';

            // 生成指标类型徽章
            const indicatorType = indicator.indicator_type || 'composite';
            const typeText = indicatorType === 'simple' ? '简单指标' : '复合指标';
            const typeClass = indicatorType === 'simple' ? 'simple-indicator' : 'composite-indicator';

            card.innerHTML = `
                <div class="card-container ${parentConnectorClass}" style="margin-left: ${indentLevel}px;">
                    <span class="indicator-type-badge ${typeClass}">${typeText}</span>
                    <div class="card-header">
                        <div class="card-id-section">
                            ${hasChildren ? `
                            <button class="expand-toggle" onclick="app.toggleIndicatorChildren('${indicator.id}')" title="展开/折叠子指标">
                                <i class="fas fa-chevron-down" id="toggle-${indicator.id}"></i>
                            </button>
                            ` : '<span class="expand-spacer"></span>'}
                            <span class="indicator-id-badge">${indicator.id}</span>
                        </div>
                        <div class="card-actions">
                            <button class="action-icon" onclick="app.showIndicatorDetail('${indicator.id}')" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-icon" onclick="app.editIndicator('${indicator.id}')" title="编辑指标">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <h3 class="card-title">${indicator.name}</h3>
                        <div class="card-description">${indicator.description || '该指标暂无详细说明'}</div>
                        <div class="card-meta">
                            <div class="meta-item">
                                <i class="fas fa-folder"></i>
                                <span>第${indicator.chapter_code || '?'}章</span>
                            </div>
                            ${indicator.section_code ? `
                            <div class="meta-item">
                                <i class="fas fa-layer-group"></i>
                                <span>第${indicator.section_code}节</span>
                            </div>
                            ` : ''}
                            ${hasChildren ? `
                            <div class="meta-item">
                                <i class="fas fa-sitemap"></i>
                                <span>${indicator.children_count || indicator.children.length}个子指标</span>
                            </div>
                            ` : ''}
                            ${indicator.component_count > 0 ? `
                            <div class="meta-item">
                                <i class="fas fa-puzzle-piece"></i>
                                <span>${indicator.component_count}个组件</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;

            container.appendChild(card);

            // 添加子指标容器（如果有子指标）
            if (hasChildren) {
                const childrenContainer = document.createElement('div');
                childrenContainer.className = 'children-container';
                childrenContainer.id = `children-${indicator.id}`;
                childrenContainer.style.display = 'none';

                // 如果已经有children数据，直接渲染；否则等待动态加载
                if (indicator.children && indicator.children.length > 0) {
                    this.renderHierarchicalCards(childrenContainer, indicator.children, level + 1);
                }

                container.appendChild(childrenContainer);
            }
        });
    }

    // 渲染指标列表（支持层级结构和树形连接线）
    renderIndicatorList() {
        const container = document.getElementById('indicatorList');
        if (!container) {
            console.log('未找到指标列表容器');
            return;
        }

        console.log('渲染指标列表，容器已找到');
        container.innerHTML = '';

        if (!this.filteredIndicators || this.filteredIndicators.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无指标数据</div>';
            return;
        }

        // 构建层级结构
        const hierarchicalData = this.buildIndicatorHierarchy(this.filteredIndicators);

        // 渲染层级列表
        this.renderHierarchicalList(container, hierarchicalData, 0);
    }

    // 渲染层级列表（列表视图版本）
    renderHierarchicalList(container, indicators, level) {
        indicators.forEach(indicator => {
            const row = document.createElement('div');
            row.className = `list-item level-${level}`;

            // 检查是否有子指标
            const hasChildren = (indicator.children && indicator.children.length > 0) ||
                               (indicator.children_count && indicator.children_count > 0);

            // 计算缩进
            const indentLevel = level * 40;

            // 生成指标类型徽章
            const indicatorType = indicator.indicator_type || 'composite';
            const typeText = indicatorType === 'simple' ? '简单指标' : '复合指标';
            const typeClass = indicatorType === 'simple' ? 'simple-indicator' : 'composite-indicator';

            row.innerHTML = `
                <div class="list-item-container" style="padding-left: ${indentLevel}px;">
                    <div class="list-col-id">
                        <div class="list-id-section">
                            ${hasChildren ? `
                            <button class="expand-toggle-list" onclick="app.toggleIndicatorChildrenList('${indicator.id}')" title="展开/折叠子指标">
                                <i class="fas fa-chevron-down" id="toggle-list-${indicator.id}"></i>
                            </button>
                            ` : '<span class="expand-spacer-list"></span>'}
                            <span class="list-item-badge">${indicator.id}</span>
                        </div>
                    </div>
                    <div class="list-col-title">
                        <span class="list-item-title">${indicator.name}</span>
                    </div>
                    <div class="list-col-type">
                        <span class="indicator-type-badge ${typeClass}">${typeText}</span>
                    </div>
                    <div class="list-col-desc">
                        <span class="list-item-desc">${indicator.description || '暂无描述'}</span>
                    </div>
                    <div class="list-col-tags">
                        <span class="tag">第${indicator.chapter_code || '?'}章</span>
                        ${indicator.section_code ? `<span class="tag">第${indicator.section_code}节</span>` : ''}
                        ${hasChildren ? `<span class="tag children-tag">${indicator.children_count || indicator.children.length}个子指标</span>` : ''}
                    </div>
                    <div class="list-col-actions">
                        <button class="action-icon" onclick="app.showIndicatorDetail('${indicator.id}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
            `;

            container.appendChild(row);

            // 添加子指标容器（如果有子指标）
            if (hasChildren) {
                const childrenContainer = document.createElement('div');
                childrenContainer.className = 'children-container-list';
                childrenContainer.id = `children-list-${indicator.id}`;
                childrenContainer.style.display = 'none';

                // 如果已经有children数据，直接渲染；否则等待动态加载
                if (indicator.children && indicator.children.length > 0) {
                    this.renderHierarchicalList(childrenContainer, indicator.children, level + 1);
                }

                container.appendChild(childrenContainer);
            }
        });
    }

    // 加载指标数据
    async loadIndicators(filters = {}) {
        this.showLoading();

        try {
            const params = new URLSearchParams(filters);
            const response = await fetch(`/api/indicators?${params}`);
            const data = await response.json();

            if (data.success) {
                this.allIndicators = data.data;
                this.filteredIndicators = data.data;
                this.renderIndicators();

                // 如果有分页信息，渲染分页
                if (data.pagination) {
                    this.renderPagination(data.pagination);
                }
            } else {
                this.showNotification('加载指标失败', 'error');
            }
        } catch (error) {
            console.error('加载指标失败:', error);
            this.showNotification('加载指标失败', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // 渲染分页
    renderPagination(pagination) {
        const container = document.getElementById('pagination');
        if (!container || !pagination || pagination.pages <= 1) {
            if (container) container.innerHTML = '';
            return;
        }

        let html = '<div class="pagination-controls">';

        // 上一页
        if (pagination.page > 1) {
            html += `<button class="pagination-btn" onclick="app.loadPage(${pagination.page - 1})">
                <i class="fas fa-chevron-left"></i>
            </button>`;
        }

        // 页码
        for (let i = 1; i <= pagination.pages; i++) {
            if (i === pagination.page) {
                html += `<button class="pagination-btn active">${i}</button>`;
            } else {
                html += `<button class="pagination-btn" onclick="app.loadPage(${i})">${i}</button>`;
            }
        }

        // 下一页
        if (pagination.page < pagination.pages) {
            html += `<button class="pagination-btn" onclick="app.loadPage(${pagination.page + 1})">
                <i class="fas fa-chevron-right"></i>
            </button>`;
        }

        html += '</div>';
        container.innerHTML = html;
    }

    // 加载指定页面
    loadPage(page) {
        const chapterCode = document.getElementById('chapterFilter')?.value || '';
        const sectionCode = document.getElementById('sectionFilter')?.value || '';

        this.loadIndicators({
            chapter: chapterCode,
            section: sectionCode,
            page: page
        });
    }

    selectChapter(chapterCode) {
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        const selectedItem = document.querySelector(`[data-chapter="${chapterCode}"]`);
        if (selectedItem) {
            selectedItem.classList.add('active');
        }

        this.currentChapter = chapterCode;

        // 如果在首页，更新筛选器
        const chapterFilter = document.getElementById('chapterFilter');
        if (chapterFilter) {
            chapterFilter.value = chapterCode;
            chapterFilter.dispatchEvent(new Event('change'));
        }
    }

    async showIndicatorDetail(indicatorId) {
        // 使用模态框显示指标详情
        this.showIndicatorDetailModal(indicatorId);
    }

    renderIndicatorDetail(data) {
        const { indicator, components, children, parent } = data;
        const modalBody = document.getElementById('modalBody');
        const modalTitle = document.querySelector('.modal-title');

        if (!modalBody || !modalTitle) return;

        if (modalTitle) modalTitle.textContent = `${indicator.id} - ${indicator.name}`;

        // 判断是否为底层指标（没有子指标的指标）
        const isBottomLevel = !children || children.length === 0;

        let html = `
            <div class="indicator-detail">
                <!-- 固定的指标头部 -->
                <div class="indicator-header-sticky">
                    <div class="indicator-title">
                        <div class="indicator-icon" style="background-color: ${indicator.color}20; color: ${indicator.color}">
                            <i class="${indicator.icon}"></i>
                        </div>
                        <div class="indicator-info">
                            <h2>${indicator.name}</h2>
                            <div class="indicator-meta">
                                <span class="meta-badge">${indicator.id}</span>
                                <span class="meta-text">${indicator.chapter_name}</span>
                                ${indicator.section_name ? `<span class="meta-text">${indicator.section_name}</span>` : ''}
                                ${isBottomLevel ? '<span class="meta-badge bottom-level">底层指标</span>' : ''}
                            </div>
                        </div>
                    </div>
                    <div class="indicator-actions">
                        <!-- 收藏按钮已移除 -->
                    </div>
                </div>

                <!-- 可滚动的内容区域 -->
                <div class="indicator-content-scrollable">
        `;

        if (indicator.description) {
            html += `
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="fas fa-info-circle section-title-icon"></i>
                        指标说明
                    </h3>
                    <div class="section-content">
                        <p>${indicator.description}</p>
                    </div>
                </div>
            `;
        }

        // 底层指标显示参考范围和当前值
        if (isBottomLevel) {
            html += `
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="fas fa-target section-title-icon"></i>
                        指标数值
                        <button class="btn btn-outline btn-sm" onclick="app.editIndicatorValues('${indicator.id}')" style="margin-left: 12px;">
                            <i class="fas fa-edit btn-icon"></i>
                            编辑数值
                        </button>
                    </h3>
                    <div class="section-content">
                        <div class="indicator-values" id="values-${indicator.id}">
                            <div class="value-grid">
                                <div class="value-item">
                                    <div class="value-label">目标值/参考范围</div>
                                    <div class="value-content ${!indicator.target_value ? 'empty-value' : ''}">
                                        ${indicator.target_value || '未设置'}
                                    </div>
                                </div>
                                <div class="value-item">
                                    <div class="value-label">当前值</div>
                                    <div class="value-content ${!indicator.current_value ? 'empty-value' : ''}">
                                        ${indicator.current_value || '未录入'}
                                    </div>
                                </div>
                                <div class="value-item">
                                    <div class="value-label">完成率</div>
                                    <div class="value-content ${!indicator.completion_rate ? 'empty-value' : ''}">
                                        ${indicator.completion_rate ? indicator.completion_rate + '%' : '未计算'}
                                    </div>
                                </div>
                                <div class="value-item">
                                    <div class="value-label">数据来源</div>
                                    <div class="value-content ${!indicator.data_source ? 'empty-value' : ''}">
                                        ${indicator.data_source || '未指定'}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="indicator-values-edit" id="values-edit-${indicator.id}" style="display: none;">
                            ${this.renderIndicatorValuesEditForm(indicator)}
                        </div>
                    </div>
                </div>
            `;
        }

        if (parent) {
            html += `
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="fas fa-level-up-alt section-title-icon"></i>
                        父指标
                    </h3>
                    <div class="section-content">
                        <div class="parent-indicator" onclick="app.showIndicatorDetail('${parent.id}')">
                            <span class="parent-id">${parent.id}</span>
                            <span class="parent-name">${parent.name}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        if (components && components.length > 0) {
            html += `
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="fas fa-calculator section-title-icon"></i>
                        计算组成部分
                    </h3>
                    <div class="section-content">
                        <div class="components-grid">
            `;

            const numerators = components.filter(c => c.component_type === 'numerator');
            const denominators = components.filter(c => c.component_type === 'denominator');

            if (numerators.length > 0) {
                html += `
                    <div class="component-group">
                        <h4 class="component-title">分子</h4>
                        ${numerators.map(comp => this.renderComponent(comp)).join('')}
                    </div>
                `;
            }

            if (denominators.length > 0) {
                html += `
                    <div class="component-group">
                        <h4 class="component-title">分母</h4>
                        ${denominators.map(comp => this.renderComponent(comp)).join('')}
                    </div>
                `;
            }

            html += `
                        </div>
                    </div>
                </div>
            `;
        }

        if (children && children.length > 0) {
            html += `
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="fas fa-sitemap section-title-icon"></i>
                        子指标 (${children.length}个)
                    </h3>
                    <div class="section-content">
                        <div class="children-grid">
                            ${children.map(child => `
                                <div class="child-indicator-card" onclick="app.showIndicatorDetail('${child.id}')">
                                    <div class="child-indicator-info">
                                        <div class="child-indicator-id">${child.id}</div>
                                        <div class="child-indicator-name">${child.name}</div>
                                        <div class="child-indicator-desc">${child.description || '暂无描述'}</div>
                                    </div>
                                    <div class="child-indicator-stats">
                                        <div class="child-stat-badge">
                                            <i class="fas fa-eye"></i>
                                            查看详情
                                        </div>
                                        ${child.component_count > 0 ? `
                                        <div class="child-stat-badge">
                                            <i class="fas fa-calculator"></i>
                                            ${child.component_count} 个组件
                                        </div>
                                        ` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
        }

        html += `
                </div> <!-- 结束 indicator-content-scrollable -->
            </div> <!-- 结束 indicator-detail -->
        `;
        modalBody.innerHTML = html;
    }

    renderComponent(component) {
        return `
            <div class="component-item" data-component-id="${component.id}">
                <div class="component-header">
                    <div class="component-name">${component.name || '未命名组件'}</div>
                    <div class="component-actions">
                        <button class="edit-btn" onclick="app.editComponent('${component.indicator_id}', '${component.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="delete-btn" onclick="app.deleteComponent('${component.indicator_id}', '${component.id}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="component-content" id="content-${component.id}">
                    <div class="component-details">
                        <div class="component-detail">
                            <strong>单位:</strong>
                            <span class="${!component.unit ? 'empty-value' : ''}">${component.unit || '未填写'}</span>
                        </div>
                        <div class="component-detail">
                            <strong>牵头科室:</strong>
                            <span class="${!component.lead_department ? 'empty-value' : ''}">${component.lead_department || '未填写'}</span>
                        </div>
                        <div class="component-detail">
                            <strong>数据来源:</strong>
                            <span class="${!component.data_source ? 'empty-value' : ''}">${component.data_source || '未填写'}</span>
                        </div>
                        <div class="component-detail">
                            <strong>逻辑定义:</strong>
                            <span class="${!component.logic_definition ? 'empty-value' : ''}">${component.logic_definition || '未填写'}</span>
                        </div>
                    </div>
                </div>
                <div class="component-edit-form" id="form-${component.id}" style="display: none;">
                    ${this.renderComponentEditForm(component)}
                </div>
            </div>
        `;
    }

    // 渲染组件编辑表单
    renderComponentEditForm(component) {
        return `
            <div class="edit-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>组件名称</label>
                        <input type="text" name="name" value="${component.name || ''}" placeholder="请输入组件名称">
                    </div>
                    <div class="form-group">
                        <label>单位</label>
                        <input type="text" name="unit" value="${component.unit || ''}" placeholder="请输入单位">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>牵头科室</label>
                        <input type="text" name="lead_department" value="${component.lead_department || ''}" placeholder="请输入牵头科室">
                    </div>
                    <div class="form-group">
                        <label>数据来源</label>
                        <input type="text" name="data_source" value="${component.data_source || ''}" placeholder="请输入数据来源">
                    </div>
                </div>
                <div class="form-group">
                    <label>逻辑定义</label>
                    <textarea name="logic_definition" placeholder="请输入逻辑定义">${component.logic_definition || ''}</textarea>
                </div>
                <div class="form-actions">
                    <button class="save-btn" onclick="app.saveComponent('${component.indicator_id}', '${component.id}')">保存</button>
                    <button class="cancel-btn" onclick="app.cancelEdit('${component.id}')">取消</button>
                </div>
            </div>
        `;
    }

    // 编辑组件
    editComponent(indicatorId, componentId) {
        const contentDiv = document.getElementById(`content-${componentId}`);
        const formDiv = document.getElementById(`form-${componentId}`);

        if (contentDiv && formDiv) {
            contentDiv.style.display = 'none';
            formDiv.style.display = 'block';
        }
    }

    // 取消编辑
    cancelEdit(componentId) {
        const contentDiv = document.getElementById(`content-${componentId}`);
        const formDiv = document.getElementById(`form-${componentId}`);

        if (contentDiv && formDiv) {
            contentDiv.style.display = 'block';
            formDiv.style.display = 'none';
        }
    }

    // 保存组件
    async saveComponent(indicatorId, componentId) {
        const formDiv = document.getElementById(`form-${componentId}`);
        if (!formDiv) return;

        // 收集表单数据
        const inputs = formDiv.querySelectorAll('input, textarea');
        const data = {};
        inputs.forEach(input => {
            data[input.name] = input.value;
        });

        try {
            formDiv.classList.add('loading');

            const response = await fetch(`/api/indicators/${indicatorId}/components/${componentId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('组件信息更新成功', 'success');
                // 重新加载指标详情
                this.showIndicatorDetail(indicatorId);
            } else {
                this.showNotification('更新失败: ' + result.error, 'error');
            }
        } catch (error) {
            this.showNotification('网络错误: ' + error.message, 'error');
        } finally {
            formDiv.classList.remove('loading');
        }
    }

    // 删除组件
    async deleteComponent(indicatorId, componentId) {
        if (!confirm('确定要删除这个组件吗？此操作不可撤销。')) {
            return;
        }

        try {
            const response = await fetch(`/api/indicators/${indicatorId}/components/${componentId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('组件删除成功', 'success');
                // 重新加载指标详情
                this.showIndicatorDetail(indicatorId);
            } else {
                this.showNotification('删除失败: ' + result.error, 'error');
            }
        } catch (error) {
            this.showNotification('网络错误: ' + error.message, 'error');
        }
    }

    // 渲染指标数值编辑表单
    renderIndicatorValuesEditForm(indicator) {
        return `
            <div class="edit-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>目标值/参考范围</label>
                        <input type="text" name="target_value" value="${indicator.target_value || ''}" placeholder="例如：≥90%、80-95%">
                    </div>
                    <div class="form-group">
                        <label>当前值</label>
                        <input type="text" name="current_value" value="${indicator.current_value || ''}" placeholder="请输入当前值">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>完成率(%)</label>
                        <input type="number" name="completion_rate" value="${indicator.completion_rate || ''}" placeholder="自动计算或手动输入" step="0.01" min="0" max="200">
                    </div>
                    <div class="form-group">
                        <label>数据来源</label>
                        <input type="text" name="data_source" value="${indicator.data_source || ''}" placeholder="请输入数据来源">
                    </div>
                </div>
                <div class="form-group">
                    <label>计算方法</label>
                    <textarea name="calculation_method" placeholder="请输入计算方法">${indicator.calculation_method || ''}</textarea>
                </div>
                <div class="form-actions">
                    <button class="save-btn" onclick="app.saveIndicatorValues('${indicator.id}')">保存</button>
                    <button class="cancel-btn" onclick="app.cancelEditValues('${indicator.id}')">取消</button>
                </div>
            </div>
        `;
    }

    // 编辑指标数值
    editIndicatorValues(indicatorId) {
        const valuesDiv = document.getElementById(`values-${indicatorId}`);
        const editDiv = document.getElementById(`values-edit-${indicatorId}`);

        if (valuesDiv && editDiv) {
            valuesDiv.style.display = 'none';
            editDiv.style.display = 'block';
        }
    }

    // 取消编辑指标数值
    cancelEditValues(indicatorId) {
        const valuesDiv = document.getElementById(`values-${indicatorId}`);
        const editDiv = document.getElementById(`values-edit-${indicatorId}`);

        if (valuesDiv && editDiv) {
            valuesDiv.style.display = 'block';
            editDiv.style.display = 'none';
        }
    }

    // 保存指标数值
    async saveIndicatorValues(indicatorId) {
        const editDiv = document.getElementById(`values-edit-${indicatorId}`);
        if (!editDiv) return;

        // 收集表单数据
        const inputs = editDiv.querySelectorAll('input, textarea');
        const data = {};
        inputs.forEach(input => {
            data[input.name] = input.value;
        });

        try {
            editDiv.classList.add('loading');

            const response = await fetch(`/api/indicators/${indicatorId}/values`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('指标数值更新成功', 'success');
                // 重新加载指标详情
                this.showIndicatorDetail(indicatorId);
            } else {
                this.showNotification('更新失败: ' + result.error, 'error');
            }
        } catch (error) {
            this.showNotification('网络错误: ' + error.message, 'error');
        } finally {
            editDiv.classList.remove('loading');
        }
    }

    showModal() {
        const modal = document.getElementById('indicatorModal');
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    hideModal() {
        const modal = document.getElementById('indicatorModal');
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    showLoading() {
        const loading = document.getElementById('loadingOverlay');
        if (loading) {
            loading.classList.add('active');
        }
    }

    hideLoading() {
        const loading = document.getElementById('loadingOverlay');
        if (loading) {
            loading.classList.remove('active');
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.getElementById('notification');
        if (!notification) {
            console.warn('Notification element not found');
            return;
        }

        const icon = notification.querySelector('.notification-icon');
        const messageEl = notification.querySelector('.notification-message');

        if (!icon || !messageEl) {
            console.warn('Notification icon or message element not found');
            return;
        }

        // 设置图标
        let iconClass = 'fas fa-info-circle';
        if (type === 'success') iconClass = 'fas fa-check-circle';
        else if (type === 'error') iconClass = 'fas fa-exclamation-circle';
        else if (type === 'warning') iconClass = 'fas fa-exclamation-triangle';

        icon.className = `notification-icon ${iconClass}`;
        messageEl.textContent = message;

        notification.className = `notification ${type} active`;

        setTimeout(() => {
            notification.classList.remove('active');
        }, 3000);
    }

    toggleFavorite(indicatorId) {
        if (this.isFavorite(indicatorId)) {
            this.removeFavorite(indicatorId);
            this.showNotification('已取消收藏', 'info');
        } else {
            this.addFavorite(indicatorId);
            this.showNotification('已添加到收藏', 'success');
        }

        // 更新按钮文本
        const btn = document.querySelector(`button[onclick="app.toggleFavorite('${indicatorId}')"]`);
        if (btn) {
            const icon = btn.querySelector('i');
            const text = btn.childNodes[btn.childNodes.length - 1];
            if (this.isFavorite(indicatorId)) {
                text.textContent = ' 取消收藏';
                icon.className = 'fas fa-star';
            } else {
                text.textContent = ' 收藏';
                icon.className = 'far fa-star';
            }
        }
    }

    isFavorite(indicatorId) {
        return this.favorites.includes(indicatorId);
    }

    addFavorite(indicatorId) {
        if (!this.isFavorite(indicatorId)) {
            this.favorites.push(indicatorId);
            this.saveFavorites();
        }
    }

    removeFavorite(indicatorId) {
        const index = this.favorites.indexOf(indicatorId);
        if (index > -1) {
            this.favorites.splice(index, 1);
            this.saveFavorites();
        }
    }

    loadFavorites() {
        try {
            const saved = localStorage.getItem('hospital_indicator_favorites');
            return saved ? JSON.parse(saved) : [];
        } catch {
            return [];
        }
    }

    saveFavorites() {
        try {
            localStorage.setItem('hospital_indicator_favorites', JSON.stringify(this.favorites));
        } catch (error) {
            console.error('保存收藏失败:', error);
        }
    }

    async showRandomIndicator() {
        // 这里可以实现随机显示指标的功能
        this.showNotification('随机指标功能开发中...', 'info');
    }

    showFavorites() {
        if (this.favorites.length === 0) {
            this.showNotification('暂无收藏的指标', 'info');
            return;
        }

        // 这里可以实现显示收藏列表的功能
        this.showNotification(`您收藏了 ${this.favorites.length} 个指标`, 'info');
    }

    // 展开全部指标
    expandAllIndicators() {
        console.log('展开全部指标');

        // 处理卡片视图
        const cardChildrenContainers = document.querySelectorAll('.children-container');
        const cardToggleButtons = document.querySelectorAll('.expand-toggle');
        const cardToggleIcons = document.querySelectorAll('.expand-toggle i');
        const cardParentContainers = document.querySelectorAll('.parent-connector');

        cardChildrenContainers.forEach(container => {
            container.style.display = 'block';
        });

        cardToggleButtons.forEach(button => {
            button.classList.add('expanded');
        });

        cardToggleIcons.forEach(icon => {
            icon.className = 'fas fa-chevron-down';
            icon.style.transform = 'rotate(180deg)';
        });

        cardParentContainers.forEach(container => {
            container.classList.add('expanded');
        });

        // 处理列表视图
        const listChildrenContainers = document.querySelectorAll('.children-container-list');
        const listToggleButtons = document.querySelectorAll('.expand-toggle-list');
        const listToggleIcons = document.querySelectorAll('.expand-toggle-list i');

        listChildrenContainers.forEach(container => {
            container.style.display = 'block';
        });

        listToggleButtons.forEach(button => {
            button.classList.add('expanded');
        });

        listToggleIcons.forEach(icon => {
            icon.className = 'fas fa-chevron-down';
            icon.style.transform = 'rotate(180deg)';
        });

        this.showNotification('已展开所有指标', 'success');
    }

    // 收起全部指标
    collapseAllIndicators() {
        console.log('收起全部指标');

        // 处理卡片视图
        const cardChildrenContainers = document.querySelectorAll('.children-container');
        const cardToggleButtons = document.querySelectorAll('.expand-toggle');
        const cardToggleIcons = document.querySelectorAll('.expand-toggle i');
        const cardParentContainers = document.querySelectorAll('.parent-connector');

        cardChildrenContainers.forEach(container => {
            container.style.display = 'none';
        });

        cardToggleButtons.forEach(button => {
            button.classList.remove('expanded');
        });

        cardToggleIcons.forEach(icon => {
            icon.className = 'fas fa-chevron-down';
            icon.style.transform = 'rotate(0deg)';
        });

        cardParentContainers.forEach(container => {
            container.classList.remove('expanded');
        });

        // 处理列表视图
        const listChildrenContainers = document.querySelectorAll('.children-container-list');
        const listToggleButtons = document.querySelectorAll('.expand-toggle-list');
        const listToggleIcons = document.querySelectorAll('.expand-toggle-list i');

        listChildrenContainers.forEach(container => {
            container.style.display = 'none';
        });

        listToggleButtons.forEach(button => {
            button.classList.remove('expanded');
        });

        listToggleIcons.forEach(icon => {
            icon.className = 'fas fa-chevron-down';
            icon.style.transform = 'rotate(0deg)';
        });

        this.showNotification('已收起所有指标', 'success');
    }

    // 切换子指标显示/隐藏（优化的卡片样式）
    async toggleIndicatorChildren(indicatorId) {
        const childrenContainer = document.getElementById(`children-${indicatorId}`);
        const toggleIcon = document.getElementById(`toggle-${indicatorId}`);
        const toggleButton = toggleIcon?.parentElement;
        const parentCard = toggleIcon?.closest('.indicator-card');
        const parentContainer = parentCard?.querySelector('.card-container');

        if (!childrenContainer) {
            console.error('Children container not found for indicator:', indicatorId);
            return;
        }

        const isExpanded = childrenContainer.style.display !== 'none';

        if (isExpanded) {
            // 折叠子指标
            childrenContainer.style.display = 'none';
            if (toggleIcon) {
                toggleIcon.className = 'fas fa-chevron-down';
            }
            if (toggleButton) {
                toggleButton.classList.remove('expanded');
            }
            if (parentContainer) {
                parentContainer.classList.remove('expanded');
            }
        } else {
            // 展开子指标
            if (childrenContainer.children.length === 0) {
                // 如果还没有加载子指标，动态加载
                await this.loadChildrenIndicators(indicatorId, childrenContainer);
            }
            childrenContainer.style.display = 'block';
            if (toggleIcon) {
                toggleIcon.className = 'fas fa-chevron-down';
            }
            if (toggleButton) {
                toggleButton.classList.add('expanded');
            }
            if (parentContainer) {
                parentContainer.classList.add('expanded');
            }
        }
    }

    // 切换子指标显示/隐藏（列表视图版本）
    async toggleIndicatorChildrenList(indicatorId) {
        const childrenContainer = document.getElementById(`children-list-${indicatorId}`);
        const toggleIcon = document.getElementById(`toggle-list-${indicatorId}`);
        const toggleButton = toggleIcon?.parentElement;

        if (!childrenContainer) {
            console.error('Children container not found for indicator:', indicatorId);
            return;
        }

        const isExpanded = childrenContainer.style.display !== 'none';

        if (isExpanded) {
            // 折叠子指标
            childrenContainer.style.display = 'none';
            if (toggleIcon) {
                toggleIcon.className = 'fas fa-chevron-down';
            }
            if (toggleButton) {
                toggleButton.classList.remove('expanded');
            }
        } else {
            // 展开子指标
            if (childrenContainer.children.length === 0) {
                // 如果还没有加载子指标，动态加载
                await this.loadChildrenIndicatorsList(indicatorId, childrenContainer);
            }
            childrenContainer.style.display = 'block';
            if (toggleIcon) {
                toggleIcon.className = 'fas fa-chevron-down';
            }
            if (toggleButton) {
                toggleButton.classList.add('expanded');
            }
        }
    }

    // 动态加载子指标
    async loadChildrenIndicators(parentId, container) {
        try {
            // 显示加载状态
            container.innerHTML = '<div class="loading-indicator">加载中...</div>';

            // 获取当前筛选条件
            const currentChapter = document.getElementById('chapterFilter')?.value || '';
            const currentSection = document.getElementById('sectionFilter')?.value || '';

            // 构建API URL
            let apiUrl = '/api/indicators?';
            const params = new URLSearchParams();

            if (currentChapter) {
                params.append('chapter', currentChapter);
            }
            if (currentSection) {
                params.append('section', currentSection);
            }
            params.append('per_page', '1000'); // 获取所有指标

            apiUrl += params.toString();

            const response = await fetch(apiUrl);
            const result = await response.json();

            if (result.success) {
                // 筛选出当前父指标的子指标
                const childrenIndicators = result.data.filter(indicator =>
                    indicator.parent_id === parentId
                );

                // 清空加载状态
                container.innerHTML = '';

                if (childrenIndicators.length > 0) {
                    // 渲染子指标
                    this.renderHierarchicalCards(container, childrenIndicators, 1);
                } else {
                    container.innerHTML = '<div class="no-children">暂无子指标</div>';
                }
            } else {
                container.innerHTML = '<div class="error-message">加载失败</div>';
            }
        } catch (error) {
            console.error('Error loading children indicators:', error);
            container.innerHTML = '<div class="error-message">加载失败</div>';
        }
    }

    // 动态加载子指标（列表视图版本）
    async loadChildrenIndicatorsList(parentId, container) {
        try {
            // 显示加载状态
            container.innerHTML = '<div class="loading-indicator">加载中...</div>';

            // 获取当前筛选条件
            const currentChapter = document.getElementById('chapterFilter')?.value || '';
            const currentSection = document.getElementById('sectionFilter')?.value || '';

            // 构建API URL
            let apiUrl = '/api/indicators?';
            const params = new URLSearchParams();

            if (currentChapter) {
                params.append('chapter', currentChapter);
            }
            if (currentSection) {
                params.append('section', currentSection);
            }
            params.append('per_page', '1000'); // 获取所有指标

            apiUrl += params.toString();

            const response = await fetch(apiUrl);
            const result = await response.json();

            if (result.success) {
                // 筛选出当前父指标的子指标
                const childrenIndicators = result.data.filter(indicator =>
                    indicator.parent_id === parentId
                );

                // 清空加载状态
                container.innerHTML = '';

                if (childrenIndicators.length > 0) {
                    // 获取当前层级
                    const parentRow = container.previousElementSibling;
                    const parentLevel = parseInt(parentRow.className.match(/level-(\d+)/)?.[1] || '0');
                    const childLevel = parentLevel + 1;

                    // 渲染子指标
                    this.renderHierarchicalList(container, childrenIndicators, childLevel);
                } else {
                    container.innerHTML = '<div class="no-children">暂无子指标</div>';
                }
            } else {
                container.innerHTML = '<div class="error-message">加载失败</div>';
            }
        } catch (error) {
            console.error('Error loading children indicators for list:', error);
            container.innerHTML = '<div class="error-message">加载失败</div>';
        }
    }
}

// 全局应用实例
let app;

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', function() {
    app = new HospitalIndicatorApp();

    // 将一些函数暴露到全局作用域，供HTML中的onclick使用
    window.showIndicatorDetail = (id) => app.showIndicatorDetail(id);
    window.toggleFavorite = (id) => app.toggleFavorite(id);
    window.app = app;
});

// 导出应用类（如果需要模块化）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HospitalIndicatorApp;
}


// 暂时隐藏参考范围相关功能
function hideReferenceRangeFeatures() {
    // 隐藏参考范围卡片
    const referenceRangeCards = document.querySelectorAll('[id*="ReferenceRange"], [class*="reference-range"]');
    referenceRangeCards.forEach(card => {
        if (card) card.style.display = 'none';
    });
    
    // 隐藏参考范围按钮
    const referenceRangeButtons = document.querySelectorAll('[onclick*="reference"], [href*="reference-range"]');
    referenceRangeButtons.forEach(btn => {
        if (btn) btn.style.display = 'none';
    });
}

// 页面加载完成后执行隐藏
document.addEventListener('DOMContentLoaded', hideReferenceRangeFeatures);
