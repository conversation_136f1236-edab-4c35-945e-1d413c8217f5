#!/usr/bin/env python3
"""
底层指标"指标参考"数据完整解决方案
统一使用indicators表存储指标参考数据，提供批量处理工具
"""

import sqlite3
import json
import os
from datetime import datetime

class IndicatorReferenceManager:
    """指标参考数据管理器"""

    def __init__(self, db_path='DATABASE-HOSPITAL/hospital_indicator_system.db'):
        self.db_path = db_path

    def get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_path)

    def analyze_current_status(self):
        """分析当前指标参考数据状态"""
        print("🔍 分析当前指标参考数据状态...")

        conn = self.get_connection()
        cursor = conn.cursor()

        # 统计所有指标
        cursor.execute("SELECT COUNT(*) FROM indicators WHERE is_active = 1")
        total_indicators = cursor.fetchone()[0]

        # 统计有指标参考数据的指标
        cursor.execute("""
            SELECT COUNT(*) FROM indicators
            WHERE is_active = 1
            AND (indicator_definition IS NOT NULL AND indicator_definition != ''
                 OR statistical_scope IS NOT NULL AND statistical_scope != ''
                 OR reference_value IS NOT NULL AND reference_value != ''
                 OR monitoring_analysis IS NOT NULL AND monitoring_analysis != '')
        """)
        indicators_with_reference = cursor.fetchone()[0]

        # 统计缺少指标参考数据的指标
        missing_reference = total_indicators - indicators_with_reference

        print(f"📊 统计结果:")
        print(f"   总指标数: {total_indicators}")
        print(f"   有指标参考数据: {indicators_with_reference}")
        print(f"   缺少指标参考数据: {missing_reference}")
        print(f"   完整率: {(indicators_with_reference/total_indicators*100):.1f}%")

        # 列出缺少指标参考数据的指标
        if missing_reference > 0:
            cursor.execute("""
                SELECT id, name FROM indicators
                WHERE is_active = 1
                AND (indicator_definition IS NULL OR indicator_definition = '')
                AND (statistical_scope IS NULL OR statistical_scope = '')
                AND (reference_value IS NULL OR reference_value = '')
                AND (monitoring_analysis IS NULL OR monitoring_analysis = '')
                ORDER BY id
                LIMIT 20
            """)

            missing_indicators = cursor.fetchall()
            print(f"\n❌ 缺少指标参考数据的指标（前20个）:")
            for indicator in missing_indicators:
                print(f"   - {indicator[0]}: {indicator[1]}")

        conn.close()
        return {
            'total': total_indicators,
            'with_reference': indicators_with_reference,
            'missing_reference': missing_reference,
            'completion_rate': indicators_with_reference/total_indicators*100
        }

    def create_reference_template(self):
        """创建指标参考数据模板"""
        print("\n📋 创建指标参考数据模板...")

        template = {
            "indicator_definition": "指标的详细定义和说明",
            "statistical_scope": "统计范围和包含的内容",
            "data_sources": "数据来源的详细说明",
            "collection_frequency_detail": "统计频率的详细说明",
            "reference_value": "标准值或参考值",
            "monitoring_analysis": "监测分析的要求和建议"
        }

        # 保存模板到文件
        with open('indicator_reference_template.json', 'w', encoding='utf-8') as f:
            json.dump(template, f, ensure_ascii=False, indent=2)

        print("✅ 模板已保存到 indicator_reference_template.json")
        return template

    def get_indicators_without_reference(self):
        """获取缺少指标参考数据的指标列表"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, name, indicator_type FROM indicators
            WHERE is_active = 1
            AND (indicator_definition IS NULL OR indicator_definition = '')
            AND (statistical_scope IS NULL OR statistical_scope = '')
            AND (reference_value IS NULL OR reference_value = '')
            AND (monitoring_analysis IS NULL OR monitoring_analysis = '')
            ORDER BY id
        """)

        indicators = cursor.fetchall()
        conn.close()

        return [{'id': row[0], 'name': row[1], 'type': row[2]} for row in indicators]

    def batch_add_reference_data(self, reference_data_dict):
        """批量添加指标参考数据"""
        print(f"\n🔄 批量添加指标参考数据...")

        conn = self.get_connection()
        cursor = conn.cursor()

        success_count = 0
        error_count = 0

        for indicator_id, data in reference_data_dict.items():
            try:
                cursor.execute("""
                    UPDATE indicators
                    SET
                        indicator_definition = ?,
                        statistical_scope = ?,
                        data_sources = ?,
                        collection_frequency_detail = ?,
                        reference_value = ?,
                        monitoring_analysis = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (
                    data.get('indicator_definition'),
                    data.get('statistical_scope'),
                    data.get('data_sources'),
                    data.get('collection_frequency_detail'),
                    data.get('reference_value'),
                    data.get('monitoring_analysis'),
                    indicator_id
                ))

                if cursor.rowcount > 0:
                    print(f"✅ {indicator_id}: 更新成功")
                    success_count += 1
                else:
                    print(f"⚠️  {indicator_id}: 指标不存在")
                    error_count += 1

            except Exception as e:
                print(f"❌ {indicator_id}: 更新失败 - {e}")
                error_count += 1

        conn.commit()
        conn.close()

        print(f"\n📊 批量更新结果:")
        print(f"   成功: {success_count}")
        print(f"   失败: {error_count}")

        return success_count, error_count

    def generate_default_reference_data(self, indicators):
        """为指标生成默认的指标参考数据"""
        print(f"\n🤖 为 {len(indicators)} 个指标生成默认参考数据...")

        reference_data = {}

        for indicator in indicators:
            indicator_id = indicator['id']
            indicator_name = indicator['name']
            indicator_type = indicator.get('type', 'simple')

            # 根据指标类型和名称生成默认数据
            if '床位' in indicator_name:
                reference_data[indicator_id] = self._generate_bed_reference(indicator_name)
            elif '人员' in indicator_name or '医师' in indicator_name or '护士' in indicator_name:
                reference_data[indicator_id] = self._generate_staff_reference(indicator_name)
            elif '科室' in indicator_name or '科' in indicator_name:
                reference_data[indicator_id] = self._generate_department_reference(indicator_name)
            elif '设备' in indicator_name or '仪器' in indicator_name:
                reference_data[indicator_id] = self._generate_equipment_reference(indicator_name)
            else:
                reference_data[indicator_id] = self._generate_general_reference(indicator_name)

        return reference_data

    def _generate_bed_reference(self, name):
        """生成床位相关指标的参考数据"""
        return {
            'indicator_definition': f'{name}是反映医院床位资源配置和利用情况的重要指标。',
            'statistical_scope': '医院所有相关床位的统计范围，包括各科室和病区的床位配置情况。',
            'data_sources': '医院信息系统(HIS)、床位管理系统、医务科统计报表等。',
            'collection_frequency_detail': '实时监测，每日统计，月度汇总分析',
            'reference_value': '根据医院等级和功能定位确定具体标准',
            'monitoring_analysis': '建议按科室、病区、时间趋势等维度进行分析，重点关注床位配置的合理性和利用效率'
        }

    def _generate_staff_reference(self, name):
        """生成人员相关指标的参考数据"""
        return {
            'indicator_definition': f'{name}是反映医院人力资源配置水平的重要指标。',
            'statistical_scope': '医院所有相关人员的统计范围，包括各类专业技术人员的配置情况。',
            'data_sources': '人力资源管理系统、医院信息系统、人事部门统计报表等。',
            'collection_frequency_detail': '月度统计，季度分析，年度评估',
            'reference_value': '按照国家卫健委和医院等级评审标准确定',
            'monitoring_analysis': '建议按人员类别、科室分布、专业结构等维度分析人力资源配置的合理性'
        }

    def _generate_department_reference(self, name):
        """生成科室相关指标的参考数据"""
        return {
            'indicator_definition': f'{name}是反映科室运营管理水平的综合指标。',
            'statistical_scope': '科室所有相关业务和管理指标的综合评价范围。',
            'data_sources': '科室信息系统、医院信息系统、质量管理系统等。',
            'collection_frequency_detail': '月度统计，季度分析，年度评估',
            'reference_value': '根据科室规模、功能定位和发展目标确定具体标准',
            'monitoring_analysis': '建议按业务量、质量指标、效率指标等维度进行综合分析'
        }

    def _generate_equipment_reference(self, name):
        """生成设备相关指标的参考数据"""
        return {
            'indicator_definition': f'{name}是反映医院设备资源配置和使用情况的指标。',
            'statistical_scope': '医院所有相关设备的配置、使用和管理情况。',
            'data_sources': '设备管理系统、医院信息系统、设备科统计报表等。',
            'collection_frequency_detail': '月度统计，季度分析，年度评估',
            'reference_value': '按照医院等级和功能需求确定配置标准',
            'monitoring_analysis': '建议按设备类型、使用率、维护情况等维度进行分析'
        }

    def _generate_general_reference(self, name):
        """生成通用指标的参考数据"""
        return {
            'indicator_definition': f'{name}是医院运营管理的重要指标。',
            'statistical_scope': '相关业务范围内的统计数据和管理指标。',
            'data_sources': '医院信息系统、相关业务系统、统计报表等。',
            'collection_frequency_detail': '根据指标特点确定统计频率，建议月度或季度统计',
            'reference_value': '根据医院实际情况和行业标准确定',
            'monitoring_analysis': '建议定期监测分析，关注趋势变化和异常情况'
        }

    def export_missing_indicators(self, filename='missing_reference_indicators.json'):
        """导出缺少指标参考数据的指标列表"""
        indicators = self.get_indicators_without_reference()

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(indicators, f, ensure_ascii=False, indent=2)

        print(f"📤 已导出 {len(indicators)} 个缺少指标参考数据的指标到 {filename}")
        return indicators

    def validate_reference_data(self):
        """验证指标参考数据的完整性"""
        print("\n✅ 验证指标参考数据完整性...")

        conn = self.get_connection()
        cursor = conn.cursor()

        # 检查各字段的完整性
        fields = [
            ('indicator_definition', '指标定义'),
            ('statistical_scope', '统计范围'),
            ('reference_value', '参考值'),
            ('monitoring_analysis', '监测分析')
        ]

        for field, label in fields:
            cursor.execute(f"""
                SELECT COUNT(*) FROM indicators
                WHERE is_active = 1 AND ({field} IS NULL OR {field} = '')
            """)
            missing_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM indicators WHERE is_active = 1")
            total_count = cursor.fetchone()[0]

            completion_rate = ((total_count - missing_count) / total_count * 100) if total_count > 0 else 0

            print(f"   {label}: {completion_rate:.1f}% 完整 ({total_count - missing_count}/{total_count})")

        conn.close()

def main():
    """主函数 - 提供完整的解决方案"""
    print("🎯 底层指标指标参考数据完整解决方案")
    print("=" * 70)

    manager = IndicatorReferenceManager()

    # 1. 分析当前状态
    status = manager.analyze_current_status()

    # 2. 如果有缺少数据的指标，提供解决方案
    if status['missing_reference'] > 0:
        print(f"\n🔧 发现 {status['missing_reference']} 个指标缺少参考数据，开始处理...")

        # 获取缺少数据的指标
        missing_indicators = manager.get_indicators_without_reference()

        # 生成默认参考数据
        default_data = manager.generate_default_reference_data(missing_indicators)

        # 批量添加数据
        success_count, error_count = manager.batch_add_reference_data(default_data)

        print(f"\n🎉 处理完成！成功为 {success_count} 个指标添加了参考数据")

        # 重新验证
        print("\n" + "="*50)
        manager.validate_reference_data()

        # 导出处理结果
        manager.export_missing_indicators('processed_indicators.json')

    else:
        print("\n✅ 所有指标都已有参考数据！")
        manager.validate_reference_data()

    # 3. 创建模板供手动编辑
    manager.create_reference_template()

    print("\n" + "=" * 70)
    print("📋 解决方案总结:")
    print("1. ✅ 自动为缺少数据的指标生成了默认参考数据")
    print("2. ✅ 创建了指标参考数据模板文件")
    print("3. ✅ 提供了数据验证和导出功能")
    print("4. ✅ 统一使用indicators表存储，简化了数据结构")

    print("\n🔗 测试链接:")
    print("- 前端系统: http://localhost:5001")
    print("- 后端管理: http://localhost:5001/admin/indicators")

    print("\n📝 后续建议:")
    print("1. 根据实际需求调整自动生成的参考数据")
    print("2. 建立定期数据质量检查机制")
    print("3. 为新增指标及时添加参考数据")

if __name__ == "__main__":
    main()
