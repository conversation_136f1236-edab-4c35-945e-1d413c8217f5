-- 只创建字段配置表 (SQLite版本)

-- 13. 指标字段配置表
CREATE TABLE IF NOT EXISTS indicator_field_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    field_name VARCHAR(50) NOT NULL UNIQUE,
    field_label VARCHAR(100) NOT NULL,
    field_type VARCHAR(20) DEFAULT 'text',
    display_section VARCHAR(20) DEFAULT 'detailed',
    is_required INTEGER DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    validation_rules TEXT,
    default_value TEXT,
    help_text TEXT,
    is_active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_field_config_display_section ON indicator_field_config(display_section);
CREATE INDEX IF NOT EXISTS idx_field_config_sort_order ON indicator_field_config(sort_order);

-- 14. 指标字段值表
CREATE TABLE IF NOT EXISTS indicator_field_values (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    indicator_id VARCHAR(20) NOT NULL,
    field_name VARCHAR(50) NOT NULL,
    field_value TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(indicator_id, field_name),
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE,
    FOREIGN KEY (field_name) REFERENCES indicator_field_config(field_name) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_field_values_indicator_id ON indicator_field_values(indicator_id);
CREATE INDEX IF NOT EXISTS idx_field_values_field_name ON indicator_field_values(field_name);

-- 插入默认字段配置
INSERT OR REPLACE INTO indicator_field_config (field_name, field_label, field_type, display_section, is_required, sort_order, help_text) VALUES
-- 基础信息字段
('unit', '单位', 'text', 'basic', 1, 1, '指标的计量单位，如：张、人、%、次等'),
('lead_department', '牵头科室', 'text', 'basic', 1, 2, '负责该指标数据收集和管理的主要科室'),
('data_source', '数据来源', 'text', 'basic', 1, 3, '指标数据的来源系统或部门'),
('logic_definition', '逻辑定义', 'textarea', 'basic', 0, 4, '指标的计算逻辑和业务规则说明'),

-- 详细信息字段
('indicator_definition', '指标定义', 'textarea', 'detailed', 0, 5, '指标的详细定义和说明'),
('statistical_scope', '统计范围', 'textarea', 'detailed', 0, 7, '指标统计的范围和对象'),
('data_sources', '数据来源详细', 'textarea', 'detailed', 0, 8, '详细的数据来源说明'),
('collection_frequency_detail', '统计频率', 'text', 'detailed', 0, 9, '数据收集的频率说明'),
('reference_value', '标准值/参考值', 'text', 'detailed', 0, 10, '指标的标准值或参考范围'),
('monitoring_analysis', '监测分析', 'textarea', 'detailed', 0, 11, '指标的监测和分析方法'),
('calculation_formula', '计算公式', 'text', 'detailed', 0, 12, '指标的具体计算公式'),
('numerator_description', '分子描述', 'textarea', 'detailed', 0, 13, '分子的详细描述'),
('denominator_description', '分母描述', 'textarea', 'detailed', 0, 14, '分母的详细描述');

-- 更新现有指标的类型（将没有子指标的指标标记为底层指标）
UPDATE indicators SET is_bottom_level = 1 
WHERE id NOT IN (SELECT DISTINCT parent_id FROM indicators WHERE parent_id IS NOT NULL);

-- 将现有指标数据迁移到字段值表
INSERT OR REPLACE INTO indicator_field_values (indicator_id, field_name, field_value)
SELECT id, 'unit', unit FROM indicators WHERE unit IS NOT NULL AND unit != '';

INSERT OR REPLACE INTO indicator_field_values (indicator_id, field_name, field_value)
SELECT id, 'lead_department', lead_department FROM indicators WHERE lead_department IS NOT NULL AND lead_department != '';

INSERT OR REPLACE INTO indicator_field_values (indicator_id, field_name, field_value)
SELECT id, 'data_source', data_source FROM indicators WHERE data_source IS NOT NULL AND data_source != '';

INSERT OR REPLACE INTO indicator_field_values (indicator_id, field_name, field_value)
SELECT id, 'logic_definition', logic_definition FROM indicators WHERE logic_definition IS NOT NULL AND logic_definition != '';

INSERT OR REPLACE INTO indicator_field_values (indicator_id, field_name, field_value)
SELECT id, 'indicator_definition', indicator_definition FROM indicators WHERE indicator_definition IS NOT NULL AND indicator_definition != '';

INSERT OR REPLACE INTO indicator_field_values (indicator_id, field_name, field_value)
SELECT id, 'statistical_scope', statistical_scope FROM indicators WHERE statistical_scope IS NOT NULL AND statistical_scope != '';

INSERT OR REPLACE INTO indicator_field_values (indicator_id, field_name, field_value)
SELECT id, 'reference_value', reference_value FROM indicators WHERE reference_value IS NOT NULL AND reference_value != '';

INSERT OR REPLACE INTO indicator_field_values (indicator_id, field_name, field_value)
SELECT id, 'monitoring_analysis', monitoring_analysis FROM indicators WHERE monitoring_analysis IS NOT NULL AND monitoring_analysis != '';

INSERT OR REPLACE INTO indicator_field_values (indicator_id, field_name, field_value)
SELECT id, 'calculation_formula', calculation_formula FROM indicators WHERE calculation_formula IS NOT NULL AND calculation_formula != '';

INSERT OR REPLACE INTO indicator_field_values (indicator_id, field_name, field_value)
SELECT id, 'numerator_description', numerator_description FROM indicators WHERE numerator_description IS NOT NULL AND numerator_description != '';

INSERT OR REPLACE INTO indicator_field_values (indicator_id, field_name, field_value)
SELECT id, 'denominator_description', denominator_description FROM indicators WHERE denominator_description IS NOT NULL AND denominator_description != '';
