-- 简化的指标字段显示配置表

-- 删除复杂的配置表（如果存在）
DROP TABLE IF EXISTS indicator_field_values;
DROP TABLE IF EXISTS indicator_field_config;

-- 创建简单的指标字段显示配置表
CREATE TABLE IF NOT EXISTS indicator_field_display (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    indicator_id VARCHAR(20) NOT NULL,
    field_name VARCHAR(50) NOT NULL,
    show_in_basic INTEGER DEFAULT 0,  -- 是否在基础信息中显示 (0=否, 1=是)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(indicator_id, field_name),
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_field_display_indicator_id ON indicator_field_display(indicator_id);
CREATE INDEX IF NOT EXISTS idx_field_display_field_name ON indicator_field_display(field_name);

-- 定义可配置的字段列表
-- 这些是底层指标可以选择在基础信息中显示的字段
INSERT OR REPLACE INTO indicator_field_display (indicator_id, field_name, show_in_basic)
SELECT 
    i.id,
    'unit',
    CASE WHEN i.unit IS NOT NULL AND i.unit != '' THEN 1 ELSE 0 END
FROM indicators i 
WHERE i.is_bottom_level = 1;

INSERT OR REPLACE INTO indicator_field_display (indicator_id, field_name, show_in_basic)
SELECT 
    i.id,
    'lead_department',
    CASE WHEN i.lead_department IS NOT NULL AND i.lead_department != '' THEN 1 ELSE 0 END
FROM indicators i 
WHERE i.is_bottom_level = 1;

INSERT OR REPLACE INTO indicator_field_display (indicator_id, field_name, show_in_basic)
SELECT 
    i.id,
    'data_source',
    CASE WHEN i.data_source IS NOT NULL AND i.data_source != '' THEN 1 ELSE 0 END
FROM indicators i 
WHERE i.is_bottom_level = 1;

INSERT OR REPLACE INTO indicator_field_display (indicator_id, field_name, show_in_basic)
SELECT 
    i.id,
    'logic_definition',
    CASE WHEN i.logic_definition IS NOT NULL AND i.logic_definition != '' THEN 1 ELSE 0 END
FROM indicators i 
WHERE i.is_bottom_level = 1;

INSERT OR REPLACE INTO indicator_field_display (indicator_id, field_name, show_in_basic)
SELECT 
    i.id,
    'reference_range',
    CASE WHEN i.reference_range IS NOT NULL AND i.reference_range != '' THEN 1 ELSE 0 END
FROM indicators i 
WHERE i.is_bottom_level = 1;

-- 创建视图：获取指标的基础信息字段
CREATE VIEW IF NOT EXISTS v_indicator_basic_fields AS
SELECT 
    i.id,
    i.name,
    i.is_bottom_level,
    -- 基础字段及其显示配置
    i.unit,
    COALESCE(ifd_unit.show_in_basic, 0) as show_unit_in_basic,
    i.lead_department,
    COALESCE(ifd_dept.show_in_basic, 0) as show_lead_department_in_basic,
    i.data_source,
    COALESCE(ifd_source.show_in_basic, 0) as show_data_source_in_basic,
    i.logic_definition,
    COALESCE(ifd_logic.show_in_basic, 0) as show_logic_definition_in_basic,
    i.reference_range,
    COALESCE(ifd_ref.show_in_basic, 0) as show_reference_range_in_basic
FROM indicators i
LEFT JOIN indicator_field_display ifd_unit ON i.id = ifd_unit.indicator_id AND ifd_unit.field_name = 'unit'
LEFT JOIN indicator_field_display ifd_dept ON i.id = ifd_dept.indicator_id AND ifd_dept.field_name = 'lead_department'
LEFT JOIN indicator_field_display ifd_source ON i.id = ifd_source.indicator_id AND ifd_source.field_name = 'data_source'
LEFT JOIN indicator_field_display ifd_logic ON i.id = ifd_logic.indicator_id AND ifd_logic.field_name = 'logic_definition'
LEFT JOIN indicator_field_display ifd_ref ON i.id = ifd_ref.indicator_id AND ifd_ref.field_name = 'reference_range'
WHERE i.is_active = 1;
