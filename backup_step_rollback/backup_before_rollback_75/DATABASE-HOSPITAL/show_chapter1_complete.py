#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示第一章完整详细数据的脚本（无交互版本）
"""

import sqlite3

def show_complete_chapter1_data():
    """
    显示第一章的完整详细数据
    """
    db_path = "hospital_indicator_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=" * 100)
        print("🏥 医院等级评审指标管理系统 - 第一章完整详细数据")
        print("=" * 100)
        
        # 获取章节信息
        cursor.execute("""
            SELECT code, name, description, icon, color 
            FROM chapters 
            WHERE code = '1'
        """)
        chapter = cursor.fetchone()
        
        if not chapter:
            print("❌ 未找到第一章数据")
            return
        
        print(f"\n📚 【第{chapter[0]}章】{chapter[1]}")
        print(f"🎨 图标: {chapter[3]} | 颜色: {chapter[4]}")
        print(f"📝 描述: {chapter[2]}")
        
        # 获取小节和指标的完整信息
        cursor.execute("""
            SELECT 
                s.code as section_code,
                s.name as section_name,
                s.description as section_desc,
                i.id as indicator_id,
                i.name as indicator_name,
                i.description as indicator_desc,
                i.category
            FROM sections s
            LEFT JOIN indicators i ON s.id = i.section_id
            WHERE s.chapter_id = (SELECT id FROM chapters WHERE code = '1')
            ORDER BY s.sort_order, i.id
        """)
        
        results = cursor.fetchall()
        
        # 按小节组织数据
        sections_data = {}
        for row in results:
            section_code = row[0]
            if section_code not in sections_data:
                sections_data[section_code] = {
                    'name': row[1],
                    'description': row[2],
                    'indicators': []
                }
            
            if row[3]:  # 如果有指标数据
                sections_data[section_code]['indicators'].append({
                    'id': row[3],
                    'name': row[4],
                    'description': row[5],
                    'category': row[6]
                })
        
        # 显示详细数据
        section_count = 0
        total_indicators = 0
        
        for section_code, section_data in sections_data.items():
            section_count += 1
            indicator_count = len(section_data['indicators'])
            total_indicators += indicator_count
            
            print(f"\n" + "=" * 80)
            print(f"📖 {section_code} {section_data['name']} ({indicator_count}个指标)")
            print("=" * 80)
            
            if section_data['indicators']:
                for i, indicator in enumerate(section_data['indicators'], 1):
                    print(f"\n  🔹 {indicator['id']} - {indicator['name']}")
                    if indicator['category']:
                        print(f"     📂 类别: {indicator['category']}")
                    if indicator['description'] and not indicator['description'].startswith("第一章指标："):
                        print(f"     📝 描述: {indicator['description']}")
            else:
                print("\n  ⚠️  暂无指标数据")
        
        # 显示汇总统计
        print(f"\n" + "=" * 100)
        print("📊 第一章数据汇总统计")
        print("=" * 100)
        
        print(f"\n📋 基本统计:")
        print(f"   • 小节总数: {section_count}")
        print(f"   • 指标总数: {total_indicators}")
        print(f"   • 平均每小节指标数: {total_indicators/section_count:.1f}")
        
        print(f"\n📈 各小节指标分布:")
        for section_code, section_data in sections_data.items():
            indicator_count = len(section_data['indicators'])
            percentage = (indicator_count / total_indicators * 100) if total_indicators > 0 else 0
            print(f"   • {section_code} {section_data['name']}: {indicator_count}个指标 ({percentage:.1f}%)")
        
        # 显示指标编号分布
        print(f"\n🔢 指标编号分布:")
        for section_code, section_data in sections_data.items():
            if section_data['indicators']:
                indicator_ids = [ind['id'] for ind in section_data['indicators']]
                print(f"   • {section_code}: {', '.join(indicator_ids)}")
        
        print(f"\n" + "=" * 100)
        print("✅ 第一章完整数据展示完成")
        print("=" * 100)
        
    except Exception as e:
        print(f"❌ 查询数据时出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if conn:
            conn.close()

def show_database_overview():
    """
    显示数据库整体概览
    """
    db_path = "hospital_indicator_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"\n" + "=" * 60)
        print("🗃️ 数据库整体概览")
        print("=" * 60)
        
        # 获取各表的记录数
        tables = ['chapters', 'sections', 'indicators', 'tags', 'departments', 'users', 'system_config']
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   📊 {table}: {count} 条记录")
        
        # 获取第一章的详细统计
        cursor.execute("""
            SELECT COUNT(*) FROM sections 
            WHERE chapter_id = (SELECT id FROM chapters WHERE code = '1')
        """)
        chapter1_sections = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) FROM indicators 
            WHERE chapter_id = (SELECT id FROM chapters WHERE code = '1')
        """)
        chapter1_indicators = cursor.fetchone()[0]
        
        print(f"\n   🎯 第一章专项统计:")
        print(f"      • 小节数: {chapter1_sections}")
        print(f"      • 指标数: {chapter1_indicators}")
        
    except Exception as e:
        print(f"❌ 查询概览数据时出错: {e}")
    finally:
        if conn:
            conn.close()

def main():
    """
    主函数
    """
    show_complete_chapter1_data()
    show_database_overview()

if __name__ == "__main__":
    main()
