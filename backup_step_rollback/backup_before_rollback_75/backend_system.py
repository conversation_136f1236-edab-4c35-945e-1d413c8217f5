#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院指标后端管理系统
"""

from flask import Flask, request
import sqlite3
import os

app = Flask(__name__)

# 数据库路径
DB_PATH = 'hospital-evaluation-database.db'

def get_db_connection():
    """获取数据库连接"""
    if not os.path.exists(DB_PATH):
        return None
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def dict_from_row(row):
    """将sqlite3.Row转换为字典"""
    return dict(zip(row.keys(), row)) if row else None

@app.route('/')
def admin_home():
    """后端管理主页"""
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>后台管理系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%); min-height: 100vh; }
            .container { max-width: 1200px; margin: 0 auto; }
            .header { background: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; position: relative; }
            .frontend-switch { position: absolute; top: 20px; right: 20px; background: #28a745; color: white; padding: 10px 20px; border-radius: 25px; text-decoration: none; font-weight: bold; box-shadow: 0 2px 10px rgba(0,0,0,0.2); transition: all 0.3s ease; }
            .frontend-switch:hover { background: #218838; transform: scale(1.05); }
            .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
            .stat-card { background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; }
            .stat-number { font-size: 36px; font-weight: bold; margin-bottom: 10px; }
            .stat-label { color: #666; font-size: 14px; }
            .management-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
            .management-card { background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
            .management-card h3 { margin-top: 0; color: #333; }
            .btn { padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; font-size: 14px; }
            .btn-primary { background: #007bff; color: white; }
            .btn-success { background: #28a745; color: white; }
            .btn-warning { background: #ffc107; color: #212529; }
            .btn-danger { background: #dc3545; color: white; }
            .btn:hover { opacity: 0.9; transform: translateY(-1px); }
            .system-info { background: rgba(255,255,255,0.95); padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #dc3545; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="system-info">
                <h3 style="margin-top: 0; color: #dc3545;">🔧 后端管理系统</h3>
                <p style="margin-bottom: 0;"><strong>当前端口：</strong>5004 | <strong>前端查看系统：</strong>5003 | <strong>跳转：</strong>右上角绿色按钮</p>
            </div>

            <div class="header">
                <a href="http://localhost:5001" class="frontend-switch" target="_blank">🌐 前端系统</a>
                <h1 style="color: #333; margin: 0 0 10px 0;">🔧 后台管理系统</h1>
                <p style="color: #666; margin: 0; font-size: 16px;">专注于数据管理和维护的后端系统</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" style="color: #007bff;">15</div>
                    <div class="stat-label">数据表总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #28a745;">127</div>
                    <div class="stat-label">指标总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #ffc107;">8</div>
                    <div class="stat-label">章节数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #dc3545;">254</div>
                    <div class="stat-label">组件数量</div>
                </div>
            </div>

            <div class="management-grid">
                <div class="management-card">
                    <h3>📊 指标管理</h3>
                    <p>管理医院指标的基本信息、层级关系和分子分母组件</p>
                    <a href="/indicators" class="btn btn-primary">指标列表</a>
                    <a href="/components" class="btn btn-warning">组件管理</a>
                </div>
                <div class="management-card">
                    <h3>📚 结构管理</h3>
                    <p>管理指标体系的章节和小节结构</p>
                    <a href="/chapters" class="btn btn-success">章节管理</a>
                    <a href="/sections" class="btn btn-success">小节管理</a>
                </div>
                <div class="management-card">
                    <h3>🔧 系统工具</h3>
                    <p>数据验证、批量操作和系统维护工具</p>
                    <a href="/validate" class="btn btn-danger">数据验证</a>
                    <a href="/tools" class="btn btn-warning">系统工具</a>
                </div>
                <div class="management-card">
                    <h3>📈 数据分析</h3>
                    <p>查看系统使用情况和数据质量报告</p>
                    <a href="/reports" class="btn btn-primary">数据报告</a>
                    <a href="/logs" class="btn btn-warning">操作日志</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/components')
def admin_components():
    """后端组件管理"""
    # 示例组件数据
    sample_components = [
        {'id': 1, 'indicator_id': '1.1.1', 'type': 'numerator', 'name': '实际开放床位数', 'unit': '张', 'department': '医务科'},
        {'id': 2, 'indicator_id': '1.1.1', 'type': 'denominator', 'name': '标准床位配置数', 'unit': '张', 'department': '规划科'},
        {'id': 3, 'indicator_id': '2.1.1', 'type': 'numerator', 'name': '门诊就诊人次', 'unit': '人次', 'department': '门诊部'},
        {'id': 4, 'indicator_id': '2.1.1', 'type': 'denominator', 'name': '门诊工作日数', 'unit': '天', 'department': '医务科'},
        {'id': 5, 'indicator_id': '3.1.1', 'type': 'numerator', 'name': '手术成功例数', 'unit': '例', 'department': '手术室'},
        {'id': 6, 'indicator_id': '3.1.1', 'type': 'denominator', 'name': '手术总例数', 'unit': '例', 'department': '手术室'},
    ]

    components_html = ""
    for comp in sample_components:
        type_label = {'numerator': '🔢 分子', 'denominator': '🔣 分母'}.get(comp['type'], '📊 其他')
        components_html += f'''
        <tr>
            <td style="padding: 12px; text-align: center;">{comp['id']}</td>
            <td style="padding: 12px; text-align: center; font-family: monospace; font-weight: bold;">{comp['indicator_id']}</td>
            <td style="padding: 12px; text-align: center;">{type_label}</td>
            <td style="padding: 12px;">{comp['name']}</td>
            <td style="padding: 12px; text-align: center;">{comp['unit']}</td>
            <td style="padding: 12px;">{comp['department']}</td>
            <td style="padding: 12px; text-align: center;">
                <a href="/components/{comp['id']}/edit" style="background: #ffc107; color: white; padding: 6px 12px; text-decoration: none; border-radius: 4px; font-size: 12px;">✏️ 编辑</a>
            </td>
        </tr>
        '''

    return f'''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>组件管理 - 后端管理系统</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .header {{ background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }}
            .nav-switches {{ display: flex; gap: 10px; }}
            .nav-switch {{ padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }}
            .frontend-switch {{ background: #28a745; color: white; }}
            .admin-switch {{ background: #6c757d; color: white; }}
            .nav-switch:hover {{ opacity: 0.9; }}
            .system-info {{ background: #fff3cd; padding: 15px; border-radius: 4px; margin-bottom: 20px; color: #856404; border: 1px solid #ffeaa7; }}
            table {{ width: 100%; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
            th {{ background: #f8f9fa; padding: 15px; text-align: left; font-weight: 600; border-bottom: 1px solid #dee2e6; }}
            td {{ padding: 12px; border-bottom: 1px solid #f1f3f4; }}
            tr:hover {{ background: #f8f9fa; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="system-info">
                <strong>🔧 后端管理系统</strong> | 端口: 5004 | 跳转到前端查看: 右上角按钮 → 端口 5003
            </div>

            <div class="header">
                <div>
                    <h1 style="margin: 0; color: #333;">🧮 组件管理</h1>
                    <p style="margin: 5px 0 0 0; color: #666;">管理指标的分子分母组件</p>
                </div>
                <div class="nav-switches">
                    <a href="http://localhost:5003" class="nav-switch frontend-switch" target="_blank">🌐 前端系统</a>
                    <a href="/" class="nav-switch admin-switch">🔧 管理首页</a>
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>指标ID</th>
                        <th>类型</th>
                        <th>名称</th>
                        <th>单位</th>
                        <th>牵头科室</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {components_html}
                </tbody>
            </table>

            <div style="margin-top: 20px; text-align: center;">
                <a href="/" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">🏠 返回首页</a>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/components/<component_id>/edit', methods=['GET', 'POST'])
def edit_component(component_id):
    """编辑组件"""
    if request.method == 'POST':
        return '''
        <script>
            alert('✅ 组件更新成功！');
            window.location.href = '/components';
        </script>
        '''

    # 示例组件数据
    component = {
        'id': component_id,
        'indicator_id': '1.1.1',
        'component_type': 'numerator',
        'name': '实际开放床位数',
        'definition': '医院实际开放并可收治患者的床位数量',
        'unit': '张',
        'data_source': 'HIS系统',
        'lead_department': '医务科',
        'logic_definition': '统计期末实际开放的床位数'
    }

    return f'''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>编辑组件 {component_id} - 后端管理系统</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
            .container {{ max-width: 800px; margin: 0 auto; }}
            .header {{ background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }}
            .nav-switches {{ display: flex; gap: 10px; }}
            .nav-switch {{ padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }}
            .frontend-switch {{ background: #28a745; color: white; }}
            .admin-switch {{ background: #6c757d; color: white; }}
            .nav-switch:hover {{ opacity: 0.9; }}
            .system-info {{ background: #fff3cd; padding: 15px; border-radius: 4px; margin-bottom: 20px; color: #856404; border: 1px solid #ffeaa7; }}
            .form-container {{ background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
            .form-group {{ margin-bottom: 20px; }}
            .form-group label {{ display: block; margin-bottom: 5px; font-weight: 600; color: #333; }}
            .form-group input, .form-group select, .form-group textarea {{ width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }}
            .form-group textarea {{ height: 80px; resize: vertical; }}
            .form-row {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }}
            .btn {{ padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin-right: 10px; font-size: 14px; }}
            .btn-primary {{ background: #007bff; color: white; }}
            .btn-secondary {{ background: #6c757d; color: white; }}
            .btn:hover {{ opacity: 0.9; }}
            .info {{ background: #e3f2fd; padding: 15px; border-radius: 4px; margin-bottom: 20px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="system-info">
                <strong>🔧 后端管理系统</strong> | 端口: 5004 | 跳转到前端查看: 右上角按钮 → 端口 5003
            </div>

            <div class="header">
                <div>
                    <h1 style="margin: 0; color: #333;">🧮 编辑组件 #{component_id}</h1>
                    <p style="margin: 5px 0 0 0; color: #666;">修改组件的详细信息</p>
                </div>
                <div class="nav-switches">
                    <a href="http://localhost:5003" class="nav-switch frontend-switch" target="_blank">🌐 前端系统</a>
                    <a href="/" class="nav-switch admin-switch">🔧 管理首页</a>
                </div>
            </div>

            <div class="form-container">
                <div class="info">
                    <strong>📊 指标：</strong>{component['indicator_id']} |
                    <strong>🏷️ 类型：</strong>{'🔢 分子' if component.get('component_type') == 'numerator' else '🔣 分母'}
                </div>

                <form method="POST">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="component_type">🏷️ 组件类型 *</label>
                            <select id="component_type" name="component_type" required>
                                <option value="numerator" {'selected' if component.get('component_type') == 'numerator' else ''}>🔢 分子</option>
                                <option value="denominator" {'selected' if component.get('component_type') == 'denominator' else ''}>🔣 分母</option>
                                <option value="other">📊 其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="name">📝 组件名称 *</label>
                            <input type="text" id="name" name="name" value="{component.get('name', '')}" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="definition">📋 组件定义</label>
                        <textarea id="definition" name="definition" placeholder="详细描述组件的含义和计算逻辑">{component.get('definition', '')}</textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="unit">📏 单位</label>
                            <input type="text" id="unit" name="unit" value="{component.get('unit', '')}" placeholder="例如：人、张、次、%">
                        </div>
                        <div class="form-group">
                            <label for="lead_department">🏥 牵头科室</label>
                            <input type="text" id="lead_department" name="lead_department" value="{component.get('lead_department', '')}" placeholder="负责此数据的科室">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="data_source">📊 数据来源</label>
                        <input type="text" id="data_source" name="data_source" value="{component.get('data_source', '')}" placeholder="例如：HIS系统、手工统计、第三方系统">
                    </div>

                    <div class="form-group">
                        <label for="logic_definition">🧮 逻辑定义</label>
                        <textarea id="logic_definition" name="logic_definition" placeholder="详细的计算逻辑和业务规则">{component.get('logic_definition', '')}</textarea>
                    </div>

                    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
                        <button type="submit" class="btn btn-primary">💾 保存更改</button>
                        <a href="/components" class="btn btn-secondary">❌ 取消</a>
                    </div>
                </form>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/indicators')
def admin_indicators():
    """后端指标管理"""
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>指标管理 - 后端管理系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1000px; margin: 0 auto; }
            .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }
            .nav-switches { display: flex; gap: 10px; }
            .nav-switch { padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 12px; }
            .frontend-switch { background: #28a745; color: white; }
            .admin-switch { background: #6c757d; color: white; }
            .nav-switch:hover { opacity: 0.9; }
            .system-info { background: #fff3cd; padding: 15px; border-radius: 4px; margin-bottom: 20px; color: #856404; border: 1px solid #ffeaa7; }
            .demo-content { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="system-info">
                <strong>🔧 后端管理系统</strong> | 端口: 5004 | 跳转到前端查看: 右上角按钮 → 端口 5003
            </div>

            <div class="header">
                <div>
                    <h1 style="margin: 0; color: #333;">📊 指标管理</h1>
                    <p style="margin: 5px 0 0 0; color: #666;">管理医院指标的基本信息</p>
                </div>
                <div class="nav-switches">
                    <a href="http://localhost:5003" class="nav-switch frontend-switch" target="_blank">🌐 前端系统</a>
                    <a href="/" class="nav-switch admin-switch">🔧 管理首页</a>
                </div>
            </div>

            <div class="demo-content">
                <h2>📊 指标管理功能</h2>
                <p>这里是后端系统的指标管理页面</p>
                <p>注意右上角的跳转按钮可以切换到前端查看系统</p>
                <div style="margin-top: 30px;">
                    <a href="/" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">🏠 返回首页</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🔧 医院指标后端管理系统")
    print("📍 访问地址: http://localhost:5004")
    print("🔗 前端查看: http://localhost:5003")
    print("🎯 专注于数据管理和维护")
    print("=" * 50)

    app.run(debug=True, host='0.0.0.0', port=5004)
