#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院指标管理系统 - 后台管理程序
用于管理和修正数据库中的指标数据
"""

from flask import Flask, render_template, jsonify, request, redirect, url_for, flash, session
import sqlite3
import json
import os
from datetime import datetime
from functools import wraps

app = Flask(__name__)
app.config['SECRET_KEY'] = 'hospital_admin_system_2024'

# 数据库路径
DB_PATH = 'DATABASE-HOSPITAL/hospital_indicator_system.db'

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def dict_from_row(row):
    """将sqlite3.Row转换为字典"""
    return dict(zip(row.keys(), row)) if row else None

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_logged_in' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def log_operation(operation_type, table_name, record_id, old_data=None, new_data=None):
    """记录操作日志"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO admin_logs (operation_type, table_name, record_id,
                                  old_data, new_data, operator, created_at)
            VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
        """, (
            operation_type,
            table_name,
            record_id,
            json.dumps(old_data, ensure_ascii=False) if old_data else None,
            json.dumps(new_data, ensure_ascii=False) if new_data else None,
            session.get('admin_user', 'unknown')
        ))

        conn.commit()
        conn.close()
    except Exception as e:
        print(f"记录日志失败: {e}")

# 登录页面
@app.route('/admin/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        # 简单的用户验证（实际项目中应该使用更安全的方式）
        if username == 'admin' and password == 'hospital2024':
            session['admin_logged_in'] = True
            session['admin_user'] = username
            flash('登录成功', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误', 'error')

    return render_template('admin/login.html')

@app.route('/admin/logout')
def logout():
    session.clear()
    flash('已退出登录', 'info')
    return redirect(url_for('login'))

# 管理首页
@app.route('/admin')
@app.route('/admin/dashboard')
@login_required
def dashboard():
    """管理首页 - 数据总览"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取统计数据
        stats = {}

        # 章节统计
        cursor.execute("SELECT COUNT(*) as count FROM chapters WHERE is_active = 1")
        stats['chapters'] = cursor.fetchone()['count']

        # 小节统计
        cursor.execute("SELECT COUNT(*) as count FROM sections WHERE is_active = 1")
        stats['sections'] = cursor.fetchone()['count']

        # 指标统计
        cursor.execute("SELECT COUNT(*) as count FROM indicators WHERE is_active = 1")
        stats['indicators'] = cursor.fetchone()['count']

        # 组件统计
        cursor.execute("SELECT COUNT(*) as count FROM indicator_components WHERE is_active = 1")
        stats['components'] = cursor.fetchone()['count']

        # 数据完整性检查
        cursor.execute("""
            SELECT COUNT(*) as count FROM indicators
            WHERE is_active = 1 AND (name IS NULL OR name = '' OR description IS NULL OR description = '')
        """)
        stats['incomplete_indicators'] = cursor.fetchone()['count']

        cursor.execute("""
            SELECT COUNT(*) as count FROM indicator_components
            WHERE is_active = 1 AND (name IS NULL OR name = '' OR unit IS NULL OR unit = '')
        """)
        stats['incomplete_components'] = cursor.fetchone()['count']

        # 最近的操作日志
        cursor.execute("""
            SELECT * FROM admin_logs
            ORDER BY created_at DESC
            LIMIT 10
        """)
        recent_logs = [dict_from_row(row) for row in cursor.fetchall()]

        conn.close()

        return render_template('admin/dashboard.html', stats=stats, recent_logs=recent_logs)

    except Exception as e:
        flash(f'加载数据失败: {str(e)}', 'error')
        return render_template('admin/dashboard.html', stats={}, recent_logs=[])

# 章节管理
@app.route('/admin/chapters')
@login_required
def chapters():
    """章节管理页面"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT c.*,
                   COUNT(DISTINCT s.id) as section_count,
                   COUNT(DISTINCT i.id) as indicator_count
            FROM chapters c
            LEFT JOIN sections s ON c.id = s.chapter_id AND s.is_active = 1
            LEFT JOIN indicators i ON c.id = i.chapter_id AND i.is_active = 1
            WHERE c.is_active = 1
            GROUP BY c.id
            ORDER BY c.code
        """)

        chapters = [dict_from_row(row) for row in cursor.fetchall()]
        conn.close()

        return render_template('admin/chapters.html', chapters=chapters)

    except Exception as e:
        flash(f'加载章节数据失败: {str(e)}', 'error')
        return render_template('admin/chapters.html', chapters=[])

# 小节管理
@app.route('/admin/sections')
@login_required
def sections():
    """小节管理页面"""
    try:
        chapter_id = request.args.get('chapter_id')

        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取章节列表用于筛选
        cursor.execute("SELECT * FROM chapters WHERE is_active = 1 ORDER BY code")
        chapters = [dict_from_row(row) for row in cursor.fetchall()]

        # 获取小节数据
        if chapter_id:
            cursor.execute("""
                SELECT s.*, c.name as chapter_name, c.code as chapter_code,
                       COUNT(i.id) as indicator_count
                FROM sections s
                LEFT JOIN chapters c ON s.chapter_id = c.id
                LEFT JOIN indicators i ON s.id = i.section_id AND i.is_active = 1
                WHERE s.is_active = 1 AND s.chapter_id = ?
                GROUP BY s.id
                ORDER BY s.sort_order
            """, (chapter_id,))
        else:
            cursor.execute("""
                SELECT s.*, c.name as chapter_name, c.code as chapter_code,
                       COUNT(i.id) as indicator_count
                FROM sections s
                LEFT JOIN chapters c ON s.chapter_id = c.id
                LEFT JOIN indicators i ON s.id = i.section_id AND i.is_active = 1
                WHERE s.is_active = 1
                GROUP BY s.id
                ORDER BY c.code, s.sort_order
            """)

        sections = [dict_from_row(row) for row in cursor.fetchall()]
        conn.close()

        return render_template('admin/sections.html',
                             sections=sections,
                             chapters=chapters,
                             selected_chapter=chapter_id)

    except Exception as e:
        flash(f'加载小节数据失败: {str(e)}', 'error')
        return render_template('admin/sections.html', sections=[], chapters=[])

# 指标管理
@app.route('/admin/indicators')
@login_required
def indicators():
    """指标管理页面"""
    try:
        chapter_id = request.args.get('chapter_id')
        section_id = request.args.get('section_id')
        parent_id = request.args.get('parent_id')
        search = request.args.get('search', '')
        page = int(request.args.get('page', 1))
        per_page = 20

        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取章节和小节列表用于筛选
        cursor.execute("SELECT * FROM chapters WHERE is_active = 1 ORDER BY code")
        chapters = [dict_from_row(row) for row in cursor.fetchall()]

        cursor.execute("SELECT * FROM sections WHERE is_active = 1 ORDER BY chapter_id, sort_order")
        sections = [dict_from_row(row) for row in cursor.fetchall()]

        # 构建查询条件
        where_conditions = ["i.is_active = 1"]
        params = []

        if chapter_id:
            where_conditions.append("i.chapter_id = ?")
            params.append(chapter_id)

        if section_id:
            where_conditions.append("i.section_id = ?")
            params.append(section_id)

        if parent_id:
            where_conditions.append("i.parent_id = ?")
            params.append(parent_id)
        elif parent_id == '':  # 只显示顶级指标
            where_conditions.append("i.parent_id IS NULL")

        if search:
            where_conditions.append("(i.name LIKE ? OR i.id LIKE ? OR i.description LIKE ?)")
            params.extend([f"%{search}%", f"%{search}%", f"%{search}%"])

        where_clause = " AND ".join(where_conditions)

        # 获取总数
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN sections s ON i.section_id = s.id
            WHERE {where_clause}
        """, params)

        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT i.*,
                   c.name as chapter_name, c.code as chapter_code,
                   s.name as section_name, s.code as section_code,
                   p.name as parent_name,
                   COUNT(DISTINCT child.id) as children_count,
                   COUNT(DISTINCT ic.id) as component_count
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN sections s ON i.section_id = s.id
            LEFT JOIN indicators p ON i.parent_id = p.id
            LEFT JOIN indicators child ON child.parent_id = i.id AND child.is_active = 1
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id AND ic.is_active = 1
            WHERE {where_clause}
            GROUP BY i.id
            ORDER BY i.id
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])

        indicators = [dict_from_row(row) for row in cursor.fetchall()]

        conn.close()

        return render_template('admin/indicators.html',
                             indicators=indicators,
                             chapters=chapters,
                             sections=sections,
                             selected_chapter=chapter_id,
                             selected_section=section_id,
                             selected_parent=parent_id,
                             search=search,
                             pagination={
                                 'page': page,
                                 'per_page': per_page,
                                 'total': total,
                                 'pages': (total + per_page - 1) // per_page
                             })

    except Exception as e:
        flash(f'加载指标数据失败: {str(e)}', 'error')
        return render_template('admin/indicators.html', indicators=[], chapters=[], sections=[])

# 指标详情管理
@app.route('/admin/indicators/<indicator_id>')
@login_required
def indicator_detail(indicator_id):
    """指标详情管理页面"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取指标基本信息
        cursor.execute("""
            SELECT i.*,
                   c.name as chapter_name, c.code as chapter_code,
                   s.name as section_name, s.code as section_code,
                   p.name as parent_name
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN sections s ON i.section_id = s.id
            LEFT JOIN indicators p ON i.parent_id = p.id
            WHERE i.id = ?
        """, (indicator_id,))

        indicator = dict_from_row(cursor.fetchone())
        if not indicator:
            flash('指标不存在', 'error')
            return redirect(url_for('indicators'))

        # 获取分子分母组件
        cursor.execute("""
            SELECT * FROM indicator_components
            WHERE indicator_id = ? AND is_active = 1
            ORDER BY component_type, sort_order
        """, (indicator_id,))

        components = [dict_from_row(row) for row in cursor.fetchall()]

        # 获取子指标
        cursor.execute("""
            SELECT i.*, COUNT(ic.id) as component_count
            FROM indicators i
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id AND ic.is_active = 1
            WHERE i.parent_id = ? AND i.is_active = 1
            GROUP BY i.id
            ORDER BY i.sort_order, i.id
        """, (indicator_id,))

        children = [dict_from_row(row) for row in cursor.fetchall()]

        # 获取科室关联信息
        cursor.execute("""
            SELECT id.*, d.name as department_name
            FROM indicator_departments id
            LEFT JOIN departments d ON id.department_id = d.id
            WHERE id.indicator_id = ?
            ORDER BY id.role_type
        """, (indicator_id,))

        departments = [dict_from_row(row) for row in cursor.fetchall()]

        conn.close()

        return render_template('admin/indicator_detail.html',
                             indicator=indicator,
                             components=components,
                             children=children,
                             departments=departments)

    except Exception as e:
        flash(f'加载指标详情失败: {str(e)}', 'error')
        return redirect(url_for('indicators'))

# 组件管理
@app.route('/admin/components')
@login_required
def components():
    """组件管理页面"""
    try:
        indicator_id = request.args.get('indicator_id')
        component_type = request.args.get('type')
        search = request.args.get('search', '')
        page = int(request.args.get('page', 1))
        per_page = 20

        conn = get_db_connection()
        cursor = conn.cursor()

        # 构建查询条件
        where_conditions = ["ic.is_active = 1"]
        params = []

        if indicator_id:
            where_conditions.append("ic.indicator_id = ?")
            params.append(indicator_id)

        if component_type:
            where_conditions.append("ic.component_type = ?")
            params.append(component_type)

        if search:
            where_conditions.append("(ic.name LIKE ? OR ic.definition LIKE ? OR i.name LIKE ?)")
            params.extend([f"%{search}%", f"%{search}%", f"%{search}%"])

        where_clause = " AND ".join(where_conditions)

        # 获取总数
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM indicator_components ic
            LEFT JOIN indicators i ON ic.indicator_id = i.id
            WHERE {where_clause}
        """, params)

        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT ic.*, i.name as indicator_name
            FROM indicator_components ic
            LEFT JOIN indicators i ON ic.indicator_id = i.id
            WHERE {where_clause}
            ORDER BY ic.indicator_id, ic.component_type, ic.sort_order
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])

        components = [dict_from_row(row) for row in cursor.fetchall()]

        conn.close()

        return render_template('admin/components.html',
                             components=components,
                             selected_indicator=indicator_id,
                             selected_type=component_type,
                             search=search,
                             pagination={
                                 'page': page,
                                 'per_page': per_page,
                                 'total': total,
                                 'pages': (total + per_page - 1) // per_page
                             })

    except Exception as e:
        flash(f'加载组件数据失败: {str(e)}', 'error')
        return render_template('admin/components.html', components=[])

if __name__ == '__main__':
    try:
        # 检查数据库文件是否存在
        if not os.path.exists(DB_PATH):
            print(f"错误：数据库文件 {DB_PATH} 不存在")
            print("请先运行主程序创建数据库")
            exit(1)

        # 创建管理日志表
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS admin_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    operation_type TEXT NOT NULL,
                    table_name TEXT NOT NULL,
                    record_id TEXT,
                    old_data TEXT,
                    new_data TEXT,
                    operator TEXT NOT NULL,
                    created_at DATETIME NOT NULL
                )
            """)

            # 创建indicator_components表（如果不存在）
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS indicator_components (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    indicator_id VARCHAR(20) NOT NULL,
                    component_type VARCHAR(20) NOT NULL CHECK (component_type IN ('numerator', 'denominator', 'other')),
                    name VARCHAR(200) NOT NULL,
                    definition TEXT,
                    unit VARCHAR(50),
                    data_source VARCHAR(200),
                    lead_department VARCHAR(100),
                    logic_definition TEXT,
                    collection_method VARCHAR(50),
                    calculation_formula TEXT,
                    notes TEXT,
                    sort_order INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
                )
            """)

            conn.commit()
            conn.close()
        except Exception as e:
            print(f"创建表失败: {e}")

        print("🔧 医院指标后台管理系统启动中...")
        print("🌐 管理地址: http://localhost:5002/admin")
        print("👤 默认账号: admin / hospital2024")
        print("=" * 50)

        app.run(debug=True, host='0.0.0.0', port=5002)
    except Exception as e:
        print(f"应用启动失败: {e}")
        import traceback
        traceback.print_exc()
