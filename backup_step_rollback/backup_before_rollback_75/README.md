# 医院等级评审指标管理系统数据库设计

## 项目概述

本项目为医院等级评审指标管理系统提供完整的数据库设计方案，支持指标管理、数据采集、审核流程、科室管理、用户权限等功能。

## 文件结构

```
├── README.md                    # 项目说明文档
├── 数据库设计说明.md             # 详细设计说明
├── database_design.sql          # 数据库结构创建脚本（纯净版，无示例数据）
├── init_sample_data.sql         # 示例数据模板（注释形式，需要用户自定义）
├── clear_database.sql           # 数据库清理脚本
├── database_maintenance.sql     # 数据库维护脚本
└── 医院等级评审指标说明手册 - Google风格.html  # 前端界面（包含数据库设计注释）
```

## 快速开始

### 1. 环境要求

- MySQL 8.0 或更高版本
- 字符集：utf8mb4
- 排序规则：utf8mb4_unicode_ci

### 2. 安装步骤

#### 步骤1：创建数据库和表结构
```bash
mysql -u root -p < database_design.sql
```

#### 步骤2：根据需要添加数据
数据库结构创建完成后，您需要根据实际需求添加章节、小节、指标等数据。
可以参考 `init_sample_data.sql` 中的示例格式。

#### 步骤3：设置维护脚本（可选）
```bash
mysql -u root -p < database_maintenance.sql
```

#### 步骤4：清理数据（如需要）
如果需要清空所有数据重新开始：
```bash
mysql -u root -p < clear_database.sql
```

### 3. 验证安装

连接到数据库并执行以下查询验证安装：

```sql
USE hospital_indicator_system;

-- 检查表是否创建成功
SHOW TABLES;

-- 检查表结构
DESCRIBE chapters;
DESCRIBE sections;
DESCRIBE indicators;

-- 检查视图是否创建成功
SHOW FULL TABLES WHERE Table_type = 'VIEW';

-- 验证表为空（初始状态）
SELECT 'chapters' AS table_name, COUNT(*) AS count FROM chapters
UNION ALL
SELECT 'sections', COUNT(*) FROM sections
UNION ALL
SELECT 'indicators', COUNT(*) FROM indicators;
```

## 核心功能

### 1. 指标管理
- 支持层级指标结构（父子关系）
- 指标分类和标签管理
- 目标值设置和完成率计算
- 状态监控（正常/警告/危险）

### 2. 数据采集
- 自动采集（SQL查询）
- 手动录入
- 混合模式
- 数据历史记录

### 3. 审核流程
- 三级审核：科室初审 → 质控办复核 → 医务科终审
- 审核状态跟踪
- 审核意见记录

### 4. 科室管理
- 科室层级结构
- 角色分工（牵头/分子/分母科室）
- 责任人管理

### 5. 用户权限
- 四级权限：管理员/经理/操作员/查看者
- 科室关联
- 操作日志

## 数据库结构

### 核心表

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| indicators | 指标表 | id, name, description, target_value, status |
| tags | 标签表 | name, color, description |
| departments | 科室表 | name, code, type, parent_id |
| users | 用户表 | username, real_name, role, department_id |

### 关联表

| 表名 | 说明 | 关联关系 |
|------|------|----------|
| indicator_tags | 指标标签关联 | 多对多 |
| indicator_departments | 指标科室关联 | 多对多（含角色） |
| indicator_relations | 指标关联关系 | 多对多（含类型） |

### 数据表

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| indicator_data_history | 指标数据历史 | value, data_period, status |
| data_audit_flow | 审核流程 | step, audit_result, audit_comment |

## 常用操作

### 1. 查询指标详情
```sql
SELECT * FROM v_indicator_details WHERE id = '1.2.1';
```

### 2. 添加新指标
```sql
INSERT INTO indicators (id, name, description, target_value, category)
VALUES ('1.3.1', '新指标名称', '指标描述', 100.0, '指标类别');
```

### 3. 关联标签
```sql
INSERT INTO indicator_tags (indicator_id, tag_id)
VALUES ('1.3.1', 1);
```

### 4. 分配科室责任
```sql
INSERT INTO indicator_departments (indicator_id, department_id, role_type, responsibility)
VALUES ('1.3.1', 1, 'lead', '负责指标管理');
```

### 5. 录入数据
```sql
INSERT INTO indicator_data_history (indicator_id, value, data_period, collector_id, status)
VALUES ('1.3.1', 95.5, '2024-03-31', 1, 'draft');
```

### 6. 数据审核
```sql
UPDATE indicator_data_history
SET status = 'approved'
WHERE id = 1;
```

## 维护操作

### 1. 更新指标状态
```sql
CALL UpdateIndicatorStatus();
```

### 2. 清理旧数据
```sql
CALL CleanupOldData(36); -- 保留36个月数据
```

### 3. 性能优化
```sql
ANALYZE TABLE indicators;
OPTIMIZE TABLE indicator_data_history;
```

### 4. 数据完整性检查
执行 `database_maintenance.sql` 中的检查脚本

## 视图说明

### v_indicator_details
整合指标、标签、科室信息的详情视图，简化复杂查询。

### v_indicator_data_stats
统计指标数据状态和完成情况的视图。

## 安全考虑

1. **密码安全**：用户密码使用bcrypt加密存储
2. **权限控制**：基于角色的访问控制
3. **数据完整性**：外键约束和触发器
4. **操作日志**：记录关键操作
5. **软删除**：使用is_active字段而非物理删除

## 性能优化

1. **索引优化**：为常用查询字段建立索引
2. **查询优化**：使用视图简化复杂查询
3. **数据分区**：大表可考虑按时间分区
4. **缓存策略**：应用层实现查询缓存

## 扩展建议

1. **读写分离**：主从复制提高查询性能
2. **分库分表**：按医院或时间维度分片
3. **数据仓库**：历史数据迁移到数据仓库
4. **监控告警**：实时监控数据库性能

## 故障排除

### 常见问题

1. **外键约束错误**
   - 检查关联表中是否存在对应记录
   - 确认数据类型匹配

2. **字符集问题**
   - 确保使用utf8mb4字符集
   - 检查连接字符集设置

3. **性能问题**
   - 检查慢查询日志
   - 分析执行计划
   - 优化索引

### 日志查看
```sql
-- 查看错误日志
SHOW VARIABLES LIKE 'log_error';

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log%';
```

## 联系方式

如有问题或建议，请联系开发团队。

## 版本历史

- v1.0.0 - 初始版本，包含基础功能
- v1.1.0 - 添加维护脚本和性能优化
- v1.2.0 - 增加数据完整性检查和监控功能

## 许可证

本项目采用 MIT 许可证。
