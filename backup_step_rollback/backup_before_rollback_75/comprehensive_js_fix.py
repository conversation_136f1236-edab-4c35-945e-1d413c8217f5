#!/usr/bin/env python3
"""
全面修复JavaScript中的textContent错误
"""

import re
import os

def find_all_textcontent_issues():
    """查找所有可能的textContent问题"""
    print("🔍 查找所有textContent相关问题...")
    
    js_files = [
        'static/js/app.js',
        'templates/indicator_detail.html',
        'templates/admin/indicator_detail.html',
        'templates/admin/indicator_edit.html'
    ]
    
    issues = []
    
    for file_path in js_files:
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # 查找所有textContent设置
            for i, line in enumerate(lines, 1):
                if '.textContent' in line and '=' in line:
                    # 检查是否有安全检查
                    if 'if (' not in line and 'if(' not in line:
                        # 提取元素ID或选择器
                        element_match = re.search(r'getElementById\([\'"]([^\'"]+)[\'"]\)', line)
                        if element_match:
                            element_id = element_match.group(1)
                            issues.append({
                                'file': file_path,
                                'line': i,
                                'code': line.strip(),
                                'element_id': element_id,
                                'type': 'getElementById'
                            })
                        
                        # 查找querySelector
                        selector_match = re.search(r'querySelector\([\'"]([^\'"]+)[\'"]\)', line)
                        if selector_match:
                            selector = selector_match.group(1)
                            issues.append({
                                'file': file_path,
                                'line': i,
                                'code': line.strip(),
                                'element_id': selector,
                                'type': 'querySelector'
                            })
            
        except Exception as e:
            print(f"❌ 读取文件失败 {file_path}: {e}")
    
    return issues

def generate_safe_fixes(issues):
    """生成安全的修复代码"""
    print(f"\n🔧 生成修复方案 (发现 {len(issues)} 个问题)...")
    
    fixes = []
    
    for issue in issues:
        original_code = issue['code']
        element_id = issue['element_id']
        
        if issue['type'] == 'getElementById':
            safe_code = f"""
    const element = document.getElementById('{element_id}');
    if (element) {{
        {original_code}
    }} else {{
        console.warn('Element not found: {element_id}');
    }}"""
        else:  # querySelector
            safe_code = f"""
    const element = document.querySelector('{element_id}');
    if (element) {{
        {original_code.replace(f"document.querySelector('{element_id}')", "element")}
    }} else {{
        console.warn('Element not found: {element_id}');
    }}"""
        
        fixes.append({
            'file': issue['file'],
            'line': issue['line'],
            'original': original_code,
            'safe': safe_code,
            'element_id': element_id
        })
    
    return fixes

def create_universal_safe_functions():
    """创建通用的安全DOM操作函数"""
    return """
// 通用安全DOM操作函数
function safeSetTextContent(elementId, value, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.textContent = value;
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}

function safeGetElement(elementId, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (!element) {
        console.warn(`Element not found: ${elementId}`);
    }
    return element;
}

function safeSetInnerHTML(elementId, html, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.innerHTML = html;
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}

function safeSetStyle(elementId, property, value, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.style[property] = value;
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}

function safeAddClass(elementId, className, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.classList.add(className);
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}

function safeRemoveClass(elementId, className, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.classList.remove(className);
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}
"""

def apply_fixes_to_indicator_detail():
    """修复前端指标详情页面的JavaScript"""
    print("\n🔧 修复前端指标详情页面...")
    
    file_path = 'templates/indicator_detail.html'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找需要修复的textContent设置
        problematic_patterns = [
            (r"document\.getElementById\('([^']+)'\)\.textContent\s*=\s*([^;]+);", 
             r"safeSetTextContent('\1', \2);"),
            (r"(\w+)\.textContent\s*=\s*([^;]+);", 
             r"if (\1) \1.textContent = \2;")
        ]
        
        modified_content = content
        
        for pattern, replacement in problematic_patterns:
            modified_content = re.sub(pattern, replacement, modified_content)
        
        # 在script标签开始处添加安全函数
        safe_functions = create_universal_safe_functions()
        
        script_start = modified_content.find('<script>')
        if script_start != -1:
            insert_pos = script_start + len('<script>')
            modified_content = (modified_content[:insert_pos] + 
                              safe_functions + 
                              modified_content[insert_pos:])
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f"✅ 修复完成: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")
        return False

def apply_fixes_to_admin_detail():
    """修复后端指标详情页面的JavaScript"""
    print("\n🔧 修复后端指标详情页面...")
    
    file_path = 'templates/admin/indicator_detail.html'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加安全函数到页面
        safe_functions = create_universal_safe_functions()
        
        # 查找script标签
        script_pattern = r'(<script[^>]*>)'
        match = re.search(script_pattern, content)
        
        if match:
            script_start = match.end()
            modified_content = (content[:script_start] + 
                              '\n' + safe_functions + '\n' + 
                              content[script_start:])
        else:
            # 如果没有script标签，在页面末尾添加
            modified_content = content + f'\n<script>{safe_functions}</script>\n'
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f"✅ 修复完成: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")
        return False

def create_error_handler_script():
    """创建全局错误处理脚本"""
    error_handler = """
// 全局JavaScript错误处理
window.addEventListener('error', function(event) {
    console.error('JavaScript Error:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
    });
    
    // 如果是textContent相关错误，显示友好提示
    if (event.message.includes('textContent') || event.message.includes('Cannot set properties of null')) {
        console.warn('DOM元素访问错误，可能是页面加载时序问题');
        
        // 延迟重试
        setTimeout(function() {
            console.log('尝试重新初始化页面功能...');
            // 这里可以添加重新初始化逻辑
        }, 1000);
    }
});

// 未捕获的Promise错误
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled Promise Rejection:', event.reason);
});
"""
    
    with open('static/js/error-handler.js', 'w', encoding='utf-8') as f:
        f.write(error_handler)
    
    print("✅ 创建全局错误处理脚本: static/js/error-handler.js")

def main():
    """主修复函数"""
    print("🎯 全面修复JavaScript textContent错误")
    print("=" * 60)
    
    # 查找所有问题
    issues = find_all_textcontent_issues()
    
    if issues:
        print(f"\n📋 发现的问题:")
        for issue in issues[:10]:  # 只显示前10个
            print(f"   {issue['file']}:{issue['line']} - {issue['element_id']}")
        
        if len(issues) > 10:
            print(f"   ... 还有 {len(issues) - 10} 个问题")
    
    # 生成修复方案
    fixes = generate_safe_fixes(issues)
    
    # 应用修复
    success_count = 0
    
    # 修复前端指标详情页面
    if apply_fixes_to_indicator_detail():
        success_count += 1
    
    # 修复后端指标详情页面
    if apply_fixes_to_admin_detail():
        success_count += 1
    
    # 创建错误处理脚本
    create_error_handler_script()
    
    print(f"\n📊 修复结果:")
    print(f"   发现问题: {len(issues)} 个")
    print(f"   修复文件: {success_count} 个")
    print(f"   创建错误处理脚本: 1 个")
    
    print(f"\n🔗 建议测试:")
    print("1. 访问前端指标详情页面")
    print("2. 访问后端指标管理页面")
    print("3. 检查浏览器控制台错误")
    print("4. 测试指标参考功能")
    
    print(f"\n💡 如果仍有错误，请:")
    print("1. 清除浏览器缓存")
    print("2. 重启服务器")
    print("3. 检查网络连接")

if __name__ == "__main__":
    main()
