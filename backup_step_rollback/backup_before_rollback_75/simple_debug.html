<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; color: #1a73e8; }
        .field { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        .field-name { font-weight: bold; color: #333; }
        .field-value { margin-top: 5px; color: #666; }
        .has-data { background: #e8f5e8; border-left: 3px solid #34a853; }
        .no-data { background: #fff3cd; border-left: 3px solid #ffc107; }
        .error { background: #f8d7da; border-left: 3px solid #dc3545; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .button { 
            background: #1a73e8; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 5px;
        }
        .button:hover { background: #1557b0; }
    </style>
</head>
<body>
    <h1>🔍 指标字段调试页面</h1>
    
    <div class="section">
        <h3>测试指标</h3>
        <button class="button" onclick="testIndicator('1.1.1')">测试 1.1.1</button>
        <button class="button" onclick="testIndicator('C-3.1')">测试 C-3.1</button>
    </div>

    <div id="results"></div>

    <script>
        async function testIndicator(indicatorId) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>加载中...</p>';

            try {
                // 获取API数据
                const response = await fetch(`/api/indicators/${indicatorId}?t=${Date.now()}`);
                const result = await response.json();

                if (!result.success) {
                    resultsDiv.innerHTML = `<div class="error">API错误: ${result.error}</div>`;
                    return;
                }

                const indicator = result.data.indicator;
                
                // 检查关键字段
                const fields = [
                    { name: '指标定义', key: 'indicator_definition' },
                    { name: '计算公式', key: 'calculation_formula' },
                    { name: '分子说明', key: 'numerator_description' },
                    { name: '分母说明', key: 'denominator_description' },
                    { name: '统计范围', key: 'statistical_scope' },
                    { name: '数据来源', key: 'data_sources' },
                    { name: '统计频率', key: 'collection_frequency_detail' },
                    { name: '参考值', key: 'reference_value' },
                    { name: '监测分析', key: 'monitoring_analysis' }
                ];

                let html = `
                    <div class="section">
                        <h3>指标 ${indicatorId} - ${indicator.name}</h3>
                        <div class="field">
                            <div class="field-name">指标类型</div>
                            <div class="field-value">${indicator.indicator_type}</div>
                        </div>
                `;

                // 检查每个字段
                fields.forEach(field => {
                    const value = indicator[field.key];
                    const hasValue = value && value.trim() !== '';
                    const cssClass = hasValue ? 'has-data' : 'no-data';
                    
                    html += `
                        <div class="field ${cssClass}">
                            <div class="field-name">${field.name} (${field.key})</div>
                            <div class="field-value">${hasValue ? value : '❌ 无数据'}</div>
                        </div>
                    `;
                });

                // 分析维度
                if (indicator.analysis_dimensions && indicator.analysis_dimensions.length > 0) {
                    html += `
                        <div class="field has-data">
                            <div class="field-name">分析维度 (${indicator.analysis_dimensions.length}个)</div>
                            <div class="field-value">
                    `;
                    
                    indicator.analysis_dimensions.forEach((dim, index) => {
                        html += `<strong>${index + 1}. ${dim.dimension_name}:</strong> ${dim.analysis_content}<br>`;
                    });
                    
                    html += `</div></div>`;
                } else {
                    html += `
                        <div class="field no-data">
                            <div class="field-name">分析维度</div>
                            <div class="field-value">❌ 无分析维度数据</div>
                        </div>
                    `;
                }

                html += `</div>`;

                // 显示原始JSON数据
                html += `
                    <div class="section">
                        <h3>原始API数据</h3>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;

                resultsDiv.innerHTML = html;

            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
            }
        }

        // 页面加载时自动测试1.1.1
        window.addEventListener('load', () => {
            testIndicator('1.1.1');
        });
    </script>
</body>
</html>
