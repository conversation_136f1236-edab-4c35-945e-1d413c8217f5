-- 增强指标表字段，添加更详细的医疗指标信息
-- 基于医院指标说明手册的标准格式

-- 为indicators表添加新字段
ALTER TABLE indicators ADD COLUMN indicator_definition TEXT; -- 指标定义
ALTER TABLE indicators ADD COLUMN calculation_formula TEXT; -- 计算公式（详细）
ALTER TABLE indicators ADD COLUMN numerator_description TEXT; -- 分子说明
ALTER TABLE indicators ADD COLUMN denominator_description TEXT; -- 分母说明
ALTER TABLE indicators ADD COLUMN statistical_scope TEXT; -- 统计范围
ALTER TABLE indicators ADD COLUMN data_sources TEXT; -- 数据来源（详细）
ALTER TABLE indicators ADD COLUMN collection_frequency_detail TEXT; -- 统计频率详细说明
ALTER TABLE indicators ADD COLUMN reference_value TEXT; -- 标准值/参考值
ALTER TABLE indicators ADD COLUMN monitoring_analysis TEXT; -- 监测分析要求
ALTER TABLE indicators ADD COLUMN analysis_dimensions TEXT; -- 分析维度
ALTER TABLE indicators ADD COLUMN quality_requirements TEXT; -- 质量要求
ALTER TABLE indicators ADD COLUMN notes_remarks TEXT; -- 备注说明
ALTER TABLE indicators ADD COLUMN related_policies TEXT; -- 相关政策依据
ALTER TABLE indicators ADD COLUMN improvement_suggestions TEXT; -- 改进建议

-- 创建指标分析维度表
CREATE TABLE IF NOT EXISTS indicator_analysis_dimensions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    indicator_id VARCHAR(20) NOT NULL,
    dimension_type VARCHAR(50) NOT NULL, -- 分析维度类型：时间趋势、科室分布、药物类型等
    dimension_name VARCHAR(100) NOT NULL, -- 维度名称
    dimension_description TEXT, -- 维度描述
    analysis_content TEXT, -- 分析内容
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
);

-- 创建指标参考标准表
CREATE TABLE IF NOT EXISTS indicator_reference_standards (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    indicator_id VARCHAR(20) NOT NULL,
    standard_type VARCHAR(50) NOT NULL, -- 标准类型：国家标准、行业标准、医院标准等
    standard_name VARCHAR(100) NOT NULL, -- 标准名称
    standard_value TEXT, -- 标准值
    standard_description TEXT, -- 标准说明
    source_organization VARCHAR(100), -- 制定机构
    effective_date DATE, -- 生效日期
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
);

-- 创建指标质量要求表
CREATE TABLE IF NOT EXISTS indicator_quality_requirements (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    indicator_id VARCHAR(20) NOT NULL,
    requirement_type VARCHAR(50) NOT NULL, -- 要求类型：数据质量、采集要求、分析要求等
    requirement_title VARCHAR(100) NOT NULL, -- 要求标题
    requirement_content TEXT, -- 要求内容
    priority_level INTEGER DEFAULT 1, -- 优先级：1-高，2-中，3-低
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE
);

-- 插入示例数据（基于图片中的抗菌药物使用强度指标）
INSERT INTO indicators (
    id, name, description, indicator_definition, calculation_formula,
    numerator_description, denominator_description, statistical_scope,
    data_sources, collection_frequency_detail, reference_value,
    monitoring_analysis, unit, lead_department, indicator_type
) VALUES (
    'C-3.1', '抗菌药物使用强度',
    '一定时期内（通常为1年）住院患者使用抗菌药物的总量与患者住院天数的比值，反映医院抗菌药物使用管理水平。',
    '一定时期内（通常为1年）住院患者使用抗菌药物的总量与患者住院天数的比值，反映医院抗菌药物使用管理水平。',
    '抗菌药物使用强度（DDDs）= 住院患者抗菌药物消耗总量（DDD）÷ 患者住院总天数 × 100',
    '住院患者抗菌药物消耗总量（DDD），即统计期内住院患者使用抗菌药物的累计用量，按照WHO制定的限定日剂量（DDD）进行换算。',
    '患者住院总天数，即统计期内所有住院患者的住院天数总和。',
    '医院所有住院患者使用的抗菌药，包括抗细菌药物、抗真菌药物、抗结核药物、抗病毒药物等。',
    '医院信息系统(HIS)、药品管理系统、电子病历系统、抗菌药物监测系统等。',
    '月度统计，季度和年度汇总点分析',
    '≤ 40 DDDs（综合医院），ICU等特殊科室标准值可单独制定',
    '建议对数据进行多维度分析',
    'DDDs', '药学部', 'composite'
) ON CONFLICT(id) DO UPDATE SET
    description = excluded.description,
    indicator_definition = excluded.indicator_definition,
    calculation_formula = excluded.calculation_formula,
    numerator_description = excluded.numerator_description,
    denominator_description = excluded.denominator_description,
    statistical_scope = excluded.statistical_scope,
    data_sources = excluded.data_sources,
    collection_frequency_detail = excluded.collection_frequency_detail,
    reference_value = excluded.reference_value,
    monitoring_analysis = excluded.monitoring_analysis;

-- 插入分析维度数据
INSERT INTO indicator_analysis_dimensions (indicator_id, dimension_type, dimension_name, dimension_description, analysis_content) VALUES
('C-3.1', '时间趋势', '时间趋势', '按月/季度/年度分析抗菌药物使用强度变化趋势', '按月/季度/年度分析抗菌药物使用强度变化趋势'),
('C-3.1', '科室分布', '科室分布', '不同科室抗菌药物使用强度对比分析', '不同科室抗菌药物使用强度对比分析'),
('C-3.1', '药物类型', '药物类型', '不同类型抗菌药物（如广谱、特殊、限制使用等）使用强度分析', '不同类型抗菌药物（如广谱、特殊、限制使用等）使用强度分析'),
('C-3.1', '给药途径', '给药途径', '静脉、口服等不同给药途径抗菌药物使用强度分析', '静脉、口服等不同给药途径抗菌药物使用强度分析'),
('C-3.1', '疾病类型', '疾病类型', '不同疾病类型患者抗菌药物使用强度分析', '不同疾病类型患者抗菌药物使用强度分析');

-- 为1.1.1指标添加示例数据
UPDATE indicators SET
    indicator_definition = '医院经卫生行政部门核定并实际开放使用的床位数量，是医院规模和服务能力的基础指标。',
    statistical_scope = '医院所有科室实际开放并可供患者使用的床位，包括普通病房、ICU、手术室观察床等，不包括临时加床。',
    data_sources = '医院信息系统(HIS)、床位管理系统、护理管理系统等。',
    collection_frequency_detail = '实时统计，日报、月报、季报、年报',
    reference_value = '根据医院等级和功能定位确定，三甲综合医院一般≥500张',
    monitoring_analysis = '建议按科室、病区、床位类型等维度进行分析'
WHERE id = '1.1.1';

-- 为1.1.1指标添加分析维度
INSERT INTO indicator_analysis_dimensions (indicator_id, dimension_type, dimension_name, dimension_description, analysis_content) VALUES
('1.1.1', '科室分布', '科室分布', '按科室分析床位配置情况', '分析各科室床位数量、使用率、周转率等指标'),
('1.1.1', '床位类型', '床位类型', '按床位类型分析配置情况', '普通病床、ICU床位、急诊观察床、手术床等不同类型床位的配置分析'),
('1.1.1', '时间趋势', '时间趋势', '床位数量变化趋势分析', '分析床位数量的月度、季度、年度变化趋势');

-- 提交事务
COMMIT;
