#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理chapter5.xlsx文件，参考第三章的导入规则
提取第五章的小节和指标数据并导入数据库
"""

import pandas as pd
import sqlite3
import re
import sys
import os

def analyze_excel_detailed(file_path):
    """
    详细分析Excel文件的结构
    """
    try:
        print(f"正在详细分析Excel文件: {file_path}")
        df = pd.read_excel(file_path, sheet_name=0)
        
        print(f"数据维度: {df.shape[0]}行 × {df.shape[1]}列")
        print(f"列名: {df.columns.tolist()}")
        
        # 分析小节分布
        print(f"\n=== 小节分布分析 ===")
        section_numbers = df['小节编号'].dropna().unique()
        print(f"发现的小节编号: {sorted(section_numbers)}")
        print(f"小节数量: {len(section_numbers)}")
        
        # 分析指标分布
        print(f"\n=== 指标分布分析 ===")
        level1_indicators = df['一级指标编号'].dropna().unique()
        level2_indicators = df['二级指标编号'].dropna().unique()
        level3_indicators = df['三级指标编号'].dropna().unique()
        
        print(f"一级指标数量: {len(level1_indicators)}")
        print(f"二级指标数量: {len(level2_indicators)}")
        print(f"三级指标数量: {len(level3_indicators)}")
        
        # 分析分子分母数据
        print(f"\n=== 分子分母数据分析 ===")
        numerator_count = df['分子'].notna().sum()
        denominator_count = df['分母'].notna().sum()
        
        print(f"有分子的行数: {numerator_count}")
        print(f"有分母的行数: {denominator_count}")
        
        return df
        
    except Exception as e:
        print(f"分析Excel文件时出错: {e}")
        return None

def process_chapter5_comprehensive(df):
    """
    全面处理第五章数据，参考第三章的处理方式
    """
    sections = []
    indicators = []
    components = []
    
    print("\n开始全面处理第五章数据...")
    
    for index, row in df.iterrows():
        try:
            # 提取小节信息
            section_number = row.get('小节编号', '')
            section_name = row.get('小节名称', '')
            
            if pd.notna(section_number) and pd.notna(section_name):
                section_code = convert_chinese_section_to_code(str(section_number).strip(), chapter_num=5)
                clean_section_name = clean_name(str(section_name).strip())
                
                if section_code and clean_section_name:
                    sections.append({
                        'code': section_code,
                        'name': clean_section_name,
                        'row': index + 1
                    })
            
            # 提取各级指标信息
            level1_id = row.get('一级指标编号', '')
            level1_name = row.get('一级指标名称', '')
            level2_id = row.get('二级指标编号', '')
            level2_name = row.get('二级指标名称', '')
            level3_id = row.get('三级指标编号', '')
            level3_name = row.get('三级指标名称', '')
            
            # 处理一级指标
            if pd.notna(level1_id) and pd.notna(level1_name):
                level1_id_str = str(level1_id).strip()
                level1_name_str = str(level1_name).strip()
                
                if level1_id_str.startswith('5.'):
                    section_code = '.'.join(level1_id_str.split('.')[:2])
                    
                    indicators.append({
                        'id': level1_id_str,
                        'name': level1_name_str,
                        'level': 1,
                        'parent_id': None,
                        'section_code': section_code,
                        'row': index + 1
                    })
            
            # 处理二级指标
            if pd.notna(level2_id) and pd.notna(level2_name):
                level2_id_str = str(level2_id).strip()
                level2_name_str = str(level2_name).strip()
                
                if level2_id_str and level1_id_str:
                    section_code = '.'.join(level1_id_str.split('.')[:2])
                    
                    indicators.append({
                        'id': level2_id_str,
                        'name': level2_name_str,
                        'level': 2,
                        'parent_id': level1_id_str,
                        'section_code': section_code,
                        'row': index + 1
                    })
            
            # 处理三级指标
            if pd.notna(level3_id) and pd.notna(level3_name):
                level3_id_str = str(level3_id).strip()
                level3_name_str = str(level3_name).strip()
                
                if level3_id_str and level2_id_str:
                    section_code = '.'.join(level1_id_str.split('.')[:2])
                    
                    indicators.append({
                        'id': level3_id_str,
                        'name': level3_name_str,
                        'level': 3,
                        'parent_id': level2_id_str,
                        'section_code': section_code,
                        'row': index + 1
                    })
            
            # 处理分子分母数据（使用最底层的指标ID）
            indicator_id = None
            if pd.notna(level3_id) and str(level3_id).strip():
                indicator_id = str(level3_id).strip()
            elif pd.notna(level2_id) and str(level2_id).strip():
                indicator_id = str(level2_id).strip()
            elif pd.notna(level1_id) and str(level1_id).strip():
                indicator_id = str(level1_id).strip()
            
            if indicator_id and indicator_id.startswith('5.'):
                # 处理分子信息
                numerator = row.get('分子', '')
                if pd.notna(numerator) and str(numerator).strip():
                    components.append({
                        'indicator_id': indicator_id,
                        'component_type': 'numerator',
                        'name': str(numerator).strip(),
                        'unit': str(row.get('分子单位', '')).strip() if pd.notna(row.get('分子单位', '')) else '',
                        'lead_department': str(row.get('分子牵头科室', '')).strip() if pd.notna(row.get('分子牵头科室', '')) else '',
                        'data_source': str(row.get('分子数据来源', '')).strip() if pd.notna(row.get('分子数据来源', '')) else '',
                        'logic_definition': str(row.get('分子逻辑定义', '')).strip() if pd.notna(row.get('分子逻辑定义', '')) else '',
                        'row': index + 1
                    })
                
                # 处理分母信息
                denominator = row.get('分母', '')
                if pd.notna(denominator) and str(denominator).strip():
                    components.append({
                        'indicator_id': indicator_id,
                        'component_type': 'denominator',
                        'name': str(denominator).strip(),
                        'unit': str(row.get('分母单位', '')).strip() if pd.notna(row.get('分母单位', '')) else '',
                        'lead_department': str(row.get('分母牵头科室', '')).strip() if pd.notna(row.get('分母牵头科室', '')) else '',
                        'data_source': str(row.get('分母数据来源', '')).strip() if pd.notna(row.get('分母数据来源', '')) else '',
                        'logic_definition': str(row.get('分母逻辑定义', '')).strip() if pd.notna(row.get('分母逻辑定义', '')) else '',
                        'row': index + 1
                    })
                    
        except Exception as e:
            print(f"处理第{index+1}行时出错: {e}")
            continue
    
    return sections, indicators, components

def convert_chinese_section_to_code(chinese_number, chapter_num=5):
    """
    将中文小节编号转换为数字编码
    """
    chinese_to_arabic = {
        '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
        '六': '6', '七': '7', '八': '8', '九': '9', '十': '10',
        '十一': '11', '十二': '12', '十三': '13', '十四': '14', '十五': '15',
        '十六': '16', '十七': '17', '十八': '18', '十九': '19', '二十': '20'
    }
    
    clean_number = chinese_number.replace('、', '').replace('，', '').replace('。', '').strip()
    
    if clean_number in chinese_to_arabic:
        return f"{chapter_num}.{chinese_to_arabic[clean_number]}"
    
    return None

def clean_name(name):
    """
    清理名称，去除括号内容
    """
    cleaned = re.sub(r'[（(].*?[）)]', '', name)
    return cleaned.strip()

def remove_duplicates(data_list, key_func):
    """
    去除重复数据
    """
    seen = set()
    unique_data = []
    
    for item in data_list:
        if callable(key_func):
            key = key_func(item)
        else:
            key = item[key_func]
            
        if key not in seen:
            seen.add(key)
            unique_data.append(item)
    
    return unique_data

def clear_and_insert_chapter5_data(sections, indicators, components, db_path):
    """
    清除并重新插入第五章数据
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取第五章的ID
        cursor.execute("SELECT id FROM chapters WHERE code = '5'")
        chapter_result = cursor.fetchone()
        if not chapter_result:
            print("错误：数据库中未找到第五章数据")
            return
        
        chapter_id = chapter_result[0]
        
        print("清除现有第五章数据...")
        # 清除现有数据
        cursor.execute("DELETE FROM indicator_components WHERE indicator_id LIKE '5.%'")
        cursor.execute("DELETE FROM indicators WHERE chapter_id = ?", (chapter_id,))
        cursor.execute("DELETE FROM sections WHERE chapter_id = ?", (chapter_id,))
        
        print("插入小节数据...")
        # 插入小节数据
        for i, section in enumerate(sections):
            cursor.execute("""
                INSERT INTO sections 
                (chapter_id, code, name, description, sort_order, is_active, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            """, (
                chapter_id,
                section['code'],
                section['name'],
                f"第五章小节：{section['name']}",
                i + 1
            ))
        
        print("插入指标数据...")
        # 按级别排序插入指标
        sorted_indicators = sorted(indicators, key=lambda x: (x['level'], x['id']))
        
        for i, indicator in enumerate(sorted_indicators):
            # 查找对应的小节ID
            cursor.execute("SELECT id FROM sections WHERE code = ? AND chapter_id = ?", 
                         (indicator['section_code'], chapter_id))
            section_result = cursor.fetchone()
            section_id = section_result[0] if section_result else None
            
            cursor.execute("""
                INSERT INTO indicators 
                (id, name, description, parent_id, chapter_id, section_id, category, sort_order, is_active, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            """, (
                indicator['id'],
                indicator['name'],
                f"第五章{indicator['level']}级指标：{indicator['name']}",
                indicator['parent_id'],
                chapter_id,
                section_id,
                '重点医疗技术临床应用质量控制',
                i + 1
            ))
        
        print("插入分子分母数据...")
        # 插入分子分母数据
        for i, component in enumerate(components):
            cursor.execute("""
                INSERT INTO indicator_components 
                (indicator_id, component_type, name, definition, unit, data_source, lead_department, logic_definition, sort_order, is_active, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            """, (
                component['indicator_id'],
                component['component_type'],
                component['name'],
                component['name'],
                component['unit'],
                component['data_source'],
                component['lead_department'],
                component['logic_definition'],
                i + 1
            ))
        
        conn.commit()
        
        print(f"✅ 成功插入:")
        print(f"   小节: {len(sections)}个")
        print(f"   指标: {len(indicators)}个")
        print(f"   分子分母: {len(components)}个")
        
    except Exception as e:
        print(f"插入数据时出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if conn:
            conn.close()

def main():
    file_path = "chapter5.xlsx"
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    print("=" * 80)
    print("🏥 医院等级评审指标管理系统 - 第五章数据处理")
    print("=" * 80)
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件 {file_path} 不存在")
        return
    
    if not os.path.exists(db_path):
        print(f"错误：数据库文件 {db_path} 不存在")
        return
    
    # 详细分析Excel文件
    df = analyze_excel_detailed(file_path)
    if df is None:
        return
    
    # 全面处理数据
    sections, indicators, components = process_chapter5_comprehensive(df)
    
    # 去重
    unique_sections = remove_duplicates(sections, 'code')
    unique_indicators = remove_duplicates(indicators, 'id')
    unique_components = remove_duplicates(components, lambda x: (x['indicator_id'], x['component_type']))
    
    print(f"\n=== 处理结果 ===")
    print(f"小节: {len(unique_sections)}个")
    print(f"指标: {len(unique_indicators)}个")
    print(f"分子分母: {len(unique_components)}个")
    
    # 清除并重新插入数据
    clear_and_insert_chapter5_data(unique_sections, unique_indicators, unique_components, db_path)
    
    print("\n✅ 第五章数据处理完成！")

if __name__ == "__main__":
    main()
