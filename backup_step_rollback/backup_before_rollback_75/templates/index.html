{% extends "base.html" %}

{% block title %}医院等级评审指标说明手册 - 首页{% endblock %}

{% block content %}
<!-- 统计概览 -->
<div class="stats-overview" id="statsOverview">
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-book"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="chapterCount">-</div>
                <div class="stat-label">章节</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-list"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="sectionCount">-</div>
                <div class="stat-label">小节</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="indicatorCount">-</div>
                <div class="stat-label">指标</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-calculator"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="componentCount">-</div>
                <div class="stat-label">分子分母</div>
            </div>
        </div>
    </div>
</div>

<!-- 工具栏 -->
<div class="toolbar">
    <div class="toolbar-left">
        <div class="view-toggle">
            <button class="view-btn active" data-view="card">
                <i class="fas fa-th-large"></i>
                卡片视图
            </button>
            <button class="view-btn" data-view="list">
                <i class="fas fa-list"></i>
                列表视图
            </button>
        </div>
    </div>

    <div class="toolbar-right">
        <div class="filter-controls">
            <select class="filter-select" id="chapterFilter">
                <option value="">所有章节</option>
            </select>
            <select class="filter-select" id="sectionFilter">
                <option value="">所有小节</option>
            </select>
        </div>
        <button class="btn btn-outline" id="refreshBtn">
            <i class="fas fa-sync-alt btn-icon"></i>
            刷新
        </button>
    </div>
</div>

<!-- 章节概览 -->
<div class="chapter-overview" id="chapterOverview">
    <h2 class="section-title">
        <i class="fas fa-book section-title-icon"></i>
        章节概览
    </h2>
    <div class="chapter-grid" id="chapterGrid">
        <!-- 动态加载章节卡片 -->
    </div>
</div>

<!-- 指标列表 -->
<div class="indicator-section">
    <div class="section-header">
        <h2 class="section-title">
            <i class="fas fa-chart-line section-title-icon"></i>
            指标列表
        </h2>
        <div class="section-actions">
            <button class="btn btn-outline" id="expandAllBtn">
                <i class="fas fa-expand-arrows-alt btn-icon"></i>
                展开全部
            </button>
            <button class="btn btn-outline" id="collapseAllBtn">
                <i class="fas fa-compress-arrows-alt btn-icon"></i>
                收起全部
            </button>
        </div>
    </div>

    <!-- 卡片视图 -->
    <div class="card-view" id="cardView">
        <div class="card-grid" id="indicatorCards">
            <!-- 动态加载指标卡片 -->
        </div>
    </div>

    <!-- 列表视图 -->
    <div class="list-view" id="listView">
        <div class="list-container">
            <div class="list-header">
                <div class="list-col-id">编号</div>
                <div class="list-col-title">指标名称</div>
                <div class="list-col-type">指标类型</div>
                <div class="list-col-desc">描述</div>
                <div class="list-col-tags">标签</div>
                <div class="list-col-actions">操作</div>
            </div>
            <div class="list-body" id="indicatorList">
                <!-- 动态加载指标列表 -->
            </div>
        </div>
    </div>

    <!-- 分页控件 -->
    <div class="pagination" id="pagination">
        <!-- 动态加载分页 -->
    </div>
</div>

<!-- 快速操作面板 -->
<div class="quick-actions">
    <button class="quick-action-btn" title="回到顶部" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </button>
    <button class="quick-action-btn" title="随机指标" id="randomIndicator">
        <i class="fas fa-random"></i>
    </button>
    <button class="quick-action-btn" title="收藏夹" id="favorites">
        <i class="fas fa-star"></i>
    </button>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面初始化 - 使用app.js中的方法
document.addEventListener('DOMContentLoaded', function() {
    // 确保app对象已经初始化
    if (window.app) {
        // 只加载必要的数据，不自动加载所有指标
        window.app.loadStatistics();
        window.app.loadChapters();
        window.app.bindHomePageEvents();
    }
});

// 查看章节详情 - 委托给app.js
function viewChapter(chapterCode) {
    if (window.app) {
        window.app.selectChapter(chapterCode);
        // 滚动到指标列表
        document.querySelector('.indicator-section').scrollIntoView({
            behavior: 'smooth'
        });
    }
}

// 查看指标详情 - 委托给app.js
function viewIndicatorDetail(indicatorId) {
    if (window.app) {
        window.app.showIndicatorDetail(indicatorId);
    }
}

// 切换收藏 - 委托给app.js
function toggleFavorite(indicatorId) {
    if (window.app) {
        window.app.toggleFavorite(indicatorId);
    }
}

// 切换子指标显示/隐藏 - 委托给app.js
function toggleIndicatorChildren(indicatorId) {
    if (window.app) {
        window.app.toggleIndicatorChildren(indicatorId);
    }
}


</script>
{% endblock %}
