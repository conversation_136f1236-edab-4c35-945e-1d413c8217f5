{% extends "admin/base.html" %}

{% block title %}章节管理 - 医院指标后台管理系统{% endblock %}

{% block breadcrumb %}
<span>后台管理</span> / <span>章节管理</span>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="page-title-section">
        <h1 class="page-title">
            📚 章节管理
        </h1>
        <p class="page-subtitle">管理医院指标体系的章节结构</p>
    </div>
    <div class="page-actions">
        <button class="md-button filled" onclick="addChapter()">
            ➕ 添加章节
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon primary">
            📁
        </div>
        <div class="stat-content">
            <div class="stat-number">{{ chapters|length }}</div>
            <div class="stat-label">总章节数</div>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon success">
            📋
        </div>
        <div class="stat-content">
            <div class="stat-number">{{ chapters|sum(attribute='section_count') or 0 }}</div>
            <div class="stat-label">总小节数</div>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon warning">
            📊
        </div>
        <div class="stat-content">
            <div class="stat-number">{{ chapters|sum(attribute='indicator_count') or 0 }}</div>
            <div class="stat-label">总指标数</div>
        </div>
    </div>
</div>

<!-- 操作工具栏 -->
<div class="toolbar">
    <div class="toolbar-left">
        <div class="search-container">
            <span class="search-icon">🔍</span>
            <input type="text" class="search-input" placeholder="搜索章节..." id="searchInput">
        </div>
        <button class="md-button outlined" onclick="batchEdit()" id="batchEditBtn" style="display: none;">
            ✏️ 批量编辑 (<span id="selectedCount">0</span>)
        </button>
    </div>
    <div class="toolbar-right">
        <button class="md-button text" onclick="toggleView()">
            <span id="viewIcon">📋</span>
            <span id="viewText">列表视图</span>
        </button>
        <div class="sort-dropdown">
            <select class="md-select" id="sortSelect">
                <option value="code">按编号排序</option>
                <option value="name">按名称排序</option>
                <option value="created_at">按创建时间排序</option>
            </select>
        </div>
    </div>
</div>

<!-- 章节列表 -->
<div class="data-table-container" id="tableView">
    <div class="data-table">
        <table>
            <thead>
                <tr>
                    <th class="checkbox-column">
                        <label class="md-checkbox">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            <span class="checkmark"></span>
                        </label>
                    </th>
                    <th class="sortable" data-sort="code">
                        编号 ↕️
                    </th>
                    <th class="sortable" data-sort="name">
                        章节信息 ↕️
                    </th>
                    <th>主题</th>
                    <th class="sortable" data-sort="section_count">
                        小节数 ↕️
                    </th>
                    <th class="sortable" data-sort="indicator_count">
                        指标数 ↕️
                    </th>
                    <th class="sortable" data-sort="created_at">
                        创建时间 ↕️
                    </th>
                    <th class="actions-column">操作</th>
                </tr>
            </thead>
            <tbody>
                {% for chapter in chapters %}
                <tr class="table-row" data-chapter-id="{{ chapter.id }}">
                    <td class="checkbox-column">
                        <label class="md-checkbox">
                            <input type="checkbox" class="chapter-checkbox" value="{{ chapter.id }}" onchange="updateSelection()">
                            <span class="checkmark"></span>
                        </label>
                    </td>
                    <td>
                        <div class="chapter-code" style="background: {{ chapter.color }}15; color: {{ chapter.color }};">
                            {{ chapter.code }}
                        </div>
                    </td>
                    <td>
                        <div class="chapter-info">
                            <div class="chapter-icon" style="background: {{ chapter.color }}15; color: {{ chapter.color }};">
                                📁
                            </div>
                            <div class="chapter-details">
                                <div class="chapter-name">{{ chapter.name }}</div>
                                {% if chapter.description %}
                                <div class="chapter-description">{{ chapter.description[:60] }}{% if chapter.description|length > 60 %}...{% endif %}</div>
                                {% endif %}
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="theme-info">
                            <div class="color-swatch" style="background: {{ chapter.color }};"></div>
                            <code class="color-code">{{ chapter.color }}</code>
                        </div>
                    </td>
                    <td>
                        <div class="metric-badge sections">
                            <span class="metric-number">{{ chapter.section_count or 0 }}</span>
                            <span class="metric-label">小节</span>
                        </div>
                    </td>
                    <td>
                        <div class="metric-badge indicators">
                            <span class="metric-number">{{ chapter.indicator_count or 0 }}</span>
                            <span class="metric-label">指标</span>
                        </div>
                    </td>
                    <td>
                        <div class="date-info">
                            {{ chapter.created_at[:10] if chapter.created_at else '-' }}
                        </div>
                    </td>
                    <td class="actions-column">
                        <div class="action-buttons">
                            <button class="icon-button" onclick="editChapter('{{ chapter.id }}')" title="编辑章节">
                                ✏️
                            </button>
                            <a href="{{ url_for('admin_sections', chapter_id=chapter.id) }}" class="icon-button" title="查看小节">
                                📋
                            </a>
                            <button class="icon-button error" onclick="deleteChapter('{{ chapter.id }}')" title="删除章节">
                                🗑️
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}

                {% if not chapters %}
                <tr>
                    <td colspan="8" class="empty-state">
                        <div class="empty-content">
                            <span class="empty-icon">📂</span>
                            <div class="empty-title">暂无章节数据</div>
                            <div class="empty-subtitle">点击"添加章节"开始创建第一个章节</div>
                            <button class="md-button filled" onclick="addChapter()">
                                ➕ 添加章节
                            </button>
                        </div>
                    </td>
                </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

<!-- 卡片视图 -->
<div class="cards-grid" id="cardsView" style="display: none;">
    {% for chapter in chapters %}
    <div class="chapter-card" data-chapter-id="{{ chapter.id }}">
        <div class="card-header" style="background: {{ chapter.color }}15;">
            <div class="card-icon" style="color: {{ chapter.color }};">
                📁
            </div>
            <div class="card-actions">
                <button class="icon-button" onclick="editChapter('{{ chapter.id }}')" title="编辑">
                    ✏️
                </button>
                <button class="icon-button error" onclick="deleteChapter('{{ chapter.id }}')" title="删除">
                    🗑️
                </button>
            </div>
        </div>
        <div class="card-content">
            <div class="card-title">{{ chapter.name }}</div>
            <div class="card-code">{{ chapter.code }}</div>
            {% if chapter.description %}
            <div class="card-description">{{ chapter.description }}</div>
            {% endif %}
            <div class="card-metrics">
                <div class="metric">
                    <span class="metric-number">{{ chapter.section_count or 0 }}</span>
                    <span class="metric-label">小节</span>
                </div>
                <div class="metric">
                    <span class="metric-number">{{ chapter.indicator_count or 0 }}</span>
                    <span class="metric-label">指标</span>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <a href="{{ url_for('admin_sections', chapter_id=chapter.id) }}" class="md-button outlined">
                📋 查看小节
            </a>
        </div>
    </div>
    {% endfor %}
</div>

<!-- 编辑模态框 -->
<div id="editModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 600px;">
        <div class="modal-header">
            <h3 id="modalTitle">编辑章节</h3>
            <button class="modal-close" onclick="closeModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="chapterForm">
                <input type="hidden" id="chapterId" name="id">

                <div class="form-row">
                    <div class="form-group">
                        <label for="chapterCode">章节编号 *</label>
                        <input type="text" id="chapterCode" name="code" required placeholder="例如：第一章">
                    </div>
                    <div class="form-group">
                        <label for="chapterName">章节名称 *</label>
                        <input type="text" id="chapterName" name="name" required placeholder="例如：医院功能与任务">
                    </div>
                </div>

                <div class="form-group">
                    <label for="chapterDescription">章节描述</label>
                    <textarea id="chapterDescription" name="description" rows="3" placeholder="请输入章节描述"></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="chapterIcon">图标类名</label>
                        <input type="text" id="chapterIcon" name="icon" placeholder="例如：fas fa-hospital">
                        <small style="color: #6c757d;">使用 FontAwesome 图标类名</small>
                    </div>
                    <div class="form-group">
                        <label for="chapterColor">主题颜色</label>
                        <input type="color" id="chapterColor" name="color" value="#3498db">
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="admin-btn admin-btn-primary">
                        💾 保存
                    </button>
                    <button type="button" class="admin-btn" onclick="closeModal()">取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Page Layout */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--md-outline-variant);
}

.page-title-section {
    flex: 1;
}

.page-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0 0 8px 0;
    font-size: 32px;
    font-weight: 400;
    color: var(--md-on-background);
    letter-spacing: -0.5px;
}

.page-icon {
    font-size: 36px;
    color: var(--md-primary);
}

.page-subtitle {
    margin: 0;
    font-size: 16px;
    color: var(--md-on-surface-variant);
    font-weight: 400;
}

.page-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* Toolbar */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 20px;
    background: var(--md-surface);
    border-radius: var(--md-corner-lg);
    box-shadow: var(--md-elevation-1);
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 12px;
    color: var(--md-on-surface-variant);
    font-size: 20px;
}

.search-input {
    padding: 12px 12px 12px 44px;
    border: 1px solid var(--md-outline-variant);
    border-radius: var(--md-corner-xl);
    background: var(--md-surface-variant);
    font-size: 14px;
    width: 280px;
    transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

.search-input:focus {
    outline: none;
    border-color: var(--md-primary);
    background: var(--md-surface);
    box-shadow: 0 0 0 2px var(--md-primary-container);
}

.md-select {
    padding: 10px 16px;
    border: 1px solid var(--md-outline-variant);
    border-radius: var(--md-corner-lg);
    background: var(--md-surface);
    font-size: 14px;
    color: var(--md-on-surface);
    cursor: pointer;
}

/* Data Table */
.data-table-container {
    background: var(--md-surface);
    border-radius: var(--md-corner-lg);
    box-shadow: var(--md-elevation-1);
    overflow: hidden;
}

.data-table table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: var(--md-surface-container);
    padding: 16px 20px;
    text-align: left;
    font-weight: 500;
    font-size: 14px;
    color: var(--md-on-surface-variant);
    border-bottom: 1px solid var(--md-outline-variant);
    position: relative;
}

.data-table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

.data-table th.sortable:hover {
    background: var(--md-surface-container-high);
}

.sort-icon {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    opacity: 0.5;
    transition: opacity var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

.data-table th.sortable:hover .sort-icon {
    opacity: 1;
}

.data-table td {
    padding: 16px 20px;
    border-bottom: 1px solid var(--md-outline-variant);
    vertical-align: middle;
}

.table-row {
    transition: background-color var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

.table-row:hover {
    background: var(--md-surface-container);
}

.checkbox-column {
    width: 60px;
    text-align: center;
}

.actions-column {
    width: 140px;
}

/* Custom Checkbox */
.md-checkbox {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.md-checkbox input {
    opacity: 0;
    position: absolute;
    width: 0;
    height: 0;
}

.checkmark {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--md-outline);
    border-radius: var(--md-corner-xs);
    position: relative;
    transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

.md-checkbox input:checked + .checkmark {
    background: var(--md-primary);
    border-color: var(--md-primary);
}

.md-checkbox input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--md-on-primary);
    font-size: 12px;
    font-weight: bold;
}

/* Chapter Info */
.chapter-code {
    display: inline-block;
    padding: 6px 12px;
    border-radius: var(--md-corner-lg);
    font-weight: 500;
    font-size: 13px;
    min-width: 60px;
    text-align: center;
}

.chapter-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.chapter-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--md-corner-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.chapter-details {
    flex: 1;
}

.chapter-name {
    font-weight: 500;
    font-size: 15px;
    color: var(--md-on-surface);
    margin-bottom: 2px;
}

.chapter-description {
    font-size: 13px;
    color: var(--md-on-surface-variant);
    line-height: 1.4;
}

/* Theme Info */
.theme-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.color-swatch {
    width: 24px;
    height: 24px;
    border-radius: var(--md-corner-xs);
    border: 1px solid var(--md-outline-variant);
}

.color-code {
    font-family: var(--md-font-family-mono);
    font-size: 11px;
    background: var(--md-surface-variant);
    padding: 2px 6px;
    border-radius: var(--md-corner-xs);
    color: var(--md-on-surface-variant);
}

/* Metric Badges */
.metric-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: var(--md-corner-lg);
    font-size: 13px;
}

.metric-badge.sections {
    background: var(--md-secondary-container);
    color: var(--md-on-secondary-container);
}

.metric-badge.indicators {
    background: var(--md-tertiary-container);
    color: var(--md-on-tertiary-container);
}

.metric-number {
    font-weight: 600;
}

.metric-label {
    font-size: 11px;
    opacity: 0.8;
}

/* Date Info */
.date-info {
    font-size: 13px;
    color: var(--md-on-surface-variant);
    font-family: var(--md-font-family-mono);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 4px;
}

.icon-button.error {
    color: var(--md-error);
}

.icon-button.error:hover {
    background-color: var(--md-error-container);
    color: var(--md-on-error-container);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 80px 40px;
}

.empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.empty-icon {
    font-size: 64px;
    color: var(--md-outline);
    opacity: 0.5;
}

.empty-title {
    font-size: 18px;
    font-weight: 500;
    color: var(--md-on-surface);
}

.empty-subtitle {
    font-size: 14px;
    color: var(--md-on-surface-variant);
    margin-bottom: 8px;
}

/* Cards Grid */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
}

.chapter-card {
    background: var(--md-surface);
    border-radius: var(--md-corner-lg);
    box-shadow: var(--md-elevation-1);
    overflow: hidden;
    transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

.chapter-card:hover {
    box-shadow: var(--md-elevation-2);
    transform: translateY(-2px);
}

.card-header {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-icon {
    font-size: 32px;
}

.card-actions {
    display: flex;
    gap: 4px;
}

.card-content {
    padding: 0 20px 20px;
}

.card-title {
    font-size: 18px;
    font-weight: 500;
    color: var(--md-on-surface);
    margin-bottom: 4px;
}

.card-code {
    font-size: 13px;
    color: var(--md-on-surface-variant);
    margin-bottom: 12px;
}

.card-description {
    font-size: 14px;
    color: var(--md-on-surface-variant);
    line-height: 1.5;
    margin-bottom: 16px;
}

.card-metrics {
    display: flex;
    gap: 16px;
}

.metric {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.card-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--md-outline-variant);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: var(--md-surface);
    border-radius: var(--md-corner-xl);
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--md-elevation-5);
}

.modal-header {
    padding: 24px;
    border-bottom: 1px solid var(--md-outline-variant);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: var(--md-on-surface);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--md-on-surface-variant);
    padding: 8px;
    border-radius: var(--md-corner-full);
    transition: background-color var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

.modal-close:hover {
    background: var(--md-surface-variant);
}

.modal-body {
    padding: 24px;
}

.form-row {
    display: flex;
    gap: 16px;
}

.form-group {
    flex: 1;
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--md-on-surface);
    font-size: 14px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--md-outline-variant);
    border-radius: var(--md-corner-lg);
    font-size: 14px;
    font-family: var(--md-font-family);
    background: var(--md-surface-variant);
    color: var(--md-on-surface);
    box-sizing: border-box;
    transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--md-primary);
    background: var(--md-surface);
    box-shadow: 0 0 0 2px var(--md-primary-container);
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid var(--md-outline-variant);
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .toolbar {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .toolbar-left,
    .toolbar-right {
        justify-content: space-between;
    }

    .search-input {
        width: 100%;
    }

    .data-table-container {
        overflow-x: auto;
    }

    .cards-grid {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let currentView = 'table';

// 选择功能
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.chapter-checkbox');
    checkboxes.forEach(cb => cb.checked = selectAll.checked);
    updateSelection();
}

function updateSelection() {
    const checkboxes = document.querySelectorAll('.chapter-checkbox');
    const selected = document.querySelectorAll('.chapter-checkbox:checked');
    const batchEditBtn = document.getElementById('batchEditBtn');
    const selectedCount = document.getElementById('selectedCount');

    if (selected.length > 0) {
        batchEditBtn.style.display = 'flex';
        selectedCount.textContent = selected.length;
    } else {
        batchEditBtn.style.display = 'none';
    }

    // 更新全选状态
    const selectAll = document.getElementById('selectAll');
    selectAll.indeterminate = selected.length > 0 && selected.length < checkboxes.length;
    selectAll.checked = selected.length === checkboxes.length && checkboxes.length > 0;
}

// 视图切换
function toggleView() {
    const tableView = document.getElementById('tableView');
    const cardsView = document.getElementById('cardsView');
    const viewIcon = document.getElementById('viewIcon');
    const viewText = document.getElementById('viewText');

    if (currentView === 'table') {
        tableView.style.display = 'none';
        cardsView.style.display = 'grid';
        viewIcon.textContent = 'view_module';
        viewText.textContent = '卡片视图';
        currentView = 'cards';
    } else {
        tableView.style.display = 'block';
        cardsView.style.display = 'none';
        viewIcon.textContent = 'view_list';
        viewText.textContent = '列表视图';
        currentView = 'table';
    }
}

// 搜索功能
document.getElementById('searchInput').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('.table-row');
    const cards = document.querySelectorAll('.chapter-card');

    // 搜索表格行
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });

    // 搜索卡片
    cards.forEach(card => {
        const text = card.textContent.toLowerCase();
        card.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// 排序功能
document.getElementById('sortSelect').addEventListener('change', function(e) {
    const sortBy = e.target.value;
    // TODO: 实现排序功能
    console.log('排序方式:', sortBy);
});

// 表格排序
document.querySelectorAll('.sortable').forEach(header => {
    header.addEventListener('click', function() {
        const sortBy = this.dataset.sort;
        // TODO: 实现表格排序
        console.log('表格排序:', sortBy);
    });
});

// 章节管理
function addChapter() {
    document.getElementById('modalTitle').textContent = '添加章节';
    document.getElementById('chapterForm').reset();
    document.getElementById('chapterId').value = '';
    document.getElementById('chapterColor').value = '#1976d2';
    document.getElementById('editModal').style.display = 'flex';
}

function editChapter(id) {
    document.getElementById('modalTitle').textContent = '编辑章节';
    document.getElementById('chapterId').value = id;
    document.getElementById('editModal').style.display = 'flex';

    // TODO: 通过AJAX获取章节数据并填充表单
    // loadChapterData(id);
}

function deleteChapter(id) {
    if (confirm('确定要删除这个章节吗？\n\n此操作将同时删除相关的小节和指标数据，且无法撤销。')) {
        // TODO: 实现删除功能
        console.log('删除章节:', id);
        // deleteChapterData(id);
    }
}

function closeModal() {
    document.getElementById('editModal').style.display = 'none';
}

function batchEdit() {
    const selected = document.querySelectorAll('.chapter-checkbox:checked');
    if (selected.length === 0) {
        alert('请先选择要编辑的章节');
        return;
    }

    const ids = Array.from(selected).map(cb => cb.value);
    console.log('批量编辑章节:', ids);

    // TODO: 实现批量编辑功能
    // showBatchEditModal(ids);
}

// 表单提交
document.getElementById('chapterForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());

    console.log('保存章节数据:', data);

    // TODO: 实现保存功能
    // saveChapterData(data);

    // 临时关闭模态框
    closeModal();
});

// 点击模态框外部关闭
document.getElementById('editModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    // ESC 关闭模态框
    if (e.key === 'Escape') {
        closeModal();
    }

    // Ctrl+N 添加新章节
    if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        addChapter();
    }
});

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化选择状态
    updateSelection();

    // 添加表格行悬停效果
    document.querySelectorAll('.table-row').forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.01)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});

// 工具函数
function showNotification(message, type = 'success') {
    // TODO: 实现通知功能
    console.log(`${type}: ${message}`);
}

function formatDate(dateString) {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('zh-CN');
}

// 数据加载函数（待实现）
async function loadChapterData(id) {
    try {
        // const response = await fetch(`/api/chapters/${id}`);
        // const data = await response.json();
        // 填充表单数据
        console.log('加载章节数据:', id);
    } catch (error) {
        console.error('加载章节数据失败:', error);
        showNotification('加载数据失败', 'error');
    }
}

async function saveChapterData(data) {
    try {
        const url = data.id ? `/api/chapters/${data.id}` : '/api/chapters';
        const method = data.id ? 'PUT' : 'POST';

        // const response = await fetch(url, {
        //     method: method,
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(data)
        // });

        console.log('保存章节数据:', data);
        showNotification(data.id ? '章节更新成功' : '章节创建成功');

        // 刷新页面或更新表格
        // location.reload();
    } catch (error) {
        console.error('保存章节数据失败:', error);
        showNotification('保存失败', 'error');
    }
}

async function deleteChapterData(id) {
    try {
        // const response = await fetch(`/api/chapters/${id}`, {
        //     method: 'DELETE'
        // });

        console.log('删除章节:', id);
        showNotification('章节删除成功');

        // 刷新页面或移除表格行
        // location.reload();
    } catch (error) {
        console.error('删除章节失败:', error);
        showNotification('删除失败', 'error');
    }
}
</script>
{% endblock %}
