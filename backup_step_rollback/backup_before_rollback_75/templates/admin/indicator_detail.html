{% extends "admin/base.html" %}

{% block title %}指标详情 - {{ indicator_id }}{% endblock %}

{% block content %}
<div class="indicator-detail-container">
    <!-- 加载状态 -->
    <div id="loading-state" class="loading-spinner">
        <div class="spinner"></div>
    </div>

    <!-- 错误状态 -->
    <div id="error-state" style="display: none;">
        <div class="error-message">
            <h3>加载失败</h3>
            <p id="error-message">无法加载指标详情，请稍后重试。</p>
            <button class="md-button outlined" onclick="loadIndicatorDetail()">
                <i class="fas fa-redo"></i>
                <span>重新加载</span>
            </button>
        </div>
    </div>

    <!-- 主要内容 -->
    <div id="main-content" style="display: none;">
        <!-- 页面头部 -->
        <div class="detail-header">
            <div class="breadcrumb">
                <a href="/admin/dashboard">后台管理</a> /
                <a href="/admin/indicators">指标管理</a> /
                <span id="indicator-id-breadcrumb">{{ indicator_id }}</span>
            </div>
            <h1 id="indicator-title">
                <span class="indicator-id">{{ indicator_id }}</span> -
                <span id="indicator-name">加载中...</span>
            </h1>
            <div class="action-buttons">
                <a href="/admin/indicators" class="md-button outlined">
                    <i class="fas fa-arrow-left"></i>
                    <span>返回列表</span>
                </a>
                <button class="md-button filled" onclick="editIndicator()">
                    <i class="fas fa-edit"></i>
                    <span>编辑指标</span>
                </button>
                <button class="md-button filled-tonal" onclick="addComponent()">
                    <i class="fas fa-plus"></i>
                    <span>添加组件</span>
                </button>
                <a href="/" class="md-button outlined">
                    <i class="fas fa-external-link-alt"></i>
                    <span>前端系统</span>
                </a>
            </div>
        </div>

        <!-- 基本信息 -->
        <div class="info-card">
            <h3>📋 基本信息</h3>
            <div class="info-grid" id="basic-info">
                <!-- 动态加载 -->
            </div>

            <!-- 指标类型编辑器 -->
            <div class="indicator-type-editor" style="margin-top: 20px; padding: 16px; background: var(--md-surface-variant); border-radius: var(--md-corner-md); border: 1px solid var(--md-outline-variant);">
                <div class="info-label" style="margin-bottom: 12px;">指标类型</div>
                <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
                    <select id="indicator-type-selector" onchange="handleIndicatorTypeChange()" style="padding: 10px 12px; border: 1px solid var(--md-outline); border-radius: var(--md-corner-sm); background: var(--md-surface); color: var(--md-on-surface); font-size: 14px; min-width: 150px; flex: 0 0 auto;">
                        <option value="composite">复合指标</option>
                        <option value="simple">简单指标</option>
                    </select>
                    <button id="save-type-btn" onclick="saveIndicatorType()" class="md-button filled">
                        <i class="fas fa-save"></i>
                        <span>保存</span>
                    </button>
                    <span id="type-save-status" style="font-size: 12px; color: var(--md-success); flex: 1;"></span>
                </div>
            </div>

            <div id="description-section" style="display: none; margin-top: 20px;">
                <div class="info-label">指标描述</div>
                <div class="info-value" id="indicator-description"></div>
            </div>
            <div id="calculation-section" style="display: none; margin-top: 20px;">
                <div class="info-label">计算方法</div>
                <div class="info-value" id="calculation-method"></div>
            </div>
        </div>

        <!-- 详细信息 -->
        <div class="info-card">
            <div class="card-header">
                <h3>📝 基本属性</h3>
                <div class="card-actions">
                    <button id="edit-details-btn" class="action-btn edit-btn" onclick="toggleDetailEdit()" title="编辑详细信息">
                        <span class="material-symbols-outlined">edit</span>
                    </button>
                    <button id="collapse-details-btn" class="action-btn collapse-btn" onclick="toggleDetailCollapse()" title="折叠/展开">
                        <span class="material-symbols-outlined">expand_less</span>
                    </button>
                </div>
            </div>
            <div id="details-content" class="card-content">
                <!-- 显示模式 -->
                <div id="details-display-mode">
                    <!-- 基本属性 -->
                    <div id="basic-attributes-section" style="margin-top: 20px;">
                        <div class="info-label">基本属性</div>
                        <div class="basic-attributes-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-top: 12px;">
                            <div id="unit-section" style="display: none;">
                                <div class="attribute-item" style="background: #f8f9fa; padding: 12px; border-radius: 6px; border-left: 3px solid var(--md-primary);">
                                    <div style="font-weight: 500; color: var(--md-on-surface); margin-bottom: 4px;">单位</div>
                                    <div id="unit-value" style="color: var(--md-on-surface-variant);"></div>
                                </div>
                            </div>
                            <div id="lead-department-section" style="display: none;">
                                <div class="attribute-item" style="background: #f8f9fa; padding: 12px; border-radius: 6px; border-left: 3px solid var(--md-secondary);">
                                    <div style="font-weight: 500; color: var(--md-on-surface); margin-bottom: 4px;">牵头科室</div>
                                    <div id="lead-department-value" style="color: var(--md-on-surface-variant);"></div>
                                </div>
                            </div>
                            <div id="data-source-section" style="display: none;">
                                <div class="attribute-item" style="background: #f8f9fa; padding: 12px; border-radius: 6px; border-left: 3px solid var(--md-tertiary);">
                                    <div style="font-weight: 500; color: var(--md-on-surface); margin-bottom: 4px;">数据来源</div>
                                    <div id="data-source-value" style="color: var(--md-on-surface-variant);"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 逻辑定义 -->
                    <div id="logic-definition-section" style="display: none; margin-top: 20px;">
                        <div class="info-label">逻辑定义</div>
                        <div class="info-value" id="logic-definition" style="background: #f8f9fa; padding: 12px; border-radius: 6px; border-left: 3px solid var(--md-primary);"></div>
                    </div>

                    <!-- 意义 -->
                    <div id="significance-section" style="display: none; margin-top: 20px;">
                        <div class="info-label">意义</div>
                        <div class="info-value" id="significance"></div>
                    </div>

                    <!-- 计算公式 -->
                    <div id="formula-section" style="display: none; margin-top: 20px;">
                        <div class="info-label">计算公式</div>
                        <div class="info-value" id="calculation-formula" style="background: #f8f9fa; padding: 12px; border-radius: 6px; font-family: monospace; border-left: 3px solid var(--md-primary);"></div>
                        <div id="numerator-denominator" style="margin-top: 12px;">
                            <div id="numerator-section" style="display: none; margin-bottom: 8px;">
                                <strong style="color: var(--md-primary);">分子：</strong><span id="numerator-description"></span>
                            </div>
                            <div id="denominator-section" style="display: none;">
                                <strong style="color: var(--md-error);">分母：</strong><span id="denominator-description"></span>
                            </div>
                        </div>
                    </div>

                    <!-- 分子分母组件 -->
                    <div id="components-section" style="display: none; margin-top: 20px;">
                        <div class="info-label" style="margin-bottom: 16px;">
                            <span class="material-symbols-outlined" style="vertical-align: middle; margin-right: 8px; color: var(--md-primary);">medical_information</span>
                            分子分母组件
                            <span style="font-size: 12px; color: var(--md-on-surface-variant); font-weight: normal; margin-left: 8px;">(仅复合指标显示)</span>
                        </div>
                        <div class="components-table-container" style="background: #f8f9fa; border-radius: 8px; overflow: hidden; border: 1px solid var(--md-outline-variant);">
                            <table class="components-table" style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background: var(--md-surface-variant);">
                                        <th style="padding: 12px; text-align: left; font-weight: 500; color: var(--md-on-surface); border-bottom: 1px solid var(--md-outline-variant); width: 80px;">类型</th>
                                        <th style="padding: 12px; text-align: left; font-weight: 500; color: var(--md-on-surface); border-bottom: 1px solid var(--md-outline-variant);">组件名称</th>
                                        <th style="padding: 12px; text-align: left; font-weight: 500; color: var(--md-on-surface); border-bottom: 1px solid var(--md-outline-variant); width: 80px;">单位</th>
                                        <th style="padding: 12px; text-align: left; font-weight: 500; color: var(--md-on-surface); border-bottom: 1px solid var(--md-outline-variant); width: 120px;">牵头科室</th>
                                        <th style="padding: 12px; text-align: left; font-weight: 500; color: var(--md-on-surface); border-bottom: 1px solid var(--md-outline-variant); width: 120px;">数据来源</th>
                                        <th style="padding: 12px; text-align: left; font-weight: 500; color: var(--md-on-surface); border-bottom: 1px solid var(--md-outline-variant); width: 80px;">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="components-table">
                                    <!-- 动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 编辑模式 -->
                <div id="details-edit-mode" style="display: none;">
                    <form id="details-edit-form">
                        <div class="edit-form-grid">
                            <!-- 基本属性字段 -->
                            <div class="form-field">
                                <label for="edit-unit">单位</label>
                                <input type="text" id="edit-unit" name="unit" placeholder="请输入单位">
                            </div>

                            <div class="form-field">
                                <label for="edit-lead-department">牵头科室</label>
                                <input type="text" id="edit-lead-department" name="lead_department" placeholder="请输入牵头科室">
                            </div>

                            <div class="form-field">
                                <label for="edit-data-source">数据来源</label>
                                <input type="text" id="edit-data-source" name="data_source" placeholder="请输入数据来源">
                            </div>

                            <div class="form-field full-width">
                                <label for="edit-logic-definition">逻辑定义</label>
                                <textarea id="edit-logic-definition" name="logic_definition" rows="2" placeholder="请输入逻辑定义"></textarea>
                            </div>

                            <div class="form-field full-width">
                                <label for="edit-significance">意义</label>
                                <textarea id="edit-significance" name="significance" rows="3" placeholder="请输入指标意义"></textarea>
                            </div>

                            <div class="form-field">
                                <label for="edit-calculation-formula">计算公式</label>
                                <textarea id="edit-calculation-formula" name="calculation_formula" rows="2" placeholder="请输入计算公式"></textarea>
                            </div>

                            <div class="form-field">
                                <label for="edit-numerator-description">分子说明</label>
                                <textarea id="edit-numerator-description" name="numerator_description" rows="2" placeholder="请输入分子说明"></textarea>
                            </div>

                            <div class="form-field">
                                <label for="edit-denominator-description">分母说明</label>
                                <textarea id="edit-denominator-description" name="denominator_description" rows="2" placeholder="请输入分母说明"></textarea>
                            </div>
                        </div>

                        <div class="edit-form-actions">
                            <button type="button" class="md-button outlined" onclick="cancelDetailEdit()">
                                <span class="material-symbols-outlined">cancel</span>
                                <span>取消</span>
                            </button>
                            <button type="submit" class="md-button filled">
                                <span class="material-symbols-outlined">save</span>
                                <span>保存</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 指标参考 -->
        <div class="info-card">
            <div class="card-header">
                <h3>📖 指标参考</h3>
                <div class="card-actions">
                    <!-- 暂时隐藏参考范围功能 <button id="edit-reference-btn" class="action-btn edit-btn" onclick="toggleReferenceEdit()" title="编辑指标参考">
                        <span class="material-symbols-outlined">edit</span> -->
                    </button>
                    <!-- 暂时隐藏参考范围功能 <button id="collapse-reference-btn" class="action-btn collapse-btn" onclick="toggleReferenceCollapse()" title="折叠/展开">
                        <span class="material-symbols-outlined">expand_less</span> -->
                    </button>
                </div>
            </div>
            <div id="reference-content" class="card-content">
                <!-- 显示模式 -->
                <div id="reference-display-mode">
                    <!-- 指标定义 -->
                    <div id="ref-definition-section" style="display: none; margin-top: 20px;">
                        <div class="info-label">指标定义</div>
                        <div class="info-value" id="ref-indicator-definition"></div>
                    </div>

                    <!-- 统计范围 -->
                    <div id="ref-scope-section" style="display: none; margin-top: 20px;">
                        <div class="info-label">统计范围</div>
                        <div class="info-value" id="ref-statistical-scope"></div>
                    </div>

                    <!-- 数据来源详细 -->
                    <div id="ref-data-sources-section" style="display: none; margin-top: 20px;">
                        <div class="info-label">数据来源</div>
                        <div class="info-value" id="ref-data-sources"></div>
                    </div>

                    <!-- 统计频率 -->
                    <div id="ref-frequency-section" style="display: none; margin-top: 20px;">
                        <div class="info-label">统计频率</div>
                        <div class="info-value" id="ref-collection-frequency"></div>
                    </div>

                    <!-- 参考值 -->
                    <div id="ref-reference-section" style="display: none; margin-top: 20px;">
                        <div class="info-label">标准值/参考值</div>
                        <div class="info-value" id="ref-reference-value"></div>
                    </div>

                    <!-- 监测分析 -->
                    <div id="ref-monitoring-section" style="display: none; margin-top: 20px;">
                        <div class="info-label">监测分析</div>
                        <div class="info-value" id="ref-monitoring-analysis"></div>
                        <div id="ref-analysis-dimensions" style="margin-top: 12px;"></div>
                    </div>
                </div>

                <!-- 编辑模式 -->
                <div id="reference-edit-mode" style="display: none;">
                    <form id="reference-edit-form">
                        <div class="edit-form-grid">
                            <div class="form-field full-width">
                                <label for="edit-ref-indicator-definition">指标定义</label>
                                <textarea id="edit-ref-indicator-definition" name="indicator_definition" rows="3" placeholder="请输入指标的详细定义"></textarea>
                            </div>

                            <div class="form-field">
                                <label for="edit-ref-statistical-scope">统计范围</label>
                                <textarea id="edit-ref-statistical-scope" name="statistical_scope" rows="2" placeholder="请输入统计范围"></textarea>
                            </div>

                            <div class="form-field">
                                <label for="edit-ref-data-sources">数据来源</label>
                                <textarea id="edit-ref-data-sources" name="data_sources" rows="2" placeholder="请输入详细数据来源"></textarea>
                            </div>

                            <div class="form-field">
                                <label for="edit-ref-collection-frequency">统计频率</label>
                                <input type="text" id="edit-ref-collection-frequency" name="collection_frequency_detail" placeholder="请输入统计频率">
                            </div>

                            <div class="form-field">
                                <label for="edit-ref-reference-value">标准值/参考值</label>
                                <input type="text" id="edit-ref-reference-value" name="reference_value" placeholder="请输入参考值">
                            </div>

                            <div class="form-field full-width">
                                <label for="edit-ref-monitoring-analysis">监测分析</label>
                                <textarea id="edit-ref-monitoring-analysis" name="monitoring_analysis" rows="3" placeholder="请输入监测分析内容"></textarea>
                            </div>
                        </div>

                        <div class="edit-form-actions">
                            <!-- 暂时隐藏参考范围功能 <button type="button" class="md-button outlined" onclick="cancelReferenceEdit()">
                                <span class="material-symbols-outlined">cancel</span> -->
                                <span>取消</span>
                            </button>
                            <button type="submit" class="md-button filled">
                                <span class="material-symbols-outlined">save</span>
                                <span>保存</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>




        <!-- 子指标 -->
        <div class="info-card" id="childrenNavCard" style="display: none;">
            <div class="card-header">
                <h3 class="info-card-title">
                    <span class="material-symbols-outlined">account_tree</span>
                    子指标
                    <span class="children-count" id="childrenCount">0</span>
                </h3>
                <div class="card-actions">
                    <button id="collapse-children-btn" class="action-btn collapse-btn" onclick="toggleChildrenCollapse()" title="折叠/展开">
                        <span class="material-symbols-outlined">expand_less</span>
                    </button>
                </div>
            </div>
            <div id="children-content" class="card-content">
                <div class="children-nav-grid" id="childrenNavGrid">
                    <!-- 动态加载子指标导航 -->
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
    .indicator-detail-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .detail-header {
        background: var(--md-surface);
        border-radius: var(--md-corner-large);
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: var(--md-elevation-1);
    }

    .detail-header h1 {
        margin: 0 0 16px 0;
        color: var(--md-on-surface);
        font-size: 28px;
        font-weight: 500;
    }

    .breadcrumb {
        color: var(--md-on-surface-variant);
        margin-bottom: 16px;
        font-size: 14px;
    }

    .breadcrumb a {
        color: var(--md-primary);
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .action-buttons {
        display: flex;
        gap: 12px;
        margin-top: 16px;
        flex-wrap: wrap;
    }

    .info-card {
        background: var(--md-surface);
        border-radius: var(--md-corner-large);
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: var(--md-elevation-1);
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 16px;
    }

    .info-item {
        margin-bottom: 16px;
    }

    .info-label {
        font-weight: 500;
        color: var(--md-on-surface);
        margin-bottom: 4px;
        font-size: 14px;
    }

    .info-value {
        color: var(--md-on-surface-variant);
        font-size: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .info-value a {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
        max-width: 100%;
    }

    .table-container {
        background: var(--md-surface);
        border-radius: var(--md-corner-large);
        overflow: hidden;
        box-shadow: var(--md-elevation-1);
        margin-bottom: 24px;
    }

    .table-header {
        padding: 20px 24px;
        background: var(--md-surface-variant);
        border-bottom: 1px solid var(--md-outline-variant);
    }

    .table-header h3 {
        margin: 0;
        color: var(--md-on-surface);
        font-size: 18px;
        font-weight: 500;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
    }

    .data-table th {
        background: var(--md-surface-variant);
        padding: 16px;
        text-align: left;
        font-weight: 500;
        color: var(--md-on-surface);
        border-bottom: 1px solid var(--md-outline-variant);
    }

    .data-table td {
        padding: 16px;
        border-bottom: 1px solid var(--md-outline-variant);
        color: var(--md-on-surface);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
    }

    .data-table td.no-wrap {
        white-space: nowrap;
    }

    .data-table td.allow-wrap {
        white-space: normal;
        max-width: none;
    }

    .data-table tr:hover {
        background: var(--md-surface-variant);
    }

    .component-type-badge {
        padding: 4px 8px;
        border-radius: var(--md-corner-small);
        font-size: 12px;
        font-weight: 500;
    }

    .component-type-numerator {
        background: var(--md-tertiary-container);
        color: var(--md-on-tertiary-container);
    }

    .component-type-denominator {
        background: var(--md-secondary-container);
        color: var(--md-on-secondary-container);
    }

    .component-type-other {
        background: var(--md-surface-variant);
        color: var(--md-on-surface-variant);
    }

    .empty-state {
        text-align: center;
        padding: 40px;
        color: var(--md-on-surface-variant);
    }

    .empty-state-icon {
        font-size: 48px;
        opacity: 0.5;
        margin-bottom: 16px;
    }

    .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid var(--md-outline-variant);
        border-top: 4px solid var(--md-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .error-message {
        background: var(--md-error-container);
        color: var(--md-on-error-container);
        padding: 16px;
        border-radius: var(--md-corner-medium);
        margin: 16px 0;
    }

    .indicator-id {
        font-family: 'Roboto Mono', monospace;
        font-weight: 600;
        color: var(--md-primary);
    }

    /* 指标类型徽章 */
    .indicator-type-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
        border: 1px solid;
        white-space: nowrap;
        min-width: 60px;
        text-align: center;
    }

    .indicator-type-badge.simple-indicator {
        background: #e8f5e8;
        color: #2e7d32;
        border-color: #4caf50;
    }

    .indicator-type-badge.composite-indicator {
        background: #e3f2fd;
        color: #1565c0;
        border-color: #2196f3;
    }

    /* 卡片头部样式 */
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid var(--md-outline-variant);
    }

    .card-header h3 {
        margin: 0;
        color: var(--md-on-surface);
        font-size: 18px;
        font-weight: 500;
    }

    .card-actions {
        display: flex;
        gap: 8px;
    }

    .action-btn {
        width: 36px;
        height: 36px;
        border: 1px solid var(--md-outline);
        background: var(--md-surface);
        color: var(--md-on-surface-variant);
        border-radius: var(--md-corner-sm);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
    }

    .action-btn:hover {
        background: var(--md-surface-variant);
        color: var(--md-on-surface);
        border-color: var(--md-outline-variant);
    }

    .action-btn.edit-btn.active {
        background: var(--md-primary);
        color: var(--md-on-primary);
        border-color: var(--md-primary);
    }

    .action-btn .material-symbols-outlined {
        font-size: 20px;
    }

    .card-content {
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .card-content.collapsed {
        max-height: 0;
        padding: 0;
        margin: 0;
        opacity: 0;
    }

    /* 编辑表单样式 */
    .edit-form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 24px;
    }

    .form-field {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .form-field.full-width {
        grid-column: 1 / -1;
    }

    .form-field label {
        font-weight: 500;
        color: var(--md-on-surface);
        font-size: 14px;
    }

    .form-field input,
    .form-field textarea {
        padding: 12px 16px;
        border: 1px solid var(--md-outline);
        border-radius: var(--md-corner-sm);
        font-family: var(--md-font-family);
        font-size: 14px;
        background: var(--md-surface);
        color: var(--md-on-surface);
        resize: vertical;
        transition: border-color 0.2s ease;
    }

    .form-field input:focus,
    .form-field textarea:focus {
        outline: none;
        border-color: var(--md-primary);
        box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
    }

    .edit-form-actions {
        display: flex;
        gap: 16px;
        justify-content: flex-end;
        padding-top: 24px;
        border-top: 1px solid var(--md-outline-variant);
    }

    /* 子指标导航样式 */
    .children-count {
        background: var(--md-primary);
        color: var(--md-on-primary);
        font-size: 12px;
        font-weight: 500;
        padding: 4px 8px;
        border-radius: 12px;
        margin-left: 8px;
        min-width: 20px;
        text-align: center;
        display: inline-block;
    }

    .children-nav-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 12px;
        margin-top: 16px;
    }

    .child-nav-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        background: var(--md-surface-variant);
        border-radius: var(--md-corner-md);
        border: 1px solid var(--md-outline-variant);
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        color: inherit;
    }

    .child-nav-item:hover {
        background: var(--md-secondary-container);
        border-color: var(--md-secondary);
        transform: translateY(-1px);
        box-shadow: var(--md-elevation-2);
    }

    .child-nav-info {
        flex: 1;
        min-width: 0;
    }

    .child-nav-id {
        font-family: 'Roboto Mono', monospace;
        font-weight: 600;
        color: var(--md-primary);
        font-size: 14px;
        margin-bottom: 4px;
    }

    .child-nav-name {
        color: var(--md-on-surface);
        font-size: 16px;
        font-weight: 500;
        line-height: 1.4;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .child-nav-arrow {
        color: var(--md-on-surface-variant);
        margin-left: 12px;
        transition: transform 0.2s ease;
    }

    .child-nav-item:hover .child-nav-arrow {
        transform: translateX(4px);
        color: var(--md-secondary);
    }

    @media (max-width: 768px) {
        .indicator-detail-container {
            padding: 16px;
        }

        .action-buttons {
            flex-direction: column;
        }

        .info-grid {
            grid-template-columns: 1fr;
        }

        .data-table {
            font-size: 14px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 8px;
        }

        .child-nav-name {
            white-space: normal;
            overflow: visible;
            text-overflow: unset;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>

// 通用安全DOM操作函数
function safeSetTextContent(elementId, value, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.textContent = value;
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}

function safeGetElement(elementId, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (!element) {
        console.warn(`Element not found: ${elementId}`);
    }
    return element;
}

function safeSetInnerHTML(elementId, html, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.innerHTML = html;
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}

function safeSetStyle(elementId, property, value, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.style[property] = value;
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}

function safeAddClass(elementId, className, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.classList.add(className);
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}

function safeRemoveClass(elementId, className, isSelector = false) {
    const element = isSelector ? document.querySelector(elementId) : document.getElementById(elementId);
    if (element) {
        element.classList.remove(className);
        return true;
    } else {
        console.warn(`Element not found: ${elementId}`);
        return false;
    }
}


// 全局变量
let indicatorData = null;
const indicatorId = '{{ indicator_id }}';

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadIndicatorDetail();
});

// 显示状态函数
function showLoading() {
    document.getElementById('loading-state').style.display = 'flex';
    document.getElementById('error-state').style.display = 'none';
    document.getElementById('main-content').style.display = 'none';
}

function showError(message) {
    document.getElementById('loading-state').style.display = 'none';
    document.getElementById('error-state').style.display = 'block';
    document.getElementById('main-content').style.display = 'none';
    document.getElementById('error-message').textContent = message;
}

function showMainContent() {
    document.getElementById('loading-state').style.display = 'none';
    document.getElementById('error-state').style.display = 'none';
    document.getElementById('main-content').style.display = 'block';
}

// 加载指标详情
async function loadIndicatorDetail() {
    try {
        showLoading();

        const response = await fetch(`/api/indicators/${indicatorId}`);
        const result = await response.json();

        if (!result.success) {
            throw new Error(result.error || '加载失败');
        }

        indicatorData = result.data;
        renderIndicatorDetail();
        showMainContent();

    } catch (error) {
        console.error('加载指标详情失败:', error);
        showError(error.message);
    }
}

// 渲染指标详情
function renderIndicatorDetail() {
    const { indicator, components, children, parent } = indicatorData;

    // 更新标题
    document.getElementById('indicator-name').textContent = indicator.name || '未命名指标';

    // 渲染基本信息
    renderBasicInfo(indicator, parent);

    // 初始化指标类型选择器
    initializeIndicatorTypeSelector(indicator);

    // 渲染描述和计算方法
    if (indicator.description) {
        document.getElementById('description-section').style.display = 'block';
        document.getElementById('indicator-description').textContent = indicator.description;
    }

    if (indicator.calculation_method) {
        document.getElementById('calculation-section').style.display = 'block';
        document.getElementById('calculation-method').textContent = indicator.calculation_method;
    }

    // 渲染详细信息字段
    renderDetailedFields(indicator);

    // 渲染组件表格
    renderComponentsTable(components);

    // 渲染子指标表格
    renderChildrenTable(children);

    // 根据指标类型控制组件模块显示
    updateComponentsVisibility(indicator.indicator_type);
}

// 渲染基本信息
function renderBasicInfo(indicator, parent) {
    const basicInfoHtml = `
        <div class="info-item">
            <div class="info-label">指标ID</div>
            <div class="info-value indicator-id">${indicator.id}</div>
        </div>
        <div class="info-item">
            <div class="info-label">指标名称</div>
            <div class="info-value">${indicator.name || '未命名'}</div>
        </div>
        <div class="info-item">
            <div class="info-label">所属章节</div>
            <div class="info-value">
                ${indicator.chapter_name ?
                    `<a href="/admin/chapters" style="color: var(--md-primary); white-space: nowrap;" title="${indicator.chapter_code} - ${indicator.chapter_name}">${indicator.chapter_code} - ${indicator.chapter_name}</a>` :
                    '未设置'}
            </div>
        </div>
        <div class="info-item">
            <div class="info-label">所属小节</div>
            <div class="info-value">
                ${indicator.section_name ?
                    `<a href="/admin/sections" style="color: var(--md-primary); white-space: nowrap;" title="${indicator.section_code} - ${indicator.section_name}">${indicator.section_code} - ${indicator.section_name}</a>` :
                    '未设置'}
            </div>
        </div>
        <div class="info-item">
            <div class="info-label">父指标</div>
            <div class="info-value">
                ${parent ?
                    `<a href="/admin/indicators/${parent.id}" style="color: var(--md-primary);">${parent.id} - ${parent.name}</a>` :
                    '顶级指标'}
            </div>
        </div>
        <div class="info-item">
            <div class="info-label">指标类型</div>
            <div class="info-value">
                ${indicator.indicator_type === 'simple' ?
                    '<span class="indicator-type-badge simple-indicator">简单指标</span>' :
                    '<span class="indicator-type-badge composite-indicator">复合指标</span>'}
            </div>
        </div>
        <div class="info-item">
            <div class="info-label">子指标数量</div>
            <div class="info-value">${indicatorData.children.length} 个</div>
        </div>
    `;

    document.getElementById('basic-info').innerHTML = basicInfoHtml;
}

// 渲染详细信息字段
function renderDetailedFields(indicator) {
    console.log('渲染详细信息字段:', indicator);

    // 基本属性
    let hasBasicAttributes = false;
    if (indicator.unit) {
        document.getElementById('unit-value').textContent = indicator.unit;
        document.getElementById('unit-section').style.display = 'block';
        hasBasicAttributes = true;
    }
    if (indicator.lead_department) {
        document.getElementById('lead-department-value').textContent = indicator.lead_department;
        document.getElementById('lead-department-section').style.display = 'block';
        hasBasicAttributes = true;
    }
    if (indicator.data_source) {
        document.getElementById('data-source-value').textContent = indicator.data_source;
        document.getElementById('data-source-section').style.display = 'block';
        hasBasicAttributes = true;
    }
    if (hasBasicAttributes) {
        document.getElementById('basic-attributes-section').style.display = 'block';
    }

    // 逻辑定义
    if (indicator.logic_definition) {
        document.getElementById('logic-definition').textContent = indicator.logic_definition;
        document.getElementById('logic-definition-section').style.display = 'block';
    }

    // 指标定义
    if (indicator.indicator_definition) {
        console.log('显示指标定义:', indicator.indicator_definition);
        document.getElementById('indicator-definition').textContent = indicator.indicator_definition;
        document.getElementById('definition-section').style.display = 'block';
    }

    // 计算公式
    if (indicator.calculation_formula || indicator.numerator_description || indicator.denominator_description) {
        if (indicator.calculation_formula) {
            document.getElementById('calculation-formula').textContent = indicator.calculation_formula;
        }
        if (indicator.numerator_description) {
            document.getElementById('numerator-description').textContent = indicator.numerator_description;
            document.getElementById('numerator-section').style.display = 'block';
        }
        if (indicator.denominator_description) {
            document.getElementById('denominator-description').textContent = indicator.denominator_description;
            document.getElementById('denominator-section').style.display = 'block';
        }
        document.getElementById('formula-section').style.display = 'block';
    }

    // 统计范围
    if (indicator.statistical_scope) {
        document.getElementById('statistical-scope').textContent = indicator.statistical_scope;
        document.getElementById('scope-section').style.display = 'block';
    }

    // 数据来源详细
    if (indicator.data_sources) {
        document.getElementById('data-sources').textContent = indicator.data_sources;
        document.getElementById('data-sources-section').style.display = 'block';
    }

    // 统计频率
    if (indicator.collection_frequency_detail) {
        document.getElementById('collection-frequency').textContent = indicator.collection_frequency_detail;
        document.getElementById('frequency-section').style.display = 'block';
    }

    // 标准值/参考值
    if (indicator.reference_value) {
        document.getElementById('reference-value').textContent = indicator.reference_value;
        document.getElementById('reference-section').style.display = 'block';
    }

    // 监测分析
    if (indicator.monitoring_analysis) {
        document.getElementById('monitoring-analysis').textContent = indicator.monitoring_analysis;

        // 如果有分析维度数据，也显示出来
        if (indicator.analysis_dimensions) {
            renderAnalysisDimensions(indicator.analysis_dimensions);
        }

        document.getElementById('monitoring-section').style.display = 'block';
    }

    // 分子分母组件（仅复合指标显示）
    if (indicator.indicator_type === 'composite' && indicatorData.components && indicatorData.components.length > 0) {
        renderComponentsTable(indicatorData.components);
        document.getElementById('components-section').style.display = 'block';
    } else {
        document.getElementById('components-section').style.display = 'none';
    }
}

// 渲染分析维度
function renderAnalysisDimensions(dimensions) {
    if (!dimensions || dimensions.length === 0) return;

    const dimensionsHtml = `
        <div style="margin-top: 16px;">
            <h4 style="color: var(--md-primary); margin-bottom: 12px; font-size: 16px;">建议对数据进行多维度分析：</h4>
            <div style="display: grid; gap: 12px;">
                ${dimensions.map(dim => `
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; border-left: 3px solid var(--md-tertiary);">
                        <div style="font-weight: 600; color: var(--md-on-surface); margin-bottom: 4px;">${dim.dimension_name}</div>
                        <div style="color: var(--md-on-surface-variant); font-size: 14px;">${dim.analysis_content}</div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
    document.getElementById('analysis-dimensions').innerHTML = dimensionsHtml;
}

// 渲染组件表格
function renderComponentsTable(components) {
    const tbody = document.getElementById('components-table');

    if (!components || components.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="empty-state">
                    <div class="empty-state-icon">
                        <span class="material-symbols-outlined" style="font-size: 48px; opacity: 0.5;">medical_information</span>
                    </div>
                    <div>暂无组件数据</div>
                    <div style="font-size: 12px; margin-top: 8px;">点击"添加组件"开始创建分子分母信息</div>
                </td>
            </tr>
        `;
        return;
    }

    const rows = components.map(component => {
        const typeClass = {
            'numerator': 'component-type-numerator',
            'denominator': 'component-type-denominator',
            'other': 'component-type-other'
        }[component.component_type] || 'component-type-other';

        const typeLabel = {
            'numerator': '分子',
            'denominator': '分母',
            'other': '其他'
        }[component.component_type] || '其他';

        return `
            <tr>
                <td class="no-wrap"><span class="component-type-badge ${typeClass}">${typeLabel}</span></td>
                <td class="allow-wrap">
                    <div style="font-weight: 500;">${component.name || '未命名'}</div>
                    ${component.definition ? `<div style="font-size: 12px; color: var(--md-on-surface-variant); margin-top: 4px;">${component.definition.substring(0, 60)}${component.definition.length > 60 ? '...' : ''}</div>` : ''}
                </td>
                <td class="no-wrap">${component.unit || '-'}</td>
                <td class="no-wrap">${component.lead_department || '-'}</td>
                <td class="no-wrap">${component.data_source || '-'}</td>
                <td class="no-wrap">
                    <button class="md-button outlined" onclick="editComponent('${component.id}')" style="padding: 4px 8px; font-size: 12px;">
                        <span class="material-symbols-outlined" style="font-size: 16px;">edit</span>
                    </button>
                </td>
            </tr>
        `;
    }).join('');

    tbody.innerHTML = rows;
}

// 渲染子指标导航卡片
function renderChildrenTable(children) {
    const childrenCard = document.getElementById('childrenNavCard');
    const childrenCount = document.getElementById('childrenCount');
    const childrenGrid = document.getElementById('childrenNavGrid');

    // 更新子指标数量
    childrenCount.textContent = children.length;

    if (!children || children.length === 0) {
        // 隐藏子指标模块
        childrenCard.style.display = 'none';
        return;
    }

    // 显示子指标模块
    childrenCard.style.display = 'block';

    // 渲染子指标导航项
    const childrenItems = children.map(child => `
        <a href="/admin/indicators/${child.id}" class="child-nav-item">
            <div class="child-nav-info">
                <div class="child-nav-id">${child.id}</div>
                <div class="child-nav-name">${child.name}</div>
            </div>
            <div class="child-nav-arrow">
                <span class="material-symbols-outlined">chevron_right</span>
            </div>
        </a>
    `).join('');

    childrenGrid.innerHTML = childrenItems;
}

// 折叠/展开子指标
function toggleChildrenCollapse() {
    const content = document.getElementById('children-content');
    const collapseBtn = document.getElementById('collapse-children-btn');
    const collapseIcon = collapseBtn.querySelector('.material-symbols-outlined');

    if (content.classList.contains('collapsed')) {
        // 展开
        content.classList.remove('collapsed');
        collapseIcon.textContent = 'expand_less';
        collapseBtn.title = '折叠';
    } else {
        // 折叠
        content.classList.add('collapsed');
        collapseIcon.textContent = 'expand_more';
        collapseBtn.title = '展开';
    }
}

// 功能函数
function editIndicator(id) {
    if (id) {
        window.location.href = `/admin/indicators/${id}/edit`;
    } else {
        window.location.href = `/admin/indicators/${indicatorId}/edit`;
    }
}

function addComponent() {
    window.location.href = `/admin/indicators/${indicatorId}/add_component`;
}

function editComponent(id) {
    window.location.href = `/admin/components/${id}/edit`;
}

// 初始化指标类型选择器
function initializeIndicatorTypeSelector(indicator) {
    const selector = document.getElementById('indicator-type-selector');
    if (selector) {
        selector.value = indicator.indicator_type || 'composite';
        // 保存按钮始终显示，只清空状态
        document.getElementById('type-save-status').textContent = '';
    }
}

// 处理指标类型变化
function handleIndicatorTypeChange() {
    const selector = document.getElementById('indicator-type-selector');
    const status = document.getElementById('type-save-status');

    if (selector) {
        const newType = selector.value;

        // 清空状态信息
        status.textContent = '';

        // 立即更新组件模块显示（预览效果）
        updateComponentsVisibility(newType);
    }
}

// 保存指标类型
async function saveIndicatorType() {
    const selector = document.getElementById('indicator-type-selector');
    const saveBtn = document.getElementById('save-type-btn');
    const status = document.getElementById('type-save-status');

    if (!selector) return;

    const newType = selector.value;

    try {
        // 显示保存中状态
        saveBtn.disabled = true;
        saveBtn.textContent = '保存中...';
        status.textContent = '';

        const response = await fetch(`/api/indicators/${indicatorId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                indicator_type: newType
            })
        });

        const result = await response.json();

        if (result.success) {
            // 更新本地数据
            indicatorData.indicator.indicator_type = newType;

            // 显示成功状态
            status.textContent = '✅ 保存成功';
            status.style.color = 'var(--md-success)';

            // 2秒后清空状态信息（保存按钮保持显示）
            setTimeout(() => {
                status.textContent = '';
            }, 2000);

            // 更新基本信息中的指标类型显示
            renderBasicInfo(indicatorData.indicator, indicatorData.parent);

            // 通知前端系统更新（如果前端页面已打开）
            notifyFrontendUpdate(indicatorId, newType);

            // 显示成功消息
            showSuccessMessage(`指标类型已更新为：${newType === 'simple' ? '简单指标' : '复合指标'}`);

        } else {
            throw new Error(result.error || '保存失败');
        }

    } catch (error) {
        console.error('保存指标类型失败:', error);
        status.textContent = '❌ 保存失败';
        status.style.color = 'var(--md-error)';
    } finally {
        saveBtn.disabled = false;
        saveBtn.textContent = '保存';
    }
}

// 更新组件模块显示
function updateComponentsVisibility(indicatorType) {
    const componentsSection = document.getElementById('components-section');
    const childrenCard = document.getElementById('childrenNavCard');

    // 控制基本属性模块内的分子分母组件部分
    if (componentsSection) {
        if (indicatorType === 'simple') {
            // 简单指标：隐藏分子分母组件部分
            componentsSection.style.display = 'none';
        } else {
            // 复合指标：显示分子分母组件部分
            componentsSection.style.display = 'block';
        }
    }

    // 子指标模块的显示逻辑：简单指标时隐藏子指标模块
    if (childrenCard && indicatorData && indicatorData.children) {
        if (indicatorType === 'simple') {
            // 简单指标：隐藏子指标模块
            childrenCard.style.display = 'none';
        } else {
            // 复合指标：根据是否有子指标决定显示
            if (indicatorData.children.length > 0) {
                childrenCard.style.display = 'block';
            } else {
                childrenCard.style.display = 'none';
            }
        }
    }
}

// 通知前端系统更新
function notifyFrontendUpdate(indicatorId, newType) {
    try {
        // 使用 localStorage 作为跨页面通信机制
        const updateData = {
            indicatorId: indicatorId,
            indicatorType: newType,
            timestamp: Date.now(),
            source: 'admin'
        };

        localStorage.setItem('indicator_type_update', JSON.stringify(updateData));

        // 触发 storage 事件（如果前端页面在同一浏览器中打开）
        window.dispatchEvent(new StorageEvent('storage', {
            key: 'indicator_type_update',
            newValue: JSON.stringify(updateData),
            url: window.location.href
        }));

        console.log('已通知前端系统更新指标类型:', indicatorId, newType);

    } catch (error) {
        console.error('通知前端系统失败:', error);
    }
}

// 详细信息编辑功能
let isDetailEditMode = false;

// 切换详细信息编辑模式
function toggleDetailEdit() {
    const editBtn = document.getElementById('edit-details-btn');
    const displayMode = document.getElementById('details-display-mode');
    const editMode = document.getElementById('details-edit-mode');
    const editIcon = editBtn.querySelector('.material-symbols-outlined');

    if (!isDetailEditMode) {
        // 进入编辑模式
        isDetailEditMode = true;
        displayMode.style.display = 'none';
        editMode.style.display = 'block';
        editBtn.classList.add('active');
        editIcon.textContent = 'close';
        editBtn.title = '取消编辑';

        // 填充编辑表单
        populateEditForm();

        // 设置表单提交事件
        setupEditFormHandler();

    } else {
        // 退出编辑模式
        cancelDetailEdit();
    }
}

// 取消详细信息编辑
function cancelDetailEdit() {
    const editBtn = document.getElementById('edit-details-btn');
    const displayMode = document.getElementById('details-display-mode');
    const editMode = document.getElementById('details-edit-mode');
    const editIcon = editBtn.querySelector('.material-symbols-outlined');

    isDetailEditMode = false;
    displayMode.style.display = 'block';
    editMode.style.display = 'none';
    editBtn.classList.remove('active');
    editIcon.textContent = 'edit';
    editBtn.title = '编辑详细信息';
}

// 填充编辑表单
function populateEditForm() {
    const indicator = indicatorData.indicator;

    // 基本属性字段
    document.getElementById('edit-unit').value = indicator.unit || '';
    document.getElementById('edit-lead-department').value = indicator.lead_department || '';
    document.getElementById('edit-data-source').value = indicator.data_source || '';
    document.getElementById('edit-logic-definition').value = indicator.logic_definition || '';

    // 详细信息字段
    document.getElementById('edit-indicator-definition').value = indicator.indicator_definition || '';
    document.getElementById('edit-calculation-formula').value = indicator.calculation_formula || '';
    document.getElementById('edit-numerator-description').value = indicator.numerator_description || '';
    document.getElementById('edit-denominator-description').value = indicator.denominator_description || '';
    document.getElementById('edit-statistical-scope').value = indicator.statistical_scope || '';
    document.getElementById('edit-data-sources').value = indicator.data_sources || '';
    document.getElementById('edit-collection-frequency').value = indicator.collection_frequency_detail || '';
    document.getElementById('edit-reference-value').value = indicator.reference_value || '';
    document.getElementById('edit-monitoring-analysis').value = indicator.monitoring_analysis || '';
}

// 设置编辑表单处理器
function setupEditFormHandler() {
    const form = document.getElementById('details-edit-form');

    // 移除之前的事件监听器
    form.removeEventListener('submit', handleDetailFormSubmit);

    // 添加新的事件监听器
    form.addEventListener('submit', handleDetailFormSubmit);
}

// 处理详细信息表单提交
async function handleDetailFormSubmit(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const updateData = {};

    // 收集表单数据
    for (const [key, value] of formData.entries()) {
        updateData[key] = value.trim();
    }

    try {
        // 显示保存状态
        const submitBtn = event.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="material-symbols-outlined">hourglass_empty</span><span>保存中...</span>';

        const response = await fetch(`/api/indicators/${indicatorId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(updateData)
        });

        const result = await response.json();

        if (result.success) {
            // 更新本地数据
            Object.assign(indicatorData.indicator, updateData);

            // 重新渲染详细信息
            renderDetailedFields(indicatorData.indicator);

            // 退出编辑模式
            cancelDetailEdit();

            // 显示成功消息
            showSuccessMessage('详细信息保存成功！');

        } else {
            throw new Error(result.error || '保存失败');
        }

    } catch (error) {
        console.error('保存详细信息失败:', error);
        showErrorMessage('保存失败：' + error.message);
    } finally {
        const submitBtn = event.target.querySelector('button[type="submit"]');
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
}

// 折叠/展开详细信息
function toggleDetailCollapse() {
    const content = document.getElementById('details-content');
    const collapseBtn = document.getElementById('collapse-details-btn');
    const collapseIcon = collapseBtn.querySelector('.material-symbols-outlined');

    if (content.classList.contains('collapsed')) {
        // 展开
        content.classList.remove('collapsed');
        collapseIcon.textContent = 'expand_less';
        collapseBtn.title = '折叠';
    } else {
        // 折叠
        content.classList.add('collapsed');
        collapseIcon.textContent = 'expand_more';
        collapseBtn.title = '展开';
    }
}

// 显示成功消息
function showSuccessMessage(message) {
    // 创建临时消息元素
    const messageEl = document.createElement('div');
    messageEl.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--md-primary);
        color: var(--md-on-primary);
        padding: 12px 20px;
        border-radius: var(--md-corner-md);
        box-shadow: var(--md-elevation-3);
        z-index: 1000;
        font-size: 14px;
        animation: slideIn 0.3s ease;
    `;
    messageEl.textContent = message;

    document.body.appendChild(messageEl);

    // 3秒后自动移除
    setTimeout(() => {
        messageEl.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(messageEl);
        }, 300);
    }, 3000);
}

// 显示错误消息
function showErrorMessage(message) {
    // 创建临时消息元素
    const messageEl = document.createElement('div');
    messageEl.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--md-error);
        color: var(--md-on-error);
        padding: 12px 20px;
        border-radius: var(--md-corner-md);
        box-shadow: var(--md-elevation-3);
        z-index: 1000;
        font-size: 14px;
        animation: slideIn 0.3s ease;
    `;
    messageEl.textContent = message;

    document.body.appendChild(messageEl);

    // 5秒后自动移除
    setTimeout(() => {
        messageEl.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(messageEl);
        }, 300);
    }, 5000);
}
</script>

<style>
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}
</style>

{% endblock %}
