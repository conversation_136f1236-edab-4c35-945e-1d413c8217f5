#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查chapter1.xlsx文件的列结构
"""

import pandas as pd

def main():
    try:
        print("正在读取Excel文件...")
        df = pd.read_excel("chapter1.xlsx", sheet_name=0)
        
        print(f"数据维度: {df.shape[0]}行 × {df.shape[1]}列")
        print(f"\n所有列名 (共{len(df.columns)}列):")
        
        for i, col in enumerate(df.columns):
            print(f"  {i+1:2d}. {col}")
        
        # 查找包含关键词的列
        keywords = ['分子', '分母', '单位', '科室', '数据来源', '定义', '计算', '公式']
        
        print(f"\n包含关键词的列:")
        for keyword in keywords:
            matching_cols = [col for col in df.columns if keyword in str(col)]
            if matching_cols:
                print(f"  {keyword}: {matching_cols}")
        
        # 显示前3行数据的样本
        print(f"\n前3行数据样本:")
        for i in range(min(3, len(df))):
            print(f"\n第{i+1}行:")
            for j, col in enumerate(df.columns[:10]):  # 只显示前10列
                value = df.iloc[i, j]
                if pd.notna(value):
                    print(f"  {col}: {str(value)[:50]}...")
        
    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
