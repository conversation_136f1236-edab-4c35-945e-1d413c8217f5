<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指标类型徽章样式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        /* 指标类型徽章样式 */
        .indicator-type-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            line-height: 1;
            white-space: nowrap;
            min-width: 60px;
            margin: 5px;
        }

        .indicator-type-badge.simple-indicator {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }

        .indicator-type-badge.composite-indicator {
            background: #e3f2fd;
            color: #1565c0;
            border: 1px solid #2196f3;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .type-column {
            width: 100px;
        }

        /* 后台管理样式 */
        .admin-badge {
            background: #e3f2fd;
            color: #1565c0;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            border: 1px solid #2196f3;
            white-space: nowrap;
            min-width: 60px;
            display: inline-block;
            text-align: center;
        }

        .admin-badge.simple {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 指标类型徽章样式测试</h1>

        <div class="test-section">
            <div class="test-title">1. 基础徽章样式</div>
            <p>测试徽章是否能在一行内完整显示，不出现换行：</p>
            <span class="indicator-type-badge simple-indicator">简单指标</span>
            <span class="indicator-type-badge composite-indicator">复合指标</span>
        </div>

        <div class="test-section">
            <div class="test-title">2. 在表格中的显示效果</div>
            <p>模拟后台管理表格中的显示效果：</p>
            <table>
                <thead>
                    <tr>
                        <th style="width: 120px;">指标ID</th>
                        <th>指标名称</th>
                        <th style="width: 100px; white-space: nowrap;">指标类型</th>
                        <th style="width: 80px; white-space: nowrap;">章节</th>
                        <th style="width: 80px; white-space: nowrap;">小节</th>
                        <th style="width: 90px; white-space: nowrap;">子指标</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1.1.1</td>
                        <td>核定床位数</td>
                        <td><span class="admin-badge">复合指标</span></td>
                        <td style="white-space: nowrap;">1</td>
                        <td style="white-space: nowrap;">1</td>
                        <td style="white-space: nowrap;"><span style="background: #fff3cd; color: #856404; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 500;">0</span></td>
                    </tr>
                    <tr>
                        <td>1.1.2</td>
                        <td>实际开放床位数</td>
                        <td><span class="admin-badge simple">简单指标</span></td>
                        <td style="white-space: nowrap;">1</td>
                        <td style="white-space: nowrap;">1</td>
                        <td style="white-space: nowrap;"><span style="background: #fff3cd; color: #856404; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 500;">0</span></td>
                    </tr>
                    <tr>
                        <td>1.1.3</td>
                        <td>床位使用率（名称修订）</td>
                        <td><span class="admin-badge">复合指标</span></td>
                        <td style="white-space: nowrap;">1</td>
                        <td style="white-space: nowrap;">1</td>
                        <td style="white-space: nowrap;"><span style="background: #fff3cd; color: #856404; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 500;">0</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <div class="test-title">3. 在窄容器中的显示效果</div>
            <p>测试在较窄的容器中徽章是否仍能正常显示：</p>
            <div style="width: 120px; border: 1px solid #ccc; padding: 10px;">
                <span class="indicator-type-badge simple-indicator">简单指标</span><br><br>
                <span class="indicator-type-badge composite-indicator">复合指标</span>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 多个徽章并排显示</div>
            <p>测试多个徽章并排显示的效果：</p>
            <span class="indicator-type-badge simple-indicator">简单指标</span>
            <span class="indicator-type-badge composite-indicator">复合指标</span>
            <span class="indicator-type-badge simple-indicator">简单指标</span>
            <span class="indicator-type-badge composite-indicator">复合指标</span>
        </div>

        <div class="test-section">
            <div class="test-title">5. 检查要点</div>
            <ul>
                <li>✅ 指标类型徽章文字不换行 (white-space: nowrap)</li>
                <li>✅ 徽章有最小宽度保证美观 (min-width: 60px)</li>
                <li>✅ 文字居中对齐 (text-align: center)</li>
                <li>✅ 颜色区分明显 (绿色=简单，蓝色=复合)</li>
                <li>✅ 表格列标题不换行 ("子指标"、"章节"、"小节")</li>
                <li>✅ 表格数据单元格内容不换行</li>
                <li>✅ 子指标数量徽章显示正常</li>
            </ul>
        </div>
    </div>
</body>
</html>
