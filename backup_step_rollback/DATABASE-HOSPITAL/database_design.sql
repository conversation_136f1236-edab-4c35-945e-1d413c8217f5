-- 医院等级评审指标管理系统数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS hospital_indicator_system
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE hospital_indicator_system;

-- 1. 章节表(chapters)
CREATE TABLE chapters (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '章节ID',
    code VARCHAR(10) NOT NULL UNIQUE COMMENT '章节编码(如1,2,3)',
    name VARCHAR(100) NOT NULL COMMENT '章节名称',
    description TEXT COMMENT '章节描述',
    icon VARCHAR(50) COMMENT '章节图标',
    color VARCHAR(20) DEFAULT '#1a73e8' COMMENT '章节颜色',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_code (code),
    INDEX idx_sort_order (sort_order)
) COMMENT='章节表';

-- 2. 小节表(sections)
CREATE TABLE sections (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '小节ID',
    chapter_id INT NOT NULL COMMENT '外键，关联章节表',
    code VARCHAR(10) NOT NULL COMMENT '小节编码(如1.1,1.2,2.1)',
    name VARCHAR(100) NOT NULL COMMENT '小节名称',
    description TEXT COMMENT '小节描述',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE,
    UNIQUE KEY unique_chapter_code (chapter_id, code),
    INDEX idx_code (code),
    INDEX idx_sort_order (sort_order)
) COMMENT='小节表';

-- 3. 指标表(indicators)
CREATE TABLE indicators (
    id VARCHAR(20) PRIMARY KEY COMMENT '指标编号(如1.2.1)',
    name VARCHAR(200) NOT NULL COMMENT '指标名称',
    description TEXT COMMENT '指标定义说明',
    calculation_method TEXT COMMENT '计算方法',
    target_value DECIMAL(10,4) COMMENT '目标值',
    current_value DECIMAL(10,4) COMMENT '当前值',
    completion_rate DECIMAL(5,2) COMMENT '完成率(%)',
    data_source VARCHAR(100) COMMENT '数据来源',
    collection_frequency ENUM('daily','weekly','monthly','quarterly','yearly') DEFAULT 'monthly' COMMENT '采集频率',
    collection_method ENUM('auto','manual','mixed') DEFAULT 'manual' COMMENT '采集方式',
    sql_query TEXT COMMENT 'SQL查询语句(自动采集用)',
    parent_id VARCHAR(20) COMMENT '父指标ID(用于子指标)',
    chapter_id INT COMMENT '外键，关联章节表',
    section_id INT COMMENT '外键，关联小节表',
    category VARCHAR(50) COMMENT '指标类别',
    status ENUM('normal','warning','danger') DEFAULT 'normal' COMMENT '指标状态',
    warning_threshold_min DECIMAL(10,4) COMMENT '警告阈值下限',
    warning_threshold_max DECIMAL(10,4) COMMENT '警告阈值上限',
    danger_threshold_min DECIMAL(10,4) COMMENT '危险阈值下限',
    danger_threshold_max DECIMAL(10,4) COMMENT '危险阈值上限',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    notes TEXT COMMENT '注意事项',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_parent_id (parent_id),
    INDEX idx_chapter_id (chapter_id),
    INDEX idx_section_id (section_id),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order),
    FOREIGN KEY (parent_id) REFERENCES indicators(id) ON DELETE SET NULL,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE SET NULL,
    FOREIGN KEY (section_id) REFERENCES sections(id) ON DELETE SET NULL
) COMMENT='指标表';

-- 4. 标签表(tags)
CREATE TABLE tags (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID',
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '标签名称',
    color VARCHAR(20) DEFAULT '#1a73e8' COMMENT '标签颜色',
    description TEXT COMMENT '标签备注说明',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='标签表';

-- 5. 指标标签关联表(indicator_tags)
CREATE TABLE indicator_tags (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    indicator_id VARCHAR(20) NOT NULL COMMENT '外键，关联指标表',
    tag_id INT NOT NULL COMMENT '外键，关联标签表',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
    UNIQUE KEY unique_indicator_tag (indicator_id, tag_id)
) COMMENT='指标标签关联表';

-- 6. 科室表(departments)
CREATE TABLE departments (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '科室ID',
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '科室名称',
    code VARCHAR(20) UNIQUE COMMENT '科室编码',
    type ENUM('clinical','medical_tech','admin','support') DEFAULT 'clinical' COMMENT '科室类型',
    parent_id INT COMMENT '上级科室ID',
    director VARCHAR(50) COMMENT '科室主任',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    description TEXT COMMENT '科室描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_parent_id (parent_id),
    INDEX idx_type (type),
    FOREIGN KEY (parent_id) REFERENCES departments(id) ON DELETE SET NULL
) COMMENT='科室表';

-- 7. 指标科室关联表(indicator_departments)
CREATE TABLE indicator_departments (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    indicator_id VARCHAR(20) NOT NULL COMMENT '外键，关联指标表',
    department_id INT NOT NULL COMMENT '外键，关联科室表',
    role_type ENUM('lead','numerator','denominator') NOT NULL COMMENT '科室角色类型',
    responsibility VARCHAR(200) COMMENT '责任描述',
    contact_person VARCHAR(50) COMMENT '联系人',
    data_description TEXT COMMENT '数据说明',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    INDEX idx_role_type (role_type)
) COMMENT='指标科室关联表';

-- 8. 指标关联表(indicator_relations)
CREATE TABLE indicator_relations (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    source_indicator_id VARCHAR(20) NOT NULL COMMENT '源指标ID',
    target_indicator_id VARCHAR(20) NOT NULL COMMENT '目标指标ID',
    relation_type ENUM('related','dependent','similar') DEFAULT 'related' COMMENT '关联类型',
    description VARCHAR(200) COMMENT '关联描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (source_indicator_id) REFERENCES indicators(id) ON DELETE CASCADE,
    FOREIGN KEY (target_indicator_id) REFERENCES indicators(id) ON DELETE CASCADE,
    UNIQUE KEY unique_relation (source_indicator_id, target_indicator_id),
    INDEX idx_relation_type (relation_type)
) COMMENT='指标关联表';

-- 9. 指标数据历史表(indicator_data_history)
CREATE TABLE indicator_data_history (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    indicator_id VARCHAR(20) NOT NULL COMMENT '外键，关联指标表',
    value DECIMAL(15,4) COMMENT '指标值',
    target_value DECIMAL(15,4) COMMENT '目标值',
    completion_rate DECIMAL(5,2) COMMENT '完成率',
    data_period DATE NOT NULL COMMENT '数据周期(年月日)',
    data_source VARCHAR(100) COMMENT '数据来源',
    collection_method ENUM('auto','manual') DEFAULT 'manual' COMMENT '采集方式',
    collector_id INT COMMENT '采集人ID',
    status ENUM('draft','submitted','approved','rejected') DEFAULT 'draft' COMMENT '数据状态',
    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (indicator_id) REFERENCES indicators(id) ON DELETE CASCADE,
    INDEX idx_data_period (data_period),
    INDEX idx_status (status),
    INDEX idx_indicator_period (indicator_id, data_period)
) COMMENT='指标数据历史表';

-- 10. 数据审核流程表(data_audit_flow)
CREATE TABLE data_audit_flow (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    data_history_id INT NOT NULL COMMENT '外键，关联指标数据历史表',
    step INT NOT NULL COMMENT '审核步骤(1:科室初审,2:质控办复核,3:医务科终审)',
    auditor_id INT COMMENT '审核人ID',
    audit_result ENUM('pending','approved','rejected') DEFAULT 'pending' COMMENT '审核结果',
    audit_comment TEXT COMMENT '审核意见',
    audit_time TIMESTAMP NULL COMMENT '审核时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (data_history_id) REFERENCES indicator_data_history(id) ON DELETE CASCADE,
    INDEX idx_step (step),
    INDEX idx_audit_result (audit_result)
) COMMENT='数据审核流程表';

-- 11. 用户表(users)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    department_id INT COMMENT '所属科室ID',
    role ENUM('admin','manager','operator','viewer') DEFAULT 'viewer' COMMENT '用户角色',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    INDEX idx_role (role),
    INDEX idx_department_id (department_id)
) COMMENT='用户表';

-- 12. 系统配置表(system_config)
CREATE TABLE system_config (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string','number','boolean','json') DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(200) COMMENT '配置描述',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='系统配置表';

-- ========================================
-- 示例数据插入
-- ========================================

-- 从Excel文件提取的章节数据
INSERT INTO chapters (code, name, description, icon, color, sort_order) VALUES
('1', '资源配置与运行数据指标', '第1章：资源配置与运行数据指标', 'fas fa-chart-bar', '#1a73e8', 1),
('2', '医疗服务能力与医院质量安全指标', '第2章：医疗服务能力与医院质量安全指标', 'fas fa-heartbeat', '#34a853', 2),
('3', '重点专业质量控制指标', '第3章：重点专业质量控制指标', 'fas fa-check-circle', '#ea4335', 3),
('4', '单病种质量控制指标', '第4章：单病种质量控制指标', 'fas fa-clipboard-list', '#fbbc04', 4),
('5', '重点医疗技术临床应用质量控制指标', '第5章：重点医疗技术临床应用质量控制指标', 'fas fa-microscope', '#9c27b0', 5);

-- 小节数据将根据实际需求插入
-- 以下是插入示例（请根据实际情况修改）：
/*
-- 插入小节数据示例
INSERT INTO sections (chapter_id, code, name, description, sort_order) VALUES
(1, '1.1', '小节名称1.1', '小节描述1.1', 1),
(1, '1.2', '小节名称1.2', '小节描述1.2', 2),
(2, '2.1', '小节名称2.1', '小节描述2.1', 1);
*/

-- 插入标签数据
INSERT INTO tags (name, color, description) VALUES
('关键指标', '#1a73e8', '用于标记医院重点关注的核心指标，这些指标直接关系到医院的整体评价和等级评审结果。'),
('医疗安全', '#34a853', '与医疗安全相关的指标，包括医疗质量、患者安全、医院感染等方面的指标。'),
('患者服务', '#fbbc04', '与患者服务体验相关的指标，包括就医流程、候诊时间、满意度等方面。');

-- 插入科室数据
INSERT INTO departments (name, code, type, director, contact_phone, description) VALUES
('医务科', 'YWK', 'admin', '张主任', '010-12345678', '负责医疗质量管理和指标监控'),
('质控办', 'ZKB', 'admin', '李主任', '010-12345679', '负责质量控制和数据审核'),
('信息科', 'XXK', 'support', '王主任', '010-12345680', '负责信息系统维护和数据采集'),
('药剂科', 'YJK', 'medical_tech', '赵主任', '010-12345681', '负责药品管理相关指标'),
('内科', 'NK', 'clinical', '陈主任', '010-12345682', '内科临床科室'),
('外科', 'WK', 'clinical', '刘主任', '010-12345683', '外科临床科室');

-- 插入用户数据
INSERT INTO users (username, password, real_name, email, phone, department_id, role) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', '<EMAIL>', '13800138000', 1, 'admin'),
('manager1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '质控主管', '<EMAIL>', '13800138001', 2, 'manager'),
('operator1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '数据录入员', '<EMAIL>', '13800138002', 3, 'operator');

-- 指标数据将由用户根据实际需求插入
-- 注意：插入指标前需要先插入对应的章节和小节数据
-- 以下是插入示例（请根据实际情况修改）：
/*
-- 插入指标数据示例
INSERT INTO indicators (id, name, description, calculation_method, target_value, chapter_id, section_id, category, data_source, collection_frequency, sort_order, notes) VALUES
('1.2.1', '指标名称示例', '指标描述示例', '计算方法示例', 0.88, NULL, NULL, '指标类别', '数据来源', 'yearly', 1, '注意事项');

-- 更新子指标的父级关系示例
UPDATE indicators SET parent_id = '1.2.1' WHERE id IN ('*******', '*******');
*/

-- 指标相关关联数据将由用户根据实际需求插入
-- 以下是插入示例（请根据实际情况修改）：
/*
-- 插入指标标签关联示例
INSERT INTO indicator_tags (indicator_id, tag_id) VALUES
('指标ID', 标签ID); -- 示例：('1.2.1', 1)

-- 插入指标科室关联示例
INSERT INTO indicator_departments (indicator_id, department_id, role_type, responsibility, contact_person) VALUES
('指标ID', 科室ID, '角色类型', '责任描述', '联系人');

-- 插入指标关联关系示例
INSERT INTO indicator_relations (source_indicator_id, target_indicator_id, relation_type, description) VALUES
('源指标ID', '目标指标ID', '关联类型', '关联描述');
*/

-- 插入系统配置
INSERT INTO system_config (config_key, config_value, config_type, description, is_system) VALUES
('system_name', '医院等级评审指标管理系统', 'string', '系统名称', TRUE),
('data_collection_reminder_days', '3', 'number', '数据采集提醒提前天数', FALSE),
('auto_audit_enabled', 'false', 'boolean', '是否启用自动审核', FALSE),
('max_file_upload_size', '10', 'number', '最大文件上传大小(MB)', FALSE),
('email_notification_enabled', 'true', 'boolean', '是否启用邮件通知', FALSE);

-- ========================================
-- 创建视图
-- ========================================

-- 指标详情视图（包含章节、小节、标签和科室信息）
CREATE VIEW v_indicator_details AS
SELECT
    i.id,
    i.name,
    i.description,
    i.calculation_method,
    i.target_value,
    i.current_value,
    i.completion_rate,
    i.data_source,
    i.collection_frequency,
    i.collection_method,
    i.parent_id,
    i.category,
    i.status,
    i.sort_order,
    i.notes,
    i.is_active,
    i.created_at,
    i.updated_at,
    -- 章节信息
    c.code AS chapter_code,
    c.name AS chapter_name,
    c.icon AS chapter_icon,
    c.color AS chapter_color,
    -- 小节信息
    s.code AS section_code,
    s.name AS section_name,
    -- 标签信息
    GROUP_CONCAT(DISTINCT CONCAT(t.name, ':', t.color) SEPARATOR ';') AS tags,
    -- 牵头科室
    (SELECT d.name FROM indicator_departments id
     JOIN departments d ON id.department_id = d.id
     WHERE id.indicator_id = i.id AND id.role_type = 'lead' LIMIT 1) AS lead_department,
    -- 分子科室
    GROUP_CONCAT(DISTINCT CASE WHEN id2.role_type = 'numerator' THEN d2.name END SEPARATOR ',') AS numerator_departments,
    -- 分母科室
    GROUP_CONCAT(DISTINCT CASE WHEN id2.role_type = 'denominator' THEN d2.name END SEPARATOR ',') AS denominator_departments
FROM indicators i
LEFT JOIN chapters c ON i.chapter_id = c.id
LEFT JOIN sections s ON i.section_id = s.id
LEFT JOIN indicator_tags it ON i.id = it.indicator_id
LEFT JOIN tags t ON it.tag_id = t.id
LEFT JOIN indicator_departments id2 ON i.id = id2.indicator_id
LEFT JOIN departments d2 ON id2.department_id = d2.id
WHERE i.is_active = TRUE
GROUP BY i.id;

-- 指标数据统计视图
CREATE VIEW v_indicator_data_stats AS
SELECT
    i.id,
    i.name,
    i.target_value,
    -- 最新数据
    (SELECT idh.value FROM indicator_data_history idh
     WHERE idh.indicator_id = i.id AND idh.status = 'approved'
     ORDER BY idh.data_period DESC LIMIT 1) AS latest_value,
    (SELECT idh.data_period FROM indicator_data_history idh
     WHERE idh.indicator_id = i.id AND idh.status = 'approved'
     ORDER BY idh.data_period DESC LIMIT 1) AS latest_period,
    -- 完成率
    CASE
        WHEN i.target_value > 0 THEN
            ROUND((SELECT idh.value FROM indicator_data_history idh
                   WHERE idh.indicator_id = i.id AND idh.status = 'approved'
                   ORDER BY idh.data_period DESC LIMIT 1) / i.target_value * 100, 2)
        ELSE NULL
    END AS completion_rate,
    -- 数据状态统计
    (SELECT COUNT(*) FROM indicator_data_history idh
     WHERE idh.indicator_id = i.id AND idh.status = 'draft') AS draft_count,
    (SELECT COUNT(*) FROM indicator_data_history idh
     WHERE idh.indicator_id = i.id AND idh.status = 'submitted') AS submitted_count,
    (SELECT COUNT(*) FROM indicator_data_history idh
     WHERE idh.indicator_id = i.id AND idh.status = 'approved') AS approved_count
FROM indicators i
WHERE i.is_active = TRUE;

-- 章节小节层级视图
CREATE VIEW v_chapter_section_hierarchy AS
SELECT
    c.id AS chapter_id,
    c.code AS chapter_code,
    c.name AS chapter_name,
    c.description AS chapter_description,
    c.icon AS chapter_icon,
    c.color AS chapter_color,
    c.sort_order AS chapter_sort_order,
    s.id AS section_id,
    s.code AS section_code,
    s.name AS section_name,
    s.description AS section_description,
    s.sort_order AS section_sort_order,
    -- 统计该小节下的指标数量
    (SELECT COUNT(*) FROM indicators i
     WHERE i.section_id = s.id AND i.is_active = TRUE) AS indicator_count,
    -- 统计该小节下已完成的指标数量
    (SELECT COUNT(*) FROM indicators i
     WHERE i.section_id = s.id AND i.is_active = TRUE
     AND i.status = 'normal') AS completed_indicator_count
FROM chapters c
LEFT JOIN sections s ON c.id = s.chapter_id
WHERE c.is_active = TRUE AND (s.is_active = TRUE OR s.id IS NULL)
ORDER BY c.sort_order, s.sort_order;

-- ========================================
-- 常用查询语句示例
-- ========================================

-- 0. 查询章节小节层级结构
/*
SELECT * FROM v_chapter_section_hierarchy ORDER BY chapter_sort_order, section_sort_order;
*/

-- 1. 查询指定章节下的所有小节和指标统计
/*
SELECT
    c.name AS chapter_name,
    s.name AS section_name,
    COUNT(i.id) AS indicator_count,
    AVG(i.completion_rate) AS avg_completion_rate
FROM chapters c
LEFT JOIN sections s ON c.id = s.chapter_id
LEFT JOIN indicators i ON s.id = i.section_id AND i.is_active = TRUE
WHERE c.code = '1'
GROUP BY c.id, s.id
ORDER BY s.sort_order;
*/

-- 2. 查询所有指标及其章节小节信息
/*
SELECT
    i.id,
    i.name,
    i.category,
    i.status,
    GROUP_CONCAT(t.name SEPARATOR ', ') AS tags
FROM indicators i
LEFT JOIN indicator_tags it ON i.id = it.indicator_id
LEFT JOIN tags t ON it.tag_id = t.id
WHERE i.is_active = TRUE
GROUP BY i.id
ORDER BY i.id;
*/

-- 2. 查询指定标签的所有指标
/*
SELECT DISTINCT i.*
FROM indicators i
JOIN indicator_tags it ON i.id = it.indicator_id
JOIN tags t ON it.tag_id = t.id
WHERE t.name = '关键指标' AND i.is_active = TRUE
ORDER BY i.id;
*/

-- 3. 查询指标的科室分工
/*
SELECT
    i.id,
    i.name,
    d.name AS department_name,
    id.role_type,
    id.responsibility,
    id.contact_person
FROM indicators i
JOIN indicator_departments id ON i.id = id.indicator_id
JOIN departments d ON id.department_id = d.id
WHERE i.id = '1.2.1'
ORDER BY id.role_type;
*/

-- 4. 查询指标的历史数据趋势
/*
SELECT
    i.name,
    idh.data_period,
    idh.value,
    idh.target_value,
    idh.completion_rate,
    idh.status
FROM indicators i
JOIN indicator_data_history idh ON i.id = idh.indicator_id
WHERE i.id = '1.2.1'
ORDER BY idh.data_period DESC
LIMIT 12; -- 最近12个周期
*/

-- 5. 查询待审核的数据
/*
SELECT
    i.name AS indicator_name,
    idh.value,
    idh.data_period,
    d.name AS department_name,
    u.real_name AS collector_name,
    idh.created_at
FROM indicator_data_history idh
JOIN indicators i ON idh.indicator_id = i.id
LEFT JOIN users u ON idh.collector_id = u.id
LEFT JOIN departments d ON u.department_id = d.id
WHERE idh.status = 'submitted'
ORDER BY idh.created_at DESC;
*/

-- 6. 查询指标完成情况统计
/*
SELECT
    i.category,
    COUNT(*) AS total_indicators,
    SUM(CASE WHEN i.status = 'normal' THEN 1 ELSE 0 END) AS normal_count,
    SUM(CASE WHEN i.status = 'warning' THEN 1 ELSE 0 END) AS warning_count,
    SUM(CASE WHEN i.status = 'danger' THEN 1 ELSE 0 END) AS danger_count
FROM indicators i
WHERE i.is_active = TRUE
GROUP BY i.category;
*/

-- 7. 查询科室负责的指标数量
/*
SELECT
    d.name AS department_name,
    COUNT(DISTINCT CASE WHEN id.role_type = 'lead' THEN id.indicator_id END) AS lead_count,
    COUNT(DISTINCT CASE WHEN id.role_type = 'numerator' THEN id.indicator_id END) AS numerator_count,
    COUNT(DISTINCT CASE WHEN id.role_type = 'denominator' THEN id.indicator_id END) AS denominator_count
FROM departments d
LEFT JOIN indicator_departments id ON d.id = id.department_id
WHERE d.is_active = TRUE
GROUP BY d.id, d.name
ORDER BY lead_count DESC;
*/

-- 8. 查询用户权限和所属科室
/*
SELECT
    u.username,
    u.real_name,
    u.role,
    d.name AS department_name,
    u.is_active,
    u.last_login_at
FROM users u
LEFT JOIN departments d ON u.department_id = d.id
ORDER BY u.role, u.real_name;
*/
