#!/bin/bash

# 医院等级评审指标管理系统 - 数据库管理脚本
# 使用方法: ./manage_database.sh [命令]

DB_NAME="hospital_indicator_system.db"

# 检查数据库文件是否存在
check_database() {
    if [ ! -f "$DB_NAME" ]; then
        echo "❌ 数据库文件不存在: $DB_NAME"
        echo "请先运行: ./manage_database.sh create"
        exit 1
    fi
}

# 创建数据库
create_database() {
    echo "🔨 正在创建数据库..."
    if [ -f "$DB_NAME" ]; then
        echo "⚠️  数据库已存在，是否要重新创建？(y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            rm "$DB_NAME"
            echo "🗑️  已删除旧数据库"
        else
            echo "❌ 操作已取消"
            exit 1
        fi
    fi
    
    sqlite3 "$DB_NAME" < create_sqlite_database.sql
    if [ $? -eq 0 ]; then
        echo "✅ 数据库创建成功: $DB_NAME"
        show_status
    else
        echo "❌ 数据库创建失败"
        exit 1
    fi
}

# 显示数据库状态
show_status() {
    check_database
    echo "📊 数据库状态报告"
    echo "=================="
    echo "📁 数据库文件: $DB_NAME"
    echo "📏 文件大小: $(ls -lh $DB_NAME | awk '{print $5}')"
    echo ""
    echo "📋 表统计信息:"
    sqlite3 "$DB_NAME" ".mode column" ".headers on" "
    SELECT 
        'chapters' as 表名, COUNT(*) as 记录数, '章节管理' as 说明 FROM chapters
    UNION ALL SELECT 'sections', COUNT(*), '小节管理' FROM sections
    UNION ALL SELECT 'indicators', COUNT(*), '指标管理' FROM indicators
    UNION ALL SELECT 'tags', COUNT(*), '标签管理' FROM tags
    UNION ALL SELECT 'departments', COUNT(*), '科室管理' FROM departments
    UNION ALL SELECT 'users', COUNT(*), '用户管理' FROM users
    UNION ALL SELECT 'system_config', COUNT(*), '系统配置' FROM system_config;
    "
}

# 查看章节数据
show_chapters() {
    check_database
    echo "📚 章节数据"
    echo "==========="
    sqlite3 "$DB_NAME" ".mode column" ".headers on" "
    SELECT 
        code as 编码,
        name as 章节名称,
        icon as 图标,
        color as 颜色,
        sort_order as 排序
    FROM chapters 
    ORDER BY sort_order;
    "
}

# 查看科室数据
show_departments() {
    check_database
    echo "🏥 科室数据"
    echo "==========="
    sqlite3 "$DB_NAME" ".mode column" ".headers on" "
    SELECT 
        code as 编码,
        name as 科室名称,
        type as 类型,
        director as 主任,
        contact_phone as 联系电话
    FROM departments 
    ORDER BY id;
    "
}

# 查看用户数据
show_users() {
    check_database
    echo "👥 用户数据"
    echo "==========="
    sqlite3 "$DB_NAME" ".mode column" ".headers on" "
    SELECT 
        username as 用户名,
        real_name as 真实姓名,
        role as 角色,
        email as 邮箱,
        phone as 电话,
        is_active as 状态
    FROM users 
    ORDER BY id;
    "
}

# 进入交互模式
interactive() {
    check_database
    echo "🔧 进入SQLite交互模式"
    echo "提示: 输入 .help 查看帮助，输入 .quit 退出"
    sqlite3 "$DB_NAME"
}

# 备份数据库
backup() {
    check_database
    BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S).db"
    cp "$DB_NAME" "$BACKUP_NAME"
    echo "💾 数据库已备份为: $BACKUP_NAME"
}

# 显示帮助信息
show_help() {
    echo "🏥 医院等级评审指标管理系统 - 数据库管理工具"
    echo ""
    echo "使用方法: $0 [命令]"
    echo ""
    echo "可用命令:"
    echo "  create      创建数据库"
    echo "  status      显示数据库状态"
    echo "  chapters    查看章节数据"
    echo "  departments 查看科室数据"
    echo "  users       查看用户数据"
    echo "  interactive 进入SQLite交互模式"
    echo "  backup      备份数据库"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 create      # 创建新数据库"
    echo "  $0 status      # 查看数据库状态"
    echo "  $0 chapters    # 查看所有章节"
}

# 主程序
case "${1:-help}" in
    "create")
        create_database
        ;;
    "status")
        show_status
        ;;
    "chapters")
        show_chapters
        ;;
    "departments")
        show_departments
        ;;
    "users")
        show_users
        ;;
    "interactive")
        interactive
        ;;
    "backup")
        backup
        ;;
    "help"|*)
        show_help
        ;;
esac
