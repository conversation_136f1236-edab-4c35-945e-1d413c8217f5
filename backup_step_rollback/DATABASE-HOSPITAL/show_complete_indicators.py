#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示包含分子、分母的完整指标数据
"""

import sqlite3

def show_complete_indicators():
    """
    显示完整的指标数据，包括分子、分母等组成部分
    """
    db_path = "hospital_indicator_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=" * 100)
        print("🏥 医院等级评审指标管理系统 - 完整指标数据（包含分子分母）")
        print("=" * 100)
        
        # 获取第一章的所有指标及其组成部分
        cursor.execute("""
            SELECT 
                i.id,
                i.name,
                i.parent_id,
                s.name as section_name,
                c.name as chapter_name
            FROM indicators i
            LEFT JOIN sections s ON i.section_id = s.id
            LEFT JOIN chapters c ON i.chapter_id = c.id
            WHERE c.code = '1'
            ORDER BY i.id
        """)
        
        indicators = cursor.fetchall()
        
        print(f"\n📊 第一章指标总览")
        print(f"指标总数: {len(indicators)}")
        
        # 统计有分子分母的指标数量
        cursor.execute("""
            SELECT COUNT(DISTINCT indicator_id) 
            FROM indicator_components 
            WHERE indicator_id LIKE '1.%'
        """)
        indicators_with_components = cursor.fetchone()[0]
        
        print(f"包含分子分母的指标数: {indicators_with_components}")
        
        # 按小节分组显示指标
        current_section = None
        
        for indicator in indicators:
            indicator_id, indicator_name, parent_id, section_name, chapter_name = indicator
            
            # 如果是新的小节，显示小节标题
            if section_name != current_section:
                current_section = section_name
                print(f"\n" + "=" * 80)
                print(f"📖 {section_name}")
                print("=" * 80)
            
            # 显示指标基本信息
            level_indicator = "  🔸" if parent_id else "  🔹"
            parent_info = f" (父级: {parent_id})" if parent_id else ""
            print(f"\n{level_indicator} {indicator_id} - {indicator_name}{parent_info}")
            
            # 查询该指标的组成部分
            cursor.execute("""
                SELECT component_type, name, unit, lead_department, data_source, logic_definition
                FROM indicator_components
                WHERE indicator_id = ?
                ORDER BY component_type DESC, id
            """, (indicator_id,))
            
            components = cursor.fetchall()
            
            if components:
                for comp_type, comp_name, unit, dept, source, logic in components:
                    type_name = "🔺 分子" if comp_type == 'numerator' else "🔻 分母"
                    print(f"    {type_name}: {comp_name}")
                    if unit:
                        print(f"        📏 单位: {unit}")
                    if dept:
                        print(f"        🏢 牵头科室: {dept}")
                    if source:
                        print(f"        📋 数据来源: {source}")
                    if logic and logic.strip():
                        print(f"        🧮 逻辑定义: {logic[:100]}...")
        
        # 显示统计信息
        print(f"\n" + "=" * 100)
        print("📊 数据统计")
        print("=" * 100)
        
        # 按小节统计指标数量
        cursor.execute("""
            SELECT 
                s.name as section_name,
                COUNT(i.id) as indicator_count,
                COUNT(DISTINCT ic.indicator_id) as indicators_with_components
            FROM sections s
            LEFT JOIN indicators i ON s.id = i.section_id
            LEFT JOIN indicator_components ic ON i.id = ic.indicator_id
            WHERE s.chapter_id = (SELECT id FROM chapters WHERE code = '1')
            GROUP BY s.id, s.name
            ORDER BY s.sort_order
        """)
        
        section_stats = cursor.fetchall()
        
        print(f"\n📋 各小节统计:")
        for section_name, indicator_count, components_count in section_stats:
            print(f"  • {section_name}: {indicator_count}个指标，其中{components_count}个有分子分母")
        
        # 组成部分统计
        cursor.execute("""
            SELECT component_type, COUNT(*) as count
            FROM indicator_components
            WHERE indicator_id LIKE '1.%'
            GROUP BY component_type
        """)
        
        component_stats = cursor.fetchall()
        
        print(f"\n🧮 组成部分统计:")
        for comp_type, count in component_stats:
            type_name = "分子" if comp_type == 'numerator' else "分母"
            print(f"  • {type_name}: {count}个")
        
        # 牵头科室统计
        cursor.execute("""
            SELECT lead_department, COUNT(*) as count
            FROM indicator_components
            WHERE indicator_id LIKE '1.%' AND lead_department != ''
            GROUP BY lead_department
            ORDER BY count DESC
        """)
        
        dept_stats = cursor.fetchall()
        
        print(f"\n🏢 牵头科室统计:")
        for dept, count in dept_stats:
            print(f"  • {dept}: {count}个组成部分")
        
        # 数据来源统计
        cursor.execute("""
            SELECT data_source, COUNT(*) as count
            FROM indicator_components
            WHERE indicator_id LIKE '1.%' AND data_source != ''
            GROUP BY data_source
            ORDER BY count DESC
        """)
        
        source_stats = cursor.fetchall()
        
        print(f"\n📋 数据来源统计:")
        for source, count in source_stats:
            print(f"  • {source}: {count}个组成部分")
        
        print(f"\n" + "=" * 100)
        print("✅ 完整指标数据展示完成")
        print("=" * 100)
        
    except Exception as e:
        print(f"❌ 查询数据时出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if conn:
            conn.close()

def show_sample_detailed_indicator():
    """
    显示一个详细的指标示例
    """
    db_path = "hospital_indicator_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"\n" + "=" * 80)
        print("📋 详细指标示例")
        print("=" * 80)
        
        # 选择一个有完整分子分母的指标作为示例
        cursor.execute("""
            SELECT DISTINCT indicator_id
            FROM indicator_components
            WHERE indicator_id LIKE '1.3.%'
            LIMIT 1
        """)
        
        sample_id = cursor.fetchone()
        if not sample_id:
            print("未找到示例指标")
            return
        
        sample_id = sample_id[0]
        
        # 获取指标基本信息
        cursor.execute("""
            SELECT i.id, i.name, i.description, s.name as section_name
            FROM indicators i
            LEFT JOIN sections s ON i.section_id = s.id
            WHERE i.id = ?
        """, (sample_id,))
        
        indicator_info = cursor.fetchone()
        if indicator_info:
            indicator_id, name, description, section_name = indicator_info
            print(f"\n🎯 指标ID: {indicator_id}")
            print(f"📝 指标名称: {name}")
            print(f"📖 所属小节: {section_name}")
            if description:
                print(f"📄 描述: {description}")
        
        # 获取组成部分详细信息
        cursor.execute("""
            SELECT component_type, name, unit, lead_department, data_source, logic_definition
            FROM indicator_components
            WHERE indicator_id = ?
            ORDER BY component_type DESC
        """, (sample_id,))
        
        components = cursor.fetchall()
        
        for comp_type, comp_name, unit, dept, source, logic in components:
            type_name = "分子" if comp_type == 'numerator' else "分母"
            print(f"\n🔸 {type_name}信息:")
            print(f"   名称: {comp_name}")
            print(f"   单位: {unit}")
            print(f"   牵头科室: {dept}")
            print(f"   数据来源: {source}")
            if logic and logic.strip():
                print(f"   逻辑定义: {logic}")
        
    except Exception as e:
        print(f"❌ 查询示例数据时出错: {e}")
    finally:
        if conn:
            conn.close()

def main():
    show_complete_indicators()
    show_sample_detailed_indicator()

if __name__ == "__main__":
    main()
