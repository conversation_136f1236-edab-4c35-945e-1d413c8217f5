{% extends "admin/base.html" %}

{% block title %}编辑指标 - {{ indicator_id }}{% endblock %}

{% block content %}
<div class="indicator-edit-container">
    <!-- 加载状态 -->
    <div id="loading-state" class="loading-spinner">
        <div class="spinner"></div>
    </div>

    <!-- 错误状态 -->
    <div id="error-state" style="display: none;">
        <div class="error-message">
            <h3>加载失败</h3>
            <p id="error-message">无法加载指标信息，请稍后重试。</p>
            <button class="md-button outlined" onclick="loadIndicatorData()">
                <span class="material-symbols-outlined">refresh</span>
                <span>重新加载</span>
            </button>
        </div>
    </div>

    <!-- 主要内容 -->
    <div id="main-content" style="display: none;">
        <!-- 页面头部 -->
        <div class="edit-header">
            <div class="breadcrumb">
                <a href="/admin/dashboard">后台管理</a> /
                <a href="/admin/indicators">指标管理</a> /
                <a href="/admin/indicators/{{ indicator_id }}">{{ indicator_id }}</a> /
                <span>编辑</span>
            </div>
            <h1 id="page-title">
                <span class="material-symbols-outlined">medical_information</span>
                编辑指标 - <span class="indicator-id">{{ indicator_id }}</span>
            </h1>
        </div>

        <!-- 测试按钮 -->
        <button onclick="testFunction()" style="margin-bottom: 20px; padding: 10px; background: red; color: white;">测试JavaScript</button>

        <!-- 编辑表单 -->
        <form id="indicator-form" class="edit-form">
            <!-- 基本信息 -->
            <div class="form-section">
                <h3>
                    <span class="material-symbols-outlined">info</span>
                    基本信息
                </h3>
                <div class="form-grid">
                    <div class="form-field">
                        <label for="name">指标名称 *</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    <div class="form-field">
                        <label for="indicator_type">指标类型</label>
                        <select id="indicator_type" name="indicator_type">
                            <option value="composite">复合指标</option>
                            <option value="simple">简单指标</option>
                        </select>
                    </div>
                </div>
                <div class="form-field">
                    <label for="description">指标描述</label>
                    <textarea id="description" name="description" rows="3"></textarea>
                </div>
            </div>

            <!-- 简单指标属性 -->
            <div class="form-section" id="simple-attributes" style="display: none;">
                <h3>
                    <span class="material-symbols-outlined">tune</span>
                    简单指标属性
                    <span class="section-hint">（仅适用于简单指标）</span>
                </h3>
                <div class="form-grid">
                    <div class="form-field">
                        <label for="unit">单位</label>
                        <input type="text" id="unit" name="unit" placeholder="例如：%、人、次">
                    </div>
                    <div class="form-field">
                        <label for="lead_department">牵头科室</label>
                        <input type="text" id="lead_department" name="lead_department" placeholder="例如：医务科">
                    </div>
                </div>
                <div class="form-grid">
                    <div class="form-field">
                        <label for="data_source">数据来源</label>
                        <input type="text" id="data_source" name="data_source" placeholder="例如：HIS系统">
                    </div>
                    <div class="form-field">
                        <label for="reference_range">参考范围</label>
                        <input type="text" id="reference_range" name="reference_range" placeholder="例如：≥95%、<5%、80-120">
                    </div>
                </div>
                <div class="form-field">
                    <label for="logic_definition">逻辑定义</label>
                    <textarea id="logic_definition" name="logic_definition" rows="3" placeholder="详细描述指标的计算逻辑和业务含义"></textarea>
                </div>
            </div>

            <!-- 复合指标详细信息 -->
            <div class="form-section" id="composite-details" style="display: block;">
                <h3>
                    <span class="material-symbols-outlined">functions</span>
                    复合指标详细信息
                    <span class="section-hint">（仅适用于复合指标）</span>
                </h3>
                <div class="form-field">
                    <label for="calculation_formula">计算公式</label>
                    <textarea id="calculation_formula" name="calculation_formula" rows="2" placeholder="指标的具体计算公式，如：分子/分母×100%"></textarea>
                </div>

                <!-- 分子信息 -->
                <div class="subsection">
                    <h4>
                        <span class="material-symbols-outlined">trending_up</span>
                        分子信息
                    </h4>
                    <div class="form-field">
                        <label for="numerator_description">分子说明</label>
                        <textarea id="numerator_description" name="numerator_description" rows="2" placeholder="分子的详细说明"></textarea>
                    </div>
                    <div class="form-grid">
                        <div class="form-field">
                            <label for="numerator_unit">分子单位</label>
                            <input type="text" id="numerator_unit" name="numerator_unit" placeholder="例如：人、次、例">
                        </div>
                        <div class="form-field">
                            <label for="numerator_department">分子牵头科室</label>
                            <input type="text" id="numerator_department" name="numerator_department" placeholder="例如：医务科">
                        </div>
                    </div>
                    <div class="form-grid">
                        <div class="form-field">
                            <label for="numerator_source">分子数据来源</label>
                            <input type="text" id="numerator_source" name="numerator_source" placeholder="例如：HIS系统">
                        </div>
                        <div class="form-field">
                            <label for="numerator_logic">分子逻辑定义</label>
                            <input type="text" id="numerator_logic" name="numerator_logic" placeholder="分子的计算逻辑">
                        </div>
                    </div>
                </div>

                <!-- 分母信息 -->
                <div class="subsection">
                    <h4>
                        <span class="material-symbols-outlined">trending_down</span>
                        分母信息
                    </h4>
                    <div class="form-field">
                        <label for="denominator_description">分母说明</label>
                        <textarea id="denominator_description" name="denominator_description" rows="2" placeholder="分母的详细说明"></textarea>
                    </div>
                    <div class="form-grid">
                        <div class="form-field">
                            <label for="denominator_unit">分母单位</label>
                            <input type="text" id="denominator_unit" name="denominator_unit" placeholder="例如：人、次、例">
                        </div>
                        <div class="form-field">
                            <label for="denominator_department">分母牵头科室</label>
                            <input type="text" id="denominator_department" name="denominator_department" placeholder="例如：医务科">
                        </div>
                    </div>
                    <div class="form-grid">
                        <div class="form-field">
                            <label for="denominator_source">分母数据来源</label>
                            <input type="text" id="denominator_source" name="denominator_source" placeholder="例如：HIS系统">
                        </div>
                        <div class="form-field">
                            <label for="denominator_logic">分母逻辑定义</label>
                            <input type="text" id="denominator_logic" name="denominator_logic" placeholder="分母的计算逻辑">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 通用详细信息 -->
            <div class="form-section">
                <h3>
                    <span class="material-symbols-outlined">description</span>
                    详细信息
                </h3>
                <div class="form-field">
                    <label for="indicator_definition">指标定义</label>
                    <textarea id="indicator_definition" name="indicator_definition" rows="3" placeholder="详细描述指标的定义和含义"></textarea>
                </div>
                <div class="form-field">
                    <label for="statistical_scope">统计范围</label>
                    <textarea id="statistical_scope" name="statistical_scope" rows="2" placeholder="指标统计的范围和对象"></textarea>
                </div>
                <div class="form-field">
                    <label for="data_sources">数据来源</label>
                    <textarea id="data_sources" name="data_sources" rows="2" placeholder="详细的数据来源说明"></textarea>
                </div>
                <div class="form-field">
                    <label for="collection_frequency_detail">统计频率</label>
                    <input type="text" id="collection_frequency_detail" name="collection_frequency_detail" placeholder="如：月度、季度、年度">
                </div>
                <div class="form-field">
                    <label for="reference_value">标准值/参考值</label>
                    <input type="text" id="reference_value" name="reference_value" placeholder="如：≥95%、<5%等">
                </div>
                <div class="form-field">
                    <label for="monitoring_analysis">监测分析</label>
                    <textarea id="monitoring_analysis" name="monitoring_analysis" rows="3" placeholder="监测分析的方法和要求"></textarea>
                </div>
            </div>

            <!-- 计算方法 -->
            <div class="form-section">
                <h3>
                    <span class="material-symbols-outlined">functions</span>
                    计算方法
                </h3>
                <div class="form-field">
                    <label for="calculation_method">计算公式</label>
                    <textarea id="calculation_method" name="calculation_method" rows="3" placeholder="描述指标的计算方法"></textarea>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="form-actions">
                <button type="button" class="md-button outlined" onclick="goBack()">
                    <span class="material-symbols-outlined">arrow_back</span>
                    <span>取消</span>
                </button>
                <button type="submit" class="md-button filled">
                    <span class="material-symbols-outlined">save</span>
                    <span>保存更改</span>
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .indicator-edit-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .edit-header {
        margin-bottom: 32px;
    }

    .edit-header h1 {
        margin: 16px 0 0 0;
        font-size: 28px;
        font-weight: 400;
        color: var(--md-on-surface);
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .breadcrumb {
        color: var(--md-on-surface-variant);
        font-size: 14px;
    }

    .breadcrumb a {
        color: var(--md-primary);
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .edit-form {
        display: flex;
        flex-direction: column;
        gap: 32px;
    }

    .form-section {
        background: var(--md-surface);
        border-radius: var(--md-corner-lg);
        padding: 24px;
        box-shadow: var(--md-elevation-1);
        border: 1px solid var(--md-outline-variant);
    }

    .form-section h3 {
        margin: 0 0 20px 0;
        font-size: 18px;
        font-weight: 500;
        color: var(--md-on-surface);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .section-hint {
        font-size: 12px;
        color: var(--md-on-surface-variant);
        font-weight: 400;
        margin-left: 8px;
    }

    .subsection {
        margin-top: 24px;
        padding: 20px;
        background: var(--md-surface-variant);
        border-radius: var(--md-corner-md);
        border: 1px solid var(--md-outline-variant);
    }

    .subsection h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 500;
        color: var(--md-on-surface);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .subsection h4 .material-symbols-outlined {
        font-size: 20px;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .form-field {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .form-field label {
        font-weight: 500;
        color: var(--md-on-surface);
        font-size: 14px;
    }

    .form-field input,
    .form-field select,
    .form-field textarea {
        padding: 12px 16px;
        border: 1px solid var(--md-outline);
        border-radius: var(--md-corner-sm);
        font-family: var(--md-font-family);
        font-size: 14px;
        background: var(--md-surface);
        color: var(--md-on-surface);
        transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
    }

    .form-field input:focus,
    .form-field select:focus,
    .form-field textarea:focus {
        outline: none;
        border-color: var(--md-primary);
        box-shadow: 0 0 0 2px var(--md-primary-container);
    }

    .form-field textarea {
        resize: vertical;
        min-height: 80px;
    }

    .form-actions {
        display: flex;
        gap: 16px;
        justify-content: flex-end;
        padding-top: 24px;
        border-top: 1px solid var(--md-outline-variant);
    }

    .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 60px;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid var(--md-outline-variant);
        border-top: 4px solid var(--md-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .error-message {
        background: var(--md-error-container);
        color: var(--md-on-error-container);
        padding: 24px;
        border-radius: var(--md-corner-lg);
        text-align: center;
    }

    .indicator-id {
        font-family: 'Roboto Mono', monospace;
        color: var(--md-primary);
    }

    @media (max-width: 768px) {
        .indicator-edit-container {
            padding: 16px;
        }

        .form-grid {
            grid-template-columns: 1fr;
        }

        .form-actions {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
var indicatorData = null;
var indicatorId = '{{ indicator_id }}';

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成');
    loadIndicatorData();
    setupFormHandlers();

    // 设置初始显示状态
    setTimeout(function() {
        toggleDirectAttributes();
    }, 100);
});

// 显示状态函数
function showLoading() {
    document.getElementById('loading-state').style.display = 'flex';
    document.getElementById('error-state').style.display = 'none';
    document.getElementById('main-content').style.display = 'none';
}

function showError(message) {
    document.getElementById('loading-state').style.display = 'none';
    document.getElementById('error-state').style.display = 'block';
    document.getElementById('main-content').style.display = 'none';
    document.getElementById('error-message').textContent = message;
}

function showMainContent() {
    document.getElementById('loading-state').style.display = 'none';
    document.getElementById('error-state').style.display = 'none';
    document.getElementById('main-content').style.display = 'block';
}

// 加载指标数据
function loadIndicatorData() {
    console.log('开始加载指标数据:', indicatorId);
    showLoading();

    fetch('/api/indicators/' + indicatorId)
        .then(function(response) {
            return response.json();
        })
        .then(function(result) {
            console.log('API响应数据:', result);
            if (result.success) {
                indicatorData = result.data;
                populateForm();
                showMainContent();
            } else {
                showError(result.error || '加载失败');
            }
        })
        .catch(function(error) {
            console.error('加载指标数据失败:', error);
            showError(error.message);
        });
}

// 填充表单
function populateForm() {
    console.log('开始填充表单');
    const indicator = indicatorData.indicator;
    console.log('指标信息:', indicator);

    // 基本信息
    document.getElementById('name').value = indicator.name || '';
    document.getElementById('description').value = indicator.description || '';
    document.getElementById('calculation_method').value = indicator.calculation_method || '';
    console.log('基本信息填充完成');

    // 详细信息字段
    document.getElementById('indicator_definition').value = indicator.indicator_definition || '';
    document.getElementById('calculation_formula').value = indicator.calculation_formula || '';
    document.getElementById('numerator_description').value = indicator.numerator_description || '';
    document.getElementById('denominator_description').value = indicator.denominator_description || '';
    document.getElementById('statistical_scope').value = indicator.statistical_scope || '';
    document.getElementById('data_sources').value = indicator.data_sources || '';
    document.getElementById('collection_frequency_detail').value = indicator.collection_frequency_detail || '';
    document.getElementById('reference_value').value = indicator.reference_value || '';
    document.getElementById('monitoring_analysis').value = indicator.monitoring_analysis || '';

    // 分子分母详细字段
    document.getElementById('numerator_unit').value = indicator.numerator_unit || '';
    document.getElementById('numerator_department').value = indicator.numerator_department || '';
    document.getElementById('numerator_source').value = indicator.numerator_source || '';
    document.getElementById('numerator_logic').value = indicator.numerator_logic || '';
    document.getElementById('denominator_unit').value = indicator.denominator_unit || '';
    document.getElementById('denominator_department').value = indicator.denominator_department || '';
    document.getElementById('denominator_source').value = indicator.denominator_source || '';
    document.getElementById('denominator_logic').value = indicator.denominator_logic || '';

    // 直接属性
    document.getElementById('unit').value = indicator.unit || '';
    document.getElementById('lead_department').value = indicator.lead_department || '';
    document.getElementById('data_source').value = indicator.data_source || '';
    document.getElementById('logic_definition').value = indicator.logic_definition || '';
    document.getElementById('reference_range').value = indicator.reference_range || '';
    document.getElementById('indicator_type').value = indicator.indicator_type || 'composite';

    // 根据指标类型显示/隐藏直接属性部分
    // 延迟执行确保DOM完全加载
    setTimeout(() => {
        toggleDirectAttributes();
    }, 100);
}

// 设置表单处理器
function setupFormHandlers() {
    // 指标类型变化时切换显示
    document.getElementById('indicator_type').addEventListener('change', toggleDirectAttributes);

    // 表单提交
    document.getElementById('indicator-form').addEventListener('submit', handleFormSubmit);
}

// 切换字段显示
function toggleDirectAttributes() {
    const indicatorTypeElement = document.getElementById('indicator_type');
    const simpleAttributesSection = document.getElementById('simple-attributes');
    const compositeDetailsSection = document.getElementById('composite-details');

    // 确保所有元素都存在
    if (!indicatorTypeElement || !simpleAttributesSection || !compositeDetailsSection) {
        console.log('等待DOM元素加载...');
        return;
    }

    const indicatorType = indicatorTypeElement.value;
    console.log('当前指标类型:', indicatorType);

    if (indicatorType === 'simple') {
        // 简单指标：显示简单指标属性，隐藏复合指标详细信息
        simpleAttributesSection.style.display = 'block';
        compositeDetailsSection.style.display = 'none';
        console.log('显示简单指标属性');
    } else {
        // 复合指标：隐藏简单指标属性，显示复合指标详细信息
        simpleAttributesSection.style.display = 'none';
        compositeDetailsSection.style.display = 'block';
        console.log('显示复合指标详细信息');
    }
}

// 处理表单提交
async function handleFormSubmit(event) {
    event.preventDefault();

    try {
        const formData = new FormData(event.target);
        const data = Object.fromEntries(formData.entries());

        const response = await fetch(`/api/indicators/${indicatorId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            alert('✅ 指标更新成功！');
            window.location.href = `/admin/indicators/${indicatorId}`;
        } else {
            throw new Error(result.error || '更新失败');
        }

    } catch (error) {
        console.error('更新指标失败:', error);
        alert('❌ 更新失败：' + error.message);
    }
}

// 返回上一页
function goBack() {
    window.location.href = `/admin/indicators/${indicatorId}`;
}
</script>
{% endblock %}
