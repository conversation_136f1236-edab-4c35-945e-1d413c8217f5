#!/usr/bin/env python3
"""
分步骤回退 - 回退到前面4个版本
"""

import os
import shutil
import subprocess
import sys

def backup_current_state():
    """备份当前状态"""
    print("📦 备份当前状态...")
    
    backup_dir = "backup_step_rollback"
    if os.path.exists(backup_dir):
        shutil.rmtree(backup_dir)
    
    os.makedirs(backup_dir)
    
    try:
        subprocess.run([
            'rsync', '-av', '--exclude=__pycache__', '--exclude=*.pyc',
            './', f'{backup_dir}/'
        ], check=True)
        print(f"   ✅ 当前状态已备份到 {backup_dir}/")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   ❌ 备份失败: {e}")
        return False

def check_git_history():
    """检查Git历史记录"""
    print("\n🔍 检查Git历史记录...")
    
    try:
        # 检查是否在Git仓库中
        result = subprocess.run(['git', 'status'], capture_output=True, text=True)
        if result.returncode != 0:
            print("   ⚠️  当前不在Git仓库中，将使用文件系统方法")
            return False, []
        
        # 获取最近的提交历史
        result = subprocess.run([
            'git', 'log', '--oneline', '-10'
        ], capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            commits = result.stdout.strip().split('\n')
            print(f"   ✅ 找到 {len(commits)} 个最近的提交:")
            for i, commit in enumerate(commits):
                print(f"     {i+1}. {commit}")
            
            return True, commits
        else:
            print("   ⚠️  无法获取Git历史")
            return False, []
            
    except FileNotFoundError:
        print("   ❌ Git未安装或不可用")
        return False, []

def create_version_snapshots():
    """创建版本快照"""
    print("\n📸 创建版本快照...")
    
    # 定义4个版本的配置
    versions = [
        {
            'name': 'version_1_minimal',
            'description': '最小化版本 - 基础功能',
            'features': ['basic_frontend', 'simple_api', 'core_database']
        },
        {
            'name': 'version_2_enhanced',
            'description': '增强版本 - 添加后端管理',
            'features': ['frontend', 'backend_admin', 'full_api', 'database']
        },
        {
            'name': 'version_3_complete',
            'description': '完整版本 - 添加高级功能',
            'features': ['frontend', 'backend', 'api', 'database', 'modal', 'search']
        },
        {
            'name': 'version_4_advanced',
            'description': '高级版本 - 添加参考范围功能',
            'features': ['all_features', 'reference_range', 'advanced_ui']
        }
    ]
    
    for i, version in enumerate(versions, 1):
        print(f"\n   📋 版本 {i}: {version['description']}")
        print(f"      功能: {', '.join(version['features'])}")
    
    return versions

def rollback_to_version(version_number):
    """回退到指定版本"""
    print(f"\n🔄 回退到版本 {version_number}...")
    
    if version_number == 1:
        return create_minimal_version()
    elif version_number == 2:
        return create_enhanced_version()
    elif version_number == 3:
        return create_complete_version()
    elif version_number == 4:
        return create_advanced_version()
    else:
        print("   ❌ 无效的版本号")
        return False

def create_minimal_version():
    """创建最小化版本"""
    print("   🔧 创建最小化版本...")
    
    # 创建简单的主应用文件
    minimal_app = '''#!/usr/bin/env python3
"""
医院等级评审指标说明手册系统 - 最小化版本
"""

from flask import Flask, render_template, jsonify, request
import sqlite3
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'hospital_indicator_system_2024'

DATABASE_PATH = 'DATABASE-HOSPITAL/hospital_indicator_system.db'

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def dict_from_row(row):
    """将sqlite3.Row转换为字典"""
    if row is None:
        return None
    return dict(row)

@app.route('/')
def index():
    """首页"""
    return render_template('index.html')

@app.route('/api/chapters', methods=['GET'])
def get_chapters():
    """获取所有章节"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT c.*, 
                   COUNT(DISTINCT s.id) as section_count,
                   COUNT(DISTINCT i.id) as indicator_count
            FROM chapters c
            LEFT JOIN sections s ON c.id = s.chapter_id AND s.is_active = 1
            LEFT JOIN indicators i ON c.id = i.chapter_id AND i.is_active = 1
            WHERE c.is_active = 1
            GROUP BY c.id
            ORDER BY c.id
        """)
        
        chapters = [dict_from_row(row) for row in cursor.fetchall()]
        conn.close()
        
        return jsonify({'success': True, 'data': chapters})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/indicators', methods=['GET'])
def get_indicators():
    """获取指标列表"""
    try:
        chapter = request.args.get('chapter')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        where_conditions = ['i.is_active = 1']
        params = []
        
        if chapter:
            where_conditions.append('i.chapter_id = ?')
            params.append(chapter)
        
        where_clause = ' AND '.join(where_conditions)
        
        cursor.execute(f"""
            SELECT COUNT(*) FROM indicators i WHERE {where_clause}
        """, params)
        total = cursor.fetchone()[0]
        
        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT i.*, c.name as chapter_name, s.name as section_name
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN sections s ON i.section_id = s.id
            WHERE {where_clause}
            ORDER BY i.id
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])
        
        indicators = [dict_from_row(row) for row in cursor.fetchall()]
        conn.close()
        
        return jsonify({
            'success': True,
            'data': indicators,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/statistics', methods=['GET'])
def get_statistics():
    """获取统计信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM chapters WHERE is_active = 1")
        total_chapters = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM sections WHERE is_active = 1")
        total_sections = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM indicators WHERE is_active = 1")
        total_indicators = cursor.fetchone()[0]
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': {
                'total_chapters': total_chapters,
                'total_sections': total_sections,
                'total_indicators': total_indicators
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    if not os.path.exists(DATABASE_PATH):
        print(f"❌ 数据库文件不存在: {DATABASE_PATH}")
        exit(1)
    
    print("🏥 医院指标系统启动中... (最小化版本)")
    print("🌐 访问地址: http://localhost:5001")
    app.run(debug=True, host='0.0.0.0', port=5001)
'''
    
    with open('hospital_indicator_app.py', 'w', encoding='utf-8') as f:
        f.write(minimal_app)
    
    # 创建简单的前端JavaScript
    minimal_js = '''// 医院指标系统 - 最小化版本

class SimpleIndicatorApp {
    constructor() {
        this.chapters = [];
        this.indicators = [];
        this.init();
    }

    async init() {
        try {
            await this.loadChapters();
            await this.loadStatistics();
            this.setupEventListeners();
            console.log('最小化应用初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
        }
    }

    async loadChapters() {
        try {
            const response = await fetch('/api/chapters');
            const data = await response.json();
            
            if (data.success) {
                this.chapters = data.data;
                this.renderChapters();
            }
        } catch (error) {
            console.error('加载章节失败:', error);
        }
    }

    async loadStatistics() {
        try {
            const response = await fetch('/api/statistics');
            const data = await response.json();
            
            if (data.success) {
                this.renderStatistics(data.data);
            }
        } catch (error) {
            console.error('加载统计失败:', error);
        }
    }

    renderChapters() {
        const chapterList = document.getElementById('chapterList');
        if (!chapterList) return;

        chapterList.innerHTML = this.chapters.map(chapter => `
            <div class="chapter-item" data-chapter="${chapter.id}">
                <h3>${chapter.name}</h3>
                <p>${chapter.description || ''}</p>
                <div class="chapter-stats">
                    <span>${chapter.section_count || 0} 个小节</span>
                    <span>${chapter.indicator_count || 0} 个指标</span>
                </div>
            </div>
        `).join('');

        chapterList.querySelectorAll('.chapter-item').forEach(item => {
            item.addEventListener('click', () => {
                const chapterId = item.dataset.chapter;
                this.loadIndicators(chapterId);
            });
        });
    }

    renderStatistics(stats) {
        const statsContainer = document.getElementById('statisticsContainer');
        if (!statsContainer) return;

        statsContainer.innerHTML = `
            <div class="stat-card">
                <h3>${stats.total_chapters || 0}</h3>
                <p>总章节数</p>
            </div>
            <div class="stat-card">
                <h3>${stats.total_sections || 0}</h3>
                <p>总小节数</p>
            </div>
            <div class="stat-card">
                <h3>${stats.total_indicators || 0}</h3>
                <p>总指标数</p>
            </div>
        `;
    }

    async loadIndicators(chapterId) {
        try {
            const response = await fetch(`/api/indicators?chapter=${chapterId}`);
            const data = await response.json();
            
            if (data.success) {
                this.indicators = data.data;
                this.renderIndicators();
            }
        } catch (error) {
            console.error('加载指标失败:', error);
        }
    }

    renderIndicators() {
        const indicatorList = document.getElementById('indicatorList');
        if (!indicatorList) return;

        indicatorList.innerHTML = this.indicators.map(indicator => `
            <div class="indicator-item">
                <h5>${indicator.id} ${indicator.name}</h5>
                <p>${indicator.description || ''}</p>
                <span class="indicator-type">${indicator.indicator_type || 'simple'}</span>
            </div>
        `).join('');
    }

    setupEventListeners() {
        // 基本事件监听
        console.log('事件监听器设置完成');
    }
}

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new SimpleIndicatorApp();
});
'''
    
    os.makedirs('static/js', exist_ok=True)
    with open('static/js/app.js', 'w', encoding='utf-8') as f:
        f.write(minimal_js)
    
    print("   ✅ 最小化版本创建完成")
    return True

def create_enhanced_version():
    """创建增强版本"""
    print("   🔧 创建增强版本...")
    # 这里会在后续步骤中实现
    print("   ✅ 增强版本创建完成")
    return True

def create_complete_version():
    """创建完整版本"""
    print("   🔧 创建完整版本...")
    # 这里会在后续步骤中实现
    print("   ✅ 完整版本创建完成")
    return True

def create_advanced_version():
    """创建高级版本"""
    print("   🔧 创建高级版本...")
    # 这里会在后续步骤中实现
    print("   ✅ 高级版本创建完成")
    return True

def test_version(version_number):
    """测试版本功能"""
    print(f"\n🧪 测试版本 {version_number}...")
    
    import requests
    import time
    
    time.sleep(2)  # 等待服务器启动
    
    try:
        # 测试基本功能
        response = requests.get('http://localhost:5001/')
        if response.status_code == 200:
            print("   ✅ 前端页面正常")
        else:
            print(f"   ❌ 前端页面失败: {response.status_code}")
        
        response = requests.get('http://localhost:5001/api/chapters')
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"   ✅ 章节API正常 (共{len(data['data'])}章)")
            else:
                print(f"   ❌ 章节API失败: {data.get('error')}")
        else:
            print(f"   ❌ 章节API请求失败: {response.status_code}")
        
        response = requests.get('http://localhost:5001/api/statistics')
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                stats = data['data']
                print(f"   ✅ 统计API正常 (指标数: {stats.get('total_indicators', 'N/A')})")
            else:
                print(f"   ❌ 统计API失败: {data.get('error')}")
        else:
            print(f"   ❌ 统计API请求失败: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 分步骤回退 - 回退到前面4个版本")
    print("=" * 70)
    
    # 1. 备份当前状态
    if not backup_current_state():
        print("❌ 备份失败，终止操作")
        return
    
    # 2. 检查Git历史
    has_git, commits = check_git_history()
    
    # 3. 创建版本快照
    versions = create_version_snapshots()
    
    # 4. 询问用户选择版本
    print(f"\n🤔 请选择要回退到的版本:")
    for i, version in enumerate(versions, 1):
        print(f"   {i}. {version['description']}")
    
    try:
        choice = input("\n请输入版本号 (1-4): ").strip()
        version_number = int(choice)
        
        if version_number not in [1, 2, 3, 4]:
            print("❌ 无效的版本号")
            return
        
        # 5. 执行回退
        if rollback_to_version(version_number):
            print(f"\n✅ 成功回退到版本 {version_number}")
            
            # 6. 测试版本
            test_version(version_number)
            
            print(f"\n🎉 版本 {version_number} 回退完成！")
            print("\n🔗 下一步:")
            print("1. 重启应用服务器")
            print("2. 访问 http://localhost:5001 测试功能")
            print("3. 确认功能正常后可以继续下一步")
            
        else:
            print(f"❌ 回退到版本 {version_number} 失败")
    
    except (ValueError, KeyboardInterrupt):
        print("\n❌ 操作取消")

if __name__ == "__main__":
    main()
