# 指标类型功能说明

## 功能概述

为医院等级评审指标说明系统增加了"简单指标"和"复合指标"的自动判断和显示功能。

## 指标类型定义

### 简单指标 (Simple Indicator)
- **定义**: 没有下级指标、也没有分子分母组件，仅只有指标标题的指标
- **特征**: 
  - 子指标数量 = 0
  - 分子分母组件数量 = 0
  - 通常是最基础的数据指标
- **显示**: 绿色徽章 "简单指标"

### 复合指标 (Composite Indicator)
- **定义**: 有分子、分母定义的指标，或者有子指标的指标
- **特征**:
  - 有分子分母组件 OR 有子指标
  - 需要通过计算或汇总得出的指标
- **显示**: 蓝色徽章 "复合指标"

## 实现功能

### 1. 自动类型判断
- 创建了 `update_indicator_types.py` 脚本
- 根据指标的子指标和分子分母情况自动判断类型
- 支持批量更新所有指标的类型

### 2. 数据库支持
- 在 `indicators` 表中添加 `indicator_type` 字段
- 默认值为 'composite'
- 支持 'simple' 和 'composite' 两种类型

### 3. 前端显示
#### 3.1 模态框详情页面
- 在指标基本信息中显示指标类型
- 使用彩色徽章区分不同类型

#### 3.2 卡片视图
- 在每个指标卡片右上角显示类型徽章
- 支持层级展示时的类型标识

#### 3.3 列表视图
- 在指标名称旁边显示类型徽章
- 保持与卡片视图一致的样式

### 4. 后台管理
- 在后台指标管理列表中添加"指标类型"列
- 使用与前端一致的徽章样式
- 支持筛选和查看

### 5. API支持
- 所有指标相关API都返回 `indicator_type` 字段
- 确保前后端数据一致性

## 样式设计

### CSS类名
```css
.indicator-type-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.simple-indicator {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #4caf50;
}

.composite-indicator {
    background: #e3f2fd;
    color: #1565c0;
    border: 1px solid #2196f3;
}
```

### 显示位置
- **卡片视图**: 右上角绝对定位
- **列表视图**: 指标名称后内联显示
- **模态框**: 基本信息网格中独立字段
- **后台管理**: 表格中独立列

## 数据统计

根据当前数据库统计：
- **复合指标**: 953个 (99.6%)
- **简单指标**: 4个 (0.4%)

### 简单指标示例
- 1.1.2 - 实际开放床位数
- 2.1.1 - 收治病种数量（ICD-10 四位亚目数量）
- 2.1.2 - 住院术种数量（ICD-9-CM-3 四位亚目数量）
- C-3.1 - 抗菌药物使用强度

### 复合指标示例
- 1.1.1 - 核定床位数 (有1个组件)
- 1.1.3 - 床位使用率 (有2个组件)
- 1.2.1 - 卫生技术人员数与开放床位数比 (有2个组件)

## 使用方法

### 1. 更新指标类型
```bash
python3 update_indicator_types.py
```

### 2. 测试功能
```bash
python3 test_indicator_types.py
```

### 3. 查看效果
- 前端页面: http://localhost:5001
- 后台管理: http://localhost:5001/admin/indicators

## 技术实现

### 判断逻辑
```sql
CASE 
    WHEN COUNT(DISTINCT children.id) = 0 AND COUNT(DISTINCT components.id) = 0 
    THEN 'simple'
    ELSE 'composite'
END as indicator_type
```

### JavaScript渲染
```javascript
const indicatorType = indicator.indicator_type || 'composite';
const typeText = indicatorType === 'simple' ? '简单指标' : '复合指标';
const typeClass = indicatorType === 'simple' ? 'simple-indicator' : 'composite-indicator';
```

## 维护说明

1. **新增指标时**: 系统会自动判断类型
2. **修改指标结构时**: 需要重新运行类型更新脚本
3. **数据一致性**: 定期运行测试脚本验证类型判断准确性

## 扩展性

该功能设计具有良好的扩展性：
- 可以轻松添加新的指标类型
- 支持自定义类型判断规则
- 样式可以通过CSS轻松调整
- API返回格式标准化，便于前端处理
