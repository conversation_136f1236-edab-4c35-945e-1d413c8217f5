<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指标详情测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; color: #1a73e8; }
        .field { margin: 10px 0; }
        .label { font-weight: bold; color: #333; }
        .value { margin-left: 10px; color: #666; }
        .analysis-dimension { 
            background: #f8f9fa; 
            padding: 10px; 
            margin: 5px 0; 
            border-left: 3px solid #34a853; 
            border-radius: 3px; 
        }
        .dimension-name { font-weight: bold; color: #202124; }
        .dimension-content { color: #5f6368; font-size: 14px; margin-top: 5px; }
    </style>
</head>
<body>
    <h1>指标详情测试页面</h1>
    
    <div>
        <label for="indicatorSelect">选择指标:</label>
        <select id="indicatorSelect" onchange="loadIndicator()">
            <option value="">请选择指标</option>
            <option value="1.1.1">1.1.1 - 核定床位数</option>
            <option value="C-3.1">C-3.1 - 抗菌药物使用强度</option>
        </select>
    </div>

    <div id="indicatorData"></div>

    <script>
        async function loadIndicator() {
            const select = document.getElementById('indicatorSelect');
            const indicatorId = select.value;
            
            if (!indicatorId) {
                document.getElementById('indicatorData').innerHTML = '';
                return;
            }

            try {
                const response = await fetch(`/api/indicators/${indicatorId}`);
                const result = await response.json();
                
                if (result.success) {
                    renderIndicator(result.data.indicator);
                } else {
                    document.getElementById('indicatorData').innerHTML = `<p style="color: red;">错误: ${result.error}</p>`;
                }
            } catch (error) {
                document.getElementById('indicatorData').innerHTML = `<p style="color: red;">网络错误: ${error.message}</p>`;
            }
        }

        function renderIndicator(indicator) {
            let html = `
                <div class="section">
                    <h3>基本信息</h3>
                    <div class="field"><span class="label">指标编号:</span><span class="value">${indicator.id}</span></div>
                    <div class="field"><span class="label">指标名称:</span><span class="value">${indicator.name}</span></div>
                    <div class="field"><span class="label">指标描述:</span><span class="value">${indicator.description || '无'}</span></div>
                </div>
            `;

            // 指标定义
            if (indicator.indicator_definition) {
                html += `
                    <div class="section">
                        <h3>指标定义</h3>
                        <div class="value">${indicator.indicator_definition}</div>
                    </div>
                `;
            }

            // 计算方法
            if (indicator.calculation_formula) {
                html += `
                    <div class="section">
                        <h3>计算方法</h3>
                        <div class="value" style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;">${indicator.calculation_formula}</div>
                `;
                
                if (indicator.numerator_description) {
                    html += `<div class="field"><span class="label" style="color: #1a73e8;">分子:</span><span class="value">${indicator.numerator_description}</span></div>`;
                }
                
                if (indicator.denominator_description) {
                    html += `<div class="field"><span class="label" style="color: #ea4335;">分母:</span><span class="value">${indicator.denominator_description}</span></div>`;
                }
                
                html += `</div>`;
            }

            // 统计范围
            if (indicator.statistical_scope) {
                html += `
                    <div class="section">
                        <h3>统计范围</h3>
                        <div class="value">${indicator.statistical_scope}</div>
                    </div>
                `;
            }

            // 数据来源
            if (indicator.data_sources) {
                html += `
                    <div class="section">
                        <h3>数据来源</h3>
                        <div class="value">${indicator.data_sources}</div>
                    </div>
                `;
            }

            // 统计频率
            if (indicator.collection_frequency_detail) {
                html += `
                    <div class="section">
                        <h3>统计频率</h3>
                        <div class="value">${indicator.collection_frequency_detail}</div>
                    </div>
                `;
            }

            // 参考值
            if (indicator.reference_value) {
                html += `
                    <div class="section">
                        <h3>标准值/参考值</h3>
                        <div class="value">${indicator.reference_value}</div>
                    </div>
                `;
            }

            // 监测分析
            if (indicator.monitoring_analysis) {
                html += `
                    <div class="section">
                        <h3>监测分析</h3>
                        <div class="value">${indicator.monitoring_analysis}</div>
                `;
                
                // 分析维度
                if (indicator.analysis_dimensions && indicator.analysis_dimensions.length > 0) {
                    html += `
                        <h4 style="margin-top: 20px; color: #1a73e8;">建议对数据进行多维度分析：</h4>
                    `;
                    
                    indicator.analysis_dimensions.forEach(dim => {
                        html += `
                            <div class="analysis-dimension">
                                <div class="dimension-name">${dim.dimension_name}</div>
                                <div class="dimension-content">${dim.analysis_content}</div>
                            </div>
                        `;
                    });
                }
                
                html += `</div>`;
            }

            document.getElementById('indicatorData').innerHTML = html;
        }
    </script>
</body>
</html>
