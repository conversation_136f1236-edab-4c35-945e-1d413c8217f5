#!/usr/bin/env python3
"""
前后端架构统一验证脚本
验证前端已成功统一为后端的整合架构
"""

import requests

def test_frontend_html_structure():
    """测试前端HTML结构变化"""
    print("🧪 测试前端HTML结构变化...")

    try:
        with open('templates/base.html', 'r', encoding='utf-8') as f:
            html_content = f.read()

        # 检查独立的分子分母信息卡片是否已删除
        if '分子分母信息卡片' not in html_content:
            print("✅ 独立的分子分母信息卡片已删除")

            # 检查基本属性模块内是否包含分子分母组件部分
            if 'modalComponentsSection' in html_content:
                print("✅ 基本属性模块内包含分子分母组件部分")

                # 检查组件容器是否在基本属性卡片内
                if 'modal-components-container' in html_content:
                    print("✅ 组件容器在基本属性卡片内")

                    # 检查是否有正确的标识
                    if '(仅复合指标显示)' in html_content:
                        print("✅ 包含复合指标显示标识")
                        return True
                    else:
                        print("❌ 缺少复合指标显示标识")
                        return False
                else:
                    print("❌ 组件容器不在基本属性卡片内")
                    return False
            else:
                print("❌ 基本属性模块内缺少分子分母组件部分")
                return False
        else:
            print("❌ 独立的分子分母信息卡片仍然存在")
            return False

    except FileNotFoundError:
        print("❌ 前端模板文件不存在")
        return False
    except Exception as e:
        print(f"❌ 读取前端模板文件失败: {e}")
        return False

def test_frontend_javascript_logic():
    """测试前端JavaScript逻辑更新"""
    print("\n🧪 测试前端JavaScript逻辑更新...")

    try:
        with open('static/js/app.js', 'r', encoding='utf-8') as f:
            js_content = f.read()

        # 检查renderComponentsInModal函数是否更新
        if 'modalComponentsSection' in js_content:
            print("✅ renderComponentsInModal函数已更新")

            # 检查显示条件是否与后端一致
            if "indicator.indicator_type === 'composite' && components.length > 0" in js_content:
                print("✅ 显示条件与后端一致")

                # 检查是否使用表格渲染
                if 'border-collapse: collapse' in js_content and '<thead>' in js_content:
                    print("✅ 使用表格渲染，与后端保持一致")

                    # 检查简单指标隐藏逻辑
                    if "indicator.indicator_type === 'simple'" in js_content and 'componentsSection.style.display' in js_content:
                        print("✅ 简单指标隐藏逻辑正确")
                        return True
                    else:
                        print("❌ 简单指标隐藏逻辑不正确")
                        return False
                else:
                    print("❌ 未使用表格渲染")
                    return False
            else:
                print("❌ 显示条件与后端不一致")
                return False
        else:
            print("❌ renderComponentsInModal函数未更新")
            return False

    except FileNotFoundError:
        print("❌ 前端JavaScript文件不存在")
        return False
    except Exception as e:
        print(f"❌ 读取前端JavaScript文件失败: {e}")
        return False

def test_api_data_consistency():
    """测试API数据一致性"""
    print("\n🧪 测试API数据一致性...")

    test_cases = [
        ("复合指标", "1.3.1"),
        ("简单指标", "1.1.1")
    ]

    results = {}

    for case_name, indicator_id in test_cases:
        try:
            response = requests.get(f'http://localhost:5001/api/indicators/{indicator_id}')
            data = response.json()

            if data['success']:
                indicator = data['data']['indicator']
                components = data['data'].get('components', [])

                results[case_name] = {
                    "indicator_id": indicator['id'],
                    "indicator_type": indicator.get('indicator_type', 'composite'),
                    "components_count": len(components),
                    "should_show_components": indicator.get('indicator_type') == 'composite' and len(components) > 0,
                    "api_success": True
                }

                print(f"✅ {case_name} API数据获取成功")
                print(f"   指标: {indicator['id']}")
                print(f"   类型: {indicator.get('indicator_type', 'composite')}")
                print(f"   组件数量: {len(components)}")
                print(f"   应显示组件: {'是' if results[case_name]['should_show_components'] else '否'}")

            else:
                results[case_name] = {"api_success": False, "error": data.get('error')}
                print(f"❌ {case_name} API请求失败: {data.get('error')}")

        except Exception as e:
            results[case_name] = {"api_success": False, "error": str(e)}
            print(f"❌ {case_name} API请求异常: {e}")

    return all(result.get("api_success", False) for result in results.values())

def test_display_logic_consistency():
    """测试前后端显示逻辑一致性"""
    print("\n🧪 测试前后端显示逻辑一致性...")

    try:
        # 读取前端JavaScript
        with open('static/js/app.js', 'r', encoding='utf-8') as f:
            frontend_js = f.read()

        # 读取后端HTML
        with open('templates/admin/indicator_detail.html', 'r', encoding='utf-8') as f:
            backend_html = f.read()

        # 检查显示条件一致性
        frontend_condition = "indicator.indicator_type === 'composite' && components.length > 0"
        backend_condition1 = "indicator.indicator_type === 'composite' && indicatorData.components && indicatorData.components.length > 0"
        backend_condition2 = "indicator.indicator_type === 'composite'"

        if frontend_condition in frontend_js and (backend_condition1 in backend_html or backend_condition2 in backend_html):
            print("✅ 前后端显示条件一致")

            # 检查隐藏条件一致性
            frontend_hide = "indicator.indicator_type === 'simple'"
            backend_hide = "indicatorType === 'simple'"

            if frontend_hide in frontend_js and backend_hide in backend_html:
                print("✅ 前后端隐藏条件一致")

                # 检查架构一致性
                frontend_integration = 'modalComponentsSection' in frontend_js
                backend_integration = 'components-section' in backend_html

                if frontend_integration and backend_integration:
                    print("✅ 前后端都采用整合架构")
                    return True
                else:
                    print("❌ 前后端架构不一致")
                    return False
            else:
                print("❌ 前后端隐藏条件不一致")
                return False
        else:
            print("❌ 前后端显示条件不一致")
            return False

    except Exception as e:
        print(f"❌ 显示逻辑一致性测试失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🎯 前后端架构统一验证")
    print("=" * 60)

    tests = [
        ("前端HTML结构变化", test_frontend_html_structure),
        ("前端JavaScript逻辑更新", test_frontend_javascript_logic),
        ("API数据一致性", test_api_data_consistency),
        ("前后端显示逻辑一致性", test_display_logic_consistency)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")

    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 恭喜！前后端架构已成功统一！")

        print("\n✨ 统一成果:")
        print("1. ✅ 前端删除了独立的分子分母信息卡片")
        print("2. ✅ 前端将分子分母组件整合到基本属性模块内")
        print("3. ✅ 前端显示条件与后端完全一致")
        print("4. ✅ 前端使用表格渲染，与后端保持一致")
        print("5. ✅ 前端简单指标隐藏逻辑与后端一致")
        print("6. ✅ API数据结构保持一致")

        print("\n🎯 架构特点:")
        print("- 信息集中化: 分子分母组件作为基本属性的一部分")
        print("- 显示逻辑统一: 复合指标且有组件时显示")
        print("- 隐藏逻辑统一: 简单指标时隐藏组件部分")
        print("- 渲染方式统一: 都使用表格形式显示组件")

        print("\n🔗 验证链接:")
        print("- 前端系统: http://localhost:5001")
        print("- 后端管理: http://localhost:5001/admin/indicators/1.3.1")

        print("\n📋 用户体验改进:")
        print("- 界面一致性: 前后端界面布局完全一致")
        print("- 操作一致性: 前后端操作逻辑完全一致")
        print("- 信息组织: 相关信息集中在同一模块内")
        print("- 认知负担: 减少用户需要适应的界面差异")

        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    main()
