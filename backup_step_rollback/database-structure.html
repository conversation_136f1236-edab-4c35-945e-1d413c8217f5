
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>医院评审指标数据库结构</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                color: #333;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }
            h1 {
                color: #2c3e50;
                border-bottom: 2px solid #3498db;
                padding-bottom: 10px;
            }
            h2 {
                color: #2980b9;
                margin-top: 30px;
                border-bottom: 1px solid #ddd;
                padding-bottom: 5px;
            }
            h3 {
                color: #3498db;
            }
            .table-info {
                background-color: #f8f9fa;
                border-radius: 5px;
                padding: 15px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 15px;
            }
            th, td {
                padding: 8px 12px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            tr:hover {
                background-color: #f5f5f5;
            }
            .schema {
                font-family: monospace;
                background-color: #f8f8f8;
                padding: 10px;
                border-radius: 5px;
                overflow-x: auto;
                white-space: pre-wrap;
                border: 1px solid #ddd;
            }
            .table-of-contents {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 30px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .table-of-contents ul {
                list-style-type: none;
                padding-left: 20px;
            }
            .table-of-contents li {
                margin-bottom: 5px;
            }
            .table-of-contents a {
                text-decoration: none;
                color: #3498db;
            }
            .table-of-contents a:hover {
                text-decoration: underline;
            }
            .sample-data {
                margin-top: 15px;
                overflow-x: auto;
            }
            .foreign-key {
                color: #27ae60;
                font-weight: bold;
            }
            .primary-key {
                color: #e74c3c;
                font-weight: bold;
            }
            .db-summary {
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;
                margin-bottom: 30px;
            }
            .summary-card {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 15px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                flex: 1;
                min-width: 200px;
                margin-right: 15px;
            }
            .summary-card h3 {
                margin-top: 0;
            }
            .tag {
                display: inline-block;
                padding: 2px 8px;
                border-radius: 3px;
                font-size: 12px;
                margin-right: 5px;
                margin-bottom: 5px;
            }
            .tag-pk {
                background-color: #ffcccc;
                border: 1px solid #ff9999;
            }
            .tag-fk {
                background-color: #ccffcc;
                border: 1px solid #99ff99;
            }
            .tag-idx {
                background-color: #ffffcc;
                border: 1px solid #ffff99;
            }
            .tag-unique {
                background-color: #ccccff;
                border: 1px solid #9999ff;
            }
            .tag-not-null {
                background-color: #ffccff;
                border: 1px solid #ff99ff;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>医院评审指标数据库结构</h1>
            
            <div class="db-summary">
                <div class="summary-card">
                    <h3>数据库概览</h3>
                    <p><strong>表数量:</strong> 20</p>
                    <p><strong>数据库路径:</strong> hospital-indicators/database/hospital-evaluation-database.db</p>
                </div>
            </div>
            
            <div class="table-of-contents">
                <h2>目录</h2>
                <ul>
    <li><a href="#chapters">chapters</a></li>
<li><a href="#data_sources">data_sources</a></li>
<li><a href="#data_validations">data_validations</a></li>
<li><a href="#denominators">denominators</a></li>
<li><a href="#departments">departments</a></li>
<li><a href="#indicator_metadata">indicator_metadata</a></li>
<li><a href="#indicator_relationships">indicator_relationships</a></li>
<li><a href="#indicator_tags">indicator_tags</a></li>
<li><a href="#indicators">indicators</a></li>
<li><a href="#logic_descriptions">logic_descriptions</a></li>
<li><a href="#numerators">numerators</a></li>
<li><a href="#reference_ranges">reference_ranges</a></li>
<li><a href="#reference_ranges_backup">reference_ranges_backup</a></li>
<li><a href="#sections">sections</a></li>
<li><a href="#sqlite_sequence">sqlite_sequence</a></li>
<li><a href="#tags">tags</a></li>
<li><a href="#term_mappings">term_mappings</a></li>
<li><a href="#terminology_standards">terminology_standards</a></li>
<li><a href="#units">units</a></li>
<li><a href="#users">users</a></li>

                </ul>
            </div>
    
            <div class="table-info" id="chapters">
                <h2>chapters</h2>
                <p><strong>行数:</strong> 5</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>chapter_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>chapter_code</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>chapter_name</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE chapters (
            chapter_id INTEGER PRIMARY KEY,
            chapter_code INTEGER,
            chapter_name TEXT
        )</div>
        
                <h3>示例数据</h3>
                <div class="sample-data">
                    <table>
                        <tr>
            <th>chapter_id</th><th>chapter_code</th><th>chapter_name</th></tr><tr><td>1</td><td>1</td><td>资源配置与运行数据指标</td></tr><tr><td>4</td><td>4</td><td>单病种</td></tr><tr><td>6</td><td>2</td><td>医疗服务能力与医院质量安全指标</td></tr><tr><td>9</td><td>3</td><td>重点专业质量控制指标</td></tr><tr><td>10</td><td>5</td><td>重点医疗技术临床应用质量控制指标</td></tr>
                    </table>
                </div>
            
            </div>
        
            <div class="table-info" id="data_sources">
                <h2>data_sources</h2>
                <p><strong>行数:</strong> 7</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>source_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>source_name</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                </table>
                
        
                <h3>索引</h3>
                <table>
                    <tr>
                        <th>名称</th>
                        <th>唯一</th>
                    </tr>
            
                    <tr>
                        <td>sqlite_autoindex_data_sources_1</td>
                        <td>是</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE data_sources (
    source_id INTEGER PRIMARY KEY,
    source_name TEXT NOT NULL UNIQUE
)</div>
        
                <h3>示例数据</h3>
                <div class="sample-data">
                    <table>
                        <tr>
            <th>source_id</th><th>source_name</th></tr><tr><td>1</td><td>系统取数</td></tr><tr><td>2</td><td>数据来源</td></tr><tr><td>3</td><td>手工填报</td></tr><tr><td>4</td><td>不适用</td></tr><tr><td>5</td><td>%</td></tr>
                    </table>
                </div>
            
            </div>
        
            <div class="table-info" id="data_validations">
                <h2>data_validations</h2>
                <p><strong>行数:</strong> 0</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>validation_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>indicator_id</td>
                    <td>INTEGER</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span><span class="tag tag-fk">外键 → indicators.indicator_id</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>validation_date</td>
                    <td>DATETIME</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>3</td>
                    <td>validation_result</td>
                    <td>BOOLEAN</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>4</td>
                    <td>error_message</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>5</td>
                    <td>error_level</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>6</td>
                    <td>corrective_action</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>外键关系</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>本表列</th>
                        <th>引用表</th>
                        <th>引用列</th>
                        <th>on_update</th>
                        <th>on_delete</th>
                    </tr>
            
                    <tr>
                        <td>1</td>
                        <td>indicator_id</td>
                        <td>indicators</td>
                        <td>indicator_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE data_validations (
    validation_id INTEGER PRIMARY KEY,
    indicator_id INTEGER NOT NULL,
    validation_date DATETIME,
    validation_result BOOLEAN,
    error_message TEXT,
    error_level TEXT,
    corrective_action TEXT,
    FOREIGN KEY (indicator_id) REFERENCES indicators(indicator_id)
)</div>
        
            </div>
        
            <div class="table-info" id="denominators">
                <h2>denominators</h2>
                <p><strong>行数:</strong> 480</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>denominator_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>indicator_id</td>
                    <td>INTEGER</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span><span class="tag tag-fk">外键 → indicators.indicator_id</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>content</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>3</td>
                    <td>unit_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-fk">外键 → units.unit_id</span></td>
                </tr>
            
                <tr>
                    <td>4</td>
                    <td>department_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-fk">外键 → departments.department_id</span></td>
                </tr>
            
                <tr>
                    <td>5</td>
                    <td>source_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-fk">外键 → data_sources.source_id</span></td>
                </tr>
            
                <tr>
                    <td>6</td>
                    <td>term_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-fk">外键 → terminology_standards.term_id</span></td>
                </tr>
            
                <tr>
                    <td>7</td>
                    <td>special_flag</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>8</td>
                    <td>context_description</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>外键关系</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>本表列</th>
                        <th>引用表</th>
                        <th>引用列</th>
                        <th>on_update</th>
                        <th>on_delete</th>
                    </tr>
            
                    <tr>
                        <td>1</td>
                        <td>source_id</td>
                        <td>data_sources</td>
                        <td>source_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                    <tr>
                        <td>2</td>
                        <td>department_id</td>
                        <td>departments</td>
                        <td>department_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                    <tr>
                        <td>3</td>
                        <td>unit_id</td>
                        <td>units</td>
                        <td>unit_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                    <tr>
                        <td>4</td>
                        <td>indicator_id</td>
                        <td>indicators</td>
                        <td>indicator_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                    <tr>
                        <td>5</td>
                        <td>term_id</td>
                        <td>terminology_standards</td>
                        <td>term_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE &quot;denominators&quot; (
    denominator_id INTEGER PRIMARY KEY,
    indicator_id INTEGER NOT NULL,
    content TEXT NOT NULL,
    unit_id INTEGER,
    department_id INTEGER,
    source_id INTEGER, term_id INTEGER REFERENCES terminology_standards(term_id), special_flag TEXT, context_description TEXT,
    FOREIGN KEY (indicator_id) REFERENCES indicators(indicator_id),
    FOREIGN KEY (unit_id) REFERENCES units(unit_id),
    FOREIGN KEY (department_id) REFERENCES departments(department_id),
    FOREIGN KEY (source_id) REFERENCES data_sources(source_id)
)</div>
        
                <h3>示例数据</h3>
                <div class="sample-data">
                    <table>
                        <tr>
            <th>denominator_id</th><th>indicator_id</th><th>content</th><th>unit_id</th><th>department_id</th><th>source_id</th><th>term_id</th><th>special_flag</th><th>context_description</th></tr><tr><td>1292</td><td>555</td><td>同期实际开放的总床日数</td><td>1</td><td>15</td><td>1</td><td></td><td></td><td></td></tr><tr><td>1293</td><td>556</td><td>同期全院实际开放床数</td><td>1</td><td>15</td><td>1</td><td></td><td></td><td></td></tr><tr><td>1294</td><td>557</td><td>同期全院实际开放床位数</td><td>1</td><td>15</td><td>1</td><td></td><td></td><td></td></tr><tr><td>1295</td><td>558</td><td>同期全院实际开放床位数</td><td>1</td><td>15</td><td>1</td><td></td><td></td><td></td></tr><tr><td>1296</td><td>559</td><td>同期全院实际开放床位数</td><td>1</td><td>15</td><td>1</td><td></td><td></td><td></td></tr>
                    </table>
                </div>
            
            </div>
        
            <div class="table-info" id="departments">
                <h2>departments</h2>
                <p><strong>行数:</strong> 27</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>department_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>department_name</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                </table>
                
        
                <h3>索引</h3>
                <table>
                    <tr>
                        <th>名称</th>
                        <th>唯一</th>
                    </tr>
            
                    <tr>
                        <td>sqlite_autoindex_departments_1</td>
                        <td>是</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE departments (
    department_id INTEGER PRIMARY KEY,
    department_name TEXT NOT NULL UNIQUE
)</div>
        
                <h3>示例数据</h3>
                <div class="sample-data">
                    <table>
                        <tr>
            <th>department_id</th><th>department_name</th></tr><tr><td>1</td><td>重症医学科</td></tr><tr><td>2</td><td>计财部</td></tr><tr><td>3</td><td>护理部</td></tr><tr><td>4</td><td>产科</td></tr><tr><td>5</td><td>急诊科</td></tr>
                    </table>
                </div>
            
            </div>
        
            <div class="table-info" id="indicator_metadata">
                <h2>indicator_metadata</h2>
                <p><strong>行数:</strong> 0</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>metadata_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>indicator_id</td>
                    <td>INTEGER</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span><span class="tag tag-fk">外键 → indicators.indicator_id</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>data_type</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>3</td>
                    <td>decimal_places</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>4</td>
                    <td>aggregation_method</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>5</td>
                    <td>trend_direction</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>6</td>
                    <td>benchmark_value</td>
                    <td>REAL</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>7</td>
                    <td>target_value</td>
                    <td>REAL</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>8</td>
                    <td>warning_threshold</td>
                    <td>REAL</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>9</td>
                    <td>critical_threshold</td>
                    <td>REAL</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>外键关系</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>本表列</th>
                        <th>引用表</th>
                        <th>引用列</th>
                        <th>on_update</th>
                        <th>on_delete</th>
                    </tr>
            
                    <tr>
                        <td>1</td>
                        <td>indicator_id</td>
                        <td>indicators</td>
                        <td>indicator_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE indicator_metadata (
    metadata_id INTEGER PRIMARY KEY,
    indicator_id INTEGER NOT NULL,
    data_type TEXT,
    decimal_places INTEGER,
    aggregation_method TEXT,
    trend_direction TEXT,
    benchmark_value REAL,
    target_value REAL,
    warning_threshold REAL,
    critical_threshold REAL,
    FOREIGN KEY (indicator_id) REFERENCES indicators(indicator_id)
)</div>
        
            </div>
        
            <div class="table-info" id="indicator_relationships">
                <h2>indicator_relationships</h2>
                <p><strong>行数:</strong> 0</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>relationship_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>indicator_id</td>
                    <td>INTEGER</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span><span class="tag tag-fk">外键 → indicators.indicator_id</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>relationship_name</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>3</td>
                    <td>relationship_description</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>4</td>
                    <td>validation_sql</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>5</td>
                    <td>severity_level</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>外键关系</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>本表列</th>
                        <th>引用表</th>
                        <th>引用列</th>
                        <th>on_update</th>
                        <th>on_delete</th>
                    </tr>
            
                    <tr>
                        <td>1</td>
                        <td>indicator_id</td>
                        <td>indicators</td>
                        <td>indicator_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE indicator_relationships (
    relationship_id INTEGER PRIMARY KEY,
    indicator_id INTEGER NOT NULL,
    relationship_name TEXT NOT NULL,
    relationship_description TEXT,
    validation_sql TEXT,
    severity_level TEXT,
    FOREIGN KEY (indicator_id) REFERENCES indicators(indicator_id)
)</div>
        
            </div>
        
            <div class="table-info" id="indicator_tags">
                <h2>indicator_tags</h2>
                <p><strong>行数:</strong> 0</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>indicator_id</td>
                    <td>INTEGER</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span><span class="tag tag-fk">外键 → indicators.indicator_id</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>tag_id</td>
                    <td>INTEGER</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span><span class="tag tag-fk">外键 → tags.tag_id</span></td>
                </tr>
            
                </table>
                
        
                <h3>外键关系</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>本表列</th>
                        <th>引用表</th>
                        <th>引用列</th>
                        <th>on_update</th>
                        <th>on_delete</th>
                    </tr>
            
                    <tr>
                        <td>1</td>
                        <td>tag_id</td>
                        <td>tags</td>
                        <td>tag_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                    <tr>
                        <td>2</td>
                        <td>indicator_id</td>
                        <td>indicators</td>
                        <td>indicator_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                </table>
            
                <h3>索引</h3>
                <table>
                    <tr>
                        <th>名称</th>
                        <th>唯一</th>
                    </tr>
            
                    <tr>
                        <td>sqlite_autoindex_indicator_tags_1</td>
                        <td>是</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE indicator_tags (
    id INTEGER PRIMARY KEY,
    indicator_id INTEGER NOT NULL,
    tag_id INTEGER NOT NULL,
    FOREIGN KEY (indicator_id) REFERENCES indicators(indicator_id),
    FOREIGN KEY (tag_id) REFERENCES tags(tag_id),
    UNIQUE (indicator_id, tag_id)
)</div>
        
            </div>
        
            <div class="table-info" id="indicators">
                <h2>indicators</h2>
                <p><strong>行数:</strong> 737</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>indicator_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>section_id</td>
                    <td>INTEGER</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span><span class="tag tag-fk">外键 → sections.section_id</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>indicator_code</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>3</td>
                    <td>indicator_name</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>4</td>
                    <td>has_fraction</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td>0</td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>5</td>
                    <td>parent_indicator_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>6</td>
                    <td>level</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td>3</td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>7</td>
                    <td>source_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-fk">外键 → data_sources.source_id</span></td>
                </tr>
            
                <tr>
                    <td>8</td>
                    <td>department_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-fk">外键 → departments.department_id</span></td>
                </tr>
            
                <tr>
                    <td>9</td>
                    <td>unit_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-fk">外键 → units.unit_id</span></td>
                </tr>
            
                <tr>
                    <td>10</td>
                    <td>relationship_type</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>11</td>
                    <td>validation_rule</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>12</td>
                    <td>expected_ratio_min</td>
                    <td>REAL</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>13</td>
                    <td>expected_ratio_max</td>
                    <td>REAL</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>14</td>
                    <td>calculation_method</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>15</td>
                    <td>data_quality_check</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>外键关系</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>本表列</th>
                        <th>引用表</th>
                        <th>引用列</th>
                        <th>on_update</th>
                        <th>on_delete</th>
                    </tr>
            
                    <tr>
                        <td>1</td>
                        <td>section_id</td>
                        <td>sections</td>
                        <td>section_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                    <tr>
                        <td>2</td>
                        <td>unit_id</td>
                        <td>units</td>
                        <td>unit_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                    <tr>
                        <td>3</td>
                        <td>department_id</td>
                        <td>departments</td>
                        <td>department_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                    <tr>
                        <td>4</td>
                        <td>source_id</td>
                        <td>data_sources</td>
                        <td>source_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                </table>
            
                <h3>索引</h3>
                <table>
                    <tr>
                        <th>名称</th>
                        <th>唯一</th>
                    </tr>
            
                    <tr>
                        <td>sqlite_autoindex_indicators_1</td>
                        <td>是</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE indicators (
    indicator_id INTEGER PRIMARY KEY,
    section_id INTEGER NOT NULL,                     -- 所属节ID
    indicator_code TEXT NOT NULL,         -- 例如：1.1.1, 1.1.2...
    indicator_name TEXT NOT NULL,        -- 例如：核对床位数
    has_fraction INTEGER DEFAULT 0, parent_indicator_id INTEGER, level INTEGER DEFAULT 3, source_id INTEGER REFERENCES data_sources(source_id), department_id INTEGER REFERENCES departments(department_id), unit_id INTEGER REFERENCES units(unit_id), relationship_type TEXT, validation_rule TEXT, expected_ratio_min REAL, expected_ratio_max REAL, calculation_method TEXT, data_quality_check TEXT,          -- 是否包含分子分母
    UNIQUE (indicator_code),
    FOREIGN KEY (section_id) REFERENCES sections(section_id)
)</div>
        
                <h3>示例数据</h3>
                <div class="sample-data">
                    <table>
                        <tr>
            <th>indicator_id</th><th>section_id</th><th>indicator_code</th><th>indicator_name</th><th>has_fraction</th><th>parent_indicator_id</th><th>level</th><th>source_id</th><th>department_id</th><th>unit_id</th><th>relationship_type</th><th>validation_rule</th><th>expected_ratio_min</th><th>expected_ratio_max</th><th>calculation_method</th><th>data_quality_check</th></tr><tr><td>553</td><td>1</td><td>1.1.1</td><td>核定床位数</td><td>0</td><td></td><td>3</td><td>3</td><td>13</td><td>1</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>554</td><td>1</td><td>1.1.2</td><td>实际开放床位数</td><td>0</td><td></td><td>3</td><td>3</td><td>13</td><td>1</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>555</td><td>1</td><td>1.1.3</td><td>床位使用率（名称修订）</td><td>1</td><td></td><td>3</td><td>3</td><td>13</td><td>15</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>556</td><td>2</td><td>1.2.1</td><td>卫生技术人员数与开放床位数比</td><td>1</td><td></td><td>3</td><td>3</td><td>23</td><td>4</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>557</td><td>2</td><td>1.2.2</td><td>全院护士人数与开放床位数比</td><td>1</td><td></td><td>3</td><td>3</td><td>23</td><td>4</td><td></td><td></td><td></td><td></td><td></td><td></td></tr>
                    </table>
                </div>
            
            </div>
        
            <div class="table-info" id="logic_descriptions">
                <h2>logic_descriptions</h2>
                <p><strong>行数:</strong> 451</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>logic_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>indicator_id</td>
                    <td>INTEGER</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span><span class="tag tag-fk">外键 → indicators.indicator_id</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>content</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>3</td>
                    <td>type</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>4</td>
                    <td>numerator_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>5</td>
                    <td>denominator_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>外键关系</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>本表列</th>
                        <th>引用表</th>
                        <th>引用列</th>
                        <th>on_update</th>
                        <th>on_delete</th>
                    </tr>
            
                    <tr>
                        <td>1</td>
                        <td>indicator_id</td>
                        <td>indicators</td>
                        <td>indicator_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE logic_descriptions (
    logic_id INTEGER PRIMARY KEY,
    indicator_id INTEGER NOT NULL,                   -- 所属指标ID
    content TEXT NOT NULL, type TEXT CHECK(type IN (&#x27;indicator&#x27;, &#x27;numerator&#x27;, &#x27;denominator&#x27;)), numerator_id INTEGER, denominator_id INTEGER,                       -- 逻辑说明内容
    FOREIGN KEY (indicator_id) REFERENCES indicators(indicator_id)
)</div>
        
                <h3>示例数据</h3>
                <div class="sample-data">
                    <table>
                        <tr>
            <th>logic_id</th><th>indicator_id</th><th>content</th><th>type</th><th>numerator_id</th><th>denominator_id</th></tr><tr><td>1157</td><td>553</td><td>医疗机构执业许可证核定编制床位</td><td>indicator</td><td></td><td></td></tr><tr><td>1158</td><td>554</td><td>医院年度发文中床位合计数</td><td>indicator</td><td></td><td></td></tr><tr><td>1159</td><td>555</td><td>省病案系统年度“实际占用的总床日数”</td><td>numerator</td><td></td><td></td></tr><tr><td>1160</td><td>555</td><td>省病案系统年度“实际开放的总床日数”</td><td>denominator</td><td></td><td></td></tr><tr><td>1161</td><td>556</td><td>参照人事科月报表，联系医务科和护理部取电子系统的数据，并剔除行政后勤人员，剔除注册地点不在院内的医师和护士</td><td>numerator</td><td></td><td></td></tr>
                    </table>
                </div>
            
            </div>
        
            <div class="table-info" id="numerators">
                <h2>numerators</h2>
                <p><strong>行数:</strong> 480</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>numerator_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>indicator_id</td>
                    <td>INTEGER</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span><span class="tag tag-fk">外键 → indicators.indicator_id</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>content</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>3</td>
                    <td>unit_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-fk">外键 → units.unit_id</span></td>
                </tr>
            
                <tr>
                    <td>4</td>
                    <td>department_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-fk">外键 → departments.department_id</span></td>
                </tr>
            
                <tr>
                    <td>5</td>
                    <td>source_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-fk">外键 → data_sources.source_id</span></td>
                </tr>
            
                <tr>
                    <td>6</td>
                    <td>term_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-fk">外键 → terminology_standards.term_id</span></td>
                </tr>
            
                <tr>
                    <td>7</td>
                    <td>special_flag</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>8</td>
                    <td>context_description</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>外键关系</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>本表列</th>
                        <th>引用表</th>
                        <th>引用列</th>
                        <th>on_update</th>
                        <th>on_delete</th>
                    </tr>
            
                    <tr>
                        <td>1</td>
                        <td>source_id</td>
                        <td>data_sources</td>
                        <td>source_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                    <tr>
                        <td>2</td>
                        <td>department_id</td>
                        <td>departments</td>
                        <td>department_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                    <tr>
                        <td>3</td>
                        <td>unit_id</td>
                        <td>units</td>
                        <td>unit_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                    <tr>
                        <td>4</td>
                        <td>indicator_id</td>
                        <td>indicators</td>
                        <td>indicator_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                    <tr>
                        <td>5</td>
                        <td>term_id</td>
                        <td>terminology_standards</td>
                        <td>term_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE &quot;numerators&quot; (
    numerator_id INTEGER PRIMARY KEY,
    indicator_id INTEGER NOT NULL,
    content TEXT NOT NULL,
    unit_id INTEGER,
    department_id INTEGER,
    source_id INTEGER, term_id INTEGER REFERENCES terminology_standards(term_id), special_flag TEXT, context_description TEXT,
    FOREIGN KEY (indicator_id) REFERENCES indicators(indicator_id),
    FOREIGN KEY (unit_id) REFERENCES units(unit_id),
    FOREIGN KEY (department_id) REFERENCES departments(department_id),
    FOREIGN KEY (source_id) REFERENCES data_sources(source_id)
)</div>
        
                <h3>示例数据</h3>
                <div class="sample-data">
                    <table>
                        <tr>
            <th>numerator_id</th><th>indicator_id</th><th>content</th><th>unit_id</th><th>department_id</th><th>source_id</th><th>term_id</th><th>special_flag</th><th>context_description</th></tr><tr><td>1337</td><td>555</td><td>实际占用的总床日数</td><td>1</td><td>15</td><td>1</td><td></td><td></td><td></td></tr><tr><td>1338</td><td>556</td><td>医院卫生技术人员数</td><td>10</td><td>15</td><td>1</td><td></td><td></td><td></td></tr><tr><td>1339</td><td>557</td><td>医院执业护士人数</td><td>10</td><td>15</td><td>1</td><td></td><td></td><td></td></tr><tr><td>1340</td><td>558</td><td>医院病区执业护士人数</td><td>10</td><td>15</td><td>1</td><td></td><td></td><td></td></tr><tr><td>1341</td><td>559</td><td>医院感染管理专职人员数</td><td>10</td><td>15</td><td>1</td><td></td><td></td><td></td></tr>
                    </table>
                </div>
            
            </div>
        
            <div class="table-info" id="reference_ranges">
                <h2>reference_ranges</h2>
                <p><strong>行数:</strong> 852</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>reference_range_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>indicator_id</td>
                    <td>INTEGER</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span><span class="tag tag-fk">外键 → indicators.indicator_id</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>region_type</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>3</td>
                    <td>description</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>4</td>
                    <td>source</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>5</td>
                    <td>last_updated</td>
                    <td>TIMESTAMP</td>
                    <td>是</td>
                    <td>CURRENT_TIMESTAMP</td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>外键关系</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>本表列</th>
                        <th>引用表</th>
                        <th>引用列</th>
                        <th>on_update</th>
                        <th>on_delete</th>
                    </tr>
            
                    <tr>
                        <td>1</td>
                        <td>indicator_id</td>
                        <td>indicators</td>
                        <td>indicator_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE &quot;reference_ranges&quot; (
                reference_range_id INTEGER PRIMARY KEY AUTOINCREMENT,
                indicator_id INTEGER NOT NULL,
                region_type TEXT NOT NULL,
                description TEXT,
                source TEXT,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (indicator_id) REFERENCES indicators(indicator_id)
            )</div>
        
                <h3>示例数据</h3>
                <div class="sample-data">
                    <table>
                        <tr>
            <th>reference_range_id</th><th>indicator_id</th><th>region_type</th><th>description</th><th>source</th><th>last_updated</th></tr><tr><td>1</td><td>971</td><td>national</td><td>根据医院实际情况和行业标准制定合理目标，持续改进。</td><td>《国家三级公立医院绩效考核操作手册》质量控制指标要求</td><td>2025-05-16 04:12:56</td></tr><tr><td>2</td><td>971</td><td>guangdong</td><td>参照广东省医疗质量控制中心标准</td><td>广东省医疗质量控制中心</td><td>2025-05-16 04:12:56</td></tr><tr><td>3</td><td>972</td><td>national</td><td>根据医院实际情况和行业标准制定合理目标，持续改进。</td><td>《国家三级公立医院绩效考核操作手册》质量控制指标要求</td><td>2025-05-16 04:12:56</td></tr><tr><td>4</td><td>972</td><td>guangdong</td><td>参照广东省医疗质量控制中心标准</td><td>广东省医疗质量控制中心</td><td>2025-05-16 04:12:56</td></tr><tr><td>5</td><td>973</td><td>national</td><td>急性心肌梗死（ST段抬高型）平均住院日应控制在7-10天。</td><td>《急性ST段抬高型心肌梗死诊断和治疗指南》中国医师协会心血管内科医师分会</td><td>2025-05-16 04:12:56</td></tr>
                    </table>
                </div>
            
            </div>
        
            <div class="table-info" id="reference_ranges_backup">
                <h2>reference_ranges_backup</h2>
                <p><strong>行数:</strong> 320</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>reference_range_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>indicator_id</td>
                    <td>INTEGER</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span><span class="tag tag-fk">外键 → indicators.indicator_id</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>min_value</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>3</td>
                    <td>max_value</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>4</td>
                    <td>description</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>5</td>
                    <td>source</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>6</td>
                    <td>last_updated</td>
                    <td>TIMESTAMP</td>
                    <td>是</td>
                    <td>CURRENT_TIMESTAMP</td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>外键关系</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>本表列</th>
                        <th>引用表</th>
                        <th>引用列</th>
                        <th>on_update</th>
                        <th>on_delete</th>
                    </tr>
            
                    <tr>
                        <td>1</td>
                        <td>indicator_id</td>
                        <td>indicators</td>
                        <td>indicator_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE &quot;reference_ranges_backup&quot; (
    reference_range_id INTEGER PRIMARY KEY AUTOINCREMENT,
    indicator_id INTEGER NOT NULL,
    min_value TEXT,
    max_value TEXT,
    description TEXT,
    source TEXT,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (indicator_id) REFERENCES indicators(indicator_id)
)</div>
        
                <h3>示例数据</h3>
                <div class="sample-data">
                    <table>
                        <tr>
            <th>reference_range_id</th><th>indicator_id</th><th>min_value</th><th>max_value</th><th>description</th><th>source</th><th>last_updated</th></tr><tr><td>966</td><td>971</td><td></td><td></td><td>根据医院实际情况和行业标准制定合理目标，持续改进。（此范围仅供参考）
广东省参考范围：参照广东省医疗质量控制中心标准
来源: 广东省医疗质量控制中心</td><td>《国家三级公立医院绩效考核操作手册》质量控制指标要求</td><td>2025-05-16 03:55:27</td></tr><tr><td>967</td><td>972</td><td></td><td></td><td>根据医院实际情况和行业标准制定合理目标，持续改进。（此范围仅供参考）
广东省参考范围：参照广东省医疗质量控制中心标准
来源: 广东省医疗质量控制中心</td><td>《国家三级公立医院绩效考核操作手册》质量控制指标要求</td><td>2025-05-16 03:55:27</td></tr><tr><td>968</td><td>973</td><td></td><td></td><td>急性心肌梗死（ST段抬高型）平均住院日应控制在7-10天。（此范围仅供参考）
广东省参考范围：6-9天
来源: 广东省心血管病医疗质量控制中心</td><td>《急性ST段抬高型心肌梗死诊断和治疗指南》中国医师协会心血管内科医师分会</td><td>2025-05-16 03:55:27</td></tr><tr><td>969</td><td>974</td><td></td><td></td><td>急性心肌梗死（ST段抬高型）次均费用应参照当地医保支付标准，一般控制在20000-30000元。（此范围仅供参考）
广东省参考范围：18000-28000元
来源: 广东省医疗保障局DRG付费标准（2023版）</td><td>《国家医疗保障疾病诊断相关分组（CHS-DRG）分组方案》</td><td>2025-05-16 03:55:27</td></tr><tr><td>970</td><td>975</td><td></td><td></td><td>急性心肌梗死（ST段抬高型）病死率应控制在≤8%。（此范围仅供参考）
广东省参考范围：≤7%
来源: 广东省心血管病医疗质量控制中心</td><td>《中国急性心肌梗死救治指南》中国医师协会心血管内科医师分会</td><td>2025-05-16 03:55:27</td></tr>
                    </table>
                </div>
            
            </div>
        
            <div class="table-info" id="sections">
                <h2>sections</h2>
                <p><strong>行数:</strong> 80</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>section_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>chapter_id</td>
                    <td>INTEGER</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span><span class="tag tag-fk">外键 → chapters.chapter_id</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>section_code</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>3</td>
                    <td>section_name</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                </table>
                
        
                <h3>外键关系</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>本表列</th>
                        <th>引用表</th>
                        <th>引用列</th>
                        <th>on_update</th>
                        <th>on_delete</th>
                    </tr>
            
                    <tr>
                        <td>1</td>
                        <td>chapter_id</td>
                        <td>chapters</td>
                        <td>chapter_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                </table>
            
                <h3>索引</h3>
                <table>
                    <tr>
                        <th>名称</th>
                        <th>唯一</th>
                    </tr>
            
                    <tr>
                        <td>sqlite_autoindex_sections_1</td>
                        <td>是</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE sections (
    section_id INTEGER PRIMARY KEY,
    chapter_id INTEGER NOT NULL,                     -- 所属章节ID
    section_code TEXT NOT NULL,           -- 例如：1, 2, 3...
    section_name TEXT NOT NULL,          -- 例如：床位配置
    UNIQUE (chapter_id, section_code),
    FOREIGN KEY (chapter_id) REFERENCES chapters(chapter_id)
)</div>
        
                <h3>示例数据</h3>
                <div class="sample-data">
                    <table>
                        <tr>
            <th>section_id</th><th>chapter_id</th><th>section_code</th><th>section_name</th></tr><tr><td>1</td><td>1</td><td>1</td><td>床位配置（3个指标）</td></tr><tr><td>2</td><td>1</td><td>2</td><td>卫生技术人员配备（5个指标）</td></tr><tr><td>3</td><td>1</td><td>3</td><td>相关科室资源配置（20个指标）</td></tr><tr><td>4</td><td>1</td><td>4</td><td>运行指标（9个指标）</td></tr><tr><td>5</td><td>1</td><td>5</td><td>科研指标（4个指标）</td></tr>
                    </table>
                </div>
            
            </div>
        
            <div class="table-info" id="sqlite_sequence">
                <h2>sqlite_sequence</h2>
                <p><strong>行数:</strong> 3</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>name</td>
                    <td></td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>seq</td>
                    <td></td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE sqlite_sequence(name,seq)</div>
        
                <h3>示例数据</h3>
                <div class="sample-data">
                    <table>
                        <tr>
            <th>name</th><th>seq</th></tr><tr><td>users</td><td>2</td></tr><tr><td>reference_ranges_backup</td><td>1285</td></tr><tr><td>reference_ranges</td><td>2386</td></tr>
                    </table>
                </div>
            
            </div>
        
            <div class="table-info" id="tags">
                <h2>tags</h2>
                <p><strong>行数:</strong> 5</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>tag_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>tag_name</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>tag_color</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td>'#4285F4'</td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>3</td>
                    <td>tag_description</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>索引</h3>
                <table>
                    <tr>
                        <th>名称</th>
                        <th>唯一</th>
                    </tr>
            
                    <tr>
                        <td>sqlite_autoindex_tags_1</td>
                        <td>是</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE tags (
    tag_id INTEGER PRIMARY KEY,
    tag_name TEXT NOT NULL UNIQUE,
    tag_color TEXT NOT NULL DEFAULT &#x27;#4285F4&#x27;,
    tag_description TEXT
)</div>
        
                <h3>示例数据</h3>
                <div class="sample-data">
                    <table>
                        <tr>
            <th>tag_id</th><th>tag_name</th><th>tag_color</th><th>tag_description</th></tr><tr><td>1</td><td>关键指标</td><td>#4285F4</td><td>医院核心关注的重要指标</td></tr><tr><td>2</td><td>医疗安全</td><td>#EA4335</td><td>与医疗安全相关的指标</td></tr><tr><td>3</td><td>患者服务</td><td>#FBBC05</td><td>与患者服务体验相关的指标</td></tr><tr><td>4</td><td>质量改进</td><td>#34A853</td><td>需要重点改进的质量指标</td></tr><tr><td>5</td><td>重点监测</td><td>#9C27B0</td><td>需要持续监测的指标</td></tr>
                    </table>
                </div>
            
            </div>
        
            <div class="table-info" id="term_mappings">
                <h2>term_mappings</h2>
                <p><strong>行数:</strong> 0</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>mapping_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>source_term_id</td>
                    <td>INTEGER</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span><span class="tag tag-fk">外键 → terminology_standards.term_id</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>target_term_id</td>
                    <td>INTEGER</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span><span class="tag tag-fk">外键 → terminology_standards.term_id</span></td>
                </tr>
            
                <tr>
                    <td>3</td>
                    <td>mapping_type</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>4</td>
                    <td>mapping_notes</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>外键关系</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>本表列</th>
                        <th>引用表</th>
                        <th>引用列</th>
                        <th>on_update</th>
                        <th>on_delete</th>
                    </tr>
            
                    <tr>
                        <td>1</td>
                        <td>target_term_id</td>
                        <td>terminology_standards</td>
                        <td>term_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                    <tr>
                        <td>2</td>
                        <td>source_term_id</td>
                        <td>terminology_standards</td>
                        <td>term_id</td>
                        <td>NO ACTION</td>
                        <td>NO ACTION</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE term_mappings (
    mapping_id INTEGER PRIMARY KEY,
    source_term_id INTEGER NOT NULL,
    target_term_id INTEGER NOT NULL,
    mapping_type TEXT NOT NULL,  -- &#x27;EXACT&#x27;（完全相同）, &#x27;BROADER&#x27;（更广泛）, &#x27;NARROWER&#x27;（更狭窄）, &#x27;RELATED&#x27;（相关）
    mapping_notes TEXT,
    FOREIGN KEY (source_term_id) REFERENCES terminology_standards(term_id),
    FOREIGN KEY (target_term_id) REFERENCES terminology_standards(term_id)
)</div>
        
            </div>
        
            <div class="table-info" id="terminology_standards">
                <h2>terminology_standards</h2>
                <p><strong>行数:</strong> 0</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>term_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>term_name</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>term_context</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>3</td>
                    <td>term_definition</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>4</td>
                    <td>standard_code</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>5</td>
                    <td>scope_limitation</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>6</td>
                    <td>exclusion_criteria</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>索引</h3>
                <table>
                    <tr>
                        <th>名称</th>
                        <th>唯一</th>
                    </tr>
            
                    <tr>
                        <td>sqlite_autoindex_terminology_standards_1</td>
                        <td>是</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE terminology_standards (
    term_id INTEGER PRIMARY KEY,
    term_name TEXT NOT NULL,
    term_context TEXT NOT NULL,  -- 使用上下文（如&quot;病死率-分母&quot;）
    term_definition TEXT NOT NULL,
    standard_code TEXT,  -- 标准编码（如ICD、SNOMED CT等）
    scope_limitation TEXT,  -- 适用范围限制
    exclusion_criteria TEXT,  -- 排除标准
    UNIQUE(term_name, term_context)
)</div>
        
            </div>
        
            <div class="table-info" id="units">
                <h2>units</h2>
                <p><strong>行数:</strong> 67</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>unit_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>unit_name</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                </table>
                
        
                <h3>索引</h3>
                <table>
                    <tr>
                        <th>名称</th>
                        <th>唯一</th>
                    </tr>
            
                    <tr>
                        <td>sqlite_autoindex_units_1</td>
                        <td>是</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE units (
    unit_id INTEGER PRIMARY KEY,
    unit_name TEXT NOT NULL UNIQUE
)</div>
        
                <h3>示例数据</h3>
                <div class="sample-data">
                    <table>
                        <tr>
            <th>unit_id</th><th>unit_name</th></tr><tr><td>1</td><td>张</td></tr><tr><td>2</td><td>u</td></tr><tr><td>3</td><td>人 </td></tr><tr><td>4</td><td>X：1</td></tr><tr><td>5</td><td>单位</td></tr>
                    </table>
                </div>
            
            </div>
        
            <div class="table-info" id="users">
                <h2>users</h2>
                <p><strong>行数:</strong> 1</p>
                
                <h3>列信息</h3>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>标签</th>
                    </tr>
        
                <tr>
                    <td>0</td>
                    <td>user_id</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td></td>
                    <td>是</td>
                    <td><span class="tag tag-pk">主键</span></td>
                </tr>
            
                <tr>
                    <td>1</td>
                    <td>user_name</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>2</td>
                    <td>password</td>
                    <td>TEXT</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td><span class="tag tag-not-null">非空</span></td>
                </tr>
            
                <tr>
                    <td>3</td>
                    <td>is_admin</td>
                    <td>INTEGER</td>
                    <td>是</td>
                    <td>0</td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>4</td>
                    <td>full_name</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>5</td>
                    <td>email</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>6</td>
                    <td>department</td>
                    <td>TEXT</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>7</td>
                    <td>created_at</td>
                    <td>TIMESTAMP</td>
                    <td>是</td>
                    <td>CURRENT_TIMESTAMP</td>
                    <td>否</td>
                    <td></td>
                </tr>
            
                </table>
                
        
                <h3>索引</h3>
                <table>
                    <tr>
                        <th>名称</th>
                        <th>唯一</th>
                    </tr>
            
                    <tr>
                        <td>sqlite_autoindex_users_1</td>
                        <td>是</td>
                    </tr>
                
                </table>
            
                <h3>创建语句</h3>
                <div class="schema">CREATE TABLE users (
        user_id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_name TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL,
        is_admin INTEGER DEFAULT 0,
        full_name TEXT,
        email TEXT,
        department TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )</div>
        
                <h3>示例数据</h3>
                <div class="sample-data">
                    <table>
                        <tr>
            <th>user_id</th><th>user_name</th><th>password</th><th>is_admin</th><th>full_name</th><th>email</th><th>department</th><th>created_at</th></tr><tr><td>2</td><td>admin</td><td>8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918</td><td>1</td><td>系统管理员</td><td></td><td></td><td>2025-05-09 13:49:33</td></tr>
                    </table>
                </div>
            
            </div>
        
        </div>
    </body>
    </html>
    