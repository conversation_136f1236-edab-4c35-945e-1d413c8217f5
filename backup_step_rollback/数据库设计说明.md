# 医院等级评审指标管理系统数据库设计说明

## 1. 概述

本数据库设计基于医院等级评审指标说明手册的需求，旨在建立一个完整的指标管理系统，支持指标定义、数据采集、审核流程、科室管理、标签分类等功能。

## 2. 数据库架构

### 2.1 核心表结构

#### 2.1.1 指标表 (indicators)
- **用途**: 存储所有指标的基本信息
- **主要字段**:
  - `id`: 指标编号，如"1.2.1"
  - `name`: 指标名称
  - `description`: 指标定义说明
  - `calculation_method`: 计算方法
  - `target_value`: 目标值
  - `current_value`: 当前值
  - `parent_id`: 父指标ID，支持层级结构
  - `category`: 指标类别
  - `status`: 指标状态（正常/警告/危险）

#### 2.1.2 标签表 (tags)
- **用途**: 管理指标分类标签
- **主要字段**:
  - `name`: 标签名称
  - `color`: 标签颜色
  - `description`: 标签说明

#### 2.1.3 科室表 (departments)
- **用途**: 管理医院科室信息
- **主要字段**:
  - `name`: 科室名称
  - `code`: 科室编码
  - `type`: 科室类型（临床/医技/行政/支持）
  - `parent_id`: 上级科室，支持层级结构

#### 2.1.4 用户表 (users)
- **用途**: 管理系统用户
- **主要字段**:
  - `username`: 用户名
  - `real_name`: 真实姓名
  - `department_id`: 所属科室
  - `role`: 用户角色（管理员/经理/操作员/查看者）

### 2.2 关联表结构

#### 2.2.1 指标标签关联表 (indicator_tags)
- **用途**: 建立指标与标签的多对多关系
- **关键约束**: 同一指标不能重复关联同一标签

#### 2.2.2 指标科室关联表 (indicator_departments)
- **用途**: 定义科室在指标中的角色
- **角色类型**:
  - `lead`: 牵头科室
  - `numerator`: 分子科室
  - `denominator`: 分母科室

#### 2.2.3 指标关联表 (indicator_relations)
- **用途**: 建立指标间的关联关系
- **关联类型**:
  - `related`: 相关指标
  - `dependent`: 依赖关系
  - `similar`: 相似指标

### 2.3 数据管理表结构

#### 2.3.1 指标数据历史表 (indicator_data_history)
- **用途**: 存储指标的历史数据
- **主要字段**:
  - `value`: 指标值
  - `data_period`: 数据周期
  - `status`: 数据状态（草稿/已提交/已审核/已拒绝）
  - `collector_id`: 采集人

#### 2.3.2 数据审核流程表 (data_audit_flow)
- **用途**: 管理数据审核流程
- **审核步骤**:
  1. 科室初审
  2. 质控办复核
  3. 医务科终审

## 3. 数据库特性

### 3.1 索引设计
- 主键索引：所有表都有主键
- 外键索引：提高关联查询性能
- 复合索引：针对常用查询组合建立索引
- 唯一索引：防止重复数据

### 3.2 约束设计
- 外键约束：保证数据完整性
- 唯一约束：防止重复关联
- 检查约束：通过ENUM类型限制值范围
- 非空约束：确保关键字段不为空

### 3.3 数据类型选择
- VARCHAR：变长字符串，节省存储空间
- TEXT：长文本内容
- DECIMAL：精确数值计算
- ENUM：枚举类型，提高查询效率
- TIMESTAMP：时间戳，自动更新

## 4. 视图设计

### 4.1 指标详情视图 (v_indicator_details)
- **功能**: 整合指标、标签、科室信息
- **优势**: 简化复杂查询，提高查询效率

### 4.2 指标数据统计视图 (v_indicator_data_stats)
- **功能**: 统计指标的数据状态和完成情况
- **包含**: 最新值、完成率、各状态数据量

## 5. 安全设计

### 5.1 用户权限
- **admin**: 系统管理员，全部权限
- **manager**: 管理者，可管理指标和审核数据
- **operator**: 操作员，可录入和编辑数据
- **viewer**: 查看者，只能查看数据

### 5.2 数据安全
- 密码加密存储
- 软删除机制（is_active字段）
- 操作日志记录
- 数据备份策略

## 6. 性能优化

### 6.1 查询优化
- 合理使用索引
- 避免全表扫描
- 使用视图简化复杂查询
- 分页查询大数据量

### 6.2 存储优化
- 选择合适的数据类型
- 规范化设计减少冗余
- 定期清理历史数据
- 数据压缩和归档

## 7. 扩展性设计

### 7.1 水平扩展
- 支持分库分表
- 读写分离
- 缓存机制

### 7.2 功能扩展
- 预留扩展字段
- 灵活的配置机制
- 插件化架构支持

## 8. 部署建议

### 8.1 环境要求
- MySQL 8.0+
- 字符集：utf8mb4
- 排序规则：utf8mb4_unicode_ci

### 8.2 初始化步骤
1. 创建数据库
2. 执行表结构脚本
3. 插入初始数据
4. 创建视图
5. 配置用户权限

### 8.3 维护建议
- 定期备份数据
- 监控数据库性能
- 定期优化查询
- 更新统计信息

## 9. 常用操作示例

### 9.1 数据查询
```sql
-- 查询指标详情
SELECT * FROM v_indicator_details WHERE id = '1.2.1';

-- 查询科室负责的指标
SELECT * FROM indicators i
JOIN indicator_departments id ON i.id = id.indicator_id
WHERE id.department_id = 1 AND id.role_type = 'lead';
```

### 9.2 数据更新
```sql
-- 更新指标状态
UPDATE indicators SET status = 'warning' WHERE completion_rate < 80;

-- 批量更新标签
UPDATE tags SET description = '更新后的描述' WHERE id IN (1,2,3);
```

### 9.3 数据统计
```sql
-- 统计各类别指标数量
SELECT category, COUNT(*) FROM indicators GROUP BY category;

-- 统计审核状态分布
SELECT status, COUNT(*) FROM indicator_data_history GROUP BY status;
```

## 10. 注意事项

1. **数据一致性**: 使用事务确保数据一致性
2. **并发控制**: 合理使用锁机制避免并发问题
3. **数据验证**: 在应用层和数据库层都要进行数据验证
4. **错误处理**: 完善的错误处理和日志记录机制
5. **版本管理**: 数据库结构变更要有版本控制
