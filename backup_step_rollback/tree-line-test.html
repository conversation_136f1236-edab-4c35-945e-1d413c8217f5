<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>树形连接线测试</title>
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            padding: 20px;
            background-color: var(--gray-50);
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-title {
            font-size: 24px;
            margin-bottom: 20px;
            color: var(--gray-900);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">树形连接线效果测试</h1>
        
        <div class="card-grid">
            <!-- 父指标 1.3.1 -->
            <div class="indicator-card level-0">
                <div class="card-container parent-connector expanded">
                    <div class="card-header">
                        <div class="card-id-section">
                            <button class="expand-toggle expanded" onclick="toggleTest('1.3.1')" title="展开/折叠子指标">
                                <i class="fas fa-chevron-down" id="toggle-1.3.1"></i>
                            </button>
                            <span class="indicator-id-badge">1.3.1</span>
                        </div>
                        <div class="card-actions">
                            <button class="action-icon" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-icon" title="编辑指标">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <h3 class="card-title">急诊医学科</h3>
                        <div class="card-description">第一章1级指标：急诊医学科</div>
                        <div class="card-meta">
                            <div class="meta-item">
                                <i class="fas fa-folder"></i>
                                <span>第1章</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-layer-group"></i>
                                <span>第1.3节</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-sitemap"></i>
                                <span>2个子指标</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 子指标容器 -->
            <div class="children-container" id="children-1.3.1" style="display: block;">
                <!-- 子指标 ******* -->
                <div class="indicator-card level-1">
                    <div class="card-container">
                        <div class="card-header">
                            <div class="card-id-section">
                                <span class="expand-spacer"></span>
                                <span class="indicator-id-badge">*******</span>
                            </div>
                            <div class="card-actions">
                                <button class="action-icon" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon" title="编辑指标">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <h3 class="card-title">固定急诊医师人数占急诊在岗医师人数的比例</h3>
                            <div class="card-description">第一章2级指标：固定急诊医师人数占急诊在岗医师人数的比例</div>
                            <div class="card-meta">
                                <div class="meta-item">
                                    <i class="fas fa-folder"></i>
                                    <span>第1章</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-layer-group"></i>
                                    <span>第1.3节</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-puzzle-piece"></i>
                                    <span>2个组件</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 子指标 ******* -->
                <div class="indicator-card level-1">
                    <div class="card-container">
                        <div class="card-header">
                            <div class="card-id-section">
                                <span class="expand-spacer"></span>
                                <span class="indicator-id-badge">*******</span>
                            </div>
                            <div class="card-actions">
                                <button class="action-icon" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon" title="编辑指标">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <h3 class="card-title">固定急诊护士人数占急诊护士人数的比例</h3>
                            <div class="card-description">第一章2级指标：固定急诊护士人数占急诊护士人数的比例</div>
                            <div class="card-meta">
                                <div class="meta-item">
                                    <i class="fas fa-folder"></i>
                                    <span>第1章</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-layer-group"></i>
                                    <span>第1.3节</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-puzzle-piece"></i>
                                    <span>2个组件</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleTest(indicatorId) {
            const childrenContainer = document.getElementById(`children-${indicatorId}`);
            const toggleIcon = document.getElementById(`toggle-${indicatorId}`);
            const toggleButton = toggleIcon?.parentElement;
            const parentContainer = toggleIcon?.closest('.indicator-card')?.querySelector('.card-container');

            if (!childrenContainer) return;

            const isExpanded = childrenContainer.style.display !== 'none';

            if (isExpanded) {
                // 折叠子指标
                childrenContainer.style.display = 'none';
                if (toggleIcon) {
                    toggleIcon.className = 'fas fa-chevron-down';
                }
                if (toggleButton) {
                    toggleButton.classList.remove('expanded');
                }
                if (parentContainer) {
                    parentContainer.classList.remove('expanded');
                }
            } else {
                // 展开子指标
                childrenContainer.style.display = 'block';
                if (toggleIcon) {
                    toggleIcon.className = 'fas fa-chevron-down';
                }
                if (toggleButton) {
                    toggleButton.classList.add('expanded');
                }
                if (parentContainer) {
                    parentContainer.classList.add('expanded');
                }
            }
        }
    </script>
</body>
</html>
