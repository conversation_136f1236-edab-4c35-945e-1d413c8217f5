#!/usr/bin/env python3
"""
修正简单指标的类型和属性
识别那些应该是简单指标但被错误标记为复合指标的情况
"""

import sqlite3
import re

def get_db_connection():
    """获取数据库连接"""
    return sqlite3.connect('DATABASE-HOSPITAL/hospital_indicator_system.db')

def identify_simple_indicators():
    """识别应该是简单指标的指标"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # 获取所有指标
    cursor.execute("""
        SELECT i.id, i.name, i.indicator_type, COUNT(ic.id) as component_count
        FROM indicators i
        LEFT JOIN indicator_components ic ON i.id = ic.indicator_id
        GROUP BY i.id, i.name, i.indicator_type
        ORDER BY i.id
    """)

    indicators = cursor.fetchall()
    simple_indicators = []

    print("🔍 分析指标类型...")
    print("=" * 80)

    for indicator_id, name, indicator_type, component_count in indicators:
        should_be_simple = False
        reason = ""

        # 判断规则：基于指标名称特征
        if any(keyword in name for keyword in [
            '床位数', '人数', '面积', '设备数', '科室数', '房间数',
            '总数', '数量', '台数', '间数', '个数'
        ]):
            # 如果是纯数量指标，通常是简单指标
            if not any(keyword in name for keyword in [
                '率', '比例', '比', '密度', '强度', '平均', '每'
            ]):
                should_be_simple = True
                reason = "数量类指标"

        # 特殊情况：某些带"率"的指标实际上是简单指标
        if '使用率' in name and component_count == 0:
            should_be_simple = True
            reason = "无分子分母的率类指标"

        # 如果当前是composite但没有组件，应该是simple
        if indicator_type == 'composite' and component_count == 0:
            should_be_simple = True
            reason = "复合指标但无组件"

        if should_be_simple:
            simple_indicators.append({
                'id': indicator_id,
                'name': name,
                'current_type': indicator_type,
                'component_count': component_count,
                'reason': reason
            })

            print(f"📋 {indicator_id} - {name}")
            print(f"   当前类型: {indicator_type}, 组件数: {component_count}")
            print(f"   建议: 改为简单指标 ({reason})")
            print()

    conn.close()
    return simple_indicators

def get_indicator_attributes(indicator_id, name):
    """根据指标名称推断属性"""
    attributes = {
        'unit': '',
        'lead_department': '医务科',
        'data_source': '医院信息系统',
        'logic_definition': ''
    }

    # 根据指标名称推断单位
    if '床位' in name:
        attributes['unit'] = '张'
        attributes['logic_definition'] = f'{name}的统计数量'
    elif '人数' in name or '人员' in name:
        attributes['unit'] = '人'
        attributes['logic_definition'] = f'{name}的统计数量'
        if '医师' in name or '医生' in name:
            attributes['lead_department'] = '医务科'
        elif '护士' in name or '护理' in name:
            attributes['lead_department'] = '护理部'
        else:
            attributes['lead_department'] = '人事科'
    elif '面积' in name:
        attributes['unit'] = '平方米'
        attributes['logic_definition'] = f'{name}的统计面积'
        attributes['lead_department'] = '总务科'
    elif '设备' in name or '台数' in name:
        attributes['unit'] = '台'
        attributes['logic_definition'] = f'{name}的统计数量'
        attributes['lead_department'] = '设备科'
    elif '科室' in name or '部门' in name:
        attributes['unit'] = '个'
        attributes['logic_definition'] = f'{name}的统计数量'
        attributes['lead_department'] = '医务科'
    else:
        attributes['unit'] = '个'
        attributes['logic_definition'] = f'{name}的统计数量'

    return attributes

def fix_simple_indicators(simple_indicators):
    """修正简单指标"""
    conn = get_db_connection()
    cursor = conn.cursor()

    print("🔧 开始修正简单指标...")
    print("=" * 80)

    for indicator in simple_indicators:
        indicator_id = indicator['id']
        name = indicator['name']

        # 获取推荐属性
        attributes = get_indicator_attributes(indicator_id, name)

        print(f"修正指标: {indicator_id} - {name}")

        # 更新指标类型和属性
        cursor.execute("""
            UPDATE indicators
            SET indicator_type = 'simple',
                unit = ?,
                lead_department = ?,
                data_source = ?,
                logic_definition = ?
            WHERE id = ?
        """, (
            attributes['unit'],
            attributes['lead_department'],
            attributes['data_source'],
            attributes['logic_definition'],
            indicator_id
        ))

        # 如果有错误的分子分母组件，删除它们
        if indicator['component_count'] > 0:
            print(f"  删除 {indicator['component_count']} 个错误的组件")
            cursor.execute("DELETE FROM indicator_components WHERE indicator_id = ?", (indicator_id,))

        print(f"  ✅ 已更新为简单指标")
        print(f"     单位: {attributes['unit']}")
        print(f"     牵头科室: {attributes['lead_department']}")
        print(f"     数据来源: {attributes['data_source']}")
        print()

    conn.commit()
    conn.close()

    print(f"✅ 完成！共修正了 {len(simple_indicators)} 个指标")

def main():
    """主函数"""
    print("🏥 医院指标类型修正工具")
    print("=" * 80)

    # 识别需要修正的简单指标
    simple_indicators = identify_simple_indicators()

    if not simple_indicators:
        print("✅ 没有发现需要修正的指标")
        return

    print(f"\n发现 {len(simple_indicators)} 个需要修正的指标")

    # 自动执行修正
    fix_simple_indicators(simple_indicators)

if __name__ == "__main__":
    main()
