#!/usr/bin/env python3
"""
启动Django医院指标管理系统
"""

import os
import sys
import subprocess
import time

def check_requirements():
    """检查依赖包"""
    print("📋 检查依赖包...")
    
    try:
        import django
        print(f"   ✅ Django {django.get_version()}")
    except ImportError:
        print("   ❌ Django未安装，正在安装...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
    
    try:
        import rest_framework
        print("   ✅ Django REST Framework")
    except ImportError:
        print("   ❌ Django REST Framework未安装")
        return False
    
    return True

def setup_database():
    """设置数据库"""
    print("💾 设置数据库...")
    
    # 检查数据库文件是否存在
    db_path = 'DATABASE-HOSPITAL/hospital_indicator_system.db'
    if not os.path.exists(db_path):
        print(f"   ❌ 数据库文件不存在: {db_path}")
        print("   请确保数据库文件存在后再启动")
        return False
    
    print(f"   ✅ 数据库文件存在: {db_path}")
    
    # 运行Django迁移
    try:
        print("   🔄 运行Django迁移...")
        result = subprocess.run([
            sys.executable, 'manage.py', 'migrate', '--run-syncdb'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✅ 数据库迁移完成")
        else:
            print(f"   ⚠️  迁移警告: {result.stderr}")
    
    except Exception as e:
        print(f"   ⚠️  迁移异常: {e}")
    
    return True

def create_superuser():
    """创建超级用户"""
    print("👤 检查超级用户...")
    
    try:
        # 检查是否已有超级用户
        result = subprocess.run([
            sys.executable, 'manage.py', 'shell', '-c',
            'from django.contrib.auth.models import User; print(User.objects.filter(is_superuser=True).exists())'
        ], capture_output=True, text=True)
        
        if 'True' in result.stdout:
            print("   ✅ 超级用户已存在")
            return True
        
        print("   🔄 创建超级用户...")
        print("   用户名: admin")
        print("   密码: admin123")
        print("   邮箱: <EMAIL>")
        
        # 创建默认超级用户
        subprocess.run([
            sys.executable, 'manage.py', 'shell', '-c',
            '''
from django.contrib.auth.models import User
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print("超级用户创建成功")
else:
    print("超级用户已存在")
            '''
        ])
        
        print("   ✅ 超级用户设置完成")
        
    except Exception as e:
        print(f"   ⚠️  超级用户设置异常: {e}")
    
    return True

def collect_static():
    """收集静态文件"""
    print("📁 收集静态文件...")
    
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'collectstatic', '--noinput'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✅ 静态文件收集完成")
        else:
            print(f"   ⚠️  静态文件收集警告: {result.stderr}")
    
    except Exception as e:
        print(f"   ⚠️  静态文件收集异常: {e}")

def start_server():
    """启动Django服务器"""
    print("🚀 启动Django服务器...")
    print("   地址: http://localhost:8000")
    print("   管理后台: http://localhost:8000/admin")
    print("   API文档: http://localhost:8000/api")
    print("=" * 50)
    
    try:
        subprocess.run([
            sys.executable, 'manage.py', 'runserver', '0.0.0.0:8000'
        ])
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")

def show_project_info():
    """显示项目信息"""
    print("\n📋 Django项目信息")
    print("=" * 50)
    print("🎯 项目特点:")
    print("✅ Django 4.2 + REST Framework")
    print("✅ Google Material Design风格")
    print("✅ 完整的前后端分离架构")
    print("✅ 强大的Django Admin管理后台")
    print("✅ RESTful API接口")
    print("✅ 957个医院指标数据")
    
    print("\n🔗 访问地址:")
    print("- 前端系统: http://localhost:8000")
    print("- 管理后台: http://localhost:8000/admin")
    print("- API接口: http://localhost:8000/api")
    
    print("\n👤 默认账户:")
    print("- 用户名: admin")
    print("- 密码: admin123")
    
    print("\n📊 数据统计:")
    try:
        # 获取数据统计
        result = subprocess.run([
            sys.executable, 'manage.py', 'shell', '-c',
            '''
from indicators.models import Chapter, Section, Indicator
print(f"章节数: {Chapter.objects.filter(is_active=True).count()}")
print(f"小节数: {Section.objects.filter(is_active=True).count()}")
print(f"指标数: {Indicator.objects.filter(is_active=True).count()}")
            '''
        ], capture_output=True, text=True)
        
        if result.stdout:
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    print(f"- {line}")
    except:
        print("- 数据统计获取失败")

def main():
    """主函数"""
    print("🎯 启动Django医院指标管理系统")
    print("=" * 70)
    
    # 检查当前目录
    if not os.path.exists('manage.py'):
        print("❌ 请在Django项目根目录下运行此脚本")
        return False
    
    try:
        # 1. 检查依赖包
        if not check_requirements():
            print("❌ 依赖包检查失败")
            return False
        
        # 2. 设置数据库
        if not setup_database():
            print("❌ 数据库设置失败")
            return False
        
        # 3. 创建超级用户
        create_superuser()
        
        # 4. 收集静态文件
        collect_static()
        
        # 5. 显示项目信息
        show_project_info()
        
        # 6. 启动服务器
        print("\n🚀 准备启动服务器...")
        time.sleep(2)
        start_server()
        
    except KeyboardInterrupt:
        print("\n🛑 启动过程被中断")
        return False
    except Exception as e:
        print(f"\n❌ 启动过程中出现错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
