#!/usr/bin/env python3
"""
创建Django示例数据
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospital_indicators.settings')
django.setup()

from indicators.models import Chapter, Section, Indicator

def create_sample_data():
    """创建示例数据"""
    print("🔄 创建示例数据...")
    
    # 创建章节
    chapters_data = [
        {'id': '1', 'name': '医院功能与任务', 'description': '医院基本功能和任务指标', 'order_num': 1},
        {'id': '2', 'name': '医院服务', 'description': '医院服务质量和效率指标', 'order_num': 2},
        {'id': '3', 'name': '患者安全', 'description': '患者安全管理指标', 'order_num': 3},
        {'id': '4', 'name': '医疗质量安全管理与持续改进', 'description': '医疗质量管理指标', 'order_num': 4},
        {'id': '5', 'name': '护理管理与质量持续改进', 'description': '护理质量管理指标', 'order_num': 5},
    ]
    
    for chapter_data in chapters_data:
        chapter, created = Chapter.objects.get_or_create(
            id=chapter_data['id'],
            defaults=chapter_data
        )
        if created:
            print(f"   ✅ 创建章节: {chapter.name}")
    
    # 创建小节
    sections_data = [
        {'id': '1.1', 'chapter_id': '1', 'name': '医院设置、功能和任务符合卫生健康行政部门的规定', 'order_num': 1},
        {'id': '1.2', 'chapter_id': '1', 'name': '医院规模与功能和任务相适应', 'order_num': 2},
        {'id': '1.3', 'chapter_id': '1', 'name': '医院有承担公共卫生职责的能力', 'order_num': 3},
        {'id': '2.1', 'chapter_id': '2', 'name': '门诊服务管理', 'order_num': 1},
        {'id': '2.2', 'chapter_id': '2', 'name': '急诊服务管理', 'order_num': 2},
        {'id': '2.3', 'chapter_id': '2', 'name': '住院服务管理', 'order_num': 3},
        {'id': '3.1', 'chapter_id': '3', 'name': '患者安全目标', 'order_num': 1},
        {'id': '3.2', 'chapter_id': '3', 'name': '患者安全管理体系', 'order_num': 2},
        {'id': '4.1', 'chapter_id': '4', 'name': '医疗质量管理组织', 'order_num': 1},
        {'id': '4.2', 'chapter_id': '4', 'name': '医疗技术管理', 'order_num': 2},
        {'id': '5.1', 'chapter_id': '5', 'name': '护理管理组织体系', 'order_num': 1},
        {'id': '5.2', 'chapter_id': '5', 'name': '护理人力资源管理', 'order_num': 2},
    ]
    
    for section_data in sections_data:
        section, created = Section.objects.get_or_create(
            id=section_data['id'],
            defaults=section_data
        )
        if created:
            print(f"   ✅ 创建小节: {section.name}")
    
    # 创建指标
    indicators_data = [
        {
            'id': '1.1.1',
            'chapter_id': '1',
            'section_id': '1.1',
            'name': '医院设置符合国家法律法规及卫生健康行政部门规定',
            'description': '医院设置应当符合国家相关法律法规和卫生健康行政部门的规定',
            'indicator_type': 'simple',
            'unit': '符合/不符合',
            'data_source': '医院设置批准文件',
            'lead_department': '医务部',
            'logic_definition': '医院设置符合相关规定为符合，否则为不符合',
            'order_num': 1
        },
        {
            'id': '1.1.2',
            'chapter_id': '1',
            'section_id': '1.1',
            'name': '医院功能任务符合医院功能定位',
            'description': '医院功能任务应当与医院功能定位相符合',
            'indicator_type': 'simple',
            'unit': '符合/不符合',
            'data_source': '医院章程、功能任务书',
            'lead_department': '医务部',
            'logic_definition': '医院功能任务与定位相符为符合，否则为不符合',
            'order_num': 2
        },
        {
            'id': '1.2.1',
            'chapter_id': '1',
            'section_id': '1.2',
            'name': '床位数与医院功能相适应',
            'description': '医院床位数应当与医院功能和任务相适应',
            'indicator_type': 'composite',
            'unit': '张',
            'data_source': '医院统计报表',
            'lead_department': '医务部',
            'logic_definition': '实际开放床位数',
            'numerator': '实际开放床位数',
            'denominator': '1',
            'order_num': 1
        },
        {
            'id': '1.2.2',
            'chapter_id': '1',
            'section_id': '1.2',
            'name': '卫生技术人员数与开放床位数比',
            'description': '卫生技术人员数与开放床位数的比例',
            'indicator_type': 'composite',
            'unit': '人/床',
            'data_source': '人事部门统计',
            'lead_department': '人事部',
            'logic_definition': '卫生技术人员总数/实际开放床位数',
            'numerator': '卫生技术人员总数',
            'denominator': '实际开放床位数',
            'order_num': 2
        },
        {
            'id': '2.1.1',
            'chapter_id': '2',
            'section_id': '2.1',
            'name': '门诊患者平均预约诊疗率',
            'description': '门诊患者通过预约方式就诊的比例',
            'indicator_type': 'composite',
            'unit': '%',
            'data_source': '门诊信息系统',
            'lead_department': '门诊部',
            'logic_definition': '预约就诊人次/门诊总人次×100%',
            'numerator': '预约就诊人次',
            'denominator': '门诊总人次',
            'order_num': 1
        },
        {
            'id': '2.1.2',
            'chapter_id': '2',
            'section_id': '2.1',
            'name': '门诊患者平均候诊时间',
            'description': '门诊患者从挂号到就诊的平均等待时间',
            'indicator_type': 'simple',
            'unit': '分钟',
            'data_source': '门诊信息系统',
            'lead_department': '门诊部',
            'logic_definition': '所有门诊患者候诊时间总和/门诊总人次',
            'order_num': 2
        },
        {
            'id': '3.1.1',
            'chapter_id': '3',
            'section_id': '3.1',
            'name': '患者身份识别准确率',
            'description': '正确识别患者身份的比例',
            'indicator_type': 'composite',
            'unit': '%',
            'data_source': '质量管理部门统计',
            'lead_department': '质量管理部',
            'logic_definition': '正确识别患者身份次数/患者身份识别总次数×100%',
            'numerator': '正确识别患者身份次数',
            'denominator': '患者身份识别总次数',
            'order_num': 1
        },
        {
            'id': '4.1.1',
            'chapter_id': '4',
            'section_id': '4.1',
            'name': '医疗质量管理委员会会议召开频次',
            'description': '医疗质量管理委员会每年召开会议的次数',
            'indicator_type': 'simple',
            'unit': '次/年',
            'data_source': '质量管理部门记录',
            'lead_department': '质量管理部',
            'logic_definition': '年度医疗质量管理委员会会议召开次数',
            'order_num': 1
        },
        {
            'id': '5.1.1',
            'chapter_id': '5',
            'section_id': '5.1',
            'name': '护理管理委员会会议召开频次',
            'description': '护理管理委员会每年召开会议的次数',
            'indicator_type': 'simple',
            'unit': '次/年',
            'data_source': '护理部记录',
            'lead_department': '护理部',
            'logic_definition': '年度护理管理委员会会议召开次数',
            'order_num': 1
        },
        {
            'id': '5.2.1',
            'chapter_id': '5',
            'section_id': '5.2',
            'name': '护士与实际开放床位比',
            'description': '护士人数与实际开放床位数的比例',
            'indicator_type': 'composite',
            'unit': '人/床',
            'data_source': '护理部统计',
            'lead_department': '护理部',
            'logic_definition': '护士总数/实际开放床位数',
            'numerator': '护士总数',
            'denominator': '实际开放床位数',
            'order_num': 1
        }
    ]
    
    for indicator_data in indicators_data:
        indicator, created = Indicator.objects.get_or_create(
            id=indicator_data['id'],
            defaults=indicator_data
        )
        if created:
            print(f"   ✅ 创建指标: {indicator.name}")
    
    print("\n📊 数据创建完成！")
    print(f"章节数: {Chapter.objects.count()}")
    print(f"小节数: {Section.objects.count()}")
    print(f"指标数: {Indicator.objects.count()}")

if __name__ == "__main__":
    create_sample_data()
