/* 医院等级评审指标说明手册 - Google风格样式 */

:root {
    --primary: #1a73e8;
    --primary-dark: #1765cc;
    --primary-light: #e8f0fe;
    --blue-50: #e8f1fe;
    --secondary: #34a853;
    --warning: #fbbc04;
    --error: #ea4335;
    --gray-50: #f8f9fa;
    --gray-100: #f1f3f4;
    --gray-200: #e8eaed;
    --gray-300: #dadce0;
    --gray-400: #bdc1c6;
    --gray-500: #9aa0a6;
    --gray-600: #80868b;
    --gray-700: #5f6368;
    --gray-800: #3c4043;
    --gray-900: #202124;
    --white: #ffffff;
    --shadow-1: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
    --shadow-2: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
    --shadow-3: 0 1px 3px 0 rgba(60, 64, 67, 0.3), 0 4px 8px 3px rgba(60, 64, 67, 0.15);
    --border-radius: 8px;
    --transition: all 0.2s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Roboto', 'Arial', sans-serif;
}

body {
    background-color: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* 顶部导航 */
.header {
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-300);
    padding: 8px 16px;
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
}

.menu-toggle {
    background: none;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--gray-700);
    margin-right: 16px;
    transition: var(--transition);
}

.menu-toggle:hover {
    background-color: var(--gray-100);
}

.logo {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: 500;
    color: var(--gray-900);
    text-decoration: none;
    margin-right: 24px;
}

.logo-icon {
    color: var(--primary);
    margin-right: 12px;
    font-size: 24px;
}

.search-wrapper {
    flex: 1;
    max-width: 720px;
    position: relative;
}

.search-input {
    width: 100%;
    height: 48px;
    border: none;
    border-radius: 24px;
    background-color: var(--gray-100);
    padding: 0 24px 0 48px;
    font-size: 16px;
    color: var(--gray-900);
    transition: var(--transition);
}

.search-input:focus {
    background-color: var(--white);
    box-shadow: var(--shadow-1);
    outline: none;
}

.search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-600);
    font-size: 18px;
}

.header-right {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.header-btn {
    background: none;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--gray-700);
    margin-left: 8px;
    transition: var(--transition);
    text-decoration: none;
}

.header-btn:hover {
    background-color: var(--gray-100);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    margin-left: 16px;
    cursor: pointer;
}

/* 主内容区域 */
.main-container {
    display: flex;
    flex: 1;
}

/* 侧边导航 */
.sidebar {
    width: 256px;
    background-color: var(--white);
    border-right: 1px solid var(--gray-300);
    overflow-y: auto;
    padding: 12px 0;
    transition: all 0.3s ease;
}

.sidebar.collapsed {
    width: 72px;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 24px;
    color: var(--gray-800);
    text-decoration: none;
    cursor: pointer;
    border-radius: 0 24px 24px 0;
    margin-right: 12px;
    transition: var(--transition);
    position: relative;
}

.nav-item:hover {
    background-color: var(--gray-100);
    color: var(--gray-800);
    text-decoration: none;
}

.nav-item.active {
    background-color: var(--primary-light);
    color: var(--primary);
    font-weight: 500;
}

.nav-icon {
    margin-right: 24px;
    width: 24px;
    text-align: center;
    font-size: 18px;
}

.sidebar.collapsed .nav-icon {
    margin-right: 0;
}

.sidebar.collapsed .nav-text {
    display: none;
}

.nav-section {
    margin-top: 24px;
    padding: 0 24px 8px;
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-700);
}

.sidebar.collapsed .nav-section {
    display: none;
}

.nav-badge {
    background-color: var(--primary);
    color: var(--white);
    font-size: 11px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
    min-width: 18px;
    text-align: center;
}

.sidebar.collapsed .nav-badge {
    display: none;
}

/* 章节导航组 */
.nav-chapter-group {
    margin-bottom: 8px;
}

.nav-chapter-header {
    display: flex;
    align-items: center;
    padding: 8px 24px 4px;
    font-size: 12px;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.chapter-icon {
    margin-right: 8px;
    font-size: 14px;
    color: var(--primary);
}

.chapter-title {
    font-weight: 600;
    color: var(--gray-700);
}

.chapter-nav-item {
    margin-left: 16px;
    margin-right: 12px;
    padding-left: 16px;
    border-left: 2px solid transparent;
    transition: var(--transition);
}

.chapter-nav-item:hover {
    border-left-color: var(--primary);
    background-color: var(--primary-light);
}

.chapter-nav-item.active {
    border-left-color: var(--primary);
    background-color: var(--primary-light);
    color: var(--primary);
}

.sidebar.collapsed .nav-chapter-group {
    display: none;
}

.sidebar.collapsed .nav-chapter-header {
    display: none;
}

/* 主内容 */
.content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

.page-header {
    margin-bottom: 24px;
}

.page-title {
    font-size: 28px;
    font-weight: 400;
    color: var(--gray-900);
    margin-bottom: 8px;
}

.page-subtitle {
    font-size: 14px;
    color: var(--gray-700);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 24px;
    border-radius: 24px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    border: none;
}

.btn-icon {
    margin-right: 8px;
}

.btn-outline {
    background-color: var(--white);
    color: var(--gray-900);
    border: 1px solid var(--gray-300);
}

.btn-outline:hover {
    background-color: var(--gray-100);
    text-decoration: none;
    color: var(--gray-900);
}

.btn-primary {
    background-color: var(--primary);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    text-decoration: none;
    color: var(--white);
}

/* 卡片网格 */
.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
    margin-bottom: 32px;
}

.card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    overflow: hidden;
    transition: var(--transition);
    height: 100%;
}

.card:hover {
    box-shadow: var(--shadow-2);
}

.card-body {
    padding: 16px;
}

.card-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.card-icon {
    color: var(--primary);
    margin-right: 12px;
    font-size: 20px;
}

.card-content {
    color: var(--gray-700);
    font-size: 14px;
    margin-bottom: 16px;
}

.card-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    color: var(--gray-600);
    padding-top: 12px;
    border-top: 1px solid var(--gray-200);
}

.card-id {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.id-badge {
    color: var(--primary);
    font-weight: 500;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -256px;
        top: 60px;
        bottom: 0;
        z-index: 90;
        transition: left 0.3s ease;
    }

    .sidebar.open {
        left: 0;
    }

    .content {
        padding: 16px;
    }

    .card-grid {
        grid-template-columns: 1fr;
    }

    .search-wrapper {
        max-width: none;
    }

    .logo-text {
        display: none;
    }
}
