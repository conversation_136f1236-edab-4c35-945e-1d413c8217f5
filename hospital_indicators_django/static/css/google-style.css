        :root {
            --primary: #1a73e8;
            --primary-dark: #1765cc;
            --primary-light: #e8f0fe;
            --blue-50: #e8f1fe;
            --secondary: #34a853;
            --warning: #fbbc04;
            --error: #ea4335;
            --gray-50: #f8f9fa;
            --gray-100: #f1f3f4;
            --gray-200: #e8eaed;
            --gray-300: #dadce0;
            --gray-400: #bdc1c6;
            --gray-500: #9aa0a6;
            --gray-600: #80868b;
            --gray-700: #5f6368;
            --gray-800: #3c4043;
            --gray-900: #202124;
            --white: #ffffff;
            --shadow-1: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
            --shadow-2: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
            --shadow-3: 0 1px 3px 0 rgba(60, 64, 67, 0.3), 0 4px 8px 3px rgba(60, 64, 67, 0.15);
            --border-radius: 8px;
            --transition: all 0.2s ease;
        }

        @font-face {
            font-family: 'Google Sans';
            src: local('Google Sans'), url(https://fonts.gstatic.com/s/googlesans/v14/4UaGrENHsxJlGDuGo1OIlL3Owp4.woff2) format('woff2');
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Google Sans', 'Roboto', 'Arial', sans-serif;
        }

        body {
            background-color: var(--gray-50);
            color: var(--gray-900);
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        /* 顶部导航 */
        .header {
            background-color: var(--white);
            border-bottom: 1px solid var(--gray-300);
            padding: 8px 16px;
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .menu-toggle {
            background: none;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--gray-700);
            margin-right: 16px;
            transition: var(--transition);
        }

        .menu-toggle:hover {
            background-color: var(--gray-100);
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: 500;
            color: var(--gray-900);
            text-decoration: none;
            margin-right: 24px;
        }

        .logo-icon {
            color: var(--primary);
            margin-right: 12px;
            font-size: 24px;
        }

        .search-wrapper {
            flex: 1;
            max-width: 720px;
            position: relative;
        }

        .search-input {
            width: 100%;
            height: 48px;
            border: none;
            border-radius: 24px;
            background-color: var(--gray-100);
            padding: 0 24px 0 48px;
            font-size: 16px;
            color: var(--gray-900);
            transition: var(--transition);
        }

        .search-input:focus {
            background-color: var(--white);
            box-shadow: var(--shadow-1);
            outline: none;
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-600);
            font-size: 18px;
        }

        .header-right {
            display: flex;
            align-items: center;
            margin-left: auto;
        }

        .header-btn {
            background: none;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--gray-700);
            margin-left: 8px;
            transition: var(--transition);
        }

        .header-btn:hover {
            background-color: var(--gray-100);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary);
            color: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            margin-left: 16px;
            cursor: pointer;
        }

        /* 主内容区域 */
        .main-container {
            display: flex;
            flex: 1;
        }

        /* 侧边导航 */
        .sidebar {
            width: 256px;
            background-color: var(--white);
            border-right: 1px solid var(--gray-300);
            overflow-y: auto;
            padding: 12px 0;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed {
            width: 72px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: var(--gray-800);
            text-decoration: none;
            cursor: pointer;
            border-radius: 0 24px 24px 0;
            margin-right: 12px;
            transition: var(--transition);
        }

        .nav-item:hover {
            background-color: var(--gray-100);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary);
            font-weight: 500;
        }

        .nav-icon {
            margin-right: 24px;
            width: 24px;
            text-align: center;
            font-size: 18px;
        }

        .sidebar.collapsed .nav-icon {
            margin-right: 0;
        }

        .sidebar.collapsed .nav-text {
            display: none;
        }

        .nav-section {
            margin-top: 24px;
            padding: 0 24px 8px;
            font-size: 14px;
            font-weight: 500;
            color: var(--gray-700);
        }

        .sidebar.collapsed .nav-section {
            display: none;
        }

        /* 主内容 */
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 400;
            color: var(--gray-900);
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: var(--gray-700);
        }

        /* 工具栏 */
        .toolbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
        }

        .view-toggle {
            display: flex;
            background-color: var(--gray-100);
            border-radius: 24px;
            padding: 4px;
        }

        .view-btn {
            background: none;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            color: var(--gray-800);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
        }

        .view-btn i {
            margin-right: 8px;
        }

        .view-btn.active {
            background-color: var(--white);
            color: var(--gray-900);
            box-shadow: var(--shadow-1);
        }

        .action-btns {
            display: flex;
            gap: 12px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 24px;
            border-radius: 24px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            border: none;
        }

        .btn-icon {
            margin-right: 8px;
        }

        .btn-outline {
            background-color: var(--white);
            color: var(--gray-900);
            border: 1px solid var(--gray-300);
        }

        .btn-outline:hover {
            background-color: var(--gray-100);
        }

        .btn-outline.active {
            background-color: var(--primary-light);
            border-color: var(--primary);
            color: var(--primary);
        }

        .filter-badge {
            position: relative;
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--primary);
            margin-left: 5px;
        }

        .btn-primary {
            background-color: var(--primary);
            color: var(--white);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-1);
            overflow: hidden;
            transition: var(--transition);
            height: 100%;
        }

        .card:hover {
            box-shadow: var(--shadow-2);
        }

        .card-body {
            padding: 16px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--gray-900);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .card-icon {
            color: var(--primary);
            margin-right: 12px;
            font-size: 20px;
        }

        .card-content {
            color: var(--gray-700);
            font-size: 14px;
            margin-bottom: 16px;
        }

        .card-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            color: var(--gray-600);
            padding-top: 12px;
            border-top: 1px solid var(--gray-200);
        }

        .card-id {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .id-badge {
            color: var(--primary);
            font-weight: 500;
            font-size: 14px;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .action-icon {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-700);
            cursor: pointer;
            transition: var(--transition);
        }

        .action-icon:hover {
            background-color: var(--gray-100);
            color: var(--gray-900);
        }

        /* 列表视图 */
        .list-view {
            display: none;
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-1);
            overflow: hidden;
            margin-bottom: 32px;
        }

        .list-header {
            display: flex;
            align-items: center;
            padding: 16px;
            background-color: var(--gray-50);
            border-bottom: 1px solid var(--gray-200);
            font-weight: 500;
            color: var(--gray-700);
            font-size: 14px;
        }

        .list-col-id {
            width: 80px;
        }

        .list-col-title {
            flex: 2;
        }

        .list-col-desc {
            flex: 2;
        }

        .list-col-tags {
            flex: 1;
        }

        .list-col-actions {
            width: 80px;
            text-align: right;
        }

        .list-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid var(--gray-200);
            transition: var(--transition);
        }

        .list-item:last-child {
            border-bottom: none;
        }

        .list-item:hover {
            background-color: var(--gray-50);
        }

        .list-item-id {
            width: 80px;
            font-size: 14px;
        }

        .list-item-badge {
            color: var(--primary);
            font-size: 14px;
            font-weight: 500;
        }

        .list-item-title {
            flex: 2;
            font-weight: 500;
            color: var(--gray-900);
            font-size: 14px;
        }

        .list-item-desc {
            flex: 2;
            color: var(--gray-700);
            font-size: 14px;
        }

        .list-item-tags {
            flex: 1;
        }

        .list-item-tags .indicator-tags {
            margin-top: 0;
        }

        .list-item-actions {
            width: 80px;
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        /* 详情面板 */
        .detail-panel {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-1);
            overflow: hidden;
            margin-top: 32px;
        }

        .detail-header {
            padding: 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--gray-200);
        }

        .detail-title {
            font-size: 20px;
            font-weight: 500;
            color: var(--gray-900);
            display: flex;
            align-items: flex-start;
        }

        .detail-title-content {
            display: flex;
            flex-direction: column;
        }

        .detail-title-text {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .detail-title-icon {
            width: 40px;
            height: 40px;
            background-color: var(--primary-light);
            color: var(--primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
        }

        .detail-actions {
            display: flex;
            gap: 12px;
        }

        .detail-body {
            padding: 24px;
        }

        .detail-section {
            margin-bottom: 24px;
        }

        .detail-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--gray-900);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .section-title-icon {
            color: var(--primary);
            margin-right: 8px;
        }

        .section-content {
            background-color: var(--gray-50);
            border-radius: var(--border-radius);
            padding: 16px;
        }

        .section-content p {
            margin-bottom: 12px;
            font-size: 14px;
            color: var(--gray-800);
        }

        .section-content p:last-child {
            margin-bottom: 0;
        }

        .data-row {
            display: flex;
            margin-bottom: 12px;
        }

        .data-row:last-child {
            margin-bottom: 0;
        }

        .data-label {
            width: 120px;
            font-weight: 500;
            color: var(--gray-800);
            font-size: 14px;
        }

        .data-value {
            flex: 1;
            color: var(--gray-700);
            font-size: 14px;
        }

        .progress-container {
            margin-top: 8px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background-color: var(--gray-200);
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--primary);
            border-radius: 2px;
        }

        .progress-fill.warning {
            background-color: var(--warning);
        }

        .progress-fill.error {
            background-color: var(--error);
        }

        .progress-fill.success {
            background-color: var(--secondary);
        }

        /* 表格 */
        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            text-align: left;
            padding: 12px 16px;
            font-size: 14px;
        }

        th {
            background-color: var(--gray-50);
            font-weight: 500;
            color: var(--gray-800);
            border-bottom: 1px solid var(--gray-200);
        }

        td {
            color: var(--gray-700);
            border-bottom: 1px solid var(--gray-200);
        }

        tr:last-child td {
            border-bottom: none;
        }

        /* 子指标样式 */
        .parent-card {
            position: relative;
        }

        .has-children {
            margin-left: 8px;
            color: var(--primary);
            cursor: pointer;
            font-size: 14px;
        }

        .parent-indicator-badge {
            margin-left: 12px;
            color: var(--primary);
            font-size: 14px;
            display: inline-flex;
            align-items: center;
        }

        .parent-indicator-badge i {
            margin-right: 4px;
        }

        .child-indicators {
            display: none;
            background-color: var(--gray-50);
            border-top: 1px solid var(--gray-200);
            padding: 12px;
            margin-top: 8px;
        }

        .parent-card.expanded .child-indicators {
            display: block;
        }

        .child-card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-1);
            margin-bottom: 8px;
            transition: var(--transition);
        }

        .child-card:last-child {
            margin-bottom: 0;
        }

        .child-card:hover {
            box-shadow: var(--shadow-2);
        }

        .child-card-body {
            padding: 12px;
        }

        .child-card-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--gray-900);
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }

        .child-card-icon {
            color: var(--primary);
            margin-right: 8px;
            font-size: 16px;
        }

        .child-card-content {
            color: var(--gray-700);
            font-size: 13px;
            margin-bottom: 8px;
        }

        .child-card-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            color: var(--gray-600);
        }

        /* 列表视图子指标 */
        .parent-list-item {
            background-color: var(--gray-50);
            font-weight: 500;
        }

        .child-list-items {
            display: none;
            background-color: var(--white);
            border-left: 3px solid var(--primary-light);
            margin-left: 40px;
        }

        .child-list-items.expanded {
            display: block;
        }

        .child-list-item {
            padding-left: 24px;
            background-color: var(--white);
            border-bottom: 1px dashed var(--gray-200) !important;
        }

        .child-list-item:last-child {
            border-bottom: none !important;
        }

        .child-badge {
            color: var(--primary);
            font-size: 13px;
        }

        /* 子指标选项卡 */
        .sub-indicators-tabs {
            background-color: var(--white);
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .tab-header {
            display: flex;
            border-bottom: 1px solid var(--gray-200);
            overflow-x: auto;
            white-space: nowrap;
            -webkit-overflow-scrolling: touch;
        }

        .tab-btn {
            padding: 12px 16px;
            background: none;
            border: none;
            font-size: 14px;
            color: var(--gray-700);
            cursor: pointer;
            transition: var(--transition);
            border-bottom: 2px solid transparent;
        }

        .tab-btn.active {
            color: var(--primary);
            border-bottom: 2px solid var(--primary);
            font-weight: 500;
        }

        .tab-content {
            display: none;
            padding: 16px;
        }

        .tab-content.active {
            display: block;
        }

        .sub-indicator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .sub-indicator-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--gray-900);
            display: flex;
            align-items: center;
        }

        .sub-indicator-title .id-badge {
            margin-right: 8px;
            background-color: transparent;
            padding: 0;
            border-radius: 0;
        }

        .sub-indicator-value {
            font-size: 18px;
            font-weight: 500;
            color: var(--primary);
        }

        .sub-indicator-desc {
            margin-bottom: 16px;
            color: var(--gray-700);
            font-size: 14px;
        }

        .sub-indicator-data {
            background-color: var(--gray-50);
            border-radius: var(--border-radius);
            padding: 12px;
        }

        /* 视图模式切换 */
        .view-mode-toggle {
            display: flex;
            margin-right: 16px;
        }

        .view-mode-btn {
            border-radius: 0;
            margin: 0;
            border-right: none;
        }

        .view-mode-btn:first-child {
            border-radius: 24px 0 0 24px;
        }

        .view-mode-btn:last-child {
            border-radius: 0 24px 24px 0;
            border-right: 1px solid var(--gray-300);
        }

        .view-mode-btn.active {
            background-color: var(--primary-light);
            color: var(--primary);
            border-color: var(--primary-light);
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            overflow-y: auto;
            padding: 20px;
        }

        .modal-overlay.active {
            display: flex;
        }

        .modal-container {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-3);
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            animation: modal-appear 0.3s ease;
        }

        @keyframes modal-appear {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            padding: 16px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--gray-200);
            position: sticky;
            top: 0;
            background-color: var(--white);
            z-index: 10;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .modal-title {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: 500;
            color: var(--gray-900);
        }

        .modal-title-text {
            margin-left: 16px;
            display: flex;
            flex-direction: column;
        }

        .modal-title-text > div:first-child {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .modal-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .modal-close-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: none;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--gray-700);
            transition: var(--transition);
            margin-left: 8px;
        }

        .modal-close-btn:hover {
            background-color: var(--gray-100);
            color: var(--gray-900);
        }

        .modal-body {
            padding: 24px;
            overflow-y: auto;
            flex: 1;
        }

        .btn-sm {
            padding: 6px 16px;
            font-size: 13px;
        }

        /* 详情面板显示/隐藏 */
        .detail-panel.hidden {
            display: none;
        }

        /* 弹窗尺寸 */
        .modal-sm {
            max-width: 500px;
        }

        .modal-md {
            max-width: 700px;
        }

        /* 表单样式 */
        .section-subtitle {
            font-size: 14px;
            font-weight: 500;
            color: var(--gray-800);
            margin-bottom: 12px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            font-size: 13px;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 6px;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--gray-300);
            border-radius: 4px;
            font-size: 14px;
            color: var(--gray-800);
            transition: var(--transition);
        }

        .form-control:focus {
            border-color: var(--primary);
            outline: none;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }

        .select-wrapper {
            position: relative;
        }

        .select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-600);
            pointer-events: none;
        }

        .input-group {
            display: flex;
            align-items: center;
        }

        .input-group .form-control {
            flex: 1;
            border-radius: 4px 0 0 4px;
        }

        .input-group-text {
            padding: 8px 12px;
            background-color: var(--gray-100);
            border: 1px solid var(--gray-300);
            border-left: none;
            border-radius: 0 4px 4px 0;
            font-size: 14px;
            color: var(--gray-700);
        }

        .btn-block {
            width: 100%;
        }

        /* 标签管理样式 */
        .tags-container, .dept-container {
            padding: 16px 0;
        }

        .tags-list, .depts-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 24px;
        }

        .tag-item {
            display: inline-flex;
            align-items: center;
            padding: 4px 0;
            color: var(--primary);
            font-size: 13px;
            font-weight: 500;
            position: relative;
            margin-right: 12px;
        }

        .tag-item i.fa-tag {
            margin-right: 6px;
        }

        .tag-actions {
            display: flex;
            align-items: center;
            margin-left: 6px;
        }

        .tag-item .tag-info,
        .tag-item .tag-remove {
            cursor: pointer;
            font-size: 12px;
            padding: 2px 4px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .tag-item .tag-info {
            color: var(--primary);
            margin-right: 2px;
        }

        .tag-item .tag-info:hover {
            background-color: rgba(26, 115, 232, 0.1);
        }

        .tag-item .tag-remove:hover {
            color: var(--error);
        }

        /* 筛选面板样式 */
        .filter-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .filter-section {
            padding-bottom: 16px;
            border-bottom: 1px solid var(--gray-200);
        }

        .filter-section:last-child {
            border-bottom: none;
        }

        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 20px;
            padding-top: 16px;
            border-top: 1px solid var(--gray-200);
        }

        .status-dot {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 6px;
        }

        .status-dot.warning {
            background-color: var(--warning);
        }

        .status-dot.danger {
            background-color: var(--error);
        }

        /* 范围滑块样式 */
        .range-slider-container {
            padding: 0 10px;
            margin-top: 10px;
        }

        .range-values {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
            color: var(--gray-700);
        }

        .range-slider {
            position: relative;
            height: 30px;
        }

        .range-slider-input {
            position: absolute;
            width: 100%;
            height: 5px;
            background: none;
            pointer-events: none;
            -webkit-appearance: none;
            appearance: none;
            z-index: 3;
        }

        .range-slider-input::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--primary);
            cursor: pointer;
            pointer-events: auto;
            margin-top: -7px;
            border: 2px solid var(--white);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .range-slider-track {
            position: absolute;
            width: 100%;
            height: 5px;
            background: var(--gray-300);
            border-radius: 5px;
            z-index: 1;
            top: 50%;
            transform: translateY(-50%);
        }

        .range-slider-fill {
            position: absolute;
            height: 5px;
            background: var(--primary);
            border-radius: 5px;
            z-index: 2;
            top: 50%;
            transform: translateY(-50%);
        }

        /* 指标标签样式 */
        .indicator-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }

        .indicator-tag {
            display: inline-flex;
            align-items: center;
            font-size: 12px;
            color: var(--gray-700);
            white-space: nowrap;
            margin-right: 8px;
        }

        .indicator-tag i {
            margin-right: 4px;
            font-size: 10px;
        }

        /* 标签备注弹窗样式 */
        .tag-description-content {
            background-color: var(--gray-50);
            border-radius: var(--border-radius);
            padding: 16px;
            margin-bottom: 16px;
            border-left: 4px solid var(--primary);
        }

        .tag-description-content p {
            margin: 0;
            line-height: 1.6;
            color: var(--gray-800);
        }

        .color-picker {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .color-option {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            cursor: pointer;
            transition: var(--transition);
            border: 2px solid transparent;
        }

        .color-option.selected {
            border-color: var(--gray-700);
        }

        /* 科室管理样式 */
        .dept-item {
            display: inline-flex;
            align-items: center;
            padding: 4px 10px;
            background-color: var(--gray-100);
            color: var(--gray-800);
            border-radius: 16px;
            font-size: 13px;
            font-weight: 500;
        }

        .dept-item .dept-remove {
            margin-left: 6px;
            cursor: pointer;
            font-size: 14px;
            padding: 2px 4px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .dept-item .dept-remove:hover {
            color: var(--white);
            background-color: var(--error);
        }

        /* 关联指标样式 */
        .related-indicators-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .related-indicators-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            max-height: 200px;
            overflow-y: auto;
            padding: 4px;
            border: 1px solid var(--gray-200);
            border-radius: 4px;
            background-color: var(--white);
        }

        .related-indicator-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            border-radius: 4px;
            background-color: var(--gray-100);
            transition: all 0.2s ease;
        }

        .related-indicator-item.selected {
            background-color: var(--blue-50);
            border-left: 3px solid var(--primary);
        }

        .related-indicator-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .related-indicator-id {
            font-weight: 500;
            color: var(--gray-700);
        }

        .related-indicator-name {
            color: var(--gray-800);
        }

        .related-indicator-remove {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: none;
            background-color: transparent;
            color: var(--gray-500);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .related-indicator-remove:hover {
            background-color: var(--error);
            color: var(--white);
        }

        .related-indicators-actions {
            display: flex;
            justify-content: flex-end;
        }

        /* 关联指标选择弹窗 */
        .related-indicator-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1100;
            overflow-y: auto;
        }

        .related-indicator-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .related-indicator-modal-content {
            width: 600px;
            max-width: 90%;
            background-color: var(--white);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            display: flex;
            flex-direction: column;
            max-height: 80vh;
        }

        .related-indicator-modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            border-bottom: 1px solid var(--gray-200);
        }

        .related-indicator-modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 500;
            color: var(--gray-900);
        }

        .related-indicator-modal-close {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--gray-500);
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .related-indicator-modal-close:hover {
            background-color: var(--gray-100);
            color: var(--gray-700);
        }

        .related-indicator-modal-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .related-indicator-search {
            position: relative;
        }

        .related-indicator-search .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
        }

        .related-indicator-categories {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .related-indicator-category {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .related-indicator-category-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid var(--gray-200);
        }

        .related-indicator-category-name {
            font-weight: 500;
            color: var(--gray-800);
        }

        .related-indicator-category-count {
            background-color: var(--gray-200);
            color: var(--gray-700);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .related-indicator-category-items {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding-left: 8px;
        }

        .related-indicator-select-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .related-indicator-select-item:hover {
            background-color: var(--gray-100);
        }

        .related-indicator-select-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .related-indicator-select-id {
            font-weight: 500;
            color: var(--gray-700);
            min-width: 50px;
        }

        .related-indicator-select-name {
            color: var(--gray-800);
        }

        .related-indicator-modal-footer {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 12px;
            padding: 16px 20px;
            border-top: 1px solid var(--gray-200);
        }

        .tabs-container .tabs-header {
            display: flex;
            border-bottom: 1px solid var(--gray-200);
            margin-bottom: 16px;
        }

        .tabs-container .tab-btn {
            padding: 8px 16px;
            background: none;
            border: none;
            font-size: 14px;
            color: var(--gray-700);
            cursor: pointer;
            transition: var(--transition);
            border-bottom: 2px solid transparent;
        }

        .tabs-container .tab-btn.active {
            color: var(--primary);
            border-bottom: 2px solid var(--primary);
            font-weight: 500;
        }

        /* 操作维护样式 */
        .operation-section {
            margin-bottom: 24px;
            padding: 16px;
            background-color: var(--gray-50);
            border-radius: var(--border-radius);
        }

        .radio-group, .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 16px;
        }

        .radio-item, .checkbox-item {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .radio-label, .checkbox-label {
            margin-left: 8px;
            font-size: 14px;
            color: var(--gray-800);
        }

        /* 复选框样式 */
        .checkbox-wrapper {
            position: relative;
            display: inline-block;
            width: 18px;
            height: 18px;
        }

        .checkbox-wrapper input[type="checkbox"] {
            opacity: 0;
            width: 0;
            height: 0;
            position: absolute;
        }

        .checkbox-wrapper label {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            border-radius: 3px;
            border: 2px solid var(--gray-400);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .checkbox-wrapper label:after {
            content: '';
            position: absolute;
            top: 4px;
            left: 3px;
            width: 8px;
            height: 4px;
            border: 2px solid var(--white);
            border-top: none;
            border-right: none;
            transform: rotate(-45deg);
            opacity: 0;
            transition: all 0.2s ease;
        }

        .checkbox-wrapper input[type="checkbox"]:checked + label {
            background-color: var(--primary);
            border-color: var(--primary);
        }

        .checkbox-wrapper input[type="checkbox"]:checked + label:after {
            opacity: 1;
        }

        .checkbox-wrapper input[type="checkbox"]:focus + label {
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }

        .code-editor {
            font-family: monospace;
            font-size: 13px;
        }

        .verification-flow {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 16px;
        }

        .flow-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-1);
        }

        .flow-step {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: var(--primary);
            color: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            margin-right: 12px;
        }

        .flow-content {
            flex: 1;
        }

        .flow-title {
            font-weight: 500;
            color: var(--gray-900);
            font-size: 14px;
        }

        .flow-desc {
            color: var(--gray-700);
            font-size: 13px;
        }

        .flow-arrow {
            display: flex;
            justify-content: center;
            color: var(--gray-400);
        }

        .threshold-settings {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .threshold-item {
            display: flex;
            align-items: center;
        }

        .threshold-label {
            width: 60px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            margin-right: 12px;
        }

        .threshold-label.warning {
            background-color: var(--warning);
            color: var(--white);
        }

        .threshold-label.danger {
            background-color: var(--error);
            color: var(--white);
        }

        .threshold-inputs {
            display: flex;
            gap: 12px;
            flex: 1;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 24px;
            padding-top: 16px;
            border-top: 1px solid var(--gray-200);
        }

        /* 自定义tooltip样式 */
        .custom-tooltip {
            position: relative;
            display: inline-block;
        }

        .custom-tooltip .tooltip-text {
            visibility: hidden;
            width: 180px;
            background-color: var(--gray-800);
            color: var(--white);
            text-align: left;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1000;
            left: 100%;
            top: -5px;
            margin-left: 5px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            line-height: 1.4;
            box-shadow: var(--shadow-2);
            pointer-events: none;
            word-wrap: break-word;
            overflow-wrap: break-word;
            max-height: 150px;
            overflow-y: auto;
        }

        /* 为左侧面板的tooltip特殊处理 */
        #sidebar-tags-container .custom-tooltip .tooltip-text {
            left: 60px;
            top: 0;
        }

        .custom-tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            top: 15px;
            right: 100%;
            margin-top: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: transparent var(--gray-800) transparent transparent;
        }

        .custom-tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        /* 左侧标签项样式增强 */
        #sidebar-tags-container .nav-item {
            position: relative;
        }

        /* 响应式设计 */
        @media (max-width: 992px) {
            .card-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }

            .search-wrapper {
                max-width: 320px;
            }

            .tab-header {
                flex-wrap: wrap;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -256px;
                top: 60px;
                bottom: 0;
                z-index: 90;
                transition: left 0.3s ease;
            }

            .sidebar.open {
                left: 0;
            }

            .content {
                padding: 16px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }

            .search-wrapper {
                max-width: none;
            }

            .logo-text {
                display: none;
            }

            .toolbar {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .action-btns {
                width: 100%;
                justify-content: flex-end;
            }

            .child-list-items {
                margin-left: 20px;
            }

            .sub-indicator-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
        }
    </style>
