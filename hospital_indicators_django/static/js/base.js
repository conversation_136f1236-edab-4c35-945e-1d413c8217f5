// 医院指标管理系统 - Django版本基础JavaScript

class HospitalIndicatorApp {
    constructor() {
        this.searchTimeout = null;
        this.init();
    }

    init() {
        this.setupMenuToggle();
        this.setupGlobalSearch();
        this.setupModalHandlers();
        this.loadChapterNavigation();
        console.log('Django版本医院指标系统初始化完成');
    }

    setupMenuToggle() {
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');

        if (menuToggle && sidebar) {
            menuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');

                // 保存状态到localStorage
                const isCollapsed = sidebar.classList.contains('collapsed');
                localStorage.setItem('sidebarCollapsed', isCollapsed);
            });

            // 恢复侧边栏状态
            const savedState = localStorage.getItem('sidebarCollapsed');
            if (savedState === 'true') {
                sidebar.classList.add('collapsed');
            }
        }
    }

    setupGlobalSearch() {
        const searchInput = document.getElementById('globalSearch');
        if (!searchInput) return;

        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();

            // 清除之前的定时器
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }

            // 设置新的定时器，防抖处理
            this.searchTimeout = setTimeout(() => {
                if (query.length >= 2) {
                    this.performSearch(query);
                } else {
                    this.hideSearchResults();
                }
            }, 300);
        });

        // 点击搜索框外部时隐藏结果
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-wrapper') && !e.target.closest('#searchModal')) {
                this.hideSearchResults();
            }
        });
    }

    async performSearch(query) {
        try {
            const response = await fetch(`/ajax/search/?q=${encodeURIComponent(query)}`);
            const data = await response.json();

            if (data.results && data.results.length > 0) {
                this.showSearchResults(data.results);
            } else {
                this.showNoResults();
            }
        } catch (error) {
            console.error('搜索失败:', error);
            this.showSearchError();
        }
    }

    showSearchResults(results) {
        const modal = document.getElementById('searchModal');
        const resultsContainer = document.getElementById('searchResults');

        if (!modal || !resultsContainer) return;

        resultsContainer.innerHTML = results.map(result => `
            <div class="search-result-item" style="padding: 12px; border-bottom: 1px solid var(--gray-200); cursor: pointer;" onclick="window.location.href='${result.url}'">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div style="flex: 1;">
                        <h4 style="margin: 0 0 4px 0; color: var(--primary); font-size: 16px;">${result.id} - ${result.name}</h4>
                        <p style="margin: 0 0 4px 0; color: var(--gray-700); font-size: 14px;">${result.description || '暂无描述'}</p>
                        <div style="font-size: 12px; color: var(--gray-600);">
                            <span>${result.chapter_name}</span>
                            ${result.section_name ? ` > ${result.section_name}` : ''}
                        </div>
                    </div>
                    <i class="fas fa-chevron-right" style="color: var(--gray-400);"></i>
                </div>
            </div>
        `).join('');

        modal.style.display = 'flex';
    }

    showNoResults() {
        const modal = document.getElementById('searchModal');
        const resultsContainer = document.getElementById('searchResults');

        if (!modal || !resultsContainer) return;

        resultsContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: var(--gray-600);">
                <i class="fas fa-search" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                <p>未找到相关指标</p>
            </div>
        `;

        modal.style.display = 'flex';
    }

    showSearchError() {
        const modal = document.getElementById('searchModal');
        const resultsContainer = document.getElementById('searchResults');

        if (!modal || !resultsContainer) return;

        resultsContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: var(--error);">
                <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                <p>搜索时发生错误，请稍后重试</p>
            </div>
        `;

        modal.style.display = 'flex';
    }

    hideSearchResults() {
        const modal = document.getElementById('searchModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    async loadChapterNavigation() {
        try {
            const response = await fetch('/api/chapters/');
            const data = await response.json();

            if (data.results) {
                this.renderChapterNavigation(data.results);
            }
        } catch (error) {
            console.error('加载章节导航失败:', error);
        }
    }

    renderChapterNavigation(chapters) {
        const container = document.getElementById('chapterNavigation');
        if (!container) return;

        const chapterIcons = ['chart-bar', 'heartbeat', 'shield-alt', 'clipboard-list', 'user-nurse'];
        const currentPath = window.location.pathname;

        container.innerHTML = chapters.map((chapter, index) => {
            const isActive = currentPath.includes(`/chapter/${chapter.code}/`);
            const activeClass = isActive ? ' active' : '';

            return `
                <div class="nav-chapter-group">
                    <div class="nav-chapter-header">
                        <i class="fas fa-${chapterIcons[index] || 'book'} chapter-icon"></i>
                        <span class="chapter-title">第${chapter.code}章</span>
                    </div>
                    <a href="/chapter/${chapter.code}/" class="nav-item chapter-nav-item${activeClass}" data-chapter="${chapter.code}">
                        <span class="nav-text">${chapter.name}</span>
                        <span class="nav-badge">${chapter.indicator_count || 0}</span>
                    </a>
                </div>
            `;
        }).join('');
    }

    setupModalHandlers() {
        // 模态框点击外部关闭
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    modal.style.display = 'none';
                });
            }
        });
    }

    // 工具方法
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
        }
    }

    showToast(message, type = 'info') {
        // 创建toast通知
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-2);
            padding: 16px 20px;
            z-index: 1000;
            max-width: 300px;
            border-left: 4px solid var(--${type === 'error' ? 'error' : type === 'success' ? 'secondary' : 'primary'});
            animation: slideInRight 0.3s ease;
        `;

        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'}"
                   style="margin-right: 8px; color: var(--${type === 'error' ? 'error' : type === 'success' ? 'secondary' : 'primary'});"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
}

// 全局函数
function closeSearchModal() {
    const modal = document.getElementById('searchModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function showIndicatorDetail(indicatorId) {
    // 显示指标详情模态框
    fetch(`/ajax/indicators/${indicatorId}/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayIndicatorModal(data.data);
            } else {
                window.app.showToast('加载指标详情失败', 'error');
            }
        })
        .catch(error => {
            console.error('加载指标详情失败:', error);
            window.app.showToast('加载指标详情失败', 'error');
        });
}

function displayIndicatorModal(indicator) {
    // 创建指标详情模态框
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content modal-lg">
            <div class="modal-header">
                <h2 class="modal-title">${indicator.id} - ${indicator.name}</h2>
                <button class="modal-close-btn" onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="indicator-detail">
                    <div class="detail-section">
                        <h3>基本信息</h3>
                        <p><strong>指标类型:</strong> ${indicator.indicator_type === 'composite' ? '复合指标' : '简单指标'}</p>
                        <p><strong>描述:</strong> ${indicator.description || '暂无描述'}</p>
                        <p><strong>单位:</strong> ${indicator.unit || '暂无单位'}</p>
                        <p><strong>所属章节:</strong> ${indicator.chapter_name}</p>
                        <p><strong>所属小节:</strong> ${indicator.section_name || '未分配'}</p>
                        ${indicator.lead_department ? `<p><strong>牵头科室:</strong> ${indicator.lead_department}</p>` : ''}
                        ${indicator.data_source ? `<p><strong>数据来源:</strong> ${indicator.data_source}</p>` : ''}
                        ${indicator.logic_definition ? `<p><strong>逻辑定义:</strong> ${indicator.logic_definition}</p>` : ''}
                    </div>
                    ${indicator.indicator_type === 'composite' && (indicator.numerator || indicator.denominator) ? `
                    <div class="detail-section">
                        <h3>复合指标信息</h3>
                        ${indicator.numerator ? `<p><strong>分子:</strong> ${indicator.numerator}</p>` : ''}
                        ${indicator.denominator ? `<p><strong>分母:</strong> ${indicator.denominator}</p>` : ''}
                    </div>
                    ` : ''}
                    ${indicator.child_indicators && indicator.child_indicators.length > 0 ? `
                    <div class="detail-section">
                        <h3>子指标 (${indicator.child_indicators.length}个)</h3>
                        <div class="child-indicators-list">
                            ${indicator.child_indicators.map(child => `
                                <div class="child-indicator-item" style="padding: 8px; border: 1px solid var(--gray-200); border-radius: 4px; margin-bottom: 8px;">
                                    <strong>${child.id}</strong> - ${child.name}
                                    ${child.description ? `<br><small style="color: var(--gray-600);">${child.description}</small>` : ''}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }

    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        align-items: center;
        justify-content: center;
    }

    .modal-lg {
        max-width: 800px;
        width: 90%;
    }
`;
document.head.appendChild(style);

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new HospitalIndicatorApp();
});
