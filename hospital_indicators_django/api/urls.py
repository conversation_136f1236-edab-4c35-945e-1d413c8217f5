from django.urls import path
from . import views

app_name = 'api'

urlpatterns = [
    # REST API endpoints
    path('chapters/', views.ChapterListView.as_view(), name='chapters'),
    path('sections/', views.SectionListView.as_view(), name='sections'),
    path('indicators/', views.IndicatorListView.as_view(), name='indicators'),
    path('indicators/<str:indicator_id>/', views.IndicatorDetailView.as_view(), name='indicator_detail'),
    
    # Custom API endpoints
    path('chapters/<str:chapter_id>/sections/', views.chapter_sections, name='chapter_sections'),
    path('statistics/', views.statistics, name='statistics'),
    path('search/', views.search_indicators, name='search'),
]
