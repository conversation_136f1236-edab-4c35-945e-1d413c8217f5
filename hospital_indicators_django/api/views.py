from rest_framework import generics, filters
from rest_framework.decorators import api_view
from rest_framework.response import Response
from django.db.models import Q, Count
from indicators.models import Chapter, Section, Indicator
from .serializers import (
    ChapterSerializer, SectionSerializer,
    IndicatorSerializer, IndicatorDetailSerializer
)

class ChapterListView(generics.ListAPIView):
    """章节列表API"""
    serializer_class = ChapterSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['order_num', 'id']
    ordering = ['order_num', 'id']

    def get_queryset(self):
        return Chapter.objects.filter(is_active=True).annotate(
            section_count=Count('section', filter=Q(section__is_active=True)),
            indicator_count=Count('indicator', filter=Q(indicator__is_active=True))
        )

class SectionListView(generics.ListAPIView):
    """小节列表API"""
    serializer_class = SectionSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['order_num', 'id']
    ordering = ['order_num', 'id']

    def get_queryset(self):
        return Section.objects.filter(is_active=True).annotate(
            indicator_count=Count('indicator', filter=Q(indicator__is_active=True))
        ).select_related('chapter')

class IndicatorListView(generics.ListAPIView):
    """指标列表API"""
    serializer_class = IndicatorSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['id', 'name', 'description', 'lead_department']
    ordering_fields = ['order_num', 'id']
    ordering = ['order_num', 'id']

    def get_queryset(self):
        return Indicator.objects.filter(is_active=True).select_related('chapter', 'section')

class IndicatorDetailView(generics.RetrieveAPIView):
    """指标详情API"""
    serializer_class = IndicatorDetailSerializer
    lookup_field = 'id'
    lookup_url_kwarg = 'indicator_id'

    def get_queryset(self):
        return Indicator.objects.filter(is_active=True).select_related('chapter', 'section')

@api_view(['GET'])
def chapter_sections(request, chapter_id):
    """获取章节的小节"""
    try:
        chapter = Chapter.objects.get(id=chapter_id, is_active=True)
        sections = Section.objects.filter(
            chapter=chapter,
            is_active=True
        ).annotate(
            indicator_count=Count('indicator', filter=Q(indicator__is_active=True))
        ).order_by('order_num', 'id')

        serializer = SectionSerializer(sections, many=True)
        return Response({
            'success': True,
            'data': serializer.data
        })

    except Chapter.DoesNotExist:
        return Response({
            'success': False,
            'error': '章节不存在'
        }, status=404)

@api_view(['GET'])
def statistics(request):
    """获取统计信息"""
    stats = {
        'total_chapters': Chapter.objects.filter(is_active=True).count(),
        'total_sections': Section.objects.filter(is_active=True).count(),
        'total_indicators': Indicator.objects.filter(is_active=True).count(),
    }

    return Response({
        'success': True,
        'data': stats
    })

@api_view(['GET'])
def search_indicators(request):
    """搜索指标"""
    query = request.GET.get('q', '').strip()
    chapter_id = request.GET.get('chapter', '')
    limit = int(request.GET.get('limit', 10))

    if not query:
        return Response({'results': []})

    indicators_queryset = Indicator.objects.filter(is_active=True)

    if chapter_id:
        indicators_queryset = indicators_queryset.filter(chapter_id=chapter_id)

    indicators = indicators_queryset.filter(
        Q(name__icontains=query) |
        Q(id__icontains=query) |
        Q(description__icontains=query)
    ).select_related('chapter', 'section')[:limit]

    results = []
    for indicator in indicators:
        results.append({
            'id': indicator.id,
            'name': indicator.name,
            'description': indicator.description or '',
            'chapter_name': indicator.chapter.name,
            'section_name': indicator.section.name if indicator.section else '',
            'indicator_type': indicator.indicator_type,
            'url': f'/indicators/{indicator.id}/'
        })

    return Response({'results': results})
