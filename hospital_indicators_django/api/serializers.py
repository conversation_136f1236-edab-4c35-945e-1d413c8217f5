from rest_framework import serializers
from indicators.models import Chapter, Section, Indicator

class ChapterSerializer(serializers.ModelSerializer):
    section_count = serializers.IntegerField(read_only=True)
    indicator_count = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = Chapter
        fields = ['id', 'name', 'description', 'order_num', 'section_count', 'indicator_count']

class SectionSerializer(serializers.ModelSerializer):
    indicator_count = serializers.IntegerField(read_only=True)
    chapter_name = serializers.CharField(source='chapter.name', read_only=True)
    
    class Meta:
        model = Section
        fields = ['id', 'name', 'description', 'chapter', 'chapter_name', 'order_num', 'indicator_count']

class IndicatorSerializer(serializers.ModelSerializer):
    chapter_name = serializers.CharField(source='chapter.name', read_only=True)
    section_name = serializers.CharField(source='section.name', read_only=True)
    
    class Meta:
        model = Indicator
        fields = [
            'id', 'name', 'description', 'indicator_type', 'unit',
            'data_source', 'lead_department', 'logic_definition',
            'numerator', 'denominator', 'parent_id', 'order_num',
            'chapter', 'chapter_name', 'section', 'section_name'
        ]

class IndicatorDetailSerializer(serializers.ModelSerializer):
    chapter_name = serializers.CharField(source='chapter.name', read_only=True)
    section_name = serializers.CharField(source='section.name', read_only=True)
    child_indicators = serializers.SerializerMethodField()
    
    class Meta:
        model = Indicator
        fields = [
            'id', 'name', 'description', 'indicator_type', 'unit',
            'data_source', 'lead_department', 'logic_definition',
            'numerator', 'denominator', 'parent_id', 'order_num',
            'chapter', 'chapter_name', 'section', 'section_name',
            'child_indicators'
        ]
    
    def get_child_indicators(self, obj):
        child_indicators = Indicator.objects.filter(
            parent_id=obj.id,
            is_active=True
        ).order_by('order_num', 'id')
        return IndicatorSerializer(child_indicators, many=True).data
