# Generated by Django 5.2.1 on 2025-05-26 03:30

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Chapter',
            fields=[
                ('id', models.CharField(max_length=10, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='章节名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('order_num', models.IntegerField(default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '章节',
                'verbose_name_plural': '章节',
                'db_table': 'chapters',
                'ordering': ['order_num', 'id'],
            },
        ),
        migrations.CreateModel(
            name='Section',
            fields=[
                ('id', models.CharField(max_length=20, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='小节名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('order_num', models.IntegerField(default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('chapter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='indicators.chapter', verbose_name='所属章节')),
            ],
            options={
                'verbose_name': '小节',
                'verbose_name_plural': '小节',
                'db_table': 'sections',
                'ordering': ['order_num', 'id'],
            },
        ),
        migrations.CreateModel(
            name='Indicator',
            fields=[
                ('id', models.CharField(max_length=50, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=500, verbose_name='指标名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='指标描述')),
                ('indicator_type', models.CharField(choices=[('simple', '简单指标'), ('composite', '复合指标')], default='simple', max_length=20, verbose_name='指标类型')),
                ('unit', models.CharField(blank=True, max_length=50, null=True, verbose_name='单位')),
                ('data_source', models.CharField(blank=True, max_length=200, null=True, verbose_name='数据来源')),
                ('lead_department', models.CharField(blank=True, max_length=200, null=True, verbose_name='牵头科室')),
                ('logic_definition', models.TextField(blank=True, null=True, verbose_name='逻辑定义')),
                ('numerator', models.TextField(blank=True, null=True, verbose_name='分子')),
                ('denominator', models.TextField(blank=True, null=True, verbose_name='分母')),
                ('parent_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='父指标ID')),
                ('order_num', models.IntegerField(default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('chapter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='indicators.chapter', verbose_name='所属章节')),
                ('section', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='indicators.section', verbose_name='所属小节')),
            ],
            options={
                'verbose_name': '指标',
                'verbose_name_plural': '指标',
                'db_table': 'indicators',
                'ordering': ['order_num', 'id'],
            },
        ),
    ]
