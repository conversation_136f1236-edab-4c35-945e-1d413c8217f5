from django.core.management.base import BaseCommand
from django.db import connection
from indicators.models import Chapter, Section, Indicator

class Command(BaseCommand):
    help = '同步现有数据库数据到Django模型'
    
    def handle(self, *args, **options):
        self.stdout.write('开始同步数据...')
        
        # 由于使用现有数据库，Django模型会自动映射
        # 这里可以添加数据验证和清理逻辑
        
        chapters_count = Chapter.objects.count()
        sections_count = Section.objects.count()
        indicators_count = Indicator.objects.count()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'数据同步完成！\n'
                f'章节数: {chapters_count}\n'
                f'小节数: {sections_count}\n'
                f'指标数: {indicators_count}'
            )
        )
