from django.db import models
from django.utils import timezone

class Chapter(models.Model):
    """章节模型"""
    id = models.AutoField(primary_key=True)
    code = models.CharField(max_length=10, verbose_name="章节编码")
    name = models.CharField(max_length=100, verbose_name="章节名称")
    description = models.TextField(blank=True, null=True, verbose_name="描述")
    icon = models.CharField(max_length=50, blank=True, null=True, verbose_name="图标")
    color = models.CharField(max_length=20, default='#1a73e8', verbose_name="颜色")
    sort_order = models.IntegerField(default=0, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(verbose_name="创建时间")
    updated_at = models.DateTimeField(verbose_name="更新时间")

    class Meta:
        db_table = 'chapters'
        verbose_name = "章节"
        verbose_name_plural = "章节"
        ordering = ['sort_order', 'code']
        managed = False  # 不让Django管理这个表

    def __str__(self):
        return f"{self.code} - {self.name}"

class Section(models.Model):
    """小节模型"""
    id = models.AutoField(primary_key=True)
    chapter = models.ForeignKey(Chapter, on_delete=models.CASCADE, db_column='chapter_id', verbose_name="所属章节")
    code = models.CharField(max_length=10, verbose_name="小节编码")
    name = models.CharField(max_length=100, verbose_name="小节名称")
    description = models.TextField(blank=True, null=True, verbose_name="描述")
    sort_order = models.IntegerField(default=0, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(verbose_name="创建时间")
    updated_at = models.DateTimeField(verbose_name="更新时间")

    class Meta:
        db_table = 'sections'
        verbose_name = "小节"
        verbose_name_plural = "小节"
        ordering = ['sort_order', 'code']
        managed = False  # 不让Django管理这个表

    def __str__(self):
        return f"{self.code} - {self.name}"

class Indicator(models.Model):
    """指标模型"""
    INDICATOR_TYPE_CHOICES = [
        ('simple', '简单指标'),
        ('composite', '复合指标'),
    ]

    STATUS_CHOICES = [
        ('normal', '正常'),
        ('warning', '警告'),
        ('danger', '危险'),
        ('inactive', '未激活'),
    ]

    COLLECTION_FREQUENCY_CHOICES = [
        ('daily', '每日'),
        ('weekly', '每周'),
        ('monthly', '每月'),
        ('quarterly', '每季度'),
        ('yearly', '每年'),
        ('realtime', '实时'),
    ]

    COLLECTION_METHOD_CHOICES = [
        ('auto', '自动'),
        ('manual', '手动'),
        ('import', '导入'),
    ]

    id = models.CharField(max_length=20, primary_key=True)
    name = models.CharField(max_length=200, verbose_name="指标名称")
    description = models.TextField(blank=True, null=True, verbose_name="指标描述")
    calculation_method = models.TextField(blank=True, null=True, verbose_name="计算方法")
    target_value = models.CharField(max_length=20, blank=True, null=True, verbose_name="目标值")
    current_value = models.CharField(max_length=20, blank=True, null=True, verbose_name="当前值")
    completion_rate = models.CharField(max_length=10, blank=True, null=True, verbose_name="完成率")
    data_source = models.CharField(max_length=100, blank=True, null=True, verbose_name="数据来源")
    collection_frequency = models.CharField(max_length=20, choices=COLLECTION_FREQUENCY_CHOICES, blank=True, null=True, verbose_name="收集频率")
    collection_method = models.CharField(max_length=20, choices=COLLECTION_METHOD_CHOICES, blank=True, null=True, verbose_name="收集方法")
    sql_query = models.TextField(blank=True, null=True, verbose_name="SQL查询")
    parent_id = models.CharField(max_length=20, blank=True, null=True, verbose_name="父指标ID")
    chapter = models.ForeignKey(Chapter, on_delete=models.SET_NULL, null=True, blank=True, db_column='chapter_id', verbose_name="所属章节")
    section = models.ForeignKey(Section, on_delete=models.SET_NULL, null=True, blank=True, db_column='section_id', verbose_name="所属小节")
    category = models.CharField(max_length=50, blank=True, null=True, verbose_name="分类")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='normal', verbose_name="状态")
    sort_order = models.IntegerField(default=0, verbose_name="排序")
    notes = models.TextField(blank=True, null=True, verbose_name="备注")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(verbose_name="创建时间")
    updated_at = models.DateTimeField(verbose_name="更新时间")

    # 扩展字段
    unit = models.CharField(max_length=50, blank=True, null=True, verbose_name="单位")
    lead_department = models.CharField(max_length=100, blank=True, null=True, verbose_name="牵头科室")
    logic_definition = models.TextField(blank=True, null=True, verbose_name="逻辑定义")
    indicator_type = models.CharField(max_length=20, default='composite', verbose_name="指标类型")
    indicator_definition = models.TextField(blank=True, null=True, verbose_name="指标定义")
    calculation_formula = models.TextField(blank=True, null=True, verbose_name="计算公式")
    numerator_description = models.TextField(blank=True, null=True, verbose_name="分子描述")
    denominator_description = models.TextField(blank=True, null=True, verbose_name="分母描述")
    statistical_scope = models.TextField(blank=True, null=True, verbose_name="统计范围")
    data_sources = models.TextField(blank=True, null=True, verbose_name="数据来源详细")
    collection_frequency_detail = models.TextField(blank=True, null=True, verbose_name="收集频率详细")
    reference_value = models.TextField(blank=True, null=True, verbose_name="参考值")
    monitoring_analysis = models.TextField(blank=True, null=True, verbose_name="监测分析")
    analysis_dimensions = models.TextField(blank=True, null=True, verbose_name="分析维度")
    quality_requirements = models.TextField(blank=True, null=True, verbose_name="质量要求")
    notes_remarks = models.TextField(blank=True, null=True, verbose_name="备注说明")
    related_policies = models.TextField(blank=True, null=True, verbose_name="相关政策")
    improvement_suggestions = models.TextField(blank=True, null=True, verbose_name="改进建议")
    reference_range = models.TextField(blank=True, null=True, verbose_name="参考范围")

    # 分子分母详细信息
    numerator_unit = models.CharField(max_length=50, blank=True, null=True, verbose_name="分子单位")
    numerator_department = models.CharField(max_length=100, blank=True, null=True, verbose_name="分子科室")
    numerator_source = models.CharField(max_length=200, blank=True, null=True, verbose_name="分子来源")
    numerator_logic = models.TextField(blank=True, null=True, verbose_name="分子逻辑")
    denominator_unit = models.CharField(max_length=50, blank=True, null=True, verbose_name="分母单位")
    denominator_department = models.CharField(max_length=100, blank=True, null=True, verbose_name="分母科室")
    denominator_source = models.CharField(max_length=200, blank=True, null=True, verbose_name="分母来源")
    denominator_logic = models.TextField(blank=True, null=True, verbose_name="分母逻辑")

    class Meta:
        db_table = 'indicators'
        verbose_name = "指标"
        verbose_name_plural = "指标"
        ordering = ['sort_order', 'id']
        managed = False  # 不让Django管理这个表

    def __str__(self):
        return f"{self.id} - {self.name}"

    @property
    def chapter_name(self):
        return self.chapter.name if self.chapter else None

    @property
    def section_name(self):
        return self.section.name if self.section else None
