from django.db import models
from django.utils import timezone

class Chapter(models.Model):
    """章节模型"""
    id = models.CharField(max_length=10, primary_key=True)
    name = models.CharField(max_length=200, verbose_name="章节名称")
    description = models.TextField(blank=True, null=True, verbose_name="描述")
    order_num = models.IntegerField(default=0, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        db_table = 'chapters'
        verbose_name = "章节"
        verbose_name_plural = "章节"
        ordering = ['order_num', 'id']
    
    def __str__(self):
        return f"{self.id} - {self.name}"

class Section(models.Model):
    """小节模型"""
    id = models.CharField(max_length=20, primary_key=True)
    chapter = models.ForeignKey(Chapter, on_delete=models.CASCADE, verbose_name="所属章节")
    name = models.CharField(max_length=200, verbose_name="小节名称")
    description = models.TextField(blank=True, null=True, verbose_name="描述")
    order_num = models.IntegerField(default=0, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        db_table = 'sections'
        verbose_name = "小节"
        verbose_name_plural = "小节"
        ordering = ['order_num', 'id']
    
    def __str__(self):
        return f"{self.id} - {self.name}"

class Indicator(models.Model):
    """指标模型"""
    INDICATOR_TYPE_CHOICES = [
        ('simple', '简单指标'),
        ('composite', '复合指标'),
    ]
    
    id = models.CharField(max_length=50, primary_key=True)
    chapter = models.ForeignKey(Chapter, on_delete=models.CASCADE, verbose_name="所属章节")
    section = models.ForeignKey(Section, on_delete=models.CASCADE, null=True, blank=True, verbose_name="所属小节")
    name = models.CharField(max_length=500, verbose_name="指标名称")
    description = models.TextField(blank=True, null=True, verbose_name="指标描述")
    indicator_type = models.CharField(max_length=20, choices=INDICATOR_TYPE_CHOICES, default='simple', verbose_name="指标类型")
    unit = models.CharField(max_length=50, blank=True, null=True, verbose_name="单位")
    data_source = models.CharField(max_length=200, blank=True, null=True, verbose_name="数据来源")
    lead_department = models.CharField(max_length=200, blank=True, null=True, verbose_name="牵头科室")
    logic_definition = models.TextField(blank=True, null=True, verbose_name="逻辑定义")
    numerator = models.TextField(blank=True, null=True, verbose_name="分子")
    denominator = models.TextField(blank=True, null=True, verbose_name="分母")
    parent_id = models.CharField(max_length=50, blank=True, null=True, verbose_name="父指标ID")
    order_num = models.IntegerField(default=0, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        db_table = 'indicators'
        verbose_name = "指标"
        verbose_name_plural = "指标"
        ordering = ['order_num', 'id']
    
    def __str__(self):
        return f"{self.id} - {self.name}"
    
    @property
    def chapter_name(self):
        return self.chapter.name if self.chapter else None
    
    @property
    def section_name(self):
        return self.section.name if self.section else None
