from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count
from .models import Chapter, Section, Indicator

def index(request):
    """首页视图"""
    # 获取统计数据
    stats = {
        'total_chapters': Chapter.objects.filter(is_active=True).count(),
        'total_sections': Section.objects.filter(is_active=True).count(),
        'total_indicators': Indicator.objects.filter(is_active=True).count(),
    }
    
    # 获取章节数据
    chapters = Chapter.objects.filter(is_active=True).annotate(
        section_count=Count('section', filter=Q(section__is_active=True)),
        indicator_count=Count('indicator', filter=Q(indicator__is_active=True))
    ).order_by('order_num', 'id')
    
    context = {
        'stats': stats,
        'chapters': chapters,
        'page_title': '医院等级评审指标说明手册',
        'page_subtitle': '基于Google Material Design风格的现代化指标管理系统'
    }
    
    return render(request, 'indicators/index.html', context)

def chapter_detail(request, chapter_id):
    """章节详情视图"""
    chapter = get_object_or_404(Chapter, id=chapter_id, is_active=True)
    
    # 获取小节
    sections = Section.objects.filter(
        chapter=chapter, 
        is_active=True
    ).annotate(
        indicator_count=Count('indicator', filter=Q(indicator__is_active=True))
    ).order_by('order_num', 'id')
    
    # 获取指标（支持搜索和分页）
    search_query = request.GET.get('search', '')
    section_filter = request.GET.get('section', '')
    
    indicators_queryset = Indicator.objects.filter(
        chapter=chapter,
        is_active=True
    ).select_related('chapter', 'section')
    
    if search_query:
        indicators_queryset = indicators_queryset.filter(
            Q(name__icontains=search_query) |
            Q(id__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    if section_filter:
        indicators_queryset = indicators_queryset.filter(section_id=section_filter)
    
    indicators_queryset = indicators_queryset.order_by('order_num', 'id')
    
    # 分页
    paginator = Paginator(indicators_queryset, 20)
    page_number = request.GET.get('page')
    indicators = paginator.get_page(page_number)
    
    context = {
        'chapter': chapter,
        'sections': sections,
        'indicators': indicators,
        'search_query': search_query,
        'section_filter': section_filter,
        'page_title': f'{chapter.name}',
        'page_subtitle': chapter.description or ''
    }
    
    return render(request, 'indicators/chapter_detail.html', context)

def indicator_detail(request, indicator_id):
    """指标详情视图"""
    indicator = get_object_or_404(Indicator, id=indicator_id, is_active=True)
    
    # 获取子指标（如果有）
    child_indicators = Indicator.objects.filter(
        parent_id=indicator_id,
        is_active=True
    ).order_by('order_num', 'id')
    
    context = {
        'indicator': indicator,
        'child_indicators': child_indicators,
        'page_title': f'{indicator.id} - {indicator.name}',
        'page_subtitle': indicator.description or ''
    }
    
    return render(request, 'indicators/indicator_detail.html', context)

def search_indicators(request):
    """搜索指标API"""
    query = request.GET.get('q', '').strip()
    chapter_id = request.GET.get('chapter', '')
    
    if not query:
        return JsonResponse({'results': []})
    
    indicators_queryset = Indicator.objects.filter(is_active=True)
    
    if chapter_id:
        indicators_queryset = indicators_queryset.filter(chapter_id=chapter_id)
    
    indicators = indicators_queryset.filter(
        Q(name__icontains=query) |
        Q(id__icontains=query) |
        Q(description__icontains=query)
    ).select_related('chapter', 'section')[:10]
    
    results = []
    for indicator in indicators:
        results.append({
            'id': indicator.id,
            'name': indicator.name,
            'description': indicator.description or '',
            'chapter_name': indicator.chapter.name,
            'section_name': indicator.section.name if indicator.section else '',
            'url': f'/indicators/{indicator.id}/'
        })
    
    return JsonResponse({'results': results})

def get_chapter_sections(request, chapter_id):
    """获取章节的小节API"""
    try:
        chapter = Chapter.objects.get(id=chapter_id, is_active=True)
        sections = Section.objects.filter(
            chapter=chapter,
            is_active=True
        ).annotate(
            indicator_count=Count('indicator', filter=Q(indicator__is_active=True))
        ).order_by('order_num', 'id')
        
        sections_data = []
        for section in sections:
            sections_data.append({
                'id': section.id,
                'name': section.name,
                'description': section.description or '',
                'indicator_count': section.indicator_count
            })
        
        return JsonResponse({
            'success': True,
            'data': sections_data
        })
    
    except Chapter.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': '章节不存在'
        })

def ajax_indicator_detail(request, indicator_id):
    """AJAX获取指标详情"""
    try:
        indicator = Indicator.objects.select_related('chapter', 'section').get(
            id=indicator_id, 
            is_active=True
        )
        
        # 获取子指标
        child_indicators = Indicator.objects.filter(
            parent_id=indicator_id,
            is_active=True
        ).order_by('order_num', 'id')
        
        child_data = []
        for child in child_indicators:
            child_data.append({
                'id': child.id,
                'name': child.name,
                'description': child.description or '',
                'unit': child.unit or '',
                'indicator_type': child.indicator_type
            })
        
        data = {
            'id': indicator.id,
            'name': indicator.name,
            'description': indicator.description or '',
            'indicator_type': indicator.indicator_type,
            'unit': indicator.unit or '',
            'data_source': indicator.data_source or '',
            'lead_department': indicator.lead_department or '',
            'logic_definition': indicator.logic_definition or '',
            'numerator': indicator.numerator or '',
            'denominator': indicator.denominator or '',
            'chapter_name': indicator.chapter.name,
            'section_name': indicator.section.name if indicator.section else '',
            'child_indicators': child_data
        }
        
        return JsonResponse({
            'success': True,
            'data': data
        })
    
    except Indicator.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': '指标不存在'
        })
