from django.contrib import admin
from .models import Chapter, Section, Indicator

@admin.register(Chapter)
class ChapterAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'order_num', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('id', 'name', 'description')
    ordering = ('order_num', 'id')
    list_editable = ('order_num', 'is_active')

@admin.register(Section)
class SectionAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'chapter', 'order_num', 'is_active', 'created_at')
    list_filter = ('chapter', 'is_active', 'created_at')
    search_fields = ('id', 'name', 'description')
    ordering = ('chapter', 'order_num', 'id')
    list_editable = ('order_num', 'is_active')

@admin.register(Indicator)
class IndicatorAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'chapter', 'section', 'indicator_type', 'is_active', 'created_at')
    list_filter = ('chapter', 'section', 'indicator_type', 'is_active', 'created_at')
    search_fields = ('id', 'name', 'description', 'lead_department')
    ordering = ('chapter', 'section', 'order_num', 'id')
    list_editable = ('indicator_type', 'is_active')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'name', 'description', 'indicator_type')
        }),
        ('分类信息', {
            'fields': ('chapter', 'section', 'parent_id')
        }),
        ('详细信息', {
            'fields': ('unit', 'data_source', 'lead_department', 'logic_definition')
        }),
        ('复合指标信息', {
            'fields': ('numerator', 'denominator'),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('order_num', 'is_active'),
            'classes': ('collapse',)
        }),
    )

# 自定义管理站点标题
admin.site.site_header = "医院等级评审指标管理系统"
admin.site.site_title = "指标管理"
admin.site.index_title = "欢迎使用医院指标管理系统"
