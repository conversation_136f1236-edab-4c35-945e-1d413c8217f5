{% extends 'base.html' %}
{% load static %}

{% block title %}{{ chapter.name }} - 医院等级评审指标说明手册{% endblock %}

{% block content %}
<div class="chapter-detail-container">
    <!-- 右侧面板 - 重新设计为左右布局 -->
    <div class="main-content">
        <!-- 章节统计卡片 -->
        <div class="chapter-stats-card">
            <div class="stats-header">
                <div class="chapter-info">
                    <h2>{{ chapter.name }}</h2>
                    <p class="chapter-description">{{ chapter.description|default:"" }}</p>
                </div>
                <div class="chapter-id-badge">第{{ chapter.code }}章</div>
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{ sections|length }}</div>
                    <div class="stat-label">小节数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalIndicators">{{ chapter.indicator_count|default:0 }}</div>
                    <div class="stat-label">指标数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="simpleIndicators">-</div>
                    <div class="stat-label">简单指标</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="compositeIndicators">-</div>
                    <div class="stat-label">复合指标</div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 - 左右分栏 -->
        <div class="content-panels">
            <!-- 左侧面板 - 章节导航（小节列表） -->
            <div class="navigation-panel">
                <div class="panel-header">
                    <h3>
                        <i class="fas fa-list-ul"></i>
                        章节导航
                    </h3>
                </div>

                <div class="section-navigation">
                    <div id="sectionList" class="section-list">
                        {% for section in sections %}
                        <div class="section-item" data-section-id="{{ section.id }}" onclick="loadSectionIndicators('{{ section.id }}', '{{ section.code }}', '{{ section.name }}')">
                            <div class="section-header">
                                <h4>{{ section.code }}</h4>
                                <span class="indicator-count">{{ section.indicator_count }}</span>
                            </div>
                            <p class="section-name">{{ section.name }}</p>
                        </div>
                        {% empty %}
                        <div class="no-sections">
                            <i class="fas fa-inbox"></i>
                            <p>暂无小节数据</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- 右侧面板 - 指标显示区域 -->
            <div class="indicators-panel">
                <div class="indicators-header">
                    <div class="header-left">
                        <h3 id="indicatorsTitle">
                            <i class="fas fa-chart-line"></i>
                            章节指标
                        </h3>
                        <span id="indicatorsSubtitle" class="subtitle">请选择左侧小节查看对应指标</span>
                    </div>
                    <div class="header-right">
                        <div class="view-controls">
                            <button id="cardViewBtn" class="view-btn active" onclick="switchView('card')" title="卡片视图">
                                <i class="fas fa-th-large"></i>
                            </button>
                            <button id="listViewBtn" class="view-btn" onclick="switchView('list')" title="列表视图">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                        <div class="indicators-controls">
                            <input type="text" id="indicatorSearch" placeholder="搜索指标..." class="search-input-small">
                            <select id="indicatorTypeFilter" class="filter-select">
                                <option value="">所有类型</option>
                                <option value="simple">简单指标</option>
                                <option value="composite">复合指标</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div id="indicatorsList" class="indicators-list card-view">
                    <!-- 默认显示提示信息 -->
                    <div class="no-selection">
                        <i class="fas fa-mouse-pointer"></i>
                        <h4>选择小节查看指标</h4>
                        <p>请点击左侧的小节来查看对应的指标内容</p>
                    </div>
                </div>

                <!-- 分页容器 -->
                <div id="paginationContainer" class="pagination-container" style="display: none;">
                    <div class="pagination" id="paginationContent">
                        <!-- 分页内容将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载状态 -->
<div id="loadingIndicator" class="loading-indicator" style="display: none;">
    <div class="loading-spinner"></div>
    <p>加载中...</p>
</div>
{% endblock %}

{% block extra_css %}
<style>
.chapter-detail-container {
    padding: 0;
    height: calc(100vh - 120px);
}

.main-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
}

/* 章节统计卡片 */
.chapter-stats-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    padding: 24px;
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.chapter-info h2 {
    margin: 0 0 8px 0;
    color: var(--gray-900);
    font-size: 24px;
}

.chapter-description {
    color: var(--gray-600);
    font-size: 14px;
    margin: 0;
}

.chapter-id-badge {
    background: var(--primary);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    white-space: nowrap;
}

/* 主要内容面板 */
.content-panels {
    display: flex;
    gap: 20px;
    flex: 1;
    min-height: 0;
}

/* 左侧导航面板 */
.navigation-panel {
    width: 320px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    display: flex;
    flex-direction: column;
}

.navigation-panel .panel-header {
    padding: 20px;
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.navigation-panel .panel-header h3 {
    margin: 0;
    color: var(--gray-800);
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-navigation {
    flex: 1;
    overflow-y: auto;
}

.section-list {
    padding: 8px 0;
}

.section-item {
    padding: 16px 20px;
    border-bottom: 1px solid var(--gray-100);
    cursor: pointer;
    transition: var(--transition);
}

.section-item:hover {
    background-color: var(--gray-50);
}

.section-item.active {
    background-color: var(--primary-light);
    border-left: 4px solid var(--primary);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.section-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary);
}

.indicator-count {
    font-size: 12px;
    color: var(--gray-600);
    background: var(--gray-100);
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.section-name {
    margin: 0;
    font-size: 13px;
    color: var(--gray-700);
    line-height: 1.4;
}

/* 右侧指标面板 */
.indicators-panel {
    flex: 1;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.indicators-header {
    padding: 20px;
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
}

.header-left h3 {
    margin: 0 0 4px 0;
    color: var(--gray-800);
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.subtitle {
    font-size: 13px;
    color: var(--gray-600);
    font-weight: normal;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* 视图切换控件 */
.view-controls {
    display: flex;
    background: var(--white);
    border-radius: 6px;
    border: 1px solid var(--gray-300);
    overflow: hidden;
}

.view-btn {
    padding: 8px 12px;
    border: none;
    background: transparent;
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.view-btn:hover {
    background: var(--gray-100);
}

.view-btn.active {
    background: var(--primary);
    color: var(--white);
}

.view-btn + .view-btn {
    border-left: 1px solid var(--gray-300);
}

.indicators-controls {
    display: flex;
    gap: 12px;
}

.search-input-small {
    width: 200px;
    height: 36px;
    border: 1px solid var(--gray-300);
    border-radius: 18px;
    padding: 0 16px;
    font-size: 14px;
}

.filter-select {
    height: 36px;
    border: 1px solid var(--gray-300);
    border-radius: 18px;
    padding: 0 12px;
    font-size: 14px;
    background: var(--white);
}

/* 指标列表 */
.indicators-list {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

/* 无选择状态 */
.no-selection {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: var(--gray-500);
}

.no-selection i {
    font-size: 48px;
    margin-bottom: 16px;
    color: var(--gray-400);
}

.no-selection h4 {
    margin: 0 0 8px 0;
    color: var(--gray-600);
}

.no-selection p {
    margin: 0;
    font-size: 14px;
}

/* 统计网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 28px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 13px;
    color: var(--gray-600);
    font-weight: 500;
}

/* 卡片视图样式 */
.indicators-list.card-view .indicator-card {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 16px;
    margin-bottom: 16px;
    transition: var(--transition);
}

.indicators-list.card-view .indicator-card:hover {
    box-shadow: var(--shadow-2);
    border-color: var(--primary-light);
}

/* 列表视图样式 */
.indicators-list.list-view {
    padding: 0;
}

.indicators-list.list-view .indicator-card {
    border: none;
    border-bottom: 1px solid var(--gray-200);
    border-radius: 0;
    padding: 16px 20px;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 16px;
}

.indicators-list.list-view .indicator-card:hover {
    background-color: var(--gray-50);
    box-shadow: none;
}

.indicators-list.list-view .indicator-header {
    flex-shrink: 0;
    width: 120px;
    margin-bottom: 0;
}

.indicators-list.list-view .indicator-content {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 16px;
}

.indicators-list.list-view .indicator-title {
    flex: 1;
    margin: 0;
    font-size: 14px;
}

.indicators-list.list-view .indicator-meta {
    flex-shrink: 0;
    display: flex;
    gap: 12px;
    margin-bottom: 0;
}

.indicators-list.list-view .indicator-actions {
    flex-shrink: 0;
}

/* 指标卡片通用样式 */
.indicator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.indicator-id {
    font-weight: 600;
    color: var(--primary);
    font-size: 14px;
}

.indicator-type-badge {
    font-size: 11px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.indicator-type-badge.simple {
    background: var(--secondary);
    color: var(--white);
}

.indicator-type-badge.composite {
    background: var(--warning);
    color: var(--white);
}

.indicator-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: var(--gray-900);
    line-height: 1.4;
}

.indicator-description {
    margin: 0 0 12px 0;
    color: var(--gray-600);
    font-size: 14px;
    line-height: 1.4;
}

.indicator-meta {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.meta-item {
    font-size: 12px;
    color: var(--gray-600);
    display: flex;
    align-items: center;
    gap: 4px;
}

.indicator-actions {
    display: flex;
    gap: 8px;
}

/* 分页 */
.pagination-container {
    padding: 16px 20px;
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.page-link {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 4px;
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition);
}

.page-link:hover {
    background: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

.page-info {
    font-size: 14px;
    color: var(--gray-600);
    margin: 0 16px;
}

/* 加载状态 */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-2);
    text-align: center;
    z-index: 1000;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .indicators-controls {
        flex-direction: column;
        gap: 8px;
    }

    .search-input-small {
        width: 150px;
    }
}

@media (max-width: 768px) {
    .content-panels {
        flex-direction: column;
    }

    .navigation-panel {
        width: 100%;
        max-height: 300px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .indicators-header {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .header-right {
        flex-direction: column;
        gap: 12px;
    }

    .indicators-controls {
        flex-direction: column;
    }

    .search-input-small {
        width: 100%;
    }

    .indicators-list.list-view .indicator-card {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .indicators-list.list-view .indicator-header {
        width: 100%;
    }
}
    box-shadow: var(--shadow-1);
    padding: 20px;
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.chapter-id-badge {
    background: var(--primary);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 500;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: 600;
    color: var(--gray-900);
}

.stat-label {
    font-size: 12px;
    color: var(--gray-600);
    margin-top: 4px;
}

.indicators-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.indicators-header {
    padding: 20px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.indicators-controls {
    display: flex;
    gap: 12px;
}

.search-input-small {
    width: 200px;
    height: 36px;
    border: 1px solid var(--gray-300);
    border-radius: 18px;
    padding: 0 16px;
    font-size: 14px;
}

.filter-select {
    height: 36px;
    border: 1px solid var(--gray-300);
    border-radius: 18px;
    padding: 0 12px;
    font-size: 14px;
}

.indicators-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.indicator-card {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 16px;
    margin-bottom: 12px;
    transition: var(--transition);
}

.indicator-card:hover {
    box-shadow: var(--shadow-2);
}

.indicator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.indicator-id {
    font-weight: 600;
    color: var(--primary);
}

.indicator-type-badge {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.indicator-type-badge.simple {
    background: var(--secondary);
    color: var(--white);
}

.indicator-type-badge.composite {
    background: var(--warning);
    color: var(--white);
}

.indicator-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: var(--gray-900);
}

.indicator-description {
    margin: 0 0 12px 0;
    color: var(--gray-600);
    font-size: 14px;
    line-height: 1.4;
}

.indicator-meta {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
}

.meta-item {
    font-size: 12px;
    color: var(--gray-600);
    display: flex;
    align-items: center;
    gap: 4px;
}

.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-2);
    text-align: center;
    z-index: 1000;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .chapter-detail-container {
        flex-direction: column;
        height: auto;
    }

    .left-panel {
        width: 100%;
        max-height: 300px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .indicators-controls {
        flex-direction: column;
        gap: 8px;
    }

    .search-input-small {
        width: 100%;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 章节详情页面的JavaScript
let currentChapterId = '{{ chapter.code }}';
let currentSectionId = null;
let currentViewMode = 'card'; // 'card' 或 'list'

// 加载小节指标
async function loadSectionIndicators(sectionId, sectionCode, sectionName) {
    currentSectionId = sectionId;

    // 更新小节选中状态
    document.querySelectorAll('.section-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-section-id="${sectionId}"]`).classList.add('active');

    // 更新标题
    document.getElementById('indicatorsTitle').innerHTML = `
        <i class="fas fa-chart-line"></i>
        ${sectionCode} - ${sectionName}
    `;
    document.getElementById('indicatorsSubtitle').textContent = `正在加载指标数据...`;

    // 显示加载状态
    document.getElementById('loadingIndicator').style.display = 'block';

    try {
        const response = await fetch(`/api/indicators/?section=${sectionId}`);
        const data = await response.json();

        if (data.results) {
            renderIndicators(data.results);
            updateStatistics(data.results);
            document.getElementById('indicatorsSubtitle').textContent = `共 ${data.results.length} 个指标`;
        }
    } catch (error) {
        console.error('加载小节指标失败:', error);
        document.getElementById('indicatorsSubtitle').textContent = '加载失败';
    } finally {
        document.getElementById('loadingIndicator').style.display = 'none';
    }
}

// 视图切换功能
function switchView(viewMode) {
    currentViewMode = viewMode;

    // 更新按钮状态
    document.querySelectorAll('.view-btn').forEach(btn => btn.classList.remove('active'));
    document.getElementById(viewMode + 'ViewBtn').classList.add('active');

    // 更新容器类名
    const container = document.getElementById('indicatorsList');
    container.className = `indicators-list ${viewMode}-view`;

    // 如果有数据，重新渲染
    if (currentSectionId) {
        // 重新获取当前显示的指标数据并渲染
        const indicators = Array.from(container.querySelectorAll('.indicator-card')).map(card => ({
            id: card.dataset.indicatorId,
            name: card.querySelector('.indicator-title').textContent,
            description: card.querySelector('.indicator-description').textContent,
            indicator_type: card.dataset.type,
            lead_department: card.querySelector('.meta-item:first-child').textContent.trim(),
            unit: card.querySelector('.meta-item:last-child').textContent.trim()
        }));

        if (indicators.length > 0) {
            renderIndicators(indicators);
        }
    }
}

// 渲染指标列表
function renderIndicators(indicators) {
    const container = document.getElementById('indicatorsList');

    if (indicators.length === 0) {
        container.innerHTML = `
            <div class="no-indicators">
                <i class="fas fa-chart-line"></i>
                <p>该小节暂无指标数据</p>
            </div>
        `;
        return;
    }

    if (currentViewMode === 'card') {
        renderCardView(indicators, container);
    } else {
        renderListView(indicators, container);
    }
}

// 卡片视图渲染
function renderCardView(indicators, container) {
    container.innerHTML = indicators.map(indicator => `
        <div class="indicator-card" data-indicator-id="${indicator.id}" data-type="${indicator.indicator_type}">
            <div class="indicator-header">
                <div class="indicator-id">${indicator.id}</div>
                <div class="indicator-type-badge ${indicator.indicator_type}">
                    ${indicator.indicator_type === 'composite' ? '复合指标' : '简单指标'}
                </div>
            </div>
            <h4 class="indicator-title">${indicator.name}</h4>
            <p class="indicator-description">${(indicator.description || '暂无描述').substring(0, 100)}${(indicator.description || '').length > 100 ? '...' : ''}</p>
            <div class="indicator-meta">
                <span class="meta-item">
                    <i class="fas fa-building"></i>
                    ${indicator.lead_department || '未指定'}
                </span>
                <span class="meta-item">
                    <i class="fas fa-ruler"></i>
                    ${indicator.unit || '无单位'}
                </span>
            </div>
            <div class="indicator-actions">
                <button onclick="showIndicatorDetail('${indicator.id}')" class="btn btn-primary btn-sm">
                    <i class="fas fa-eye"></i> 查看详情
                </button>
            </div>
        </div>
    `).join('');
}

// 列表视图渲染
function renderListView(indicators, container) {
    container.innerHTML = indicators.map(indicator => `
        <div class="indicator-card" data-indicator-id="${indicator.id}" data-type="${indicator.indicator_type}">
            <div class="indicator-header">
                <div class="indicator-id">${indicator.id}</div>
                <div class="indicator-type-badge ${indicator.indicator_type}">
                    ${indicator.indicator_type === 'composite' ? '复合指标' : '简单指标'}
                </div>
            </div>
            <div class="indicator-content">
                <h4 class="indicator-title">${indicator.name}</h4>
                <div class="indicator-meta">
                    <span class="meta-item">
                        <i class="fas fa-building"></i>
                        ${indicator.lead_department || '未指定'}
                    </span>
                    <span class="meta-item">
                        <i class="fas fa-ruler"></i>
                        ${indicator.unit || '无单位'}
                    </span>
                </div>
            </div>
            <div class="indicator-actions">
                <button onclick="showIndicatorDetail('${indicator.id}')" class="btn btn-primary btn-sm">
                    <i class="fas fa-eye"></i> 查看详情
                </button>
            </div>
        </div>
    `).join('');
}

// 更新指标标题
function updateIndicatorsTitle(sectionId) {
    const sectionElement = document.querySelector(`[data-section-id="${sectionId}"]`);
    const sectionName = sectionElement.querySelector('.section-name').textContent;
    document.getElementById('indicatorsTitle').textContent = `${sectionId} - ${sectionName}`;
}

// 更新统计信息
function updateStatistics(indicators) {
    const simpleCount = indicators.filter(i => i.indicator_type === 'simple').length;
    const compositeCount = indicators.filter(i => i.indicator_type === 'composite').length;

    document.getElementById('totalIndicators').textContent = indicators.length;
    document.getElementById('simpleIndicators').textContent = simpleCount;
    document.getElementById('compositeIndicators').textContent = compositeCount;
}

// 搜索功能
document.getElementById('indicatorSearch').addEventListener('input', function(e) {
    const query = e.target.value.toLowerCase();
    filterIndicators();
});

// 类型筛选
document.getElementById('indicatorTypeFilter').addEventListener('change', function(e) {
    filterIndicators();
});

// 筛选指标
function filterIndicators() {
    const query = document.getElementById('indicatorSearch').value.toLowerCase();
    const typeFilter = document.getElementById('indicatorTypeFilter').value;

    document.querySelectorAll('.indicator-card').forEach(card => {
        const title = card.querySelector('.indicator-title').textContent.toLowerCase();
        const id = card.querySelector('.indicator-id').textContent.toLowerCase();
        const type = card.dataset.type;

        const matchesSearch = title.includes(query) || id.includes(query);
        const matchesType = !typeFilter || type === typeFilter;

        card.style.display = matchesSearch && matchesType ? 'block' : 'none';
    });
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 如果有小节，默认选中第一个
    const firstSection = document.querySelector('.section-item');
    if (firstSection) {
        const firstSectionId = firstSection.dataset.sectionId;
        loadSectionIndicators(firstSectionId);
    }
});
</script>
{% endblock %}
