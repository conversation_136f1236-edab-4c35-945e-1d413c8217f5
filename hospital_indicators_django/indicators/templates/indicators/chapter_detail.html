{% extends 'base.html' %}
{% load static %}

{% block title %}{{ chapter.name }} - 医院等级评审指标说明手册{% endblock %}

{% block content %}
<div class="chapter-detail-container">
    <!-- 左侧面板 - 小节导航 -->
    <div class="left-panel">
        <div class="panel-header">
            <h2>{{ chapter.name }}</h2>
            <p class="chapter-description">{{ chapter.description|default:"" }}</p>
        </div>
        
        <div class="section-navigation">
            <h3>小节列表</h3>
            <div id="sectionList" class="section-list">
                {% for section in sections %}
                <div class="section-item" data-section-id="{{ section.id }}" onclick="loadSectionIndicators('{{ section.id }}')">
                    <div class="section-header">
                        <h4>{{ section.id }}</h4>
                        <span class="indicator-count">{{ section.indicator_count }} 个指标</span>
                    </div>
                    <p class="section-name">{{ section.name }}</p>
                </div>
                {% empty %}
                <div class="no-sections">
                    <i class="fas fa-inbox"></i>
                    <p>暂无小节数据</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- 右侧面板 - 章节统计和指标显示 -->
    <div class="right-panel">
        <!-- 章节统计卡片 -->
        <div class="chapter-stats-card">
            <div class="stats-header">
                <h3>{{ chapter.name }} 统计</h3>
                <div class="chapter-id-badge">{{ chapter.id }}</div>
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{ sections|length }}</div>
                    <div class="stat-label">小节数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalIndicators">{{ chapter.indicator_count|default:0 }}</div>
                    <div class="stat-label">指标数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="simpleIndicators">-</div>
                    <div class="stat-label">简单指标</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="compositeIndicators">-</div>
                    <div class="stat-label">复合指标</div>
                </div>
            </div>
        </div>

        <!-- 指标显示区域 -->
        <div class="indicators-container">
            <div class="indicators-header">
                <h3 id="indicatorsTitle">章节指标</h3>
                <div class="indicators-controls">
                    <input type="text" id="indicatorSearch" placeholder="搜索指标..." class="search-input-small">
                    <select id="indicatorTypeFilter" class="filter-select">
                        <option value="">所有类型</option>
                        <option value="simple">简单指标</option>
                        <option value="composite">复合指标</option>
                    </select>
                </div>
            </div>
            
            <div id="indicatorsList" class="indicators-list">
                <!-- 默认显示章节所有指标 -->
                {% for indicator in indicators.object_list %}
                <div class="indicator-card" data-indicator-id="{{ indicator.id }}" data-type="{{ indicator.indicator_type }}">
                    <div class="indicator-header">
                        <div class="indicator-id">{{ indicator.id }}</div>
                        <div class="indicator-type-badge {{ indicator.indicator_type }}">
                            {% if indicator.indicator_type == 'composite' %}复合指标{% else %}简单指标{% endif %}
                        </div>
                    </div>
                    <h4 class="indicator-title">{{ indicator.name }}</h4>
                    <p class="indicator-description">{{ indicator.description|default:"暂无描述"|truncatechars:100 }}</p>
                    <div class="indicator-meta">
                        <span class="meta-item">
                            <i class="fas fa-building"></i>
                            {{ indicator.lead_department|default:"未指定" }}
                        </span>
                        <span class="meta-item">
                            <i class="fas fa-ruler"></i>
                            {{ indicator.unit|default:"无单位" }}
                        </span>
                    </div>
                    <div class="indicator-actions">
                        <button onclick="showIndicatorDetail('{{ indicator.id }}')" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye"></i> 查看详情
                        </button>
                    </div>
                </div>
                {% empty %}
                <div class="no-indicators">
                    <i class="fas fa-chart-line"></i>
                    <p>暂无指标数据</p>
                </div>
                {% endfor %}
            </div>

            <!-- 分页 -->
            {% if indicators.has_other_pages %}
            <div class="pagination-container">
                <div class="pagination">
                    {% if indicators.has_previous %}
                        <a href="?page={{ indicators.previous_page_number }}" class="page-link">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% endif %}
                    
                    <span class="page-info">
                        第 {{ indicators.number }} 页，共 {{ indicators.paginator.num_pages }} 页
                    </span>
                    
                    {% if indicators.has_next %}
                        <a href="?page={{ indicators.next_page_number }}" class="page-link">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 加载状态 -->
<div id="loadingIndicator" class="loading-indicator" style="display: none;">
    <div class="loading-spinner"></div>
    <p>加载中...</p>
</div>
{% endblock %}

{% block extra_css %}
<style>
.chapter-detail-container {
    display: flex;
    gap: 24px;
    height: calc(100vh - 120px);
}

.left-panel {
    width: 320px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    overflow-y: auto;
}

.right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.panel-header {
    padding: 20px;
    border-bottom: 1px solid var(--gray-200);
}

.panel-header h2 {
    margin-bottom: 8px;
    color: var(--gray-900);
}

.chapter-description {
    color: var(--gray-600);
    font-size: 14px;
}

.section-navigation h3 {
    padding: 16px 20px 12px;
    margin: 0;
    font-size: 16px;
    color: var(--gray-800);
    border-bottom: 1px solid var(--gray-200);
}

.section-item {
    padding: 16px 20px;
    border-bottom: 1px solid var(--gray-100);
    cursor: pointer;
    transition: var(--transition);
}

.section-item:hover {
    background-color: var(--gray-50);
}

.section-item.active {
    background-color: var(--primary-light);
    border-left: 4px solid var(--primary);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.section-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary);
}

.indicator-count {
    font-size: 12px;
    color: var(--gray-600);
    background: var(--gray-100);
    padding: 2px 8px;
    border-radius: 12px;
}

.section-name {
    margin: 0;
    font-size: 13px;
    color: var(--gray-700);
    line-height: 1.4;
}

.chapter-stats-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    padding: 20px;
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.chapter-id-badge {
    background: var(--primary);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 500;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: 600;
    color: var(--gray-900);
}

.stat-label {
    font-size: 12px;
    color: var(--gray-600);
    margin-top: 4px;
}

.indicators-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.indicators-header {
    padding: 20px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.indicators-controls {
    display: flex;
    gap: 12px;
}

.search-input-small {
    width: 200px;
    height: 36px;
    border: 1px solid var(--gray-300);
    border-radius: 18px;
    padding: 0 16px;
    font-size: 14px;
}

.filter-select {
    height: 36px;
    border: 1px solid var(--gray-300);
    border-radius: 18px;
    padding: 0 12px;
    font-size: 14px;
}

.indicators-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.indicator-card {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 16px;
    margin-bottom: 12px;
    transition: var(--transition);
}

.indicator-card:hover {
    box-shadow: var(--shadow-2);
}

.indicator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.indicator-id {
    font-weight: 600;
    color: var(--primary);
}

.indicator-type-badge {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.indicator-type-badge.simple {
    background: var(--secondary);
    color: var(--white);
}

.indicator-type-badge.composite {
    background: var(--warning);
    color: var(--white);
}

.indicator-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: var(--gray-900);
}

.indicator-description {
    margin: 0 0 12px 0;
    color: var(--gray-600);
    font-size: 14px;
    line-height: 1.4;
}

.indicator-meta {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
}

.meta-item {
    font-size: 12px;
    color: var(--gray-600);
    display: flex;
    align-items: center;
    gap: 4px;
}

.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-2);
    text-align: center;
    z-index: 1000;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .chapter-detail-container {
        flex-direction: column;
        height: auto;
    }
    
    .left-panel {
        width: 100%;
        max-height: 300px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .indicators-controls {
        flex-direction: column;
        gap: 8px;
    }
    
    .search-input-small {
        width: 100%;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 章节详情页面的JavaScript
let currentChapterId = '{{ chapter.id }}';
let currentSectionId = null;

// 加载小节指标
async function loadSectionIndicators(sectionId) {
    currentSectionId = sectionId;
    
    // 更新小节选中状态
    document.querySelectorAll('.section-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-section-id="${sectionId}"]`).classList.add('active');
    
    // 显示加载状态
    document.getElementById('loadingIndicator').style.display = 'block';
    
    try {
        const response = await fetch(`/api/indicators/?section=${sectionId}`);
        const data = await response.json();
        
        if (data.results) {
            renderIndicators(data.results);
            updateIndicatorsTitle(sectionId);
            updateStatistics(data.results);
        }
    } catch (error) {
        console.error('加载小节指标失败:', error);
    } finally {
        document.getElementById('loadingIndicator').style.display = 'none';
    }
}

// 渲染指标列表
function renderIndicators(indicators) {
    const container = document.getElementById('indicatorsList');
    
    if (indicators.length === 0) {
        container.innerHTML = `
            <div class="no-indicators">
                <i class="fas fa-chart-line"></i>
                <p>该小节暂无指标数据</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = indicators.map(indicator => `
        <div class="indicator-card" data-indicator-id="${indicator.id}" data-type="${indicator.indicator_type}">
            <div class="indicator-header">
                <div class="indicator-id">${indicator.id}</div>
                <div class="indicator-type-badge ${indicator.indicator_type}">
                    ${indicator.indicator_type === 'composite' ? '复合指标' : '简单指标'}
                </div>
            </div>
            <h4 class="indicator-title">${indicator.name}</h4>
            <p class="indicator-description">${indicator.description || '暂无描述'}</p>
            <div class="indicator-meta">
                <span class="meta-item">
                    <i class="fas fa-building"></i>
                    ${indicator.lead_department || '未指定'}
                </span>
                <span class="meta-item">
                    <i class="fas fa-ruler"></i>
                    ${indicator.unit || '无单位'}
                </span>
            </div>
            <div class="indicator-actions">
                <button onclick="showIndicatorDetail('${indicator.id}')" class="btn btn-primary btn-sm">
                    <i class="fas fa-eye"></i> 查看详情
                </button>
            </div>
        </div>
    `).join('');
}

// 更新指标标题
function updateIndicatorsTitle(sectionId) {
    const sectionElement = document.querySelector(`[data-section-id="${sectionId}"]`);
    const sectionName = sectionElement.querySelector('.section-name').textContent;
    document.getElementById('indicatorsTitle').textContent = `${sectionId} - ${sectionName}`;
}

// 更新统计信息
function updateStatistics(indicators) {
    const simpleCount = indicators.filter(i => i.indicator_type === 'simple').length;
    const compositeCount = indicators.filter(i => i.indicator_type === 'composite').length;
    
    document.getElementById('totalIndicators').textContent = indicators.length;
    document.getElementById('simpleIndicators').textContent = simpleCount;
    document.getElementById('compositeIndicators').textContent = compositeCount;
}

// 搜索功能
document.getElementById('indicatorSearch').addEventListener('input', function(e) {
    const query = e.target.value.toLowerCase();
    filterIndicators();
});

// 类型筛选
document.getElementById('indicatorTypeFilter').addEventListener('change', function(e) {
    filterIndicators();
});

// 筛选指标
function filterIndicators() {
    const query = document.getElementById('indicatorSearch').value.toLowerCase();
    const typeFilter = document.getElementById('indicatorTypeFilter').value;
    
    document.querySelectorAll('.indicator-card').forEach(card => {
        const title = card.querySelector('.indicator-title').textContent.toLowerCase();
        const id = card.querySelector('.indicator-id').textContent.toLowerCase();
        const type = card.dataset.type;
        
        const matchesSearch = title.includes(query) || id.includes(query);
        const matchesType = !typeFilter || type === typeFilter;
        
        card.style.display = matchesSearch && matchesType ? 'block' : 'none';
    });
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 如果有小节，默认选中第一个
    const firstSection = document.querySelector('.section-item');
    if (firstSection) {
        const firstSectionId = firstSection.dataset.sectionId;
        loadSectionIndicators(firstSectionId);
    }
});
</script>
{% endblock %}
