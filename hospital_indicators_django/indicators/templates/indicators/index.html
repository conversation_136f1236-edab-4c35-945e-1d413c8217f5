{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">{{ page_title }}</h1>
    <p class="page-subtitle">{{ page_subtitle }}</p>
</div>

<!-- 统计卡片 -->
<div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 32px;">
    <div class="stat-card" style="background: var(--white); padding: 20px; border-radius: var(--border-radius); box-shadow: var(--shadow-1);">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <div style="font-size: 24px; font-weight: 500; color: var(--gray-900);">{{ stats.total_chapters }}</div>
                <div style="font-size: 14px; color: var(--gray-700);">章节</div>
            </div>
            <i class="fas fa-book" style="font-size: 24px; color: var(--primary);"></i>
        </div>
    </div>
    
    <div class="stat-card" style="background: var(--white); padding: 20px; border-radius: var(--border-radius); box-shadow: var(--shadow-1);">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <div style="font-size: 24px; font-weight: 500; color: var(--gray-900);">{{ stats.total_sections }}</div>
                <div style="font-size: 14px; color: var(--gray-700);">小节</div>
            </div>
            <i class="fas fa-list" style="font-size: 24px; color: var(--secondary);"></i>
        </div>
    </div>
    
    <div class="stat-card" style="background: var(--white); padding: 20px; border-radius: var(--border-radius); box-shadow: var(--shadow-1);">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <div style="font-size: 24px; font-weight: 500; color: var(--gray-900);">{{ stats.total_indicators }}</div>
                <div style="font-size: 14px; color: var(--gray-700);">指标</div>
            </div>
            <i class="fas fa-chart-line" style="font-size: 24px; color: var(--warning);"></i>
        </div>
    </div>
</div>

<!-- 章节卡片 -->
<div class="card-grid">
    {% for chapter in chapters %}
    <div class="card">
        <div class="card-body">
            <h3 class="card-title">
                <i class="fas fa-{% cycle 'chart-bar' 'heartbeat' 'check-circle' 'clipboard-list' 'microscope' %} card-icon"></i>
                {{ chapter.name }}
            </h3>
            <div class="card-content">
                {{ chapter.description|default:"暂无描述" }}
            </div>
            <div class="card-meta">
                <div class="card-id">
                    <span class="id-badge">{{ chapter.id }}</span>
                </div>
                <div style="display: flex; gap: 16px; font-size: 12px; color: var(--gray-600);">
                    <span>{{ chapter.section_count }} 个小节</span>
                    <span>{{ chapter.indicator_count }} 个指标</span>
                </div>
            </div>
            <div style="margin-top: 16px;">
                <a href="{% url 'indicators:chapter_detail' chapter.id %}" class="btn btn-primary">
                    <i class="fas fa-eye btn-icon"></i>
                    查看详情
                </a>
            </div>
        </div>
    </div>
    {% empty %}
    <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: var(--gray-600);">
        <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
        <p>暂无章节数据</p>
    </div>
    {% endfor %}
</div>

<!-- 快速操作 -->
<div style="margin-top: 40px; padding: 24px; background: var(--white); border-radius: var(--border-radius); box-shadow: var(--shadow-1);">
    <h3 style="margin-bottom: 16px; color: var(--gray-900);">快速操作</h3>
    <div style="display: flex; gap: 12px; flex-wrap: wrap;">
        <a href="/admin/" class="btn btn-outline">
            <i class="fas fa-cog btn-icon"></i>
            管理后台
        </a>
        <a href="{% url 'api:chapters' %}" class="btn btn-outline">
            <i class="fas fa-code btn-icon"></i>
            API文档
        </a>
        <button class="btn btn-outline" onclick="window.print()">
            <i class="fas fa-print btn-icon"></i>
            打印页面
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 首页特定的JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 统计卡片动画
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // 章节卡片悬停效果
    const chapterCards = document.querySelectorAll('.card');
    chapterCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
{% endblock %}
