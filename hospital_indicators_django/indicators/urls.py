from django.urls import path
from . import views

app_name = 'indicators'

urlpatterns = [
    # 前端页面
    path('', views.index, name='index'),
    path('chapter/<str:chapter_id>/', views.chapter_detail, name='chapter_detail'),
    path('indicators/<str:indicator_id>/', views.indicator_detail, name='indicator_detail'),
    
    # AJAX API
    path('ajax/search/', views.search_indicators, name='search_indicators'),
    path('ajax/chapters/<str:chapter_id>/sections/', views.get_chapter_sections, name='chapter_sections'),
    path('ajax/indicators/<str:indicator_id>/', views.ajax_indicator_detail, name='ajax_indicator_detail'),
]
