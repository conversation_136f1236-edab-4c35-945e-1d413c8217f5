#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查第一章Excel文件中的分子分母数据
"""

import pandas as pd
import sqlite3

def check_chapter1_excel():
    """
    检查第一章Excel文件的数据
    """
    try:
        print("=" * 80)
        print("🔍 检查第一章Excel文件数据")
        print("=" * 80)
        
        # 读取Excel文件
        df = pd.read_excel("chapter1.xlsx", sheet_name=0)
        
        print(f"数据维度: {df.shape[0]}行 × {df.shape[1]}列")
        print(f"列名: {df.columns.tolist()}")
        
        # 查找指标1.1.3的数据
        print(f"\n=== 查找指标1.1.3的数据 ===")
        
        # 检查是否有1.1.3相关的行
        mask_113 = df.apply(lambda row: '1.1.3' in str(row).replace('nan', ''), axis=1)
        rows_113 = df[mask_113]
        
        print(f"包含'1.1.3'的行数: {len(rows_113)}")
        
        if len(rows_113) > 0:
            print("\n找到的相关行:")
            for idx, row in rows_113.iterrows():
                print(f"\n第{idx+1}行:")
                for col in df.columns:
                    value = row[col]
                    if pd.notna(value) and str(value).strip():
                        print(f"  {col}: {value}")
        
        # 检查所有指标的分子分母数据
        print(f"\n=== 检查所有指标的分子分母数据 ===")
        
        # 统计有分子分母的指标
        numerator_count = df['分子'].notna().sum() if '分子' in df.columns else 0
        denominator_count = df['分母'].notna().sum() if '分母' in df.columns else 0
        
        print(f"有分子的行数: {numerator_count}")
        print(f"有分母的行数: {denominator_count}")
        
        # 显示前几个有分子分母的指标
        if '分子' in df.columns and '分母' in df.columns:
            has_components = df[(df['分子'].notna()) | (df['分母'].notna())]
            print(f"\n前5个有分子分母的指标:")
            for idx, row in has_components.head().iterrows():
                indicator_id = ''
                for col in ['三级指标编号', '二级指标编号', '一级指标编号']:
                    if col in df.columns and pd.notna(row[col]):
                        indicator_id = str(row[col]).strip()
                        break
                
                numerator = row.get('分子', '')
                denominator = row.get('分母', '')
                
                print(f"  {indicator_id}: 分子={numerator}, 分母={denominator}")
        
        # 检查1.1.3指标是否应该有分子分母
        print(f"\n=== 检查1.1.3指标详情 ===")
        
        # 查找所有包含1.1.3的行
        for idx, row in df.iterrows():
            row_str = ' '.join([str(val) for val in row.values if pd.notna(val)])
            if '1.1.3' in row_str:
                print(f"\n第{idx+1}行包含1.1.3:")
                for col in df.columns:
                    value = row[col]
                    if pd.notna(value) and str(value).strip():
                        print(f"  {col}: {value}")
                
                # 检查这行是否有分子分母
                numerator = row.get('分子', '')
                denominator = row.get('分母', '')
                if pd.notna(numerator) or pd.notna(denominator):
                    print(f"  >>> 分子: {numerator}")
                    print(f"  >>> 分母: {denominator}")
                else:
                    print(f"  >>> 此行没有分子分母数据")
        
    except Exception as e:
        print(f"检查Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()

def check_database_1_1_3():
    """
    检查数据库中1.1.3指标的数据
    """
    try:
        print(f"\n" + "=" * 80)
        print("🗃️ 检查数据库中1.1.3指标的数据")
        print("=" * 80)
        
        conn = sqlite3.connect("DATABASE-HOSPITAL/hospital_indicator_system.db")
        cursor = conn.cursor()
        
        # 查询1.1.3指标
        cursor.execute("""
            SELECT id, name, description, parent_id, category
            FROM indicators 
            WHERE id = '1.1.3'
        """)
        
        indicator = cursor.fetchone()
        if indicator:
            print(f"指标信息:")
            print(f"  ID: {indicator[0]}")
            print(f"  名称: {indicator[1]}")
            print(f"  描述: {indicator[2]}")
            print(f"  父指标: {indicator[3]}")
            print(f"  类别: {indicator[4]}")
        else:
            print("❌ 数据库中未找到1.1.3指标")
            return
        
        # 查询分子分母
        cursor.execute("""
            SELECT component_type, name, unit, lead_department, data_source, logic_definition
            FROM indicator_components 
            WHERE indicator_id = '1.1.3'
        """)
        
        components = cursor.fetchall()
        if components:
            print(f"\n分子分母信息:")
            for comp in components:
                print(f"  {comp[0]}:")
                print(f"    名称: {comp[1]}")
                print(f"    单位: {comp[2]}")
                print(f"    牵头科室: {comp[3]}")
                print(f"    数据来源: {comp[4]}")
                print(f"    逻辑定义: {comp[5]}")
        else:
            print("❌ 数据库中未找到1.1.3指标的分子分母数据")
        
        # 查询第一章所有有分子分母的指标
        cursor.execute("""
            SELECT DISTINCT ic.indicator_id, i.name, COUNT(ic.id) as component_count
            FROM indicator_components ic
            JOIN indicators i ON ic.indicator_id = i.id
            WHERE ic.indicator_id LIKE '1.%'
            GROUP BY ic.indicator_id, i.name
            ORDER BY ic.indicator_id
        """)
        
        indicators_with_components = cursor.fetchall()
        print(f"\n第一章有分子分母的指标 ({len(indicators_with_components)}个):")
        for ind in indicators_with_components:
            print(f"  {ind[0]} - {ind[1]} ({ind[2]}个组成部分)")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    # 检查Excel文件
    check_chapter1_excel()
    
    # 检查数据库
    check_database_1_1_3()

if __name__ == "__main__":
    main()
