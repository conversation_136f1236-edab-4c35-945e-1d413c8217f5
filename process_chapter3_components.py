#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理chapter3.xlsx文件中的分子、分母等详细信息
"""

import pandas as pd
import sqlite3
import sys
import os

def process_chapter3_components():
    """
    处理第三章指标的分子、分母等组成部分
    """
    file_path = "chapter3.xlsx"
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    try:
        print("=" * 80)
        print("🏥 医院等级评审指标管理系统 - 第三章分子分母数据处理")
        print("=" * 80)
        
        print("正在读取Excel文件...")
        df = pd.read_excel(file_path, sheet_name=0)
        
        print(f"数据维度: {df.shape[0]}行 × {df.shape[1]}列")
        print(f"列名: {list(df.columns)}")
        
        components = []
        
        print("\n开始处理第三章分子、分母数据...")
        
        for index, row in df.iterrows():
            try:
                # 获取指标ID（优先使用最底层的指标）
                indicator_id = None
                
                # 检查三级指标
                if '三级指标编号' in df.columns:
                    level3_id = row.get('三级指标编号', '')
                    if pd.notna(level3_id) and str(level3_id).strip():
                        indicator_id = str(level3_id).strip()
                
                # 如果没有三级指标，检查二级指标
                if not indicator_id and '二级指标编号' in df.columns:
                    level2_id = row.get('二级指标编号', '')
                    if pd.notna(level2_id) and str(level2_id).strip():
                        indicator_id = str(level2_id).strip()
                
                # 如果没有二级指标，使用一级指标
                if not indicator_id and '一级指标编号' in df.columns:
                    level1_id = row.get('一级指标编号', '')
                    if pd.notna(level1_id) and str(level1_id).strip():
                        indicator_id = str(level1_id).strip()
                
                if not indicator_id or not indicator_id.startswith('3.'):
                    continue
                
                # 处理分子信息
                numerator = row.get('分子', '')
                if pd.notna(numerator) and str(numerator).strip():
                    numerator_unit = row.get('分子单位', '')
                    numerator_dept = row.get('分子牵头科室', '')
                    numerator_source = row.get('分子数据来源', '')
                    numerator_logic = row.get('分子逻辑定义', '')
                    
                    components.append({
                        'indicator_id': indicator_id,
                        'component_type': 'numerator',
                        'name': str(numerator).strip(),
                        'unit': str(numerator_unit).strip() if pd.notna(numerator_unit) else '',
                        'lead_department': str(numerator_dept).strip() if pd.notna(numerator_dept) else '',
                        'data_source': str(numerator_source).strip() if pd.notna(numerator_source) else '',
                        'logic_definition': str(numerator_logic).strip() if pd.notna(numerator_logic) else '',
                        'row': index + 1
                    })
                    print(f"分子: {indicator_id} - {str(numerator)[:50]}...")
                
                # 处理分母信息
                denominator = row.get('分母', '')
                if pd.notna(denominator) and str(denominator).strip():
                    denominator_unit = row.get('分母单位', '')
                    denominator_dept = row.get('分母牵头科室', '')
                    denominator_source = row.get('分母数据来源', '')
                    denominator_logic = row.get('分母逻辑定义', '')
                    
                    components.append({
                        'indicator_id': indicator_id,
                        'component_type': 'denominator',
                        'name': str(denominator).strip(),
                        'unit': str(denominator_unit).strip() if pd.notna(denominator_unit) else '',
                        'lead_department': str(denominator_dept).strip() if pd.notna(denominator_dept) else '',
                        'data_source': str(denominator_source).strip() if pd.notna(denominator_source) else '',
                        'logic_definition': str(denominator_logic).strip() if pd.notna(denominator_logic) else '',
                        'row': index + 1
                    })
                    print(f"分母: {indicator_id} - {str(denominator)[:50]}...")
                
            except Exception as e:
                print(f"处理第{index+1}行时出错: {e}")
                continue
        
        # 去重处理
        unique_components = []
        seen = set()
        for comp in components:
            key = (comp['indicator_id'], comp['component_type'])
            if key not in seen:
                seen.add(key)
                unique_components.append(comp)
            else:
                print(f"发现重复组成部分: {comp['indicator_id']} {comp['component_type']}，已跳过")
        
        print(f"\n=== 处理结果 ===")
        print(f"提取到 {len(unique_components)} 个唯一组成部分")
        
        # 统计分子分母数量
        numerator_count = len([c for c in unique_components if c['component_type'] == 'numerator'])
        denominator_count = len([c for c in unique_components if c['component_type'] == 'denominator'])
        
        print(f"分子: {numerator_count}个")
        print(f"分母: {denominator_count}个")
        
        # 插入数据库
        if unique_components:
            insert_components_to_db(unique_components, db_path)
        else:
            print("未找到有效的组成部分数据")
            
    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()

def insert_components_to_db(components, db_path):
    """
    将组成部分数据插入数据库
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 清除现有的第三章组成部分数据
        cursor.execute("DELETE FROM indicator_components WHERE indicator_id LIKE '3.%'")
        print("已清除现有的第三章组成部分数据")
        
        # 插入新的组成部分数据
        for i, component in enumerate(components):
            cursor.execute("""
                INSERT INTO indicator_components 
                (indicator_id, component_type, name, definition, unit, data_source, lead_department, logic_definition, sort_order, is_active, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            """, (
                component['indicator_id'],
                component['component_type'],
                component['name'],
                component['name'],  # definition 使用 name
                component['unit'],
                component['data_source'],
                component['lead_department'],
                component['logic_definition'],
                i + 1
            ))
            
            comp_type = "分子" if component['component_type'] == 'numerator' else "分母"
            print(f"插入{comp_type}: {component['indicator_id']} - {component['name'][:50]}...")
        
        conn.commit()
        print(f"成功插入 {len(components)} 个组成部分")
        
        # 显示插入结果统计
        cursor.execute("SELECT component_type, COUNT(*) FROM indicator_components WHERE indicator_id LIKE '3.%' GROUP BY component_type")
        results = cursor.fetchall()
        
        print(f"\n数据库中第三章的组成部分统计:")
        for comp_type, count in results:
            type_name = "分子" if comp_type == 'numerator' else "分母"
            print(f"  {type_name}: {count}个")
        
    except Exception as e:
        print(f"插入组成部分数据时出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if conn:
            conn.close()

def show_chapter3_summary():
    """
    显示第三章数据汇总
    """
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"\n" + "=" * 80)
        print("📊 第三章数据汇总")
        print("=" * 80)
        
        # 统计第三章的指标数量
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN parent_id IS NULL THEN '一级指标'
                    WHEN id IN (SELECT DISTINCT parent_id FROM indicators WHERE parent_id IS NOT NULL AND id LIKE '3.%') THEN '二级指标'
                    ELSE '三级指标'
                END as level_type,
                COUNT(*) as count
            FROM indicators 
            WHERE id LIKE '3.%'
            GROUP BY level_type
        """)
        
        level_counts = cursor.fetchall()
        
        print(f"📋 指标层级统计:")
        total_indicators = 0
        for level_type, count in level_counts:
            print(f"  {level_type}: {count}个")
            total_indicators += count
        print(f"  总计: {total_indicators}个")
        
        # 统计小节
        cursor.execute("""
            SELECT s.name, COUNT(i.id) as indicator_count
            FROM sections s
            LEFT JOIN indicators i ON s.id = i.section_id
            WHERE s.chapter_id = (SELECT id FROM chapters WHERE code = '3')
            GROUP BY s.id, s.name
            ORDER BY s.sort_order
        """)
        
        section_stats = cursor.fetchall()
        
        print(f"\n📖 小节统计:")
        for section_name, indicator_count in section_stats:
            print(f"  {section_name}: {indicator_count}个指标")
        
        # 统计组成部分
        cursor.execute("""
            SELECT component_type, COUNT(*) as count
            FROM indicator_components
            WHERE indicator_id LIKE '3.%'
            GROUP BY component_type
        """)
        
        component_stats = cursor.fetchall()
        
        print(f"\n🧮 组成部分统计:")
        for comp_type, count in component_stats:
            type_name = "分子" if comp_type == 'numerator' else "分母"
            print(f"  {type_name}: {count}个")
        
    except Exception as e:
        print(f"查询汇总数据时出错: {e}")
    finally:
        if conn:
            conn.close()

def main():
    # 检查文件是否存在
    if not os.path.exists("chapter3.xlsx"):
        print("错误：文件 chapter3.xlsx 不存在")
        return
    
    if not os.path.exists("DATABASE-HOSPITAL/hospital_indicator_system.db"):
        print("错误：数据库文件不存在")
        return
    
    # 处理组成部分数据
    process_chapter3_components()
    
    # 显示汇总信息
    show_chapter3_summary()
    
    print("\n" + "=" * 80)
    print("✅ 第三章分子分母数据处理完成")
    print("=" * 80)

if __name__ == "__main__":
    main()
