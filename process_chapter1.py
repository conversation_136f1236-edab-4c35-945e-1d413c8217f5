#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理chapter1.xlsx文件，提取第一章的小节和指标数据并导入数据库
"""

import pandas as pd
import sqlite3
import re
import sys
import os

def analyze_excel_structure(file_path):
    """
    分析Excel文件的结构
    """
    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {file_path}")
        
        # 尝试读取所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"工作表列表: {excel_file.sheet_names}")
        
        # 读取第一个工作表
        df = pd.read_excel(file_path, sheet_name=0)
        
        print(f"数据维度: {df.shape[0]}行 × {df.shape[1]}列")
        print(f"列名: {df.columns.tolist()}")
        
        # 显示前几行数据
        print("\n前10行数据:")
        for i, row in df.head(10).iterrows():
            print(f"第{i+1}行: {row.tolist()}")
        
        return df
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def extract_section_info(text):
    """
    从文本中提取小节信息
    例如：'1.1 床位配置' -> ('1.1', '床位配置')
    """
    if pd.isna(text) or not isinstance(text, str):
        return None, None
    
    # 匹配 "数字.数字 名称" 的模式
    pattern = r'^(\d+\.\d+)\s*(.+)$'
    match = re.match(pattern, text.strip())
    
    if match:
        code = match.group(1)
        name = match.group(2).strip()
        return code, name
    
    return None, None

def extract_indicator_info(text):
    """
    从文本中提取指标信息
    例如：'1.1.1 实际开放床位数' -> ('1.1.1', '实际开放床位数')
    """
    if pd.isna(text) or not isinstance(text, str):
        return None, None
    
    # 匹配 "数字.数字.数字 名称" 的模式
    pattern = r'^(\d+\.\d+\.\d+)\s*(.+)$'
    match = re.match(pattern, text.strip())
    
    if match:
        code = match.group(1)
        name = match.group(2).strip()
        return code, name
    
    return None, None

def process_chapter1_data(df):
    """
    处理第一章数据，提取小节和指标信息
    """
    sections = []
    indicators = []
    
    print("\n开始处理第一章数据...")
    
    for index, row in df.iterrows():
        # 检查每一列，寻找小节和指标信息
        for col_idx, cell_value in enumerate(row):
            if pd.isna(cell_value):
                continue
                
            cell_str = str(cell_value).strip()
            
            # 尝试提取小节信息 (1.x格式)
            section_code, section_name = extract_section_info(cell_str)
            if section_code and section_name:
                # 检查是否是第一章的小节
                if section_code.startswith('1.') and len(section_code.split('.')) == 2:
                    sections.append({
                        'code': section_code,
                        'name': section_name,
                        'row': index + 1,
                        'col': col_idx + 1,
                        'original_text': cell_str
                    })
                    print(f"发现小节: {section_code} - {section_name}")
            
            # 尝试提取指标信息 (1.x.x格式)
            indicator_code, indicator_name = extract_indicator_info(cell_str)
            if indicator_code and indicator_name:
                # 检查是否是第一章的指标
                if indicator_code.startswith('1.') and len(indicator_code.split('.')) == 3:
                    # 确定所属小节
                    section_code = '.'.join(indicator_code.split('.')[:2])
                    
                    indicators.append({
                        'id': indicator_code,
                        'name': indicator_name,
                        'section_code': section_code,
                        'row': index + 1,
                        'col': col_idx + 1,
                        'original_text': cell_str
                    })
                    print(f"发现指标: {indicator_code} - {indicator_name}")
    
    return sections, indicators

def remove_duplicates(data_list, key_field):
    """
    去除重复数据
    """
    seen = set()
    unique_data = []
    
    for item in data_list:
        key = item[key_field]
        if key not in seen:
            seen.add(key)
            unique_data.append(item)
        else:
            print(f"发现重复{key_field}: {key}，已跳过")
    
    return unique_data

def insert_sections_to_db(sections, db_path):
    """
    将小节数据插入数据库
    """
    if not sections:
        print("没有小节数据需要插入")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取第一章的ID
        cursor.execute("SELECT id FROM chapters WHERE code = '1'")
        chapter_result = cursor.fetchone()
        if not chapter_result:
            print("错误：数据库中未找到第一章数据")
            return
        
        chapter_id = chapter_result[0]
        
        # 插入小节数据
        for i, section in enumerate(sections):
            cursor.execute("""
                INSERT OR REPLACE INTO sections 
                (chapter_id, code, name, description, sort_order, is_active, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            """, (
                chapter_id,
                section['code'],
                section['name'],
                f"第一章小节：{section['name']}",
                i + 1
            ))
            print(f"插入小节: {section['code']} - {section['name']}")
        
        conn.commit()
        print(f"成功插入 {len(sections)} 个小节")
        
    except Exception as e:
        print(f"插入小节数据时出错: {e}")
    finally:
        if conn:
            conn.close()

def insert_indicators_to_db(indicators, db_path):
    """
    将指标数据插入数据库
    """
    if not indicators:
        print("没有指标数据需要插入")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取第一章的ID
        cursor.execute("SELECT id FROM chapters WHERE code = '1'")
        chapter_result = cursor.fetchone()
        if not chapter_result:
            print("错误：数据库中未找到第一章数据")
            return
        
        chapter_id = chapter_result[0]
        
        # 为每个指标找到对应的小节ID
        for i, indicator in enumerate(indicators):
            # 查找对应的小节ID
            cursor.execute("SELECT id FROM sections WHERE code = ? AND chapter_id = ?", 
                         (indicator['section_code'], chapter_id))
            section_result = cursor.fetchone()
            section_id = section_result[0] if section_result else None
            
            if not section_id:
                print(f"警告：未找到小节 {indicator['section_code']}，指标 {indicator['id']} 将不关联小节")
            
            # 插入指标数据
            cursor.execute("""
                INSERT OR REPLACE INTO indicators 
                (id, name, description, chapter_id, section_id, category, sort_order, is_active, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            """, (
                indicator['id'],
                indicator['name'],
                f"第一章指标：{indicator['name']}",
                chapter_id,
                section_id,
                '资源配置与运行数据',
                i + 1
            ))
            print(f"插入指标: {indicator['id']} - {indicator['name']}")
        
        conn.commit()
        print(f"成功插入 {len(indicators)} 个指标")
        
    except Exception as e:
        print(f"插入指标数据时出错: {e}")
    finally:
        if conn:
            conn.close()

def main():
    file_path = "chapter1.xlsx"
    db_path = "DATABASE-HOSPITAL/hospital_indicator_system.db"
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件 {file_path} 不存在")
        return
    
    if not os.path.exists(db_path):
        print(f"错误：数据库文件 {db_path} 不存在")
        return
    
    # 分析Excel文件结构
    df = analyze_excel_structure(file_path)
    if df is None:
        return
    
    # 处理第一章数据
    sections, indicators = process_chapter1_data(df)
    
    # 去重
    unique_sections = remove_duplicates(sections, 'code')
    unique_indicators = remove_duplicates(indicators, 'id')
    
    print(f"\n=== 处理结果 ===")
    print(f"提取到 {len(unique_sections)} 个唯一小节")
    print(f"提取到 {len(unique_indicators)} 个唯一指标")
    
    # 显示提取结果
    if unique_sections:
        print("\n=== 小节列表 ===")
        for section in unique_sections:
            print(f"{section['code']} - {section['name']}")
    
    if unique_indicators:
        print("\n=== 指标列表 ===")
        for indicator in unique_indicators:
            print(f"{indicator['id']} - {indicator['name']}")
    
    # 插入数据库
    if unique_sections or unique_indicators:
        print("\n=== 开始导入数据库 ===")
        insert_sections_to_db(unique_sections, db_path)
        insert_indicators_to_db(unique_indicators, db_path)
        print("数据导入完成！")
    else:
        print("未找到有效的第一章数据")

if __name__ == "__main__":
    main()
