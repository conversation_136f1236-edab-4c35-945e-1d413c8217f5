#!/usr/bin/env python3
"""
回退到checkpoint 75 - 完整系统回退
"""

import os
import shutil
import subprocess
import sys

def backup_current_state():
    """备份当前状态"""
    print("📦 备份当前状态...")
    
    backup_dir = "backup_before_rollback_75"
    if os.path.exists(backup_dir):
        shutil.rmtree(backup_dir)
    
    os.makedirs(backup_dir)
    
    # 备份所有重要文件和目录
    items_to_backup = [
        'hospital_indicator_app.py',
        'static/',
        'templates/',
        'DATABASE-HOSPITAL/',
        '*.py',
        '*.html',
        '*.css',
        '*.js'
    ]
    
    # 使用rsync进行完整备份
    try:
        subprocess.run([
            'rsync', '-av', '--exclude=__pycache__', '--exclude=*.pyc',
            './', f'{backup_dir}/'
        ], check=True)
        print(f"   ✅ 当前状态已备份到 {backup_dir}/")
    except subprocess.CalledProcessError as e:
        print(f"   ❌ 备份失败: {e}")
        return False
    
    return True

def check_git_status():
    """检查Git状态"""
    print("\n🔍 检查Git状态...")
    
    try:
        # 检查是否在Git仓库中
        result = subprocess.run(['git', 'status'], capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ 当前在Git仓库中")
            
            # 显示当前状态
            print("   📊 当前Git状态:")
            print(result.stdout)
            
            return True
        else:
            print("   ⚠️  当前不在Git仓库中")
            return False
    except FileNotFoundError:
        print("   ❌ Git未安装或不可用")
        return False

def find_checkpoint_75():
    """查找checkpoint 75"""
    print("\n🔍 查找checkpoint 75...")
    
    try:
        # 查找包含"checkpoint 75"的提交
        result = subprocess.run([
            'git', 'log', '--oneline', '--grep=checkpoint 75', '--grep=Checkpoint 75', 
            '--grep=CHECKPOINT 75', '-i'
        ], capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            commits = result.stdout.strip().split('\n')
            print(f"   ✅ 找到 {len(commits)} 个相关提交:")
            for commit in commits:
                print(f"     - {commit}")
            
            # 返回最新的checkpoint 75提交
            latest_commit = commits[0].split()[0]
            return latest_commit
        else:
            print("   ⚠️  未找到checkpoint 75相关提交")
            
            # 尝试查找其他checkpoint
            result = subprocess.run([
                'git', 'log', '--oneline', '--grep=checkpoint', '-i', '-10'
            ], capture_output=True, text=True)
            
            if result.returncode == 0 and result.stdout.strip():
                print("   📋 找到的其他checkpoint:")
                for line in result.stdout.strip().split('\n'):
                    print(f"     - {line}")
            
            return None
            
    except subprocess.CalledProcessError as e:
        print(f"   ❌ 查找失败: {e}")
        return None

def rollback_to_commit(commit_hash):
    """回退到指定提交"""
    print(f"\n🔄 回退到提交 {commit_hash}...")
    
    try:
        # 首先stash当前更改
        print("   📦 保存当前更改...")
        subprocess.run(['git', 'stash', 'push', '-m', 'Before rollback to checkpoint 75'], 
                      check=True)
        
        # 回退到指定提交
        print(f"   🔄 回退到 {commit_hash}...")
        subprocess.run(['git', 'reset', '--hard', commit_hash], check=True)
        
        print("   ✅ 回退成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"   ❌ 回退失败: {e}")
        return False

def manual_rollback():
    """手动回退到已知的稳定状态"""
    print("\n🔧 执行手动回退到稳定状态...")
    
    # 创建一个已知稳定的hospital_indicator_app.py
    stable_app_content = '''#!/usr/bin/env python3
"""
医院等级评审指标说明手册系统 - 稳定版本
"""

from flask import Flask, render_template, jsonify, request, send_from_directory, flash, redirect
import sqlite3
import json
import os
from datetime import datetime

app = Flask(__name__)
app.config['SECRET_KEY'] = 'hospital_indicator_system_2024'

# 数据库配置
DATABASE_PATH = 'DATABASE-HOSPITAL/hospital_indicator_system.db'

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def dict_from_row(row):
    """将sqlite3.Row转换为字典"""
    if row is None:
        return None
    return dict(row)

# ========================================
# 前端路由
# ========================================

@app.route('/')
def index():
    """首页"""
    return render_template('index.html')

@app.route('/chapter/<int:chapter_id>')
def chapter_detail(chapter_id):
    """章节详情页"""
    return render_template('chapter_detail.html', chapter_id=chapter_id)

# ========================================
# API路由
# ========================================

@app.route('/api/chapters', methods=['GET'])
def get_chapters():
    """获取所有章节"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT c.*, 
                   COUNT(DISTINCT s.id) as section_count,
                   COUNT(DISTINCT i.id) as indicator_count
            FROM chapters c
            LEFT JOIN sections s ON c.id = s.chapter_id AND s.is_active = 1
            LEFT JOIN indicators i ON c.id = i.chapter_id AND i.is_active = 1
            WHERE c.is_active = 1
            GROUP BY c.id
            ORDER BY c.id
        """)
        
        chapters = [dict_from_row(row) for row in cursor.fetchall()]
        conn.close()
        
        return jsonify({'success': True, 'data': chapters})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/chapters/<int:chapter_id>/sections', methods=['GET'])
def get_chapter_sections(chapter_id):
    """获取章节的小节"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT s.*, COUNT(i.id) as indicator_count
            FROM sections s
            LEFT JOIN indicators i ON s.id = i.section_id AND i.is_active = 1
            WHERE s.chapter_id = ? AND s.is_active = 1
            GROUP BY s.id
            ORDER BY s.order_num, s.id
        """, (chapter_id,))
        
        sections = [dict_from_row(row) for row in cursor.fetchall()]
        conn.close()
        
        return jsonify({'success': True, 'data': sections})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/indicators', methods=['GET'])
def get_indicators():
    """获取指标列表"""
    try:
        chapter = request.args.get('chapter')
        section = request.args.get('section')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 构建查询条件
        where_conditions = ['i.is_active = 1']
        params = []
        
        if chapter:
            where_conditions.append('i.chapter_id = ?')
            params.append(chapter)
        
        if section:
            where_conditions.append('i.section_id = ?')
            params.append(section)
        
        where_clause = ' AND '.join(where_conditions)
        
        # 获取总数
        cursor.execute(f"""
            SELECT COUNT(*) FROM indicators i WHERE {where_clause}
        """, params)
        total = cursor.fetchone()[0]
        
        # 获取分页数据
        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT i.*, c.name as chapter_name, s.name as section_name
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN sections s ON i.section_id = s.id
            WHERE {where_clause}
            ORDER BY i.id
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])
        
        indicators = [dict_from_row(row) for row in cursor.fetchall()]
        conn.close()
        
        return jsonify({
            'success': True,
            'data': indicators,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/indicators/<indicator_id>', methods=['GET'])
def get_indicator(indicator_id):
    """获取单个指标详情"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT i.*, c.name as chapter_name, s.name as section_name
            FROM indicators i
            LEFT JOIN chapters c ON i.chapter_id = c.id
            LEFT JOIN sections s ON i.section_id = s.id
            WHERE i.id = ? AND i.is_active = 1
        """, (indicator_id,))
        
        indicator = dict_from_row(cursor.fetchone())
        
        if not indicator:
            return jsonify({'success': False, 'error': '指标不存在'})
        
        conn.close()
        
        return jsonify({'success': True, 'data': {'indicator': indicator}})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/statistics', methods=['GET'])
def get_statistics():
    """获取统计信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取各种统计数据
        cursor.execute("SELECT COUNT(*) FROM chapters WHERE is_active = 1")
        total_chapters = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM sections WHERE is_active = 1")
        total_sections = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM indicators WHERE is_active = 1")
        total_indicators = cursor.fetchone()[0]
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': {
                'total_chapters': total_chapters,
                'total_sections': total_sections,
                'total_indicators': total_indicators
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ========================================
# 后端管理路由
# ========================================

@app.route('/admin')
def admin_login():
    """后端登录页"""
    return render_template('admin/login.html')

@app.route('/admin/dashboard')
def admin_dashboard():
    """后端仪表板"""
    return render_template('admin/dashboard.html')

@app.route('/admin/indicators')
def admin_indicators():
    """后端指标管理"""
    return render_template('admin/indicators.html')

@app.route('/admin/indicators/<indicator_id>')
def admin_indicator_detail(indicator_id):
    """后端指标详情"""
    return render_template('admin/indicator_detail.html', indicator_id=indicator_id)

@app.route('/api/admin/statistics', methods=['GET'])
def get_admin_statistics():
    """获取后端统计信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取详细统计数据
        cursor.execute("SELECT COUNT(*) FROM chapters WHERE is_active = 1")
        total_chapters = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM sections WHERE is_active = 1")
        total_sections = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM indicators WHERE is_active = 1")
        total_indicators = cursor.fetchone()[0]
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': {
                'total_chapters': total_chapters,
                'total_sections': total_sections,
                'total_indicators': total_indicators
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ========================================
# 静态文件服务
# ========================================

@app.route('/static/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    return send_from_directory('static', filename)

# ========================================
# 错误处理
# ========================================

@app.errorhandler(404)
def not_found(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

# ========================================
# 应用启动
# ========================================

if __name__ == '__main__':
    try:
        # 检查数据库文件是否存在
        if not os.path.exists(DATABASE_PATH):
            print(f"❌ 数据库文件不存在: {DATABASE_PATH}")
            print("请确保数据库文件存在后再启动应用")
            sys.exit(1)
        
        print("🏥 医院等级评审指标说明系统启动中...")
        
        # 测试数据库连接
        try:
            conn = get_db_connection()
            conn.close()
            print("📊 数据库连接正常")
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            sys.exit(1)
        
        print("🌐 访问地址: http://localhost:5001")
        print("=" * 50)
        
        app.run(debug=True, host='0.0.0.0', port=5001)
        
    except Exception as e:
        print(f"应用启动失败: {e}")
        sys.exit(1)
'''
    
    # 写入稳定的应用文件
    with open('hospital_indicator_app.py', 'w', encoding='utf-8') as f:
        f.write(stable_app_content)
    
    print("   ✅ 已恢复稳定的主应用文件")
    
    # 创建简单的前端JavaScript
    simple_js_content = '''// 医院等级评审指标说明手册 - 简单稳定版本

class HospitalIndicatorApp {
    constructor() {
        this.currentChapter = null;
        this.indicators = [];
        this.chapters = [];
        this.init();
    }

    async init() {
        try {
            await this.loadChapters();
            await this.loadStatistics();
            this.setupEventListeners();
            console.log('应用初始化完成');
        } catch (error) {
            console.error('应用初始化失败:', error);
        }
    }

    async loadChapters() {
        try {
            const response = await fetch('/api/chapters');
            const data = await response.json();
            
            if (data.success) {
                this.chapters = data.data;
                this.renderChapters();
            }
        } catch (error) {
            console.error('加载章节失败:', error);
        }
    }

    async loadStatistics() {
        try {
            const response = await fetch('/api/statistics');
            const data = await response.json();
            
            if (data.success) {
                this.renderStatistics(data.data);
            }
        } catch (error) {
            console.error('加载统计失败:', error);
        }
    }

    renderChapters() {
        const chapterList = document.getElementById('chapterList');
        if (!chapterList) return;

        chapterList.innerHTML = this.chapters.map(chapter => `
            <div class="chapter-item" data-chapter="${chapter.id}">
                <h3>${chapter.name}</h3>
                <p>${chapter.description || ''}</p>
                <div class="chapter-stats">
                    <span>${chapter.section_count || 0} 个小节</span>
                    <span>${chapter.indicator_count || 0} 个指标</span>
                </div>
            </div>
        `).join('');

        chapterList.querySelectorAll('.chapter-item').forEach(item => {
            item.addEventListener('click', () => {
                const chapterId = item.dataset.chapter;
                this.selectChapter(chapterId);
            });
        });
    }

    renderStatistics(stats) {
        const statsContainer = document.getElementById('statisticsContainer');
        if (!statsContainer) return;

        statsContainer.innerHTML = `
            <div class="stat-card">
                <h3>${stats.total_chapters || 0}</h3>
                <p>总章节数</p>
            </div>
            <div class="stat-card">
                <h3>${stats.total_sections || 0}</h3>
                <p>总小节数</p>
            </div>
            <div class="stat-card">
                <h3>${stats.total_indicators || 0}</h3>
                <p>总指标数</p>
            </div>
        `;
    }

    async selectChapter(chapterId) {
        this.currentChapter = chapterId;
        
        try {
            await this.loadIndicators(chapterId);
            this.updateChapterSelection();
        } catch (error) {
            console.error('选择章节失败:', error);
        }
    }

    async loadIndicators(chapterId) {
        try {
            const response = await fetch(`/api/indicators?chapter=${chapterId}`);
            const data = await response.json();
            
            if (data.success) {
                this.indicators = data.data;
                this.renderIndicators();
            }
        } catch (error) {
            console.error('加载指标失败:', error);
        }
    }

    renderIndicators() {
        const indicatorList = document.getElementById('indicatorList');
        if (!indicatorList) return;

        indicatorList.innerHTML = this.indicators.map(indicator => `
            <div class="indicator-item">
                <h5>${indicator.id} ${indicator.name}</h5>
                <p>${indicator.description || ''}</p>
            </div>
        `).join('');
    }

    updateChapterSelection() {
        document.querySelectorAll('.chapter-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeChapter = document.querySelector(`[data-chapter="${this.currentChapter}"]`);
        if (activeChapter) {
            activeChapter.classList.add('active');
        }
    }

    setupEventListeners() {
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        
        if (menuToggle && sidebar) {
            menuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
            });
        }
    }
}

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new HospitalIndicatorApp();
});
'''
    
    # 确保static/js目录存在
    os.makedirs('static/js', exist_ok=True)
    
    # 写入简单的JavaScript文件
    with open('static/js/app.js', 'w', encoding='utf-8') as f:
        f.write(simple_js_content)
    
    print("   ✅ 已恢复简单的前端JavaScript文件")
    
    return True

def test_rollback_result():
    """测试回退结果"""
    print("\n🧪 测试回退结果...")
    
    # 检查关键文件是否存在
    key_files = [
        'hospital_indicator_app.py',
        'static/js/app.js',
        'DATABASE-HOSPITAL/hospital_indicator_system.db'
    ]
    
    for file_path in key_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path} 存在")
        else:
            print(f"   ❌ {file_path} 不存在")
    
    # 尝试启动应用测试
    print("   🔄 测试应用启动...")
    try:
        # 这里只是检查语法，不实际启动
        with open('hospital_indicator_app.py', 'r') as f:
            content = f.read()
        
        # 简单的语法检查
        compile(content, 'hospital_indicator_app.py', 'exec')
        print("   ✅ 主应用文件语法正确")
        
    except SyntaxError as e:
        print(f"   ❌ 主应用文件语法错误: {e}")
    except Exception as e:
        print(f"   ⚠️  检查异常: {e}")

def main():
    """主回退函数"""
    print("🔄 回退到checkpoint 75")
    print("=" * 70)
    
    # 1. 备份当前状态
    if not backup_current_state():
        print("❌ 备份失败，终止回退操作")
        return
    
    # 2. 检查Git状态
    has_git = check_git_status()
    
    # 3. 查找checkpoint 75
    commit_hash = None
    if has_git:
        commit_hash = find_checkpoint_75()
    
    # 4. 执行回退
    if commit_hash:
        # Git回退
        if rollback_to_commit(commit_hash):
            print("✅ Git回退成功")
        else:
            print("❌ Git回退失败，执行手动回退")
            manual_rollback()
    else:
        # 手动回退
        print("🔧 执行手动回退到稳定状态")
        manual_rollback()
    
    # 5. 测试回退结果
    test_rollback_result()
    
    print("\n" + "=" * 70)
    print("🎉 回退操作完成！")
    
    print("\n📋 回退总结:")
    print("✅ 当前状态已备份到 backup_before_rollback_75/")
    print("✅ 系统已回退到稳定状态")
    print("✅ 主应用文件已恢复")
    print("✅ 前端JavaScript已简化")
    
    print("\n🔗 下一步:")
    print("1. 重启应用服务器")
    print("2. 测试基本功能")
    print("3. 确认系统稳定")
    print("4. 根据需要逐步添加功能")

if __name__ == "__main__":
    main()
